import { Link } from "react-router-dom";
import { ArrowRight, Mail, Phone, MapPin } from "lucide-react";

const Contact = () => {
  return (
    <div className="pt-20 min-h-screen bg-brand-black">
      <div className="section-padding">
        <div className="container-custom">
          <div className="max-w-4xl mx-auto">
            <div className="text-center mb-16">
              <h1 className="text-4xl md:text-6xl font-bold text-brand-white mb-6">
                Contact Us
              </h1>
              <p className="text-xl text-brand-grey-300 leading-relaxed">
                Ready to transform your digital presence? Get in touch with our
                team for a free consultation and discover how we can help your
                business grow.
              </p>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
              {/* Contact Information */}
              <div>
                <h2 className="text-2xl font-bold text-brand-white mb-8">
                  Get In Touch
                </h2>

                <div className="space-y-6">
                  <div className="flex items-start space-x-4">
                    <div className="w-12 h-12 bg-brand-white rounded-lg flex items-center justify-center flex-shrink-0">
                      <Mail className="text-brand-black" size={20} />
                    </div>
                    <div>
                      <h3 className="text-brand-white font-semibold mb-1">
                        Email
                      </h3>
                      <p className="text-brand-grey-300">
                        <EMAIL>
                      </p>
                      <p className="text-brand-grey-400 text-sm">
                        We'll respond within 24 hours
                      </p>
                    </div>
                  </div>

                  <div className="flex items-start space-x-4">
                    <div className="w-12 h-12 bg-brand-white rounded-lg flex items-center justify-center flex-shrink-0">
                      <Phone className="text-brand-black" size={20} />
                    </div>
                    <div>
                      <h3 className="text-brand-white font-semibold mb-1">
                        Phone
                      </h3>
                      <p className="text-brand-grey-300">+971 58 309 2091</p>
                     
                    </div>
                  </div>

                  <div className="flex items-start space-x-4">
                    <div className="w-12 h-12 bg-brand-white rounded-lg flex items-center justify-center flex-shrink-0">
                      <MapPin className="text-brand-black" size={20} />
                    </div>
                    <div>
                      
                      <p className="text-brand-grey-300">
                        Dubai, United Arab Emirates
                       
                      </p>
                     
                    </div>
                  </div>
                </div>
              </div>

              {/* Contact Form Placeholder */}
              <div className="bg-brand-grey-900 border border-brand-grey-700 rounded-2xl p-8">
                <h2 className="text-2xl font-bold text-brand-white mb-6">
                  Send Us a Message
                </h2>
                

                <div className="space-y-4">
                  <a
                    href="mailto:<EMAIL>?subject=Project Inquiry&body=Hi Click ForYou team,%0D%0A%0D%0AI'm interested in discussing a potential project.%0D%0A%0D%0APlease contact me to schedule a consultation.%0D%0A%0D%0AThank you!"
                    className="btn-primary w-full text-center inline-block"
                  >
                    Send Email Now
                    <ArrowRight className="ml-2 h-5 w-5" />
                  </a>

                  <Link
                    to="/"
                    className="btn-secondary w-full text-center inline-block"
                  >
                    Back to Home
                  </Link>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Contact;
