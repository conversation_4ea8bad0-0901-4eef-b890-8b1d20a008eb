import { <PERSON> } from "react-router-dom";
import { motion } from "framer-motion";
import {
  Users,
  Target,
  Award,
  Lightbulb,
  Heart,
  Shield,
  ArrowRight,
  CheckCircle,
  Quote,
  Sparkles,
  Zap,
  Star,
  Globe,
  TrendingUp,
  Rocket,
  Eye,
  Brain,
  Layers,
} from "lucide-react";
import AnimatedSection from "@/shared/components/AnimatedSection";
import ParticleSystem from "@/shared/components/ParticleSystem";
import FloatingElements from "@/shared/components/FloatingElements";
import { useLanguage } from "@/core/providers/LanguageContext";
import { cn } from "@/core/utils/utils";

const About = () => {
  const { t, isRTL } = useLanguage();

  const teamMembers = [
    {
      name: "<PERSON><PERSON><PERSON><PERSON>",
      role: "CEO & Founder",
      image:"https://icheck24.com/wp-content/uploads/2024/11/Taswirti.jpg",
      bio: "Visionary leader with 10+ years in digital marketing, driving company growth and strategic innovation to help businesses achieve exceptional results.",
      expertise: ["Digital Strategy", "Business Growth", "Innovation"],
      achievements: "150+ Successful Projects"
    },
  ];

  const stats = [
    { number: "4+", label: "Years Experience", icon: <TrendingUp className="w-6 h-6" /> },
    { number: "150+", label: "Happy Clients", icon: <Users className="w-6 h-6" /> },
    { number: "500+", label: "Projects Completed", icon: <Target className="w-6 h-6" /> },
    { number: "⭐", label: "Client Satisfaction", icon: <Star className="w-6 h-6" /> },
  ];

  const values = [
    {
      icon: <Heart className="w-8 h-8" />,
      title: "Client-Centric",
      description: "We put our clients' success at the heart of everything we do, building lasting partnerships based on trust and results.",
      gradient: "from-red-500 to-pink-500"
    },
    {
      icon: <Lightbulb className="w-8 h-8" />,
      title: "Innovation",
      description: "We stay ahead of digital trends and continuously evolve our strategies to deliver cutting-edge solutions.",
      gradient: "from-yellow-500 to-orange-500"
    },
    {
      icon: <Shield className="w-8 h-8" />,
      title: "Integrity",
      description: "We believe in transparent communication, honest reporting, and ethical business practices in all our relationships.",
      gradient: "from-blue-500 to-cyan-500"
    },
    {
      icon: <Award className="w-8 h-8" />,
      title: "Excellence",
      description: "We strive for perfection in every campaign, creative piece, and strategic recommendation we deliver.",
      gradient: "from-purple-500 to-indigo-500"
    },
    {
      icon: <Users className="w-8 h-8" />,
      title: "Collaboration",
      description: "We work as an extension of your team, fostering open communication and shared success.",
      gradient: "from-green-500 to-emerald-500"
    },
    {
      icon: <CheckCircle className="w-8 h-8" />,
      title: "Results-Driven",
      description: "We focus on measurable outcomes and data-driven insights to ensure every investment delivers value.",
      gradient: "from-teal-500 to-cyan-500"
    }
  ];

  const milestones = [
    { year: "🌟", title: "Company Founded", description: "Started with a vision to transform digital marketing" },
    { year: "🎯", title: "First Milestone Clients", description: "Reached our first major milestone in client acquisition" },
    { year: "👥", title: "Team Expansion", description: "Grew our team to serve clients better" },
    { year: "🚀", title: "Countless Projects", description: "Successfully completed numerous digital marketing projects" },
    { year: "🌍", title: "Global Reach", description: "Expanded services to international markets" },
  ];

  

  return (
    <div className="relative min-h-screen bg-brand-black overflow-hidden">
      {/* Background Elements */}
      <ParticleSystem />
      <FloatingElements />
      
      {/* Hero Section */}
      <AnimatedSection className="relative pt-32 pb-20">
        <div className="container-custom">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center max-w-5xl mx-auto mb-20"
          >
            <motion.div
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              className="inline-flex items-center gap-2 bg-brand-purple-900/30 border border-brand-purple-500/30 rounded-full px-6 py-3 mb-8"
            >
              <Sparkles className="w-5 h-5 text-brand-purple-400" />
              <span className="text-brand-purple-300 font-medium">About Our Journey</span>
            </motion.div>

            <motion.h1
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.3 }}
              className="text-5xl md:text-7xl lg:text-8xl font-bold text-brand-white mb-8 leading-tight"
            >
              Crafting Digital
              <br />
              <span className="gradient-text bg-gradient-to-r from-brand-purple-400 via-brand-pink-400 to-brand-cyan-400 bg-clip-text text-transparent">
                Excellence
              </span>
            </motion.h1>

            <motion.p
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.5 }}
              className="text-xl md:text-2xl text-brand-grey-300 leading-relaxed max-w-4xl mx-auto"
            >
              We're a forward-thinking digital marketing agency dedicated to transforming brands through 
              innovative strategies, creative excellence, and data-driven results that matter.
            </motion.p>
          </motion.div>

          {/* Stats Section */}
          <motion.div
            initial={{ opacity: 0, y: 40 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.7 }}
            className="grid grid-cols-2 md:grid-cols-4 gap-8 mb-20"
          >
            {stats.map((stat, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.6, delay: 0.8 + index * 0.1 }}
                className="text-center group"
              >
                <div className="bg-gradient-to-br from-brand-purple-900/40 to-brand-pink-900/40 border border-brand-purple-500/20 rounded-2xl p-6 hover:border-brand-purple-400/40 transition-all duration-300 hover:scale-105">
                  <div className="text-brand-purple-400 mb-3 flex justify-center group-hover:scale-110 transition-transform duration-300">
                    {stat.icon}
                  </div>
                  <div className="text-3xl md:text-4xl font-bold text-brand-white mb-2">{stat.number}</div>
                  <div className="text-brand-grey-400 text-sm font-medium">{stat.label}</div>
                </div>
              </motion.div>
            ))}
          </motion.div>
        </div>
      </AnimatedSection>

      {/* Our Story Section */}
      <AnimatedSection className="py-20 bg-gradient-to-br from-brand-grey-900 to-brand-black">
        <div className="container-custom">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center">
            <motion.div
              initial={{ opacity: 0, x: -50 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
            >
              <div className="inline-flex items-center gap-2 bg-brand-purple-900/30 border border-brand-purple-500/30 rounded-full px-4 py-2 mb-6">
                <Rocket className="w-4 h-4 text-brand-purple-400" />
                <span className="text-brand-purple-300 text-sm font-medium">Our Journey</span>
              </div>

              <h2 className="text-4xl md:text-5xl font-bold text-brand-white mb-6 leading-tight">
                From Vision to
                <span className="gradient-text bg-gradient-to-r from-brand-purple-400 to-brand-pink-400 bg-clip-text text-transparent"> Reality</span>
              </h2>

              <p className="text-brand-grey-300 mb-6 leading-relaxed text-lg">
                Founded in 2020, Click ForYou emerged from a simple yet powerful belief: every business deserves 
                exceptional digital marketing that drives real growth. Our founders, seasoned marketing professionals, 
                recognized the need for an agency that combines creative innovation with strategic thinking.
              </p>

              <p className="text-brand-grey-300 mb-8 leading-relaxed text-lg">
                Today, we're proud to serve over 150 clients worldwide, helping them navigate the complex digital 
                landscape and achieve their business objectives through tailored marketing solutions.
              </p>

              <motion.div
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.3 }}
                viewport={{ once: true }}
                className="flex flex-wrap gap-4"
              >
                <div className="bg-gradient-to-r from-brand-purple-600 to-brand-pink-600 rounded-xl p-4 text-center min-w-[120px]">
                  <div className="text-2xl font-bold text-white">4+</div>
                  <div className="text-brand-purple-100 text-sm">Years</div>
                </div>
                <div className="bg-gradient-to-r from-brand-cyan-600 to-brand-blue-600 rounded-xl p-4 text-center min-w-[120px]">
                  <div className="text-2xl font-bold text-white">150+</div>
                  <div className="text-brand-cyan-100 text-sm">Clients</div>
                </div>
                <div className="bg-gradient-to-r from-brand-green-600 to-brand-emerald-600 rounded-xl p-4 text-center min-w-[120px]">
                  <div className="text-2xl font-bold text-white">500+</div>
                  <div className="text-brand-green-100 text-sm">Projects</div>
                </div>
              </motion.div>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, x: 50 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
              className="relative"
            >
              <div className="bg-gradient-to-br from-brand-purple-600/20 to-brand-pink-600/20 rounded-3xl p-8 border border-brand-purple-500/20 backdrop-blur-sm">
                <div className="text-center mb-8">
                  <div className="w-20 h-20 bg-gradient-to-br from-brand-purple-500 to-brand-pink-500 rounded-2xl flex items-center justify-center mx-auto mb-4">
                    <Eye className="w-10 h-10 text-white" />
                  </div>
                  <h3 className="text-2xl font-bold text-brand-white mb-2">Our Vision</h3>
                  <p className="text-brand-grey-300">
                    To be the leading digital marketing agency that transforms businesses through innovative, 
                    data-driven strategies and exceptional creative solutions.
                  </p>
                </div>

                <div className="border-t border-brand-purple-500/20 pt-8">
                  <div className="text-center">
                    <div className="w-20 h-20 bg-gradient-to-br from-brand-cyan-500 to-brand-blue-500 rounded-2xl flex items-center justify-center mx-auto mb-4">
                      <Target className="w-10 h-10 text-white" />
                    </div>
                    <h3 className="text-2xl font-bold text-brand-white mb-2">Our Mission</h3>
                    <p className="text-brand-grey-300">
                      To empower businesses of all sizes with cutting-edge digital marketing solutions that 
                      drive measurable growth and lasting success.
                    </p>
                  </div>
                </div>
              </div>
            </motion.div>
          </div>
        </div>
      </AnimatedSection>

      {/* Values Section */}
      <AnimatedSection className="py-20 bg-brand-black">
        <div className="container-custom">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="text-center mb-16"
          >
            <div className="inline-flex items-center gap-2 bg-brand-purple-900/30 border border-brand-purple-500/30 rounded-full px-6 py-3 mb-8">
              <Heart className="w-5 h-5 text-brand-purple-400" />
              <span className="text-brand-purple-300 font-medium">Our Core Values</span>
            </div>

            <h2 className="text-4xl md:text-5xl font-bold text-brand-white mb-6">
              What Drives Us
              <span className="gradient-text bg-gradient-to-r from-brand-purple-400 to-brand-cyan-400 bg-clip-text text-transparent"> Forward</span>
            </h2>

            <p className="text-xl text-brand-grey-300 max-w-3xl mx-auto">
              Our values shape every decision we make and every solution we create for our clients.
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {values.map((value, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
                className="group"
              >
                <div className="bg-gradient-to-br from-brand-grey-900/50 to-brand-black/50 border border-brand-grey-700 rounded-2xl p-8 h-full hover:border-brand-purple-500/50 transition-all duration-300 hover:scale-105 backdrop-blur-sm">
                  <div className={`w-16 h-16 bg-gradient-to-br ${value.gradient} rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300`}>
                    <div className="text-white">
                      {value.icon}
                    </div>
                  </div>

                  <h3 className="text-xl font-bold text-brand-white mb-4 group-hover:text-brand-purple-300 transition-colors duration-300">
                    {value.title}
                  </h3>

                  <p className="text-brand-grey-300 leading-relaxed">
                    {value.description}
                  </p>
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </AnimatedSection>

      {/* Team Section */}
      <AnimatedSection className="py-20 bg-gradient-to-br from-brand-grey-900 to-brand-black">
        <div className="container-custom">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="text-center mb-16"
          >
            <div className="inline-flex items-center gap-2 bg-brand-purple-900/30 border border-brand-purple-500/30 rounded-full px-6 py-3 mb-8">
              <Users className="w-5 h-5 text-brand-purple-400" />
              <span className="text-brand-purple-300 font-medium">Meet Our Team</span>
            </div>

            <h2 className="text-4xl md:text-5xl font-bold text-brand-white mb-6">
              The Minds Behind
              <span className="gradient-text bg-gradient-to-r from-brand-purple-400 to-brand-cyan-400 bg-clip-text text-transparent"> Success</span>
            </h2>

            <p className="text-xl text-brand-grey-300 max-w-3xl mx-auto">
              Our passionate team of experts brings together creativity, strategy, and technical excellence.
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {teamMembers.map((member, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.2 }}
                viewport={{ once: true }}
                className="group"
              >
                <div className="bg-gradient-to-br from-brand-grey-900/50 to-brand-black/50 border border-brand-grey-700 rounded-2xl p-8 text-center hover:border-brand-purple-500/50 transition-all duration-300 hover:scale-105 backdrop-blur-sm">
                  <div className="relative mb-6">
                    <div className="w-32 h-32 mx-auto rounded-2xl overflow-hidden border-4 border-brand-purple-500/30 group-hover:border-brand-purple-400/60 transition-colors duration-300">
                      <img
                        src={member.image}
                        alt={member.name}
                        className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-300"
                      />
                    </div>
                    <div className="absolute -bottom-2 -right-2 w-8 h-8 bg-gradient-to-br from-brand-purple-500 to-brand-pink-500 rounded-full flex items-center justify-center">
                      <Star className="w-4 h-4 text-white" />
                    </div>
                  </div>

                  <h3 className="text-xl font-bold text-brand-white mb-2 group-hover:text-brand-purple-300 transition-colors duration-300">
                    {member.name}
                  </h3>

                  <p className="text-brand-purple-400 font-medium mb-4">
                    {member.role}
                  </p>

                  <p className="text-brand-grey-300 text-sm leading-relaxed mb-6">
                    {member.bio}
                  </p>

                  <div className="space-y-3">
                    <div className="flex flex-wrap gap-2 justify-center">
                      {member.expertise.map((skill, skillIndex) => (
                        <span
                          key={skillIndex}
                          className="px-3 py-1 bg-brand-purple-900/30 text-brand-purple-300 rounded-full text-xs font-medium border border-brand-purple-500/20"
                        >
                          {skill}
                        </span>
                      ))}
                    </div>
                    <div className="text-brand-cyan-400 text-sm font-medium">
                      {member.achievements}
                    </div>
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </AnimatedSection>

      {/* Call to Action Section */}
      <AnimatedSection className="py-20 bg-gradient-to-br from-brand-purple-900/20 to-brand-pink-900/20">
        <div className="container-custom">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="text-center max-w-4xl mx-auto"
          >
            <div className="inline-flex items-center gap-2 bg-brand-purple-900/30 border border-brand-purple-500/30 rounded-full px-6 py-3 mb-8">
              <Rocket className="w-5 h-5 text-brand-purple-400" />
              <span className="text-brand-purple-300 font-medium">Ready to Start?</span>
            </div>

            <h2 className="text-4xl md:text-5xl font-bold text-brand-white mb-6">
              Let's Build Something
              <span className="gradient-text bg-gradient-to-r from-brand-purple-400 to-brand-cyan-400 bg-clip-text text-transparent"> Amazing Together</span>
            </h2>

            <p className="text-xl text-brand-grey-300 mb-10 leading-relaxed">
              Ready to transform your digital presence? Our team is here to help you achieve
              extraordinary results with innovative marketing strategies tailored to your business.
            </p>

            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <motion.div
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                <Link
                  to="/contact"
                  className="inline-flex items-center gap-2 bg-gradient-to-r from-brand-purple-600 to-brand-pink-600 hover:from-brand-purple-500 hover:to-brand-pink-500 text-white font-semibold px-8 py-4 rounded-xl transition-all duration-300 shadow-lg hover:shadow-brand-purple-500/25"
                >
                  <span>Get Started Today</span>
                  <ArrowRight className="w-5 h-5" />
                </Link>
              </motion.div>

              <motion.div
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                <Link
                  to="/services"
                  className="inline-flex items-center gap-2 bg-transparent border-2 border-brand-purple-500 hover:bg-brand-purple-500/10 text-brand-purple-300 hover:text-brand-purple-200 font-semibold px-8 py-4 rounded-xl transition-all duration-300"
                >
                  <span>View Our Services</span>
                  <Eye className="w-5 h-5" />
                </Link>
              </motion.div>
            </div>
          </motion.div>
        </div>
      </AnimatedSection>
    </div>
  );
};

export default About;
