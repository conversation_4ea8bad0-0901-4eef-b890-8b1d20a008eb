import{aG as T,r as n,o as L,ai as R,j as s,aj as f,aF as q}from"./react-vendor-Dq0qSR31.js";import{b6 as A,b4 as B,b5 as g}from"./vendor-OXu-rwpf.js";import{C as d,e as m,f as x,h as p,l as y,m as u,i as D,A as M,k as U,F as G,n as N,o as b,p as C,q as v,I as P,r as k,t as H}from"./index-CasGuY6o.js";import"./utils-vendor-DSNVchvY.js";import"./state-vendor-DU4y5LsH.js";const V=B({password:g().min(6,"Password must be at least 6 characters"),confirmPassword:g().min(6,"Please confirm your password")}).refine(e=>e.password===e.confirmPassword,{message:"Passwords don't match",path:["confirmPassword"]});function Q(){const{resettoken:e}=T(),[F,S]=n.useState(!1),[h,j]=n.useState(!1),[w,a]=n.useState(""),[E,l]=n.useState(!1),o=L(),t=R({resolver:A(V),defaultValues:{password:"",confirmPassword:""}}),I=async r=>{if(!e){a("Invalid reset token"),l(!0);return}a(""),j(!0),l(!1);try{await H(e,r.password),S(!0),setTimeout(()=>o("/login"),2e3)}catch(i){const c=i?.response?.data?.message;c?.toLowerCase().includes("invalid")||c?.toLowerCase().includes("expired")?(a("This reset link is invalid or has expired. Please request a new password reset."),l(!0)):a(c||(i instanceof Error?i.message:"Failed to reset password"))}finally{j(!1)}};return E?s.jsx("div",{className:"min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8",children:s.jsxs(d,{className:"w-full max-w-md",children:[s.jsxs(m,{className:"text-center",children:[s.jsx("div",{className:"mx-auto flex h-12 w-12 items-center justify-center rounded-full bg-red-100",children:s.jsx(f,{className:"h-6 w-6 text-red-600"})}),s.jsx(x,{className:"text-2xl font-bold",children:"Reset Link Invalid"}),s.jsx(p,{children:"This reset link is invalid or has expired. Please request a new password reset."})]}),s.jsx(y,{className:"flex justify-center",children:s.jsx(u,{onClick:()=>o("/forgot-password"),children:"Request new reset link"})})]})}):F?s.jsx("div",{className:"min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8",children:s.jsxs(d,{className:"w-full max-w-md",children:[s.jsxs(m,{className:"text-center",children:[s.jsx("div",{className:"mx-auto flex h-12 w-12 items-center justify-center rounded-full bg-green-100",children:s.jsx(q,{className:"h-6 w-6 text-green-600"})}),s.jsx(x,{className:"text-2xl font-bold",children:"Password updated"}),s.jsx(p,{children:"Your password has been successfully updated."})]}),s.jsx(y,{className:"flex justify-center",children:s.jsx(u,{onClick:()=>o("/login"),children:"Back to login"})})]})}):s.jsx("div",{className:"min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8",children:s.jsxs("div",{className:"max-w-md w-full space-y-8",children:[s.jsxs("div",{className:"text-center",children:[s.jsx("h2",{className:"mt-6 text-3xl font-extrabold text-gray-900",children:"Reset your password"}),s.jsx("p",{className:"mt-2 text-sm text-gray-600",children:"Enter a new password for your account"})]}),s.jsxs(d,{className:"w-full max-w-md",children:[s.jsxs(m,{children:[s.jsx(x,{children:"New password"}),s.jsx(p,{children:"Choose a strong and secure password"})]}),s.jsxs(D,{children:[w&&s.jsxs(M,{variant:"destructive",className:"mb-4",children:[s.jsx(f,{className:"h-4 w-4"}),s.jsx(U,{children:w})]}),s.jsx(G,{...t,children:s.jsxs("form",{onSubmit:t.handleSubmit(I),className:"space-y-4",children:[s.jsx(N,{control:t.control,name:"password",render:({field:r})=>s.jsxs(b,{children:[s.jsx(C,{children:"New password"}),s.jsx(v,{children:s.jsx(P,{type:"password",placeholder:"••••••••",...r})}),s.jsx(k,{})]})}),s.jsx(N,{control:t.control,name:"confirmPassword",render:({field:r})=>s.jsxs(b,{children:[s.jsx(C,{children:"Confirm new password"}),s.jsx(v,{children:s.jsx(P,{type:"password",placeholder:"••••••••",...r})}),s.jsx(k,{})]})}),s.jsx(u,{type:"submit",className:"w-full",disabled:h,children:h?"Updating password...":"Update password"})]})})]})]})]})})}export{Q as default};
