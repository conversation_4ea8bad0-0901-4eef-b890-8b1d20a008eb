var G=Object.defineProperty,J=Object.defineProperties;var K=Object.getOwnPropertyDescriptors;var j=Object.getOwnPropertySymbols;var R=Object.prototype.hasOwnProperty,D=Object.prototype.propertyIsEnumerable;var E=(s,a,t)=>a in s?G(s,a,{enumerable:!0,configurable:!0,writable:!0,value:t}):s[a]=t,d=(s,a)=>{for(var t in a||(a={}))R.call(a,t)&&E(s,t,a[t]);if(j)for(var t of j(a))D.call(a,t)&&E(s,t,a[t]);return s},g=(s,a)=>J(s,K(a));var h=(s,a)=>{var t={};for(var r in s)R.call(s,r)&&a.indexOf(r)<0&&(t[r]=s[r]);if(s!=null&&j)for(var r of j(s))a.indexOf(r)<0&&D.call(s,r)&&(t[r]=s[r]);return t};var P=(s,a,t)=>new Promise((r,i)=>{var o=n=>{try{f(t.next(n))}catch(m){i(m)}},b=n=>{try{f(t.throw(n))}catch(m){i(m)}},f=n=>n.done?r(n.value):Promise.resolve(n.value).then(o,b);f((t=t.apply(s,a)).next())});import{r as c,j as e,h as p,X as M,a6 as W,e as Y,a1 as Z,k as $,a7 as ee,Q as y,I as u,a8 as te,a9 as ae}from"./index-hEW_vQ3f.js";import{R as se,P as re,C as I,a as le,T as O,O as A,D as L}from"./index-lne2Edaq.js";const ne=se,ie=re,T=c.forwardRef((r,t)=>{var i=r,{className:s}=i,a=h(i,["className"]);return e.jsx(A,g(d({className:p("fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",s)},a),{ref:t}))});T.displayName=A.displayName;const de=W("fixed z-50 gap-4 bg-background p-6 shadow-lg transition ease-in-out data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:duration-300 data-[state=open]:duration-500",{variants:{side:{top:"inset-x-0 top-0 border-b data-[state=closed]:slide-out-to-top data-[state=open]:slide-in-from-top",bottom:"inset-x-0 bottom-0 border-t data-[state=closed]:slide-out-to-bottom data-[state=open]:slide-in-from-bottom",left:"inset-y-0 left-0 h-full w-3/4 border-r data-[state=closed]:slide-out-to-left data-[state=open]:slide-in-from-left sm:max-w-sm",right:"inset-y-0 right-0 h-full w-3/4  border-l data-[state=closed]:slide-out-to-right data-[state=open]:slide-in-from-right sm:max-w-sm"}},defaultVariants:{side:"right"}}),z=c.forwardRef((o,i)=>{var b=o,{side:s="right",className:a,children:t}=b,r=h(b,["side","className","children"]);return e.jsxs(ie,{children:[e.jsx(T,{}),e.jsxs(I,g(d({ref:i,className:p(de({side:s}),a)},r),{children:[t,e.jsxs(le,{className:"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-secondary",children:[e.jsx(M,{className:"h-4 w-4"}),e.jsx("span",{className:"sr-only",children:"Close"})]})]}))]})});z.displayName=I.displayName;const F=t=>{var r=t,{className:s}=r,a=h(r,["className"]);return e.jsx("div",d({className:p("flex flex-col space-y-2 text-center sm:text-left",s)},a))};F.displayName="SheetHeader";const U=t=>{var r=t,{className:s}=r,a=h(r,["className"]);return e.jsx("div",d({className:p("flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2",s)},a))};U.displayName="SheetFooter";const H=c.forwardRef((r,t)=>{var i=r,{className:s}=i,a=h(i,["className"]);return e.jsx(O,d({ref:t,className:p("text-lg font-semibold text-foreground",s)},a))});H.displayName=O.displayName;const ce=c.forwardRef((r,t)=>{var i=r,{className:s}=i,a=h(i,["className"]);return e.jsx(L,d({ref:t,className:p("text-sm text-muted-foreground",s)},a))});ce.displayName=L.displayName;const oe={title:"",description:"",image:"",subServices:[]},ge=()=>{const s=Y(),{id:a}=Z(),[t,r]=c.useState(oe),[i,o]=c.useState(!1),[b,f]=c.useState(null),[n,m]=c.useState({title:"",description:"",image:""}),[S,v]=c.useState(!1),[k,N]=c.useState(null);$.useEffect(()=>{a&&(v(!0),ee(a).then(l=>r(l.data)).catch(()=>N("Failed to load service")).finally(()=>v(!1)))},[a]);const w=l=>{r(g(d({},t),{[l.target.name]:l.target.value}))},V=()=>{f(null),m({title:"",description:"",image:""}),o(!0)},B=l=>{var x;f(l),m(((x=t.subServices)==null?void 0:x[l])||{title:"",description:"",image:""}),o(!0)},C=l=>{m(g(d({},n),{[l.target.name]:l.target.value}))},Q=()=>{if(!n.title.trim())return;let l=[...t.subServices||[]];b!==null?l[b]=n:l.push(n),r(g(d({},t),{subServices:l})),o(!1)},X=l=>{r(g(d({},t),{subServices:(t.subServices||[]).filter((x,q)=>q!==l)}))},_=()=>P(null,null,function*(){v(!0),N(null);try{const l={title:t.title,description:t.description,image:t.image,subServices:t.subServices};a?yield te(a,l):yield ae(l),s("/dashboard/services")}catch(l){N("Failed to save service")}finally{v(!1)}});return e.jsxs("div",{className:"min-h-screen bg-brand-grey-950 flex flex-col items-center py-10",children:[e.jsxs("div",{className:"w-full max-w-2xl bg-brand-grey-900 rounded-2xl shadow-2xl p-8 border border-brand-grey-800 flex flex-col gap-8",children:[e.jsx("h1",{className:"text-2xl font-bold text-brand-accent-100 mb-4",children:a?"Edit Service":"Add Service"}),e.jsxs("div",{className:"flex flex-col gap-6",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block mb-1 font-semibold text-brand-accent-100 text-lg",children:"Title"}),e.jsx(y,{name:"title",value:t.title,onChange:w,placeholder:"Enter service title",className:"bg-brand-grey-900 border-brand-grey-700 text-brand-white"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block mb-1 font-semibold text-brand-accent-100 text-lg",children:"Description"}),e.jsx("textarea",{name:"description",value:t.description,onChange:w,placeholder:"Enter service description",className:"bg-brand-grey-900 border-brand-grey-700 text-brand-white w-full rounded-md p-2",rows:4})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block mb-1 font-semibold text-brand-accent-100 text-lg",children:"Image URL"}),e.jsx(y,{name:"image",value:t.image,onChange:w,placeholder:"Paste image URL here (optional)",className:"bg-brand-grey-900 border-brand-grey-700 text-brand-white"}),t.image&&e.jsx("img",{src:t.image,alt:"Preview",className:"mt-2 max-h-40 rounded-xl border border-brand-grey-700 w-full object-cover shadow-lg"})]})]}),e.jsxs("div",{className:"flex flex-col gap-4 mt-8",children:[e.jsxs("div",{className:"flex items-center justify-between mb-2",children:[e.jsx("h2",{className:"text-xl font-bold text-brand-accent-100",children:"Sub-Services"}),e.jsx(u,{type:"button",onClick:V,className:"bg-brand-accent-100 text-brand-black font-semibold px-4 py-2 rounded-lg shadow hover:bg-brand-accent-200 transition",children:"Add Sub-Service"})]}),(t.subServices||[]).length===0&&e.jsx("div",{className:"text-brand-grey-400 text-sm",children:"No sub-services yet. Add your first one!"}),e.jsx("div",{className:"flex flex-col gap-3",children:(t.subServices||[]).map((l,x)=>e.jsxs("div",{className:"bg-brand-grey-800 border border-brand-grey-700 rounded-lg p-4 flex items-center gap-4",children:[l.image&&e.jsx("img",{src:l.image,alt:l.title,className:"w-12 h-12 object-cover rounded border border-brand-grey-700"}),e.jsxs("div",{className:"flex-1",children:[e.jsx("div",{className:"font-semibold text-brand-white",children:l.title}),e.jsx("div",{className:"text-brand-grey-300 text-xs",children:l.description})]}),e.jsx(u,{type:"button",size:"sm",variant:"secondary",onClick:()=>B(x),children:"Edit"}),e.jsx(u,{type:"button",size:"sm",variant:"destructive",onClick:()=>X(x),children:"Delete"})]},x))})]}),e.jsxs("div",{className:"flex flex-row gap-4 justify-end mt-8",children:[e.jsx(u,{type:"button",variant:"secondary",onClick:()=>s("/dashboard/services"),disabled:S,children:"Cancel"}),e.jsx(u,{type:"button",onClick:_,className:"bg-brand-accent-100 text-brand-black font-bold",disabled:S,children:S?"Saving...":"Save Service"})]}),k&&e.jsx("div",{className:"text-red-500 mt-2 text-center",children:k})]}),e.jsx(ne,{open:i,onOpenChange:o,children:e.jsxs(z,{side:"left",className:"bg-brand-grey-900 border-r border-brand-grey-800 max-w-md w-full",children:[e.jsx(F,{children:e.jsx(H,{children:b!==null?"Edit Sub-Service":"Add Sub-Service"})}),e.jsxs("div",{className:"flex-1 flex flex-col gap-6 p-6",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block mb-1 font-semibold text-brand-accent-100 text-lg",children:"Title"}),e.jsx(y,{name:"title",value:n.title,onChange:C,placeholder:"Enter sub-service title",className:"bg-brand-grey-900 border-brand-grey-700 text-brand-white"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block mb-1 font-semibold text-brand-accent-100 text-lg",children:"Description"}),e.jsx("textarea",{name:"description",value:n.description,onChange:C,placeholder:"Enter sub-service description",className:"bg-brand-grey-900 border-brand-grey-700 text-brand-white w-full rounded-md p-2",rows:3})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block mb-1 font-semibold text-brand-accent-100 text-lg",children:"Image URL"}),e.jsx(y,{name:"image",value:n.image,onChange:C,placeholder:"Paste image URL here (optional)",className:"bg-brand-grey-900 border-brand-grey-700 text-brand-white"}),n.image&&e.jsx("img",{src:n.image,alt:"Preview",className:"mt-2 max-h-32 rounded-xl border border-brand-grey-700 w-full object-cover shadow"})]})]}),e.jsxs(U,{children:[e.jsx(u,{type:"button",variant:"secondary",onClick:()=>o(!1),children:"Cancel"}),e.jsx(u,{type:"button",onClick:Q,className:"bg-brand-accent-100 text-brand-black font-bold",children:"Save"})]})]})})]})};export{ge as default};
