var L=Object.defineProperty,A=Object.defineProperties;var D=Object.getOwnPropertyDescriptors;var j=Object.getOwnPropertySymbols;var M=Object.prototype.hasOwnProperty,P=Object.prototype.propertyIsEnumerable;var f=(t,a,s)=>a in t?L(t,a,{enumerable:!0,configurable:!0,writable:!0,value:s}):t[a]=s,x=(t,a)=>{for(var s in a||(a={}))M.call(a,s)&&f(t,s,a[s]);if(j)for(var s of j(a))P.call(a,s)&&f(t,s,a[s]);return t},g=(t,a)=>A(t,D(a));var p=(t,a,s)=>new Promise((d,n)=>{var m=r=>{try{c(s.next(r))}catch(i){n(i)}},l=r=>{try{c(s.throw(r))}catch(i){n(i)}},c=r=>r.done?d(r.value):Promise.resolve(r.value).then(m,l);c((s=s.apply(t,a)).next())});import{c as R,r as h,q as T,j as e,s as y,t as w,v as N,w as b,x as k,y as v,z as C,D as H,F,H as S,I as E,L as I,J as V,K as B,N as W,O as q,P as z,Q as J,V as K,W as O,Y as Q,$ as Y,a0 as $}from"./index-hEW_vQ3f.js";/**
 * @license lucide-react v0.336.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const G=R("MailCheck",[["path",{d:"M22 13V6a2 2 0 0 0-2-2H4a2 2 0 0 0-2 2v12c0 1.1.9 2 2 2h8",key:"12jkf8"}],["path",{d:"m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7",key:"1ocrg3"}],["path",{d:"m16 19 2 2 4-4",key:"1b14m6"}]]),U=Y({email:$().email("Invalid email address")});function _(){const[t,a]=h.useState(!1),[s,d]=h.useState(!1),[n,m]=h.useState(""),l=T({resolver:Q(U),defaultValues:{email:""}}),c=r=>p(null,null,function*(){var i,u;m(""),d(!0);try{yield O(r.email),a(!0)}catch(o){m(((u=(i=o==null?void 0:o.response)==null?void 0:i.data)==null?void 0:u.message)||(o instanceof Error?o.message:"Failed to send reset email"))}finally{d(!1)}});return t?e.jsx("div",{className:"min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8",children:e.jsxs(y,{className:"w-full max-w-md",children:[e.jsxs(w,{children:[e.jsx("div",{className:"mx-auto flex h-12 w-12 items-center justify-center rounded-full bg-green-100",children:e.jsx(G,{className:"h-6 w-6 text-green-600"})}),e.jsx(N,{className:"text-center text-2xl font-bold tracking-tight",children:"Check your email"}),e.jsxs(b,{className:"text-center",children:["We've sent a password reset link to ",l.watch("email")]})]}),e.jsx(k,{children:e.jsxs(v,{children:[e.jsx(C,{className:"h-4 w-4"}),e.jsx(H,{children:"Email sent"}),e.jsx(F,{children:"If you don't see the email, check your spam folder or try again."})]})}),e.jsxs(S,{className:"flex flex-col gap-4",children:[e.jsx(E,{asChild:!0,className:"w-full",children:e.jsx(I,{to:"/login",children:"Back to login"})}),e.jsxs("p",{className:"text-center text-sm text-muted-foreground",children:["Didn't receive the email?"," ",e.jsx("button",{onClick:()=>a(!1),className:"font-medium text-indigo-600 hover:text-indigo-500",children:"Resend"})]})]})]})}):e.jsx("div",{className:"min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8",children:e.jsxs("div",{className:"max-w-md w-full space-y-8",children:[e.jsxs("div",{className:"text-center",children:[e.jsx("h2",{className:"mt-6 text-3xl font-extrabold text-gray-900",children:"Forgot your password?"}),e.jsx("p",{className:"mt-2 text-sm text-gray-600",children:"Enter your email and we'll send you a link to reset your password"})]}),e.jsxs(y,{className:"w-full max-w-md",children:[e.jsxs(w,{children:[e.jsx(N,{children:"Reset password"}),e.jsx(b,{children:"Enter your email to receive a reset link"})]}),e.jsxs(k,{children:[n&&e.jsxs(v,{variant:"destructive",className:"mb-4",children:[e.jsx(C,{className:"h-4 w-4"}),e.jsx(F,{children:n})]}),e.jsx(V,g(x({},l),{children:e.jsxs("form",{onSubmit:l.handleSubmit(c),className:"space-y-4",children:[e.jsx(B,{control:l.control,name:"email",render:({field:r})=>e.jsxs(W,{children:[e.jsx(q,{children:"Email address"}),e.jsx(z,{children:e.jsx(J,x({type:"email",placeholder:"<EMAIL>"},r))}),e.jsx(K,{})]})}),e.jsx(E,{type:"submit",className:"w-full",disabled:s,children:s?"Sending reset link...":"Send reset link"})]})}))]}),e.jsx(S,{className:"flex flex-col items-start gap-2 text-sm",children:e.jsxs("p",{className:"text-center text-sm text-gray-600 w-full",children:["Remember your password?"," ",e.jsx(I,{to:"/login",className:"font-medium text-indigo-600 hover:text-indigo-500",children:"Sign in"})]})})]})]})})}export{_ as default};
