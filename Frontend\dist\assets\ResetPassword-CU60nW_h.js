var H=Object.defineProperty,U=Object.defineProperties;var V=Object.getOwnPropertyDescriptors;var C=Object.getOwnPropertySymbols;var Y=Object.prototype.hasOwnProperty,z=Object.prototype.propertyIsEnumerable;var P=(e,a,r)=>a in e?H(e,a,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[a]=r,p=(e,a)=>{for(var r in a||(a={}))Y.call(a,r)&&P(e,r,a[r]);if(C)for(var r of C(a))z.call(a,r)&&P(e,r,a[r]);return e},F=(e,a)=>U(e,V(a));var S=(e,a,r)=>new Promise((m,i)=>{var u=t=>{try{c(r.next(t))}catch(n){i(n)}},o=t=>{try{c(r.throw(t))}catch(n){i(n)}},c=t=>t.done?m(t.value):Promise.resolve(t.value).then(u,o);c((r=r.apply(e,a)).next())});import{a1 as J,r as w,e as K,q as O,j as s,s as j,t as f,z as k,v as y,w as N,H as E,I as g,a2 as Q,x as $,y as G,F as W,J as X,K as I,N as T,O as L,P as R,Q as q,V as A,Y as Z,a3 as _,$ as M,a0 as B}from"./index-hEW_vQ3f.js";const ss=M({password:B().min(6,"Password must be at least 6 characters"),confirmPassword:B().min(6,"Please confirm your password")}).refine(e=>e.password===e.confirmPassword,{message:"Passwords don't match",path:["confirmPassword"]});function as(){const{resettoken:e}=J(),[a,r]=w.useState(!1),[m,i]=w.useState(!1),[u,o]=w.useState(""),[c,t]=w.useState(!1),n=K(),h=O({resolver:Z(ss),defaultValues:{password:"",confirmPassword:""}}),D=d=>S(null,null,function*(){var v,b;if(!e){o("Invalid reset token"),t(!0);return}o(""),i(!0),t(!1);try{yield _(e,d.password),r(!0),setTimeout(()=>n("/login"),2e3)}catch(x){const l=(b=(v=x==null?void 0:x.response)==null?void 0:v.data)==null?void 0:b.message;l!=null&&l.toLowerCase().includes("invalid")||l!=null&&l.toLowerCase().includes("expired")?(o("This reset link is invalid or has expired. Please request a new password reset."),t(!0)):o(l||(x instanceof Error?x.message:"Failed to reset password"))}finally{i(!1)}});return c?s.jsx("div",{className:"min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8",children:s.jsxs(j,{className:"w-full max-w-md",children:[s.jsxs(f,{className:"text-center",children:[s.jsx("div",{className:"mx-auto flex h-12 w-12 items-center justify-center rounded-full bg-red-100",children:s.jsx(k,{className:"h-6 w-6 text-red-600"})}),s.jsx(y,{className:"text-2xl font-bold",children:"Reset Link Invalid"}),s.jsx(N,{children:"This reset link is invalid or has expired. Please request a new password reset."})]}),s.jsx(E,{className:"flex justify-center",children:s.jsx(g,{onClick:()=>n("/forgot-password"),children:"Request new reset link"})})]})}):a?s.jsx("div",{className:"min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8",children:s.jsxs(j,{className:"w-full max-w-md",children:[s.jsxs(f,{className:"text-center",children:[s.jsx("div",{className:"mx-auto flex h-12 w-12 items-center justify-center rounded-full bg-green-100",children:s.jsx(Q,{className:"h-6 w-6 text-green-600"})}),s.jsx(y,{className:"text-2xl font-bold",children:"Password updated"}),s.jsx(N,{children:"Your password has been successfully updated."})]}),s.jsx(E,{className:"flex justify-center",children:s.jsx(g,{onClick:()=>n("/login"),children:"Back to login"})})]})}):s.jsx("div",{className:"min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8",children:s.jsxs("div",{className:"max-w-md w-full space-y-8",children:[s.jsxs("div",{className:"text-center",children:[s.jsx("h2",{className:"mt-6 text-3xl font-extrabold text-gray-900",children:"Reset your password"}),s.jsx("p",{className:"mt-2 text-sm text-gray-600",children:"Enter a new password for your account"})]}),s.jsxs(j,{className:"w-full max-w-md",children:[s.jsxs(f,{children:[s.jsx(y,{children:"New password"}),s.jsx(N,{children:"Choose a strong and secure password"})]}),s.jsxs($,{children:[u&&s.jsxs(G,{variant:"destructive",className:"mb-4",children:[s.jsx(k,{className:"h-4 w-4"}),s.jsx(W,{children:u})]}),s.jsx(X,F(p({},h),{children:s.jsxs("form",{onSubmit:h.handleSubmit(D),className:"space-y-4",children:[s.jsx(I,{control:h.control,name:"password",render:({field:d})=>s.jsxs(T,{children:[s.jsx(L,{children:"New password"}),s.jsx(R,{children:s.jsx(q,p({type:"password",placeholder:"••••••••"},d))}),s.jsx(A,{})]})}),s.jsx(I,{control:h.control,name:"confirmPassword",render:({field:d})=>s.jsxs(T,{children:[s.jsx(L,{children:"Confirm new password"}),s.jsx(R,{children:s.jsx(q,p({type:"password",placeholder:"••••••••"},d))}),s.jsx(A,{})]})}),s.jsx(g,{type:"submit",className:"w-full",disabled:m,children:m?"Updating password...":"Update password"})]})}))]})]})]})})}export{as as default};
