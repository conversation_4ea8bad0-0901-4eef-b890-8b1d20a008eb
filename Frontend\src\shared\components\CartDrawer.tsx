import React from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { X, Plus, Minus, Trash2, ShoppingBag, LogIn } from 'lucide-react';
import { useCart } from '../../core/contexts/CartContext';
import { useAuth } from '../../core/providers/AuthContext';
import { useUserAuthModal } from '../../core/contexts/UserAuthModalContext';
import { useAppSelector } from '../../store/hooks';

interface CartDrawerProps {
  isOpen: boolean;
  onClose: () => void;
}

const CartDrawer: React.FC<CartDrawerProps> = ({ isOpen, onClose }) => {
  const { items, totalItems, totalPrice, removeFromCart, updateQuantity, clearCart, refreshCart } = useCart();
  const { user } = useAuth();
  const { openModal: openUserAuthModal } = useUserAuthModal();
  const reduxUser = useAppSelector((state) => state.auth.user);
  const reduxIsAuthenticated = useAppSelector((state) => state.auth.isAuthenticated);

  // Check if user is authenticated using either source
  const userIsAuthenticated = Boolean((user || reduxUser) && reduxIsAuthenticated);

  const handleQuantityChange = async (serviceId: string, subServiceId: string | undefined, newQuantity: number) => {
    await updateQuantity(serviceId, subServiceId, newQuantity);
  };

  const handleRemoveItem = async (serviceId: string, subServiceName?: string) => {
    try {
      await removeFromCart(serviceId, subServiceName);
      // Cart will auto-refresh from context
    } catch (error) {
      console.error('❌ Error removing item:', error);
    }
  };

  const handleClearCart = async () => {
    if (window.confirm('Are you sure you want to clear your cart?')) {
      await clearCart();
    }
  };

  return (
    <AnimatePresence>
      {isOpen && (
        <>
          {/* Backdrop */}
          <motion.div
            className="fixed inset-0 bg-black/50 backdrop-blur-sm z-40"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            onClick={onClose}
          />

          {/* Drawer */}
          <motion.div
            className="fixed right-0 top-0 h-full w-full max-w-md bg-brand-black border-l border-brand-purple-500/20 shadow-2xl z-50"
            initial={{ x: '100%' }}
            animate={{ x: 0 }}
            exit={{ x: '100%' }}
            transition={{ type: 'spring', damping: 30, stiffness: 300 }}
          >
            {/* Header */}
            <div className="flex items-center justify-between p-6 border-b border-brand-purple-500/20">
              <div className="flex items-center gap-3">
                <span className="text-2xl">🛒</span>
                <h2 className="text-xl font-bold text-white">My Cart</h2>
                {totalItems > 0 && (
                  <span className="bg-brand-purple-500 text-white text-sm px-2 py-1 rounded-full">
                    {totalItems}
                  </span>
                )}
              </div>
              <div className="flex items-center gap-2">
                <button
                  onClick={refreshCart}
                  className="p-2 hover:bg-brand-purple-500/20 rounded-lg transition-colors"
                  title="Refresh cart"
                >
                  <svg className="w-4 h-4 text-brand-grey-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                  </svg>
                </button>
                <button
                  onClick={onClose}
                  className="p-2 hover:bg-brand-purple-500/20 rounded-lg transition-colors"
                >
                  <X className="w-5 h-5 text-brand-grey-300" />
                </button>
              </div>
            </div>

            {/* Content */}
            <div className="flex-1 overflow-y-auto p-6">
              {!userIsAuthenticated ? (
                <div className="flex flex-col items-center justify-center h-64 text-center">
                  <LogIn className="w-16 h-16 text-brand-purple-400 mb-4" />
                  <h3 className="text-lg font-semibold text-white mb-2">Connect to Your Account</h3>
                  <p className="text-brand-grey-300 text-sm mb-6">
                    Please connect to your account to view and manage your cart items.
                  </p>
                  <button
                    onClick={() => {
                      openUserAuthModal('Please connect to your account to view and manage your cart items');
                      onClose();
                    }}
                    className="w-full bg-brand-purple-600 hover:bg-brand-purple-700 text-white font-semibold py-3 px-6 rounded-lg transition-colors duration-300"
                  >
                    Connect to Account
                  </button>
                </div>
              ) : items.length === 0 ? (
                <div className="flex flex-col items-center justify-center h-64 text-center">
                  <ShoppingBag className="w-16 h-16 text-brand-grey-500 mb-4" />
                  <h3 className="text-lg font-semibold text-brand-grey-300 mb-2">Your cart is empty</h3>
                  <p className="text-brand-grey-500">Add some services to get started!</p>
                </div>
              ) : (
                <div className="space-y-4">
                  {items.map((item, index) => {
                    // Create a unique key that handles object serviceIds
                    const serviceIdStr = item.serviceId && typeof item.serviceId === 'object'
                      ? (item.serviceId as any)._id || (item.serviceId as any).id || JSON.stringify(item.serviceId)
                      : item.serviceId || 'unknown';
                    const uniqueKey = `${serviceIdStr}-${item.subServiceId || 'main'}-${index}`;

                    return (
                      <motion.div
                        key={uniqueKey}
                        className="bg-brand-purple-900/20 rounded-xl p-4 border border-brand-purple-500/20"
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ delay: index * 0.1 }}
                      >
                      {/* Item Info */}
                      <div className="flex justify-between items-start mb-3">
                        <div className="flex-1">
                          <h4 className="font-semibold text-white text-sm">
                            {item.serviceName}
                          </h4>
                          {item.subServiceName && (
                            <p className="text-brand-purple-300 text-xs mt-1">
                              {item.subServiceName}
                            </p>
                          )}
                          {item.description && (
                            <p className="text-brand-grey-400 text-xs mt-1 line-clamp-2">
                              {item.description}
                            </p>
                          )}
                        </div>
                        <button
                          onClick={() => {
                            console.log('🗑️ Delete button clicked for item:', {
                              serviceId: item.serviceId,
                              subServiceId: item.subServiceId,
                              serviceName: item.serviceName,
                              subServiceName: item.subServiceName
                            });
                            handleRemoveItem(item.serviceId, item.subServiceName);
                          }}
                          className="p-1 hover:bg-red-500/20 rounded text-red-400 hover:text-red-300 transition-colors"
                          title="Remove item from cart"
                        >
                          <Trash2 className="w-4 h-4" />
                        </button>
                      </div>

                      {/* Quantity and Price */}
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          <button
                            onClick={() => handleQuantityChange(item.serviceId, item.subServiceId, item.quantity - 1)}
                            className="p-1 hover:bg-brand-purple-500/20 rounded text-brand-grey-300 hover:text-white transition-colors"
                          >
                            <Minus className="w-3 h-3" />
                          </button>
                          <span className="text-white font-medium text-sm min-w-[2rem] text-center">
                            {item.quantity}
                          </span>
                          <button
                            onClick={() => handleQuantityChange(item.serviceId, item.subServiceId, item.quantity + 1)}
                            className="p-1 hover:bg-brand-purple-500/20 rounded text-brand-grey-300 hover:text-white transition-colors"
                          >
                            <Plus className="w-3 h-3" />
                          </button>
                        </div>
                        <div className="text-right">
                          <p className="text-brand-purple-300 font-semibold text-sm">
                            {item.price > 0 ? `$${(item.price * item.quantity).toFixed(2)}` : 'Contact for Quote'}
                          </p>
                          {item.quantity > 1 && item.price > 0 && (
                            <p className="text-brand-grey-500 text-xs">
                              ${item.price.toFixed(2)} each
                            </p>
                          )}
                        </div>
                      </div>
                    </motion.div>
                    );
                  })}
                </div>
              )}
            </div>

            {/* Footer */}
            {items.length > 0 && (
              <div className="border-t border-brand-purple-500/20 p-6 space-y-4">
                {/* Total */}
                <div className="flex justify-between items-center">
                  <span className="text-lg font-semibold text-white">Total:</span>
                  <span className="text-xl font-bold text-brand-purple-300">
                    {totalPrice > 0 ? `$${totalPrice.toFixed(2)}` : 'Contact for Quote'}
                  </span>
                </div>

                {/* Actions */}
                <div className="space-y-3">
                  <button className="w-full bg-gradient-to-r from-brand-purple-500 to-brand-purple-600 hover:from-brand-purple-400 hover:to-brand-purple-500 text-white font-semibold py-3 px-4 rounded-lg transition-all duration-200 shadow-lg">
                    {totalPrice > 0 ? 'Proceed to Checkout 🚀' : 'Request Quote 💬'}
                  </button>
                  <button
                    onClick={handleClearCart}
                    className="w-full bg-transparent border border-brand-grey-500 text-brand-grey-300 hover:bg-brand-grey-500/10 hover:text-white font-medium py-2 px-4 rounded-lg transition-all duration-200"
                  >
                    Clear Cart
                  </button>
                </div>
              </div>
            )}
          </motion.div>
        </>
      )}
    </AnimatePresence>
  );
};

export default CartDrawer;
