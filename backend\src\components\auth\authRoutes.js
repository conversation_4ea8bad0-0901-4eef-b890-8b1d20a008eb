import { Router } from 'express';
import {
  registerUser,
  loginUser,
  getMe,
  logout,
  updateProfile,
  forgotPassword,
  resetPassword,
  verifyEmail,
  resendVerificationEmail,
  debugUserStatus,
  manualVerifyUser,
  fixVisitorRecords,
} from './authController.js';
import { protect } from '../../shared/middleware/auth.js';

const router = Router();

// Public routes
router.post('/register', registerUser);
router.post('/login', loginUser);
router.post('/forgotpassword', forgotPassword);
router.put('/resetpassword/:resettoken', resetPassword);
router.get('/verify-email', verifyEmail);
router.post('/resend-verification', resendVerificationEmail);
router.get('/debug/:email', debugUserStatus);
router.post('/fix-visitors', fixVisitorRecords);
router.post('/manual-verify/:email', manualVerifyUser);

// Protected routes
router.get('/me', protect, getMe);
router.put('/updatedetails', protect, updateProfile);
router.post('/logout', logout);

export default router;
