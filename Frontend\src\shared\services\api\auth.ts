const TOKEN_KEY = import.meta.env.VITE_TOKEN_KEY || 'auth_token';

/**
 * Remove authentication tokens and user data from storage
 */
export const removeToken = (): void => {
  // Clear tokens from localStorage
  localStorage.removeItem(TOKEN_KEY);
  localStorage.removeItem('user');
  
  // Clear tokens from sessionStorage
  sessionStorage.removeItem('temp_token');
  
  console.log('Cleared all auth tokens from storage');
};

/**
 * Check if user is authenticated on the client side
 * Note: This is a client-side check only and should be used in conjunction with server-side validation
 * @returns {boolean} True if the user appears to be authenticated based on localStorage
 */
export const isAuthenticated = (): boolean => {
  // First check if we have a token in localStorage (legacy support)
  const hasToken = !!localStorage.getItem(TOKEN_KEY);

  // Check if we have a temporary token in sessionStorage
  const hasTempToken = !!sessionStorage.getItem('temp_token');

  // For cookie-based authentication, we can't reliably check client-side
  // The actual authentication is handled by HTTP-only cookies
  // We'll let the AuthContext handle this by checking the user state

  // Log the auth state for debugging
  console.log('Auth check:', { hasToken, hasTempToken });

  // Return true if we have any token, otherwise let the AuthContext decide
  // based on the actual user data from the server
  return hasToken || hasTempToken;
};
