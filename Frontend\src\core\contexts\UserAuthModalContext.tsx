import React, { createContext, useContext, useState, useEffect } from 'react';

interface UserAuthModalContextType {
  isOpen: boolean;
  openModal: (message?: string) => void;
  closeModal: () => void;
  message?: string;
}

const UserAuthModalContext = createContext<UserAuthModalContextType | undefined>(undefined);

export const UserAuthModalProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [isOpen, setIsOpen] = useState(false);
  const [message, setMessage] = useState<string | undefined>();

  const openModal = (customMessage?: string) => {
    setMessage(customMessage);
    setIsOpen(true);
    // Scroll to top when modal opens
    window.scrollTo({ top: 0, behavior: 'smooth' });
    // Prevent body scroll when modal is open
    document.body.style.overflow = 'hidden';
  };

  const closeModal = () => {
    setIsOpen(false);
    setMessage(undefined);
    // Re-enable body scroll when modal is closed
    document.body.style.overflow = 'unset';
  };

  // Handle ESC key and cleanup on unmount
  useEffect(() => {
    const handleEscKey = (event: KeyboardEvent) => {
      if (event.key === 'Escape' && isOpen) {
        closeModal();
      }
    };

    if (isOpen) {
      document.addEventListener('keydown', handleEscKey);
    }

    return () => {
      document.removeEventListener('keydown', handleEscKey);
      document.body.style.overflow = 'unset';
    };
  }, [isOpen]);

  return (
    <UserAuthModalContext.Provider value={{
      isOpen,
      openModal,
      closeModal,
      message
    }}>
      {children}
    </UserAuthModalContext.Provider>
  );
};

export const useUserAuthModal = () => {
  const context = useContext(UserAuthModalContext);
  if (context === undefined) {
    throw new Error('useUserAuthModal must be used within a UserAuthModalProvider');
  }
  return context;
};
