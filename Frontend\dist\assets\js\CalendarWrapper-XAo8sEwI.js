const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/js/EventCalendar-CqbCrlNP.js","assets/js/react-vendor-Dq0qSR31.js","assets/js/vendor-OXu-rwpf.js","assets/vendor-Dgihpmma.css","assets/js/utils-vendor-DSNVchvY.js","assets/react-vendor-DZrYvFpI.css","assets/js/index-CasGuY6o.js","assets/js/state-vendor-DU4y5LsH.js","assets/index-Cpgs4Ywp.css","assets/js/dialog-BVPp5ZjS.js"])))=>i.map(i=>d[i]);
import{b3 as r}from"./vendor-OXu-rwpf.js";import{j as e,r as s,R as t}from"./react-vendor-Dq0qSR31.js";import"./utils-vendor-DSNVchvY.js";const l=t.lazy(()=>r(()=>import("./EventCalendar-CqbCrlNP.js"),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9])).catch(()=>({default:()=>e.jsx("div",{className:"p-8 text-center",children:e.jsx("p",{className:"text-gray-500",children:"Calendar component is temporarily unavailable."})})}))),n=()=>e.jsxs("div",{className:"flex h-64 w-full items-center justify-center",children:[e.jsx("div",{className:"h-8 w-8 animate-spin rounded-full border-2 border-gray-300 border-t-blue-500"}),e.jsx("span",{className:"ml-2 text-gray-600",children:"Loading calendar..."})]}),o=({className:a})=>e.jsx("div",{className:a,children:e.jsx(s.Suspense,{fallback:e.jsx(n,{}),children:e.jsx(l,{})})});export{o as default};
