import api from './api/axios';

export interface CartActionData {
  action: 'add' | 'remove';
  serviceName: string;
  subServiceName: string;
  subServiceDescription?: string;
  sessionId: string;
  userEmail?: string;
  userName?: string;
}

// Track cart actions (add/remove) for analytics
export const trackCartAction = async (data: CartActionData): Promise<void> => {
  try {
    console.log('🛒 Tracking cart action:', data);
    
    await api.post('/visitor/cart-action', {
      ...data,
      path: window.location.pathname,
      timestamp: new Date().toISOString()
    });
    
    console.log('✅ Cart action tracked successfully');
  } catch (error) {
    console.error('❌ Error tracking cart action:', error);
    // Don't throw error - tracking should be silent
  }
};

// Get session ID for tracking
export const getSessionId = (): string => {
  let sessionId = sessionStorage.getItem('sessionId');
  if (!sessionId) {
    sessionId = 'session_' + Math.random().toString(36).substr(2, 9) + '_' + Date.now();
    sessionStorage.setItem('sessionId', sessionId);
  }
  return sessionId;
};
