import { defineConfig } from "vite";
import react from "@vitejs/plugin-react-swc";
import path from "path";

// https://vitejs.dev/config/
export default defineConfig(() => ({
  server: {
    host: "::",
    port: 8080,
    proxy: {
      "/api": {
        target: "http://localhost:5000", // Backend server
        changeOrigin: true,
        secure: false,
      },
    },
  },
  plugins: [react()],
  resolve: {
    alias: {
      "@": path.resolve(__dirname, "./src"),
      react: path.resolve(__dirname, "node_modules/react"),
      "react-dom": path.resolve(__dirname, "node_modules/react-dom"),
      // Fix FullCalendar imports
      "@fullcalendar/core/index.js": path.resolve(__dirname, "node_modules/@fullcalendar/core/index.js"),
      "@fullcalendar/core/internal": path.resolve(__dirname, "node_modules/@fullcalendar/core/internal.js"),
      "@fullcalendar/core/internal.js": path.resolve(__dirname, "node_modules/@fullcalendar/core/internal.js"),
      "@fullcalendar/core/preact.js": path.resolve(__dirname, "node_modules/@fullcalendar/core/preact.js"),
    },
  },
  base: './',
  build: {
    target: 'es2015',
    minify: true,
    sourcemap: false,
    cssCodeSplit: false,
    rollupOptions: {
      output: {
        manualChunks: undefined,
        entryFileNames: 'assets/[name].js',
        chunkFileNames: 'assets/[name].js',
        assetFileNames: 'assets/[name].[ext]',
      },
    },
  },

}));
