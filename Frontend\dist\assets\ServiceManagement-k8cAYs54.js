var b=(o,g,r)=>new Promise((m,i)=>{var n=a=>{try{l(r.next(a))}catch(d){i(d)}},x=a=>{try{l(r.throw(a))}catch(d){i(d)}},l=a=>a.done?m(a.value):Promise.resolve(a.value).then(n,x);l((r=r.apply(o,g)).next())});import{r as c,e as N,az as y,j as e,aA as v}from"./index-hEW_vQ3f.js";import{F as p,a as w,b as S}from"./index-82_zGhWH.js";const k=()=>e.jsxs("div",{className:"animate-pulse bg-brand-grey-900 rounded-xl p-4 flex flex-col gap-4 shadow-md min-h-[160px]",children:[e.jsx("div",{className:"h-6 bg-brand-grey-800 rounded w-2/3 mb-2"}),e.jsx("div",{className:"h-4 bg-brand-grey-800 rounded w-1/2 mb-2"}),e.jsx("div",{className:"h-3 bg-brand-grey-800 rounded w-1/3"}),e.jsxs("div",{className:"flex gap-2 mt-4",children:[e.jsx("div",{className:"h-8 w-8 bg-brand-grey-800 rounded-full"}),e.jsx("div",{className:"h-8 w-8 bg-brand-grey-800 rounded-full"})]})]}),D=()=>{const[o,g]=c.useState([]),[r,m]=c.useState(!1),[i,n]=c.useState(null),[x,l]=c.useState(null),a=N();c.useEffect(()=>{d()},[]);const d=()=>b(null,null,function*(){m(!0),n(null);try{const s=yield y();g(s.data)}catch(s){n("Failed to fetch services")}finally{m(!1)}}),h=()=>{a("/dashboard/services/new")},u=s=>{a(`/dashboard/services/${s.id}/edit`)},j=s=>b(null,null,function*(){if(window.confirm("Are you sure you want to delete this service?")){l(s);try{yield v(s),d()}catch(t){n("Failed to delete service")}finally{l(null)}}});return e.jsxs("div",{className:"p-6 max-w-7xl mx-auto",children:[e.jsxs("div",{className:"flex flex-col sm:flex-row justify-between items-center mb-8 gap-4",children:[e.jsx("h2",{className:"text-3xl font-bold text-brand-white tracking-tight",children:"Service Management"}),e.jsxs("button",{className:"flex items-center gap-2 px-4 py-2 rounded-lg bg-brand-primary hover:bg-brand-primary-dark text-white font-semibold shadow transition",onClick:h,children:[e.jsx(p,{className:"text-lg"})," Add Service"]})]}),i&&e.jsx("div",{className:"text-red-500 mb-4",children:i}),r?e.jsx("div",{className:"grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-6",children:[...Array(6)].map((s,t)=>e.jsx(k,{},t))}):o.length===0?e.jsxs("div",{className:"flex flex-col items-center justify-center py-20 text-center text-brand-grey-400",children:[e.jsx("img",{src:"/placeholder.svg",alt:"No services",className:"w-32 mb-6 opacity-60"}),e.jsx("h3",{className:"text-xl font-semibold mb-2",children:"No services found"}),e.jsx("p",{className:"mb-4",children:"Start by adding your first service!"}),e.jsxs("button",{className:"flex items-center gap-2 px-4 py-2 rounded-lg bg-brand-primary hover:bg-brand-primary-dark text-white font-semibold shadow transition",onClick:h,children:[e.jsx(p,{className:"text-lg"})," Add Service"]})]}):e.jsx("div",{className:"grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-6",children:o.map(s=>e.jsxs("div",{className:"bg-brand-grey-900 rounded-xl shadow-md p-5 flex flex-col gap-3 hover:shadow-lg transition group border border-brand-grey-800",children:[e.jsx("div",{className:"flex items-center gap-2 mb-2",children:s.image&&e.jsx("img",{src:s.image,alt:s.title,className:"w-12 h-12 object-cover rounded"})}),e.jsx("h3",{className:"text-lg font-bold text-brand-white group-hover:text-brand-primary transition line-clamp-2",children:s.title}),e.jsx("p",{className:"text-brand-grey-300 text-sm line-clamp-3 mb-2",children:s.description}),s.subServices&&s.subServices.length>0&&e.jsxs("details",{className:"mb-2",children:[e.jsxs("summary",{className:"cursor-pointer text-brand-primary font-semibold mb-2",children:[s.subServices.length," Sub-Service",s.subServices.length>1?"s":""]}),e.jsx("div",{className:"flex flex-col gap-3 mt-2",children:s.subServices.map((t,f)=>e.jsxs("div",{className:"bg-brand-grey-800 border border-brand-grey-700 rounded-lg p-3 flex items-center gap-4",children:[t.image&&e.jsx("img",{src:t.image,alt:t.title,className:"w-10 h-10 object-cover rounded border border-brand-grey-700"}),e.jsxs("div",{className:"flex-1",children:[e.jsx("div",{className:"font-semibold text-brand-white",children:t.title}),e.jsx("div",{className:"text-brand-grey-300 text-xs",children:t.description})]})]},f))})]}),e.jsxs("div",{className:"flex items-center justify-between mt-auto pt-2",children:[e.jsx("span",{className:"text-xs text-brand-grey-400",children:s.createdAt?new Date(s.createdAt).toLocaleDateString():""}),e.jsxs("div",{className:"flex gap-2",children:[e.jsx("button",{className:"p-2 rounded-full hover:bg-brand-grey-800 transition",title:"Edit",onClick:()=>u(s),children:e.jsx(w,{className:"text-brand-primary text-lg"})}),e.jsx("button",{className:`p-2 rounded-full hover:bg-red-600/20 transition ${x===s.id?"opacity-50 pointer-events-none":""}`,title:"Delete",onClick:()=>j(s.id),disabled:x===s.id,children:e.jsx(S,{className:"text-red-500 text-lg"})})]})]})]},s.id))})]})};export{D as default};
