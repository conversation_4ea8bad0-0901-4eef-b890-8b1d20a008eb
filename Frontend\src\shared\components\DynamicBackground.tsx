import { useState, useEffect, memo } from 'react';
import { motion, AnimatePresence } from 'framer-motion';

interface DynamicBackgroundProps {
  className?: string;
  interval?: number; // in milliseconds
}

const DynamicBackground = memo(({ className = '', interval = 1000 }: DynamicBackgroundProps) => {
  const [currentImageIndex, setCurrentImageIndex] = useState(0);

  // AI and Digital Marketing themed images
  const backgroundImages = [
    'https://images.unsplash.com/photo-1677442136019-21780ecad995?w=1920&h=1080&fit=crop', // AI Brain
    'https://images.unsplash.com/photo-1485827404703-89b55fcc595e?w=1920&h=1080&fit=crop', // Digital Technology
    'https://images.unsplash.com/photo-1518709268805-4e9042af2176?w=1920&h=1080&fit=crop', // AI Robot
    'https://images.unsplash.com/photo-1460925895917-afdab827c52f?w=1920&h=1080&fit=crop', // Digital Marketing
    'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=1920&h=1080&fit=crop', // AI Network
    'https://images.unsplash.com/photo-1551288049-bebda4e38f71?w=1920&h=1080&fit=crop', // Data Analytics
    'https://images.unsplash.com/photo-1620712943543-bcc4688e7485?w=1920&h=1080&fit=crop', // AI Technology
    'https://images.unsplash.com/photo-1611224923853-80b023f02d71?w=1920&h=1080&fit=crop', // Social Media
    'https://images.unsplash.com/photo-1504639725590-34d0984388bd?w=1920&h=1080&fit=crop', // Digital Innovation
    'https://images.unsplash.com/photo-1556742049-0cfed4f6a45d?w=1920&h=1080&fit=crop', // Marketing Analytics
    'https://images.unsplash.com/photo-1516321318423-f06f85e504b3?w=1920&h=1080&fit=crop', // AI Future
    'https://images.unsplash.com/photo-1542435503-956c469947f6?w=1920&h=1080&fit=crop', // Content Strategy
  ];

  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentImageIndex((prevIndex) => 
        (prevIndex + 1) % backgroundImages.length
      );
    }, interval);

    return () => clearInterval(timer);
  }, [interval, backgroundImages.length]);

  return (
    <div className={`fixed inset-0 -z-10 ${className}`}>
      <AnimatePresence mode="wait">
        <motion.div
          key={currentImageIndex}
          initial={{ opacity: 0, scale: 1.1 }}
          animate={{ opacity: 1, scale: 1 }}
          exit={{ opacity: 0, scale: 0.9 }}
          transition={{ 
            duration: 0.8,
            ease: "easeInOut"
          }}
          className="absolute inset-0"
        >
          <img
            src={backgroundImages[currentImageIndex]}
            alt={`AI and Digital Marketing Background ${currentImageIndex + 1}`}
            className="w-full h-full object-cover"
            loading="lazy"
          />
          {/* Purple gradient overlay */}
          <div className="absolute inset-0 bg-gradient-to-br from-brand-black/80 via-brand-purple-950/70 to-brand-black/90" />
          
          {/* Additional animated overlay */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 0.3 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 1 }}
            className="absolute inset-0 bg-gradient-to-r from-brand-purple-600/20 via-transparent to-brand-purple-800/20"
          />
        </motion.div>
      </AnimatePresence>
      
      {/* Static gradient overlay for consistency */}
      <div className="absolute inset-0 bg-gradient-to-t from-brand-black/50 via-transparent to-brand-black/30 pointer-events-none" />
    </div>
  );
});

DynamicBackground.displayName = 'DynamicBackground';

export default DynamicBackground;
