import React, { Suspense } from "react";
import { Toaster } from "@/shared/components/ui/toaster";
import { Toaster as Sonner } from "@/shared/components/ui/sonner";
import { TooltipProvider } from "@/shared/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import { ROLES } from "@/core/constants/constants";
import { LanguageProvider } from "@/core/providers/LanguageContext";
import { AuthProvider } from "@/core/providers/AuthContext";
import { ProtectedRoute } from "@/modules/auth/auth/ProtectedRoute";
import Layout from "@/shared/components/Layout";
import { Provider } from 'react-redux';
import store from '@/store';
import { ClickTrackingProvider } from "@/core/providers/ClickTrackingContext";
import { CartProvider } from "@/core/contexts/CartContext";
import { UserAuthModalProvider } from "@/core/contexts/UserAuthModalContext";
import { ToastProvider } from "@/core/contexts/ToastContext";
import AnalyticsTracker from "@/shared/components/AnalyticsTracker";
import PageTracker from "@/shared/components/PageTracker";
import PerformanceMonitor from "@/shared/components/PerformanceMonitor";
import ResourcePreloader from "@/shared/components/ResourcePreloader";

// Import critical components directly to avoid waterfall loading
import NotFound from "@/modules/common/NotFound";
import Unauthorized from "@/modules/common/Unauthorized";
import Index from "@/modules/common/Index";
import Login from "@/modules/auth/Login";
import Register from "@/modules/auth/Register";
import About from "@/modules/common/About";
import InnovativeServices from "@/modules/services/InnovativeServices";
import Contact from "@/modules/common/Contact";
import CartPage from "@/modules/cart/CartPage";

// Only lazy load heavy/rarely used components
const Blog = React.lazy(() => import("@/modules/blog/Blog"));
const MarketingDashboard = React.lazy(() => import("@/modules/dashboard/MarketingDashboard"));
const ForgotPassword = React.lazy(() => import("@/modules/auth/ForgotPassword"));
const ResetPassword = React.lazy(() => import("@/modules/auth/ResetPassword"));
const BlogArticle = React.lazy(() => import("@/modules/blog/BlogArticle"));

const SubServiceEditor = React.lazy(() => import("@/modules/dashboard/SubServiceEditor"));
const ServiceEditor = React.lazy(() => import("@/modules/dashboard/ServiceEditor"));

// Lazy load React Query Devtools only in development
const ReactQueryDevtools = React.lazy(() =>
  import('@tanstack/react-query-devtools').then(module => ({
    default: module.ReactQueryDevtools
  }))
);

// Optimized loading components with fixed dimensions to prevent layout shift
const LoadingSpinner = () => (
  <div className="flex h-screen w-full items-center justify-center bg-white">
    <div className="h-12 w-12 animate-spin rounded-full border-4 border-gray-300 border-t-blue-500" />
  </div>
);

// Fast loading component for critical routes
const FastLoader = () => (
  <div className="flex items-center justify-center" style={{ height: '200px', width: '100%' }}>
    <div className="h-6 w-6 animate-spin rounded-full border-2 border-gray-300 border-t-blue-500" />
  </div>
);

// Smaller loading component for route transitions with fixed height
const RouteLoader = () => (
  <div className="flex items-center justify-center" style={{ height: '400px', width: '100%' }}>
    <div className="h-8 w-8 animate-spin rounded-full border-2 border-gray-300 border-t-blue-500" />
  </div>
);

const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      refetchOnWindowFocus: false,
      retry: 1,
      staleTime: 5 * 60 * 1000, // 5 minutes
    },
  },
});

const App = () => {
  return (
    <Provider store={store}>
      <QueryClientProvider client={queryClient}>
        <TooltipProvider>
          <LanguageProvider>
            <AuthProvider>
              <ClickTrackingProvider>
                <UserAuthModalProvider>
                  <ToastProvider>
                    <CartProvider>
                    <Toaster />
                    <Sonner />
                    <BrowserRouter>
                  <PageTracker />
                  <AnalyticsTracker />
                  <PerformanceMonitor />
                  <ResourcePreloader />
                  <Suspense fallback={<LoadingSpinner />}>
                    <Routes>
                      {/* Auth Routes */}
                      <Route path="/login" element={<Login />} />
                      <Route path="/register" element={<Register />} />
                      <Route path="/unauthorized" element={<Unauthorized />} />

                      {/* Less critical auth routes */}
                      <Route path="/forgot-password" element={
                        <Suspense fallback={<RouteLoader />}>
                          <ForgotPassword />
                        </Suspense>
                      } />
                      <Route path="/reset-password/:resettoken" element={
                        <Suspense fallback={<RouteLoader />}>
                          <ResetPassword />
                        </Suspense>
                      } />

                      {/* Dashboard Routes (protected) */}
                      <Route
                        path="/dashboard/*"
                        element={
                          <ProtectedRoute
                            allowedRoles={[ROLES.ADMIN, ROLES.MARKETING]}
                            loadingComponent={<LoadingSpinner />}
                          >
                            <Suspense fallback={<LoadingSpinner />}>
                              <MarketingDashboard />
                            </Suspense>
                          </ProtectedRoute>
                        }
                      />
                      <Route path="/dashboard/services/subservice/new" element={
                        <Suspense fallback={<LoadingSpinner />}>
                          <SubServiceEditor />
                        </Suspense>
                      } />
                      <Route path="/dashboard/services/subservice/edit" element={
                        <Suspense fallback={<LoadingSpinner />}>
                          <SubServiceEditor />
                        </Suspense>
                      } />
                      <Route path="/dashboard/services/new" element={
                        <Suspense fallback={<LoadingSpinner />}>
                          <ServiceEditor />
                        </Suspense>
                      } />
                      <Route path="/dashboard/services/:id/edit" element={
                        <Suspense fallback={<LoadingSpinner />}>
                          <ServiceEditor />
                        </Suspense>
                      } />

                      {/* Main Site Routes (with main layout) */}
                      <Route
                        path="/*"
                        element={
                          <Layout>
                            <Routes>
                              {/* Critical routes - no lazy loading */}
                              <Route path="/" element={<Index />} />
                              <Route path="/about" element={<About />} />
                              <Route path="/services" element={<InnovativeServices />} />
                              <Route path="/services/:slug" element={<InnovativeServices />} />
                              <Route path="/contact" element={<Contact />} />
                              <Route path="/cart" element={<CartPage />} />
                              <Route path="*" element={<NotFound />} />

                              {/* Less critical routes with lazy loading */}
                              <Route path="/blog" element={
                                <Suspense fallback={<RouteLoader />}>
                                  <Blog />
                                </Suspense>
                              } />
                              <Route path="/blog/:slug" element={
                                <Suspense fallback={<RouteLoader />}>
                                  <BlogArticle />
                                </Suspense>
                              } />
                            </Routes>
                          </Layout>
                        }
                      />
                    </Routes>
                  </Suspense>
                </BrowserRouter>
                {process.env.NODE_ENV === 'development' && (
                  <Suspense fallback={null}>
                    <ReactQueryDevtools initialIsOpen={false} />
                  </Suspense>
                )}
                    </CartProvider>
                  </ToastProvider>
                </UserAuthModalProvider>
              </ClickTrackingProvider>
            </AuthProvider>
          </LanguageProvider>
        </TooltipProvider>
      </QueryClientProvider>
    </Provider>
  );
};

export default App;
