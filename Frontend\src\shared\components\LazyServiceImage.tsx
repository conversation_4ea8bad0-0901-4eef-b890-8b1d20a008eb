import { useState, useRef, useEffect } from 'react';
import { cn } from '@/core/utils/utils';

interface LazyServiceImageProps {
  src: string;
  alt: string;
  className?: string;
  width?: number;
  height?: number;
  aspectRatio?: string;
  minHeight?: string;
  responsiveHeight?: string;
  onLoad?: () => void;
  priority?: boolean; // For above-the-fold images
}

const LazyServiceImage: React.FC<LazyServiceImageProps> = ({
  src,
  alt,
  className = '',
  width = 400,
  height = 225,
  aspectRatio = '16/9',
  minHeight = '160px',
  responsiveHeight = 'clamp(160px, 12vw, 208px)',
  onLoad,
  priority = false
}) => {
  const [isLoaded, setIsLoaded] = useState(false);
  const [isInView, setIsInView] = useState(priority); // Load immediately if priority
  const [hasError, setHasError] = useState(false);
  const imgRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (priority) return; // Skip intersection observer for priority images

    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsInView(true);
          observer.disconnect();
        }
      },
      { 
        rootMargin: '50px', // Start loading 50px before image comes into view
        threshold: 0.1
      }
    );

    if (imgRef.current) {
      observer.observe(imgRef.current);
    }

    return () => observer.disconnect();
  }, [priority]);

  const handleLoad = () => {
    setIsLoaded(true);
    onLoad?.();
  };

  const handleError = () => {
    setHasError(true);
  };

  // Placeholder while loading
  const placeholder = (
    <div 
      className={cn(
        "absolute inset-0 bg-gradient-to-br from-brand-grey-800 to-brand-grey-900 animate-pulse flex items-center justify-center",
        className
      )}
    >
      <div className="text-brand-grey-500 text-sm font-medium">Loading...</div>
    </div>
  );

  // Error state
  if (hasError) {
    return (
      <div 
        ref={imgRef}
        className="relative w-full overflow-hidden"
        style={{
          aspectRatio,
          minHeight,
          height: responsiveHeight
        }}
      >
        <div className={cn(
          "absolute inset-0 bg-brand-grey-800 flex items-center justify-center",
          className
        )}>
          <div className="text-brand-grey-500 text-sm">Failed to load image</div>
        </div>
      </div>
    );
  }

  return (
    <div 
      ref={imgRef}
      className="relative w-full overflow-hidden"
      style={{
        aspectRatio,
        minHeight,
        height: responsiveHeight
      }}
    >
      {/* Show placeholder until image is in view */}
      {!isInView && placeholder}
      
      {/* Load image when in view */}
      {isInView && (
        <>
          {/* Show placeholder while loading */}
          {!isLoaded && placeholder}
          
          <img
            src={src}
            alt={alt}
            className={cn(
              "w-full h-full object-cover object-center transition-all duration-500",
              isLoaded ? "opacity-100 scale-100" : "opacity-0 scale-105",
              className
            )}
            width={width}
            height={height}
            loading={priority ? "eager" : "lazy"}
            decoding="async"
            onLoad={handleLoad}
            onError={handleError}
          />
        </>
      )}
    </div>
  );
};

export default LazyServiceImage;
