var j=Object.defineProperty,w=Object.defineProperties;var D=Object.getOwnPropertyDescriptors;var c=Object.getOwnPropertySymbols;var p=Object.prototype.hasOwnProperty,g=Object.prototype.propertyIsEnumerable;var m=(a,e,t)=>e in a?j(a,e,{enumerable:!0,configurable:!0,writable:!0,value:t}):a[e]=t,l=(a,e)=>{for(var t in e||(e={}))p.call(e,t)&&m(a,t,e[t]);if(c)for(var t of c(e))g.call(e,t)&&m(a,t,e[t]);return a},x=(a,e)=>w(a,D(e));var d=(a,e)=>{var t={};for(var s in a)p.call(a,s)&&e.indexOf(s)<0&&(t[s]=a[s]);if(a!=null&&c)for(var s of c(a))e.indexOf(s)<0&&g.call(a,s)&&(t[s]=a[s]);return t};import{r,j as i,h as n,X as v}from"./index-hEW_vQ3f.js";import{R,P as T,C as u,a as k,T as y,D as b,O as N}from"./index-lne2Edaq.js";const C=r.forwardRef((s,t)=>{var o=s,{className:a}=o,e=d(o,["className"]);return i.jsx("textarea",l({className:n("flex min-h-[80px] w-full rounded-md border border-gray-600 bg-gray-800 px-3 py-2 text-sm ring-offset-background placeholder:text-gray-400 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 text-white",a),ref:t},e))});C.displayName="Textarea";const B=R,z=T,h=r.forwardRef((s,t)=>{var o=s,{className:a}=o,e=d(o,["className"]);return i.jsx(N,l({ref:t,className:n("fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",a)},e))});h.displayName=N.displayName;const O=r.forwardRef((o,s)=>{var f=o,{className:a,children:e}=f,t=d(f,["className","children"]);return i.jsxs(z,{children:[i.jsx(h,{}),i.jsxs(u,x(l({ref:s,className:n("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",a)},t),{children:[e,i.jsxs(k,{className:"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground",children:[i.jsx(v,{className:"h-4 w-4"}),i.jsx("span",{className:"sr-only",children:"Close"})]})]}))]})});O.displayName=u.displayName;const P=t=>{var s=t,{className:a}=s,e=d(s,["className"]);return i.jsx("div",l({className:n("flex flex-col space-y-1.5 text-center sm:text-left",a)},e))};P.displayName="DialogHeader";const E=r.forwardRef((s,t)=>{var o=s,{className:a}=o,e=d(o,["className"]);return i.jsx(y,l({ref:t,className:n("text-lg font-semibold leading-none tracking-tight",a)},e))});E.displayName=y.displayName;const H=r.forwardRef((s,t)=>{var o=s,{className:a}=o,e=d(o,["className"]);return i.jsx(b,l({ref:t,className:n("text-sm text-muted-foreground",a)},e))});H.displayName=b.displayName;export{B as D,C as T,O as a,P as b,E as c,H as d};
