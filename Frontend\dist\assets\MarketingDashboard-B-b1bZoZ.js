const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["./CalendarWrapper-FP0Lciu2.js","./index-hEW_vQ3f.js","./BlogManagement-TytBW7yf.js","./dialog-DaFUYITg.js","./index-lne2Edaq.js","./quill.snow-Crc82pRX.js","./isEqual-CATH4cxC.js","./index-82_zGhWH.js","./ServiceManagement-k8cAYs54.js","./FinancialHealthDashboard-BnA59exg.js","./VisitorAnalyticsTable-DF2x6UiZ.js","./LeadScoringMetrics-B7qvH4ZH.js","./PieChart-DxmJye2U.js","./MapComponents-TRM_TpO4.js"])))=>i.map(i=>d[i]);
var ae=Object.defineProperty;var B=Object.getOwnPropertySymbols;var ne=Object.prototype.hasOwnProperty,re=Object.prototype.propertyIsEnumerable;var I=(r,d,l)=>d in r?ae(r,d,{enumerable:!0,configurable:!0,writable:!0,value:l}):r[d]=l,G=(r,d)=>{for(var l in d||(d={}))ne.call(d,l)&&I(r,l,d[l]);if(B)for(var l of B(d))re.call(d,l)&&I(r,l,d[l]);return r};var S=(r,d,l)=>new Promise((p,a)=>{var x=c=>{try{h(l.next(c))}catch(g){a(g)}},o=c=>{try{h(l.throw(c))}catch(g){a(g)}},h=c=>c.done?p(c.value):Promise.resolve(c.value).then(x,o);h((l=l.apply(r,d)).next())});import{c as y,r as u,d as le,e as ie,f as oe,j as e,T as de,U as C,C as ce,S as Z,h as K,L as z,X as xe,M as me,R as he,i as _,k as N,l as ge,E as be,n as ue,Z as pe,G as J,o as ye,p as je,_ as w}from"./index-hEW_vQ3f.js";/**
 * @license lucide-react v0.336.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const fe=y("Activity",[["path",{d:"M22 12h-4l-3 9L9 3l-3 9H2",key:"d5dnw9"}]]);/**
 * @license lucide-react v0.336.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ve=y("ArrowDownRight",[["path",{d:"m7 7 10 10",key:"1fmybs"}],["path",{d:"M17 7v10H7",key:"6fjiku"}]]);/**
 * @license lucide-react v0.336.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ne=y("ArrowUpRight",[["path",{d:"M7 7h10v10",key:"1tivn9"}],["path",{d:"M7 17 17 7",key:"1vkiza"}]]);/**
 * @license lucide-react v0.336.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const we=y("BarChart3",[["path",{d:"M3 3v18h18",key:"1s2lah"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]]);/**
 * @license lucide-react v0.336.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ke=y("Bell",[["path",{d:"M6 8a6 6 0 0 1 12 0c0 7 3 9 3 9H3s3-2 3-9",key:"1qo2s2"}],["path",{d:"M10.3 21a1.94 1.94 0 0 0 3.4 0",key:"qgo35s"}]]);/**
 * @license lucide-react v0.336.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Se=y("Building2",[["path",{d:"M6 22V4a2 2 0 0 1 2-2h8a2 2 0 0 1 2 2v18Z",key:"1b4qmf"}],["path",{d:"M6 12H4a2 2 0 0 0-2 2v6a2 2 0 0 0 2 2h2",key:"i71pzd"}],["path",{d:"M18 9h2a2 2 0 0 1 2 2v9a2 2 0 0 1-2 2h-2",key:"10jefs"}],["path",{d:"M10 6h4",key:"1itunk"}],["path",{d:"M10 10h4",key:"tcdvrf"}],["path",{d:"M10 14h4",key:"kelpxr"}],["path",{d:"M10 18h4",key:"1ulq68"}]]);/**
 * @license lucide-react v0.336.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const _e=y("Download",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"7 10 12 15 17 10",key:"2ggqvy"}],["line",{x1:"12",x2:"12",y1:"15",y2:"3",key:"1vk2je"}]]);/**
 * @license lucide-react v0.336.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Me=y("FileText",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]]);/**
 * @license lucide-react v0.336.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ze=y("Home",[["path",{d:"m3 9 9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z",key:"y5dka4"}],["polyline",{points:"9 22 9 12 15 12 15 22",key:"e2us08"}]]);/**
 * @license lucide-react v0.336.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const $e=y("PieChart",[["path",{d:"M21.21 15.89A10 10 0 1 1 8 2.83",key:"k2fpak"}],["path",{d:"M22 12A10 10 0 0 0 12 2v10z",key:"1rfc4y"}]]);/**
 * @license lucide-react v0.336.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const De=y("Search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]]);/**
 * @license lucide-react v0.336.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const W=y("UserCheck",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["polyline",{points:"16 11 18 13 22 9",key:"1pwet4"}]]),Ce=({children:r})=>{var c;const[d,l]=u.useState(!1),p=le(),a=ie(),{user:x,logout:o}=oe(),h=[{name:"Dashboard",href:"/dashboard",icon:e.jsx(we,{size:20})},{name:"Financial Health",href:"/dashboard/financial-health",icon:e.jsx($e,{size:20})},{name:"Campaigns",href:"/dashboard/campaigns",icon:e.jsx(de,{size:20})},{name:"Leads",href:"/dashboard/leads",icon:e.jsx(C,{size:20})},{name:"Calendar",href:"/dashboard/calendar",icon:e.jsx(ce,{size:20})},...(x==null?void 0:x.role)==="admin"?[{name:"Blogs",href:"/dashboard/blogs",icon:e.jsx(Me,{size:20})},{name:"Services",href:"/dashboard/services",icon:e.jsx(Z,{size:20})}]:[]];return e.jsxs("div",{className:"min-h-screen bg-gradient-to-br from-brand-black via-brand-grey-900 to-brand-grey-800 flex",children:[e.jsxs("aside",{className:K("fixed inset-y-0 left-0 z-50 w-72 bg-brand-grey-950 border-r border-brand-grey-800 shadow-xl transform transition-transform duration-300 flex flex-col lg:translate-x-0",d?"translate-x-0":"-translate-x-full lg:translate-x-0"),children:[e.jsxs("div",{className:"flex items-center justify-between h-20 px-8 border-b border-brand-grey-800 bg-brand-grey-950",children:[e.jsxs(z,{to:"/",className:"text-2xl font-extrabold tracking-tight text-brand-white hover:text-brand-grey-300 transition-colors",children:[e.jsx("span",{className:"inline-block align-middle",children:"Click"}),e.jsx("span",{className:"text-brand-grey-300 inline-block align-middle",children:"4You"})]}),e.jsx("button",{onClick:()=>l(!1),className:"lg:hidden text-brand-grey-400 hover:text-brand-white",children:e.jsx(xe,{size:24})})]}),e.jsx("nav",{className:"flex-1 p-6 space-y-2 overflow-y-auto custom-scrollbar",children:h.map(g=>e.jsxs(z,{to:g.href,className:K("flex items-center gap-4 px-4 py-3 rounded-xl text-base font-semibold transition-all duration-200",p.pathname===g.href?"bg-brand-grey-800 text-brand-white shadow-lg":"text-brand-grey-400 hover:text-brand-white hover:bg-brand-grey-800 hover:shadow-md"),children:[g.icon,e.jsx("span",{children:g.name})]},g.name))}),e.jsxs("div",{className:"p-6 border-t border-brand-grey-800 bg-brand-grey-950 flex flex-col gap-3",children:[e.jsxs(z,{to:"/dashboard/settings",className:"flex items-center gap-3 px-4 py-2 rounded-lg text-base font-medium text-brand-grey-400 hover:text-brand-white hover:bg-brand-grey-800 transition-colors",children:[e.jsx(Z,{size:20}),e.jsx("span",{children:"Settings"})]}),e.jsx("button",{onClick:()=>S(null,null,function*(){yield o(),a("/login")}),className:"flex items-center gap-3 px-4 py-2 rounded-lg text-base font-medium text-red-500 hover:text-white hover:bg-red-600 transition-colors",children:e.jsx("span",{children:"Logout"})})]})]}),e.jsxs("div",{className:"flex-1 flex flex-col lg:ml-72 min-h-screen",children:[e.jsxs("header",{className:"h-20 bg-brand-grey-950 border-b border-brand-grey-800 flex items-center justify-between px-8 shadow-sm sticky top-0 z-30",children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx("button",{onClick:()=>l(!0),className:"lg:hidden text-brand-grey-400 hover:text-brand-white",children:e.jsx(me,{size:24})}),e.jsxs("div",{className:"relative hidden md:block",children:[e.jsx(De,{size:18,className:"absolute left-4 top-1/2 transform -translate-y-1/2 text-brand-grey-500"}),e.jsx("input",{type:"text",placeholder:"Search dashboard...",className:"bg-brand-grey-900 border border-brand-grey-700 text-brand-white pl-12 pr-4 py-3 rounded-lg focus:border-brand-grey-500 focus:outline-none w-72 shadow-sm"})]})]}),e.jsxs("div",{className:"flex items-center gap-6",children:[e.jsxs("button",{className:"relative text-brand-grey-400 hover:text-brand-white",children:[e.jsx(ke,{size:22}),e.jsx("span",{className:"absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full border-2 border-brand-grey-950"})]}),e.jsxs(z,{to:"/",className:"flex items-center gap-2 text-brand-grey-400 hover:text-brand-white transition-colors",children:[e.jsx(ze,{size:18}),e.jsx("span",{className:"hidden sm:inline",children:"Main Site"})]}),e.jsx("div",{className:"w-10 h-10 bg-brand-grey-700 rounded-full flex items-center justify-center shadow-md",children:e.jsx("span",{className:"text-brand-white text-lg font-bold uppercase",children:((c=x==null?void 0:x.name)==null?void 0:c[0])||"U"})})]})]}),e.jsx("main",{className:"flex-1 bg-gradient-to-br from-brand-grey-900/80 to-brand-black/90 p-8 min-h-[calc(100vh-5rem)] overflow-x-auto",children:r})]}),d&&e.jsx("div",{className:"fixed inset-0 bg-black/60 z-40 lg:hidden",onClick:()=>l(!1)})]})};function Le(r,d){return S(this,null,function*(){const l=`geocode_${r}_${d}`,p=localStorage.getItem(l);if(p)try{const o=JSON.parse(p);if(Array.isArray(o)&&o.length===2&&typeof o[0]=="number"&&typeof o[1]=="number")return[o[0],o[1]]}catch(o){}const x=`https://nominatim.openstreetmap.org/search?format=json&q=${encodeURIComponent(`${r}, ${d}`)}&limit=1`;try{const o=yield fetch(x,{headers:{"Accept-Language":"en"}});if(!o.ok)return null;const h=yield o.json();if(h&&h.length>0){const c=parseFloat(h[0].lat),g=parseFloat(h[0].lon);return localStorage.setItem(l,JSON.stringify([c,g])),[c,g]}}catch(o){}return null})}const Ae=N.lazy(()=>w(()=>import("./CalendarWrapper-FP0Lciu2.js"),__vite__mapDeps([0,1]),import.meta.url)),Te=N.lazy(()=>w(()=>import("./BlogManagement-TytBW7yf.js"),__vite__mapDeps([2,1,3,4,5,6,7]),import.meta.url)),Re=N.lazy(()=>w(()=>import("./ServiceManagement-k8cAYs54.js"),__vite__mapDeps([8,1,7]),import.meta.url)),Pe=N.lazy(()=>w(()=>import("./FinancialHealthDashboard-BnA59exg.js"),__vite__mapDeps([9,1]),import.meta.url)),Ee=N.lazy(()=>w(()=>import("./VisitorAnalyticsTable-DF2x6UiZ.js"),__vite__mapDeps([10,1]),import.meta.url)),Ve=N.lazy(()=>w(()=>import("./LeadScoringMetrics-B7qvH4ZH.js"),__vite__mapDeps([11,1,12,6]),import.meta.url)),Ue=N.lazy(()=>w(()=>import("./MapComponents-TRM_TpO4.js"),__vite__mapDeps([13,1]),import.meta.url)),qe=5*60*1e3,D=new Map,He=r=>S(null,null,function*(){var l,p;console.log("Processing visitor geo data:",r);const d=[];for(const a of r){const x=((l=a._id)==null?void 0:l.country)||a.country||"Unknown",o=((p=a._id)==null?void 0:p.region)||a.region||"Unknown",h=a.count||a.visits||0;if(console.log(`Processing location: ${o}, ${x} (${h} visits)`),x!=="Unknown"&&o!=="Unknown")try{console.log(`Geocoding ${o}, ${x}...`);const c=yield Le(o,x);c?(console.log(`Geocoded ${o}, ${x} to:`,c),d.push({lat:c[0],lon:c[1],region:o,country:x,count:h})):console.warn(`No coordinates found for ${o}, ${x}`)}catch(c){console.warn(`Failed to geocode ${o}, ${x}:`,c)}}return console.log("Final geo data:",d),d}),Oe=()=>{var T,R,P,E,V,U,q,H,O,F;console.log("MarketingDashboard component loaded");const[r,d]=u.useState(null),[l,p]=u.useState([]),[a,x]=u.useState(null),[o,h]=u.useState([]),[c,g]=u.useState(!0),[L,M]=u.useState(null),[j,Q]=u.useState("Last 7 days"),X=s=>{const t=D.get(s);return t&&Date.now()-t.timestamp<qe?t.data:null},Y=(s,t)=>{D.set(s,{data:t,timestamp:Date.now()})},$=(s,t=3)=>S(null,null,function*(){for(let i=0;i<t;i++)try{const n=yield fetch(s,{credentials:"include",headers:{"Content-Type":"application/json"}});if(n.status===429){const m=Math.pow(2,i)*1e3;console.warn(`Rate limited. Retrying in ${m}ms...`),yield new Promise(b=>setTimeout(b,m));continue}if(!n.ok)throw new Error(`HTTP ${n.status}: ${n.statusText}`);return yield n.json()}catch(n){if(i===t-1)throw n;yield new Promise(m=>setTimeout(m,1e3*(i+1)))}}),A=(s=!1)=>S(null,null,function*(){try{if(g(!0),M(null),s)D.clear();else{const k=`analytics_${j}`,f=X(k);if(f){d(f.traffic),p(f.countryStats),x(f.visitorAnalytics),h(f.geoData),g(!1);return}}const t=k=>new Promise(f=>setTimeout(f,k));console.log("Fetching visitor traffic...");const i=yield $(`http://localhost:5000/api/visitor/traffic?period=${j}`);yield t(500),console.log("Fetching visitor country stats...");const n=yield $(`http://localhost:5000/api/visitor/country-stats?period=${j}`);yield t(500),console.log("Fetching visitor analytics...");const m=yield $(`http://localhost:5000/api/visitor/analytics?period=${j}`);console.log("Processing geo data with country data:",n);const b=yield He(n);console.log("Processed geo data result:",b);const v=`analytics_${j}`;Y(v,{traffic:i,countryStats:n,visitorAnalytics:m,geoData:b}),d(i),p(n),x(m),h(b)}catch(t){console.error("Error fetching analytics data:",t),t.message.includes("429")?M("Too many requests. Please wait a moment and try again."):t.message.includes("Failed to fetch")?M("Unable to connect to analytics server. Please check if the backend is running."):M(`Failed to load analytics data: ${t.message}`)}finally{g(!1)}});u.useEffect(()=>{const s=setTimeout(()=>{A()},1e3);return()=>clearTimeout(s)},[j]);const ee=()=>{A(!0)},se=({title:s,value:t,change:i,icon:n,color:m})=>e.jsx("div",{className:"bg-brand-grey-900 rounded-xl p-4 border border-brand-grey-700",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("p",{className:"text-brand-grey-400 text-sm font-medium",children:s}),e.jsx("p",{className:"text-2xl font-bold text-brand-white mt-1",children:t}),i&&e.jsxs("div",{className:`flex items-center gap-1 mt-2 ${i>=0?"text-green-400":"text-red-400"}`,children:[i>=0?e.jsx(Ne,{size:16}):e.jsx(ve,{size:16}),e.jsxs("span",{className:"text-sm font-medium",children:[Math.abs(i),"%"]})]})]}),e.jsx("div",{className:`p-3 rounded-lg ${m}`,children:e.jsx(n,{size:24,className:"text-white"})})]})});return e.jsx(Ce,{children:e.jsxs(he,{children:[e.jsx(_,{path:"/calendar",element:e.jsx(u.Suspense,{fallback:e.jsx("div",{className:"flex items-center justify-center h-64",children:e.jsx("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-brand-accent-100"})}),children:e.jsx(Ae,{})})}),e.jsx(_,{path:"/",element:e.jsxs("div",{className:"p-4 space-y-4",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("h1",{className:"text-2xl font-bold text-brand-white",children:"Marketing Analytics"}),e.jsx("p",{className:"text-brand-grey-400 text-sm",children:"Visitor tracking & marketing performance"})]}),e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsxs("button",{onClick:ee,disabled:c,className:"flex items-center gap-2 px-3 py-2 bg-brand-grey-800 hover:bg-brand-grey-700 disabled:opacity-50 disabled:cursor-not-allowed rounded-lg border border-brand-grey-700 text-brand-grey-300 hover:text-brand-white transition-colors",children:[e.jsx(_e,{size:16,className:c?"animate-spin":""}),c?"Loading...":"Refresh"]}),e.jsx("div",{className:"flex bg-brand-grey-800 rounded-lg p-1",children:["7d","30d","90d","1y"].map(s=>e.jsx("button",{onClick:()=>Q(s==="7d"?"Last 7 days":s==="30d"?"Last 30 days":s==="90d"?"Last 3 months":"Last year"),className:`px-3 py-1 rounded text-sm font-medium transition-all ${s==="7d"&&j==="Last 7 days"||s==="30d"&&j==="Last 30 days"||s==="90d"&&j==="Last 3 months"||s==="1y"&&j==="Last year"?"bg-brand-accent-100 text-brand-grey-950":"text-brand-grey-400 hover:text-brand-white"}`,children:s},s))})]})]}),L&&e.jsxs("div",{className:"bg-red-900/20 border border-red-700 rounded-lg p-4 flex items-center gap-3",children:[e.jsx(ge,{size:20,className:"text-red-400"}),e.jsxs("div",{children:[e.jsx("p",{className:"text-red-400 font-medium",children:"Analytics Error"}),e.jsx("p",{className:"text-red-300 text-sm",children:L})]})]}),e.jsx("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-3 mb-6",children:[{title:"Total Visits",value:(R=(T=r==null?void 0:r.totalVisits)==null?void 0:T.toLocaleString())!=null?R:"0",change:12.5,icon:C,color:"bg-blue-600"},{title:"Page Views",value:(E=(P=r==null?void 0:r.pageViews)==null?void 0:P.toLocaleString())!=null?E:"0",change:8.2,icon:be,color:"bg-green-600"},{title:"Unique Visitors",value:(U=(V=r==null?void 0:r.uniqueVisitors)==null?void 0:V.toLocaleString())!=null?U:"0",change:15.3,icon:W,color:"bg-purple-600"},{title:"Avg. Session",value:r!=null&&r.avgSessionDuration?`${Math.round(r.avgSessionDuration/1e3)}s`:"0s",change:-2.1,icon:ue,color:"bg-orange-600"}].map((s,t)=>e.jsx(se,G({},s),t))}),e.jsxs("div",{className:"grid lg:grid-cols-2 gap-6",children:[e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"bg-brand-grey-900 rounded-xl p-6 border border-brand-grey-700",children:[e.jsx("div",{className:"flex items-center justify-between mb-4",children:e.jsxs("h3",{className:"text-lg font-semibold text-brand-white flex items-center gap-2",children:[e.jsx(fe,{size:20,className:"text-blue-400"}),"Traffic Trends"]})}),e.jsxs("div",{className:"space-y-4",children:[e.jsx("h4",{className:"text-brand-grey-300 font-medium",children:"Visitor Status Distribution"}),e.jsxs("div",{className:"space-y-3",children:[(q=a==null?void 0:a.visitorsByStatus)==null?void 0:q.map((s,t)=>{const i={new:"New Visitors",in_progress:"In Progress",qualified:"Qualified Leads",converted:"Converted",lost:"Lost"},n={new:"text-blue-400",in_progress:"text-yellow-400",qualified:"text-green-400",converted:"text-purple-400",lost:"text-red-400"};return e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"text-brand-grey-300",children:i[s._id]||s._id||"Unknown"}),e.jsx("span",{className:`font-medium ${n[s._id]||"text-brand-white"}`,children:s.count||0})]},t)}),(!(a!=null&&a.visitorsByStatus)||a.visitorsByStatus.length===0)&&e.jsx("div",{className:"text-brand-grey-400 text-center py-4",children:"No visitor status data available"})]})]})]}),e.jsxs("div",{className:"bg-brand-grey-900 rounded-xl p-6 border border-brand-grey-700",children:[e.jsxs("h3",{className:"text-lg font-semibold text-brand-white mb-4 flex items-center gap-2",children:[e.jsx(pe,{size:20,className:"text-green-400"}),"Lead Score Distribution"]}),e.jsxs("div",{className:"space-y-3",children:[(H=a==null?void 0:a.leadScoreDistribution)==null?void 0:H.map((s,t)=>{const i=["0-25","25-50","50-75","75-100"],n=["text-red-400","text-yellow-400","text-blue-400","text-green-400"];return e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"text-brand-grey-300",children:i[t]||"Other"}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("span",{className:`font-medium ${n[t]||"text-brand-white"}`,children:s.count||0}),e.jsx("span",{className:"text-brand-grey-400 text-sm",children:"visitors"})]})]},t)}),(!(a!=null&&a.leadScoreDistribution)||a.leadScoreDistribution.length===0)&&e.jsx("div",{className:"text-brand-grey-400 text-center py-4",children:"No lead score data available"})]})]})]}),e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"bg-brand-grey-900 rounded-xl p-6 border border-brand-grey-700",children:[e.jsxs("h3",{className:"text-lg font-semibold text-brand-white mb-4 flex items-center gap-2",children:[e.jsx(J,{size:20,className:"text-purple-400"}),"Visitor Location Map"]}),e.jsx("div",{className:"text-brand-grey-400 text-sm mb-4",children:"Real-time visitor locations based on analytics data"}),e.jsx(u.Suspense,{fallback:e.jsx("div",{className:"h-96 bg-brand-grey-800 rounded-lg animate-pulse flex items-center justify-center",children:e.jsx("span",{className:"text-brand-grey-400",children:"Loading map..."})}),children:o.length>0?e.jsx(Ue,{geoData:o}):e.jsx("div",{className:"h-96 bg-brand-grey-800 rounded-lg flex items-center justify-center",children:e.jsxs("div",{className:"text-center",children:[e.jsx(J,{size:48,className:"text-brand-grey-600 mx-auto mb-2"}),e.jsx("p",{className:"text-brand-grey-400",children:"Processing visitor locations..."}),e.jsx("p",{className:"text-brand-grey-500 text-sm",children:"Map will appear once location data is geocoded"})]})})})]}),e.jsxs("div",{className:"bg-brand-grey-900 rounded-xl p-6 border border-brand-grey-700",children:[e.jsxs("h3",{className:"text-lg font-semibold text-brand-white mb-4 flex items-center gap-2",children:[e.jsx(ye,{size:20,className:"text-orange-400"}),"Regional Visitor Statistics"]}),e.jsx("div",{className:"space-y-3 max-h-64 overflow-y-auto",children:l.length>0?l.map((s,t)=>{var k,f;console.log("Processing stat for table:",s);const i=((k=s._id)==null?void 0:k.country)||s.country||"Unknown",n=((f=s._id)==null?void 0:f.region)||s.region||"Unknown",m=s.count||0,b=Math.max(...l.map(te=>te.count||0)),v=b>0?Math.round(m/b*100):0;return console.log(`Table display: ${i} - ${n} (${m} visits, ${v}%)`),e.jsxs("div",{className:"flex items-center justify-between p-3 bg-brand-grey-800 rounded-lg hover:bg-brand-grey-700 transition-colors",children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("div",{className:"w-8 h-8 bg-purple-600 rounded-full flex items-center justify-center text-white text-sm font-bold",children:(i==null?void 0:i.slice(0,2).toUpperCase())||"UN"}),e.jsxs("div",{children:[e.jsx("span",{className:"text-brand-white font-medium",children:i}),n!=="Unknown"&&e.jsx("div",{className:"text-brand-grey-400 text-sm",children:n})]})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("span",{className:"text-brand-white font-medium",children:m}),e.jsxs("span",{className:"text-brand-grey-400 text-sm",children:["(",v,"%)"]}),e.jsx("div",{className:"w-16 bg-brand-grey-700 rounded-full h-2",children:e.jsx("div",{className:"bg-purple-500 h-2 rounded-full",style:{width:`${Math.min(100,v)}%`}})})]})]},t)}):e.jsx("div",{className:"text-center py-8 text-brand-grey-400",children:"No regional data available"})})]})]})]}),e.jsxs("div",{className:"grid lg:grid-cols-2 gap-6 mt-6",children:[e.jsxs("div",{className:"bg-brand-grey-900 rounded-xl p-6 border border-brand-grey-700",children:[e.jsxs("h3",{className:"text-lg font-semibold text-brand-white mb-4 flex items-center gap-2",children:[e.jsx(Se,{size:20,className:"text-cyan-400"}),"Business Domains Distribution"]}),e.jsx("div",{className:"space-y-3 max-h-64 overflow-y-auto",children:((O=a==null?void 0:a.businessDomainStats)==null?void 0:O.length)>0?a.businessDomainStats.map((s,t)=>{const i=["text-cyan-400","text-blue-400","text-purple-400","text-pink-400","text-orange-400"],n=["bg-cyan-500","bg-blue-500","bg-purple-500","bg-pink-500","bg-orange-500"],m=Math.max(...a.businessDomainStats.map(v=>v.count||0)),b=m>0?Math.round(s.count/m*100):0;return e.jsxs("div",{className:"flex items-center justify-between p-3 bg-brand-grey-800 rounded-lg",children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("div",{className:`w-3 h-3 rounded-full ${n[t%n.length]}`}),e.jsx("span",{className:"text-brand-white font-medium",children:s._id||"Not Specified"})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("span",{className:`font-medium ${i[t%i.length]}`,children:s.count||0}),e.jsxs("span",{className:"text-brand-grey-400 text-sm",children:["(",b,"%)"]}),e.jsx("div",{className:"w-16 bg-brand-grey-700 rounded-full h-2",children:e.jsx("div",{className:`${n[t%n.length]} h-2 rounded-full`,style:{width:`${Math.min(100,b)}%`}})})]})]},t)}):e.jsx("div",{className:"text-brand-grey-400 text-center py-4",children:"No business domain data available"})})]}),e.jsxs("div",{className:"bg-brand-grey-900 rounded-xl p-6 border border-brand-grey-700",children:[e.jsxs("h3",{className:"text-lg font-semibold text-brand-white mb-4 flex items-center gap-2",children:[e.jsx(C,{size:20,className:"text-emerald-400"}),"Client Segments Distribution"]}),e.jsx("div",{className:"space-y-3 max-h-64 overflow-y-auto",children:((F=a==null?void 0:a.clientSegmentStats)==null?void 0:F.length)>0?a.clientSegmentStats.map((s,t)=>{const i=["text-emerald-400","text-teal-400","text-green-400","text-lime-400","text-yellow-400"],n=["bg-emerald-500","bg-teal-500","bg-green-500","bg-lime-500","bg-yellow-500"],m=Math.max(...a.clientSegmentStats.map(v=>v.count||0)),b=m>0?Math.round(s.count/m*100):0;return e.jsxs("div",{className:"flex items-center justify-between p-3 bg-brand-grey-800 rounded-lg",children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("div",{className:`w-3 h-3 rounded-full ${n[t%n.length]}`}),e.jsx("span",{className:"text-brand-white font-medium",children:s._id||"Not Specified"})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("span",{className:`font-medium ${i[t%i.length]}`,children:s.count||0}),e.jsxs("span",{className:"text-brand-grey-400 text-sm",children:["(",b,"%)"]}),e.jsx("div",{className:"w-16 bg-brand-grey-700 rounded-full h-2",children:e.jsx("div",{className:`${n[t%n.length]} h-2 rounded-full`,style:{width:`${Math.min(100,b)}%`}})})]})]},t)}):e.jsx("div",{className:"text-brand-grey-400 text-center py-4",children:"No client segment data available"})})]})]}),e.jsxs("div",{className:"grid lg:grid-cols-2 gap-6 mt-6",children:[e.jsxs("div",{className:"bg-brand-grey-900 rounded-xl p-6 border border-brand-grey-700",children:[e.jsxs("h3",{className:"text-lg font-semibold text-brand-white mb-4 flex items-center gap-2",children:[e.jsx(W,{size:20,className:"text-blue-400"}),"Detailed Visitor Analytics"]}),e.jsx(u.Suspense,{fallback:e.jsx("div",{className:"h-64 bg-brand-grey-800 rounded-lg animate-pulse"}),children:e.jsx(Ee,{})})]}),e.jsxs("div",{className:"bg-brand-grey-900 rounded-xl p-6 border border-brand-grey-700",children:[e.jsxs("h3",{className:"text-lg font-semibold text-brand-white mb-4 flex items-center gap-2",children:[e.jsx(je,{size:20,className:"text-green-400"}),"Lead Scoring & Performance"]}),e.jsx(u.Suspense,{fallback:e.jsx("div",{className:"h-64 bg-brand-grey-800 rounded-lg animate-pulse"}),children:e.jsx(Ve,{})})]})]}),e.jsxs("div",{className:"flex gap-3 mt-6",children:[e.jsx("button",{className:"px-4 py-2 bg-brand-accent-100 hover:bg-brand-accent-200 text-brand-grey-950 rounded-lg font-medium transition-colors",children:"Export Report"}),e.jsx("button",{className:"px-4 py-2 bg-brand-grey-800 hover:bg-brand-grey-700 text-brand-white rounded-lg font-medium border border-brand-grey-700 transition-colors",children:"Email Campaign"})]})]})}),e.jsx(_,{path:"blogs",element:e.jsx(Te,{})}),e.jsx(_,{path:"services",element:e.jsx(Re,{})}),e.jsx(_,{path:"financial-health",element:e.jsx(Pe,{})})]})})},Ie=Object.freeze(Object.defineProperty({__proto__:null,default:Oe},Symbol.toStringTag,{value:"Module"}));export{Ne as A,we as B,Me as F,Ie as M,W as U,ve as a,fe as b,Le as g};
