import api from '@/shared/services/api/axios';

export interface CartItem {
  id?: string;
  serviceId: string;
  serviceName: string;
  subServiceId?: string;
  subServiceName?: string;
  description: string;
  price: number;
  quantity: number;
  addedAt?: string;
}

export interface Cart {
  _id?: string;
  userId?: string;
  sessionId: string;
  items: CartItem[];
  totalItems: number;
  totalPrice: number;
  status: 'active' | 'abandoned' | 'converted';
  createdAt?: string;
  updatedAt?: string;
}

export interface CartSummary {
  totalItems: number;
  totalPrice: number;
  itemCount: number;
}

// Get session ID from sessionStorage or create new one
const getSessionId = (): string => {
  let sessionId = sessionStorage.getItem('cart-session-id');
  if (!sessionId) {
    sessionId = Math.random().toString(36).substring(2) + Date.now().toString(36);
    sessionStorage.setItem('cart-session-id', sessionId);
  }
  return sessionId;
};

// Get user's cart
export const getCart = async (): Promise<Cart> => {
  try {
    const sessionId = getSessionId();
    const response = await api.get('/cart', {
      headers: {
        'session-id': sessionId
      },
      withCredentials: true // Include cookies for authentication
    });
    return response.data.data || { items: [], totalItems: 0, totalPrice: 0, sessionId };
  } catch (error) {
    console.error('Error fetching cart:', error);
    return { items: [], totalItems: 0, totalPrice: 0, sessionId: getSessionId(), status: 'active' };
  }
};

// Get cart summary
export const getCartSummary = async (): Promise<CartSummary> => {
  try {
    const sessionId = getSessionId();
    const response = await api.get('/cart/summary', {
      headers: {
        'session-id': sessionId
      }
    });
    return response.data.data || { totalItems: 0, totalPrice: 0, itemCount: 0 };
  } catch (error) {
    console.error('Error fetching cart summary:', error);
    return { totalItems: 0, totalPrice: 0, itemCount: 0 };
  }
};

// Add item to cart
export const addToCart = async (item: Omit<CartItem, 'id' | 'quantity'> & { quantity?: number }): Promise<Cart> => {
  try {
    const sessionId = getSessionId();
    const response = await api.post('/cart/add', {
      ...item,
      quantity: item.quantity || 1
    }, {
      headers: {
        'session-id': sessionId
      },
      withCredentials: true // Include cookies for authentication
    });
    return response.data.data;
  } catch (error) {
    console.error('Error adding to cart:', error);
    throw error;
  }
};

// Remove item from cart
export const removeFromCart = async (serviceId: string, subServiceName?: string): Promise<Cart> => {
  try {
    const sessionId = getSessionId();
    const url = subServiceName
      ? `/cart/remove/${encodeURIComponent(serviceId)}/${encodeURIComponent(subServiceName)}`
      : `/cart/remove/${encodeURIComponent(serviceId)}`;

    console.log('🗑️ CartService - Removing item:', {
      serviceId,
      subServiceName,
      url,
      sessionId
    });

    const response = await api.delete(url, {
      headers: {
        'session-id': sessionId
      },
      withCredentials: true
    });

    console.log('✅ CartService - Remove response:', response.data);
    return response.data.data;
  } catch (error) {
    console.error('❌ CartService - Error removing from cart:', error);
    throw error;
  }
};

// Update item quantity
export const updateCartItemQuantity = async (
  serviceId: string, 
  subServiceId: string | undefined, 
  quantity: number
): Promise<Cart> => {
  try {
    const sessionId = getSessionId();
    const url = subServiceId 
      ? `/cart/update/${serviceId}/${subServiceId}`
      : `/cart/update/${serviceId}`;
    
    const response = await api.put(url, { quantity }, {
      headers: {
        'session-id': sessionId
      }
    });
    return response.data.data;
  } catch (error) {
    console.error('Error updating cart item:', error);
    throw error;
  }
};

// Clear cart
export const clearCart = async (): Promise<Cart> => {
  try {
    const sessionId = getSessionId();
    const response = await api.delete('/cart/clear', {
      headers: {
        'session-id': sessionId
      }
    });
    return response.data.data;
  } catch (error) {
    console.error('Error clearing cart:', error);
    throw error;
  }
};
