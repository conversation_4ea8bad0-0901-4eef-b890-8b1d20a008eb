import{j as r,bx as s,by as a,bz as i,bA as n}from"./react-vendor-Dq0qSR31.js";import{b9 as l,ba as c,bb as p,bc as m}from"./vendor-OXu-rwpf.js";import{bd as f}from"./vendor-OXu-rwpf.js";import{g as M}from"./MarketingDashboard-Cvb5huQ3.js";import"./utils-vendor-DSNVchvY.js";import"./index-CasGuY6o.js";import"./state-vendor-DU4y5LsH.js";l.Icon.Default.mergeOptions({iconRetinaUrl:m,iconUrl:p,shadowUrl:c});const g=({geoData:o})=>r.jsxs(s,{center:[20,0],zoom:2,style:{height:"400px",width:"100%"},className:"rounded-lg",children:[r.jsx(a,{url:"https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png",attribution:'© <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'}),o.map((e,t)=>r.jsx(i,{center:[e.lat,e.lon],radius:Math.max(5,Math.min(20,e.count/10)),fillColor:"blue",color:"blue",weight:1,opacity:.8,fillOpacity:.6,children:r.jsx(n,{children:r.jsxs("div",{children:[r.jsx("strong",{children:e.region}),e.country&&r.jsxs("div",{children:["Country: ",e.country]}),r.jsxs("div",{children:["Visitors: ",e.count]})]})})},t))]});export{f as IsoToLatLong,g as default,M as geocodeRegion};
