import Cart from './cartModel.js';
import Service from '../service/serviceModel.js';
import { v4 as uuidv4 } from 'uuid';

// Get cart for user/session
const getCart = async (req, res) => {
  try {
    const sessionId = req.sessionID || req.headers['session-id'] || uuidv4();
    const userId = req.user?.id || req.user?._id || null;

    console.log('Cart - Get Cart:', {
      sessionId,
      userId,
      hasUser: !!req.user,
      userEmail: req.user?.email
    });

    const cart = await Cart.createOrGetCart(sessionId, userId);
    
    res.status(200).json({
      success: true,
      data: cart,
      sessionId: sessionId
    });
  } catch (error) {
    console.error('Error getting cart:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get cart',
      error: error.message
    });
  }
};

// Add item to cart
const addToCart = async (req, res) => {
  try {
    const { serviceId, subServiceId, subServiceName, quantity = 1 } = req.body;
    const sessionId = req.sessionID || req.headers['session-id'] || uuidv4();
    const userId = req.user?.id || req.user?._id || null;

    console.log('Cart - Add to Cart:', {
      sessionId,
      userId,
      hasUser: !!req.user,
      userEmail: req.user?.email,
      serviceId,
      subServiceId
    });

    // Validate required fields
    if (!serviceId) {
      return res.status(400).json({
        success: false,
        message: 'Service ID is required'
      });
    }

    // Get service details
    const service = await Service.findById(serviceId);
    if (!service) {
      return res.status(404).json({
        success: false,
        message: 'Service not found'
      });
    }

    // Validate sub-service if provided
    let subService = null;
    if (subServiceId) {
      // Try to find by _id first (if it exists), then by title, then by custom id
      subService = service.subServices.find(sub =>
        (sub._id && sub._id.toString() === subServiceId) ||
        sub.title === subServiceId ||
        subServiceId.includes(sub.title)
      );

      if (!subService) {
        return res.status(404).json({
          success: false,
          message: 'Sub-service not found'
        });
      }
    }

    // Get or create cart
    const cart = await Cart.createOrGetCart(sessionId, userId);

    // Prepare item data
    const itemData = {
      serviceId: service._id,
      serviceName: service.title,
      subServiceId: subServiceId || null,
      subServiceName: subServiceName || (subService ? subService.title : null),
      description: subService ? subService.description : service.description,
      price: subService ? (subService.price || 0) : (service.price || 0),
      quantity: parseInt(quantity) || 1
    };

    // Add item to cart
    await cart.addItem(itemData);

    res.status(200).json({
      success: true,
      message: 'Item added to cart successfully',
      data: cart,
      sessionId: sessionId
    });
  } catch (error) {
    console.error('Error adding to cart:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to add item to cart',
      error: error.message
    });
  }
};

// Remove item from cart
const removeFromCart = async (req, res) => {
  try {
    // Decode URL parameters to handle special characters
    const serviceId = decodeURIComponent(req.params.serviceId);
    const subServiceName = req.params.subServiceName ? decodeURIComponent(req.params.subServiceName) : undefined;
    const sessionId = req.sessionID || req.headers['session-id'];
    const userId = req.user?.id || req.user?._id || null;

    console.log('Cart - Remove Item:', {
      rawServiceId: req.params.serviceId,
      rawSubServiceName: req.params.subServiceName,
      decodedServiceId: serviceId,
      decodedSubServiceName: subServiceName,
      sessionId,
      userId,
      hasUser: !!req.user,
      userEmail: req.user?.email
    });

    if (!sessionId) {
      return res.status(400).json({
        success: false,
        message: 'Session ID is required'
      });
    }

    // Find cart
    let cart;
    if (userId) {
      cart = await Cart.findByUserId(userId);
    }
    if (!cart) {
      cart = await Cart.findBySessionId(sessionId);
    }

    if (!cart) {
      return res.status(404).json({
        success: false,
        message: 'Cart not found'
      });
    }

    // Log cart before removal
    console.log('🗑️ Cart BEFORE removal:', {
      cartId: cart._id,
      itemCount: cart.items.length,
      items: cart.items.map(item => ({
        serviceId: item.serviceId,
        subServiceId: item.subServiceId,
        serviceName: item.serviceName,
        subServiceName: item.subServiceName
      }))
    });

    // Remove item using subServiceName instead of subServiceId
    await cart.removeItem(serviceId, subServiceName);

    // Log cart after removal
    console.log('🗑️ Cart AFTER removal:', {
      cartId: cart._id,
      itemCount: cart.items.length,
      items: cart.items.map(item => ({
        serviceId: item.serviceId,
        subServiceId: item.subServiceId,
        serviceName: item.serviceName,
        subServiceName: item.subServiceName
      }))
    });

    res.status(200).json({
      success: true,
      message: 'Item removed from cart successfully',
      data: cart
    });
  } catch (error) {
    console.error('Error removing from cart:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to remove item from cart',
      error: error.message
    });
  }
};

// Update item quantity
const updateCartItem = async (req, res) => {
  try {
    const { serviceId, subServiceId } = req.params;
    const { quantity } = req.body;
    const sessionId = req.sessionID || req.headers['session-id'];
    const userId = req.user?.id || req.user?._id || null;

    if (!sessionId) {
      return res.status(400).json({
        success: false,
        message: 'Session ID is required'
      });
    }

    if (!quantity || quantity < 1) {
      return res.status(400).json({
        success: false,
        message: 'Valid quantity is required'
      });
    }

    // Find cart
    let cart;
    if (userId) {
      cart = await Cart.findByUserId(userId);
    }
    if (!cart) {
      cart = await Cart.findBySessionId(sessionId);
    }

    if (!cart) {
      return res.status(404).json({
        success: false,
        message: 'Cart not found'
      });
    }

    // Update item quantity
    await cart.updateItemQuantity(serviceId, subServiceId, parseInt(quantity));

    res.status(200).json({
      success: true,
      message: 'Cart item updated successfully',
      data: cart
    });
  } catch (error) {
    console.error('Error updating cart item:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update cart item',
      error: error.message
    });
  }
};

// Clear cart
const clearCart = async (req, res) => {
  try {
    const sessionId = req.sessionID || req.headers['session-id'];
    const userId = req.user?.id || null;

    if (!sessionId) {
      return res.status(400).json({
        success: false,
        message: 'Session ID is required'
      });
    }

    // Find cart
    let cart;
    if (userId) {
      cart = await Cart.findByUserId(userId);
    }
    if (!cart) {
      cart = await Cart.findBySessionId(sessionId);
    }

    if (!cart) {
      return res.status(404).json({
        success: false,
        message: 'Cart not found'
      });
    }

    // Clear cart
    await cart.clearCart();

    res.status(200).json({
      success: true,
      message: 'Cart cleared successfully',
      data: cart
    });
  } catch (error) {
    console.error('Error clearing cart:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to clear cart',
      error: error.message
    });
  }
};

// Get cart summary
const getCartSummary = async (req, res) => {
  try {
    const sessionId = req.sessionID || req.headers['session-id'];
    const userId = req.user?.id || null;

    if (!sessionId) {
      return res.status(400).json({
        success: false,
        message: 'Session ID is required'
      });
    }

    // Find cart
    let cart;
    if (userId) {
      cart = await Cart.findByUserId(userId);
    }
    if (!cart) {
      cart = await Cart.findBySessionId(sessionId);
    }

    const summary = {
      totalItems: cart ? cart.totalItems : 0,
      totalPrice: cart ? cart.totalPrice : 0,
      itemCount: cart ? cart.items.length : 0
    };

    res.status(200).json({
      success: true,
      data: summary
    });
  } catch (error) {
    console.error('Error getting cart summary:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get cart summary',
      error: error.message
    });
  }
};

export {
  getCart,
  addToCart,
  removeFromCart,
  updateCartItem,
  clearCart,
  getCartSummary
};
