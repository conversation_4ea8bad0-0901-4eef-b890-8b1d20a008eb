import { useEffect, useState, useRef } from 'react';
import { motion, useMotionValue, useSpring } from 'framer-motion';

interface MagneticCursorProps {
  children: React.ReactNode;
  className?: string;
  strength?: number;
  size?: number;
  color?: string;
  enabled?: boolean;
}

const MagneticCursor = ({
  children,
  className = '',
  strength = 0.3,
  size = 40,
  color = '#9333ea',
  enabled = true
}: MagneticCursorProps) => {
  const [isHovered, setIsHovered] = useState(false);
  const [cursorVariant, setCursorVariant] = useState('default');
  const ref = useRef<HTMLDivElement>(null);

  const cursorX = useMotionValue(-100);
  const cursorY = useMotionValue(-100);
  
  const springConfig = { damping: 25, stiffness: 700 };
  const cursorXSpring = useSpring(cursorX, springConfig);
  const cursorYSpring = useSpring(cursorY, springConfig);

  useEffect(() => {
    if (!enabled) return;

    const moveCursor = (e: MouseEvent) => {
      cursorX.set(e.clientX - size / 2);
      cursorY.set(e.clientY - size / 2);
    };

    const handleMouseEnter = () => {
      setIsHovered(true);
      setCursorVariant('hover');
    };

    const handleMouseLeave = () => {
      setIsHovered(false);
      setCursorVariant('default');
    };

    window.addEventListener('mousemove', moveCursor);
    
    if (ref.current) {
      ref.current.addEventListener('mouseenter', handleMouseEnter);
      ref.current.addEventListener('mouseleave', handleMouseLeave);
    }

    return () => {
      window.removeEventListener('mousemove', moveCursor);
      if (ref.current) {
        ref.current.removeEventListener('mouseenter', handleMouseEnter);
        ref.current.removeEventListener('mouseleave', handleMouseLeave);
      }
    };
  }, [cursorX, cursorY, size, enabled]);

  const variants = {
    default: {
      scale: 1,
      opacity: 0.6,
      backgroundColor: color,
    },
    hover: {
      scale: 1.5,
      opacity: 0.8,
      backgroundColor: color,
      mixBlendMode: 'difference' as const,
    },
    click: {
      scale: 0.8,
      opacity: 1,
    }
  };

  return (
    <>
      {enabled && (
        <motion.div
          className="fixed top-0 left-0 pointer-events-none z-50 rounded-full mix-blend-difference"
          style={{
            x: cursorXSpring,
            y: cursorYSpring,
            width: size,
            height: size,
          }}
          variants={variants}
          animate={cursorVariant}
          transition={{ type: "spring", stiffness: 500, damping: 28 }}
        />
      )}
      
      <div
        ref={ref}
        className={`${className} ${enabled ? 'cursor-none' : ''}`}
        style={{ cursor: enabled ? 'none' : 'auto' }}
      >
        {children}
      </div>
    </>
  );
};

// Magnetic element that attracts the cursor
interface MagneticElementProps {
  children: React.ReactNode;
  className?: string;
  strength?: number;
  distance?: number;
}

export const MagneticElement = ({
  children,
  className = '',
  strength = 0.3,
  distance = 100
}: MagneticElementProps) => {
  const ref = useRef<HTMLDivElement>(null);
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });
  const [isNear, setIsNear] = useState(false);

  const x = useMotionValue(0);
  const y = useMotionValue(0);

  useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      if (!ref.current) return;

      const rect = ref.current.getBoundingClientRect();
      const centerX = rect.left + rect.width / 2;
      const centerY = rect.top + rect.height / 2;
      
      const deltaX = e.clientX - centerX;
      const deltaY = e.clientY - centerY;
      const distanceFromCenter = Math.sqrt(deltaX * deltaX + deltaY * deltaY);

      if (distanceFromCenter < distance) {
        setIsNear(true);
        const magnetStrength = (distance - distanceFromCenter) / distance;
        x.set(deltaX * strength * magnetStrength);
        y.set(deltaY * strength * magnetStrength);
      } else {
        setIsNear(false);
        x.set(0);
        y.set(0);
      }

      setMousePosition({ x: e.clientX, y: e.clientY });
    };

    window.addEventListener('mousemove', handleMouseMove);
    return () => window.removeEventListener('mousemove', handleMouseMove);
  }, [x, y, strength, distance]);

  return (
    <motion.div
      ref={ref}
      className={className}
      style={{ x, y }}
      transition={{ type: "spring", stiffness: 300, damping: 30 }}
    >
      {children}
    </motion.div>
  );
};

// Trail cursor effect
export const TrailCursor = ({ enabled = true }: { enabled?: boolean }) => {
  const [trails, setTrails] = useState<Array<{ id: string; x: number; y: number }>>([]);
  const trailIdCounter = useRef(0);

  useEffect(() => {
    if (!enabled) return;

    const handleMouseMove = (e: MouseEvent) => {
      trailIdCounter.current += 1;
      const newTrail = {
        id: `trail-${Date.now()}-${trailIdCounter.current}`,
        x: e.clientX,
        y: e.clientY,
      };

      setTrails(prev => {
        const updated = [...prev, newTrail];
        return updated.slice(-10); // Keep only last 10 trails
      });
    };

    window.addEventListener('mousemove', handleMouseMove);
    return () => window.removeEventListener('mousemove', handleMouseMove);
  }, [enabled]);

  useEffect(() => {
    const interval = setInterval(() => {
      setTrails(prev => prev.slice(1));
    }, 50);

    return () => clearInterval(interval);
  }, []);

  if (!enabled) return null;

  return (
    <div className="fixed inset-0 pointer-events-none z-40">
      {trails.map((trail, index) => (
        <motion.div
          key={trail.id}
          className="absolute w-2 h-2 bg-brand-purple-400 rounded-full"
          style={{
            left: trail.x - 4,
            top: trail.y - 4,
          }}
          initial={{ scale: 1, opacity: 0.8 }}
          animate={{ 
            scale: 0,
            opacity: 0,
          }}
          transition={{ 
            duration: 0.5,
            ease: "easeOut"
          }}
        />
      ))}
    </div>
  );
};

export default MagneticCursor;
