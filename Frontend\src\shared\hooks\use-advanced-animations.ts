import { useEffect, useRef, useState } from 'react';
import { useInView, useAnimation, Variants } from 'framer-motion';

// Animation variants for different types of animations
export const animationVariants: Record<string, Variants> = {
  fadeIn: {
    hidden: { opacity: 0 },
    visible: { opacity: 1 }
  },
  slideUp: {
    hidden: { opacity: 0, y: 50 },
    visible: { opacity: 1, y: 0 }
  },
  slideDown: {
    hidden: { opacity: 0, y: -50 },
    visible: { opacity: 1, y: 0 }
  },
  slideLeft: {
    hidden: { opacity: 0, x: 50 },
    visible: { opacity: 1, x: 0 }
  },
  slideRight: {
    hidden: { opacity: 0, x: -50 },
    visible: { opacity: 1, x: 0 }
  },
  scaleIn: {
    hidden: { opacity: 0, scale: 0.8 },
    visible: { opacity: 1, scale: 1 }
  },
  rotateIn: {
    hidden: { opacity: 0, rotate: -180 },
    visible: { opacity: 1, rotate: 0 }
  },
  bounceIn: {
    hidden: { opacity: 0, scale: 0.3 },
    visible: { 
      opacity: 1, 
      scale: 1,
      transition: {
        type: "spring",
        damping: 10,
        stiffness: 100
      }
    }
  },
  staggerContainer: {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
        delayChildren: 0.2
      }
    }
  },
  staggerItem: {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0 }
  }
};

// Hook for scroll-triggered animations
export const useScrollAnimation = (
  animationType: keyof typeof animationVariants = 'fadeIn',
  threshold: number = 0.1,
  triggerOnce: boolean = true
) => {
  const ref = useRef(null);
  const isInView = useInView(ref, { threshold, once: triggerOnce });
  const controls = useAnimation();

  useEffect(() => {
    if (isInView) {
      controls.start('visible');
    } else if (!triggerOnce) {
      controls.start('hidden');
    }
  }, [isInView, controls, triggerOnce]);

  return {
    ref,
    controls,
    variants: animationVariants[animationType],
    isInView
  };
};

// Hook for staggered animations
export const useStaggerAnimation = (
  itemCount: number,
  staggerDelay: number = 0.1,
  threshold: number = 0.1
) => {
  const ref = useRef(null);
  const isInView = useInView(ref, { threshold, once: true });
  const controls = useAnimation();

  useEffect(() => {
    if (isInView) {
      controls.start('visible');
    }
  }, [isInView, controls]);

  const containerVariants: Variants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: staggerDelay,
        delayChildren: 0.2
      }
    }
  };

  const itemVariants: Variants = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0 }
  };

  return {
    ref,
    controls,
    containerVariants,
    itemVariants,
    isInView
  };
};

// Hook for mouse-following animations
export const useMouseFollower = () => {
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });
  const [isHovering, setIsHovering] = useState(false);

  useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      setMousePosition({ x: e.clientX, y: e.clientY });
    };

    window.addEventListener('mousemove', handleMouseMove);
    return () => window.removeEventListener('mousemove', handleMouseMove);
  }, []);

  return {
    mousePosition,
    isHovering,
    setIsHovering
  };
};

// Hook for parallax effects
export const useParallaxEffect = (speed: number = 0.5) => {
  const [offsetY, setOffsetY] = useState(0);
  const ref = useRef<HTMLElement>(null);

  useEffect(() => {
    const handleScroll = () => {
      if (ref.current) {
        const rect = ref.current.getBoundingClientRect();
        const scrolled = window.pageYOffset;
        const rate = scrolled * speed;
        setOffsetY(rate);
      }
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, [speed]);

  return { ref, offsetY };
};

// Hook for intersection-based animations with custom triggers
export const useIntersectionAnimation = (
  threshold: number = 0.1,
  rootMargin: string = '0px'
) => {
  const [isVisible, setIsVisible] = useState(false);
  const [hasAnimated, setHasAnimated] = useState(false);
  const ref = useRef<HTMLElement>(null);

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting && !hasAnimated) {
          setIsVisible(true);
          setHasAnimated(true);
        }
      },
      { threshold, rootMargin }
    );

    if (ref.current) {
      observer.observe(ref.current);
    }

    return () => {
      if (ref.current) {
        observer.unobserve(ref.current);
      }
    };
  }, [threshold, rootMargin, hasAnimated]);

  return { ref, isVisible, hasAnimated };
};

// Hook for complex animation sequences
export const useAnimationSequence = (
  sequences: Array<{
    delay: number;
    duration: number;
    animation: keyof typeof animationVariants;
  }>
) => {
  const [currentSequence, setCurrentSequence] = useState(0);
  const [isPlaying, setIsPlaying] = useState(false);
  const controls = useAnimation();

  const playSequence = async () => {
    setIsPlaying(true);
    
    for (let i = 0; i < sequences.length; i++) {
      setCurrentSequence(i);
      const sequence = sequences[i];
      
      await new Promise(resolve => setTimeout(resolve, sequence.delay));
      
      await controls.start({
        ...animationVariants[sequence.animation].visible,
        transition: { duration: sequence.duration }
      });
    }
    
    setIsPlaying(false);
  };

  return {
    controls,
    currentSequence,
    isPlaying,
    playSequence,
    variants: sequences[currentSequence] 
      ? animationVariants[sequences[currentSequence].animation]
      : animationVariants.fadeIn
  };
};

// Hook for performance-optimized animations
export const useOptimizedAnimation = (
  animationType: keyof typeof animationVariants = 'fadeIn',
  options: {
    threshold?: number;
    triggerOnce?: boolean;
    reducedMotion?: boolean;
  } = {}
) => {
  const {
    threshold = 0.1,
    triggerOnce = true,
    reducedMotion = false
  } = options;

  const prefersReducedMotion = window.matchMedia('(prefers-reduced-motion: reduce)').matches;
  const shouldAnimate = !reducedMotion && !prefersReducedMotion;

  const { ref, controls, variants, isInView } = useScrollAnimation(
    shouldAnimate ? animationType : 'fadeIn',
    threshold,
    triggerOnce
  );

  return {
    ref,
    controls,
    variants: shouldAnimate ? variants : { hidden: {}, visible: {} },
    isInView,
    shouldAnimate
  };
};
