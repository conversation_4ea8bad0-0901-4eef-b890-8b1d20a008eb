import{j as e,bB as k,W as A,aH as I,r as l,a1 as N}from"./react-vendor-Dq0qSR31.js";import{d as f,O as D,I as F,m as v}from"./index-CasGuY6o.js";import{D as L,a as P,b as R,c as B,d as T,T as z}from"./dialog-BVPp5ZjS.js";import{H as w}from"./utils-vendor-DSNVchvY.js";import"./vendor-OXu-rwpf.js";import"./state-vendor-DU4y5LsH.js";function _({className:x,classNames:c,showOutsideDays:a=!0,...h}){return e.jsx(k,{showOutsideDays:a,className:f("p-6 bg-gradient-to-br from-blue-50 via-white to-purple-50 rounded-2xl shadow-lg border border-blue-100 max-w-md mx-auto font-sans text-gray-900",x),classNames:{months:"flex flex-col sm:flex-row space-y-6 sm:space-x-6 sm:space-y-0",month:"space-y-6",caption:"flex justify-center pt-2 relative items-center mb-2",caption_label:"text-base font-semibold tracking-wide text-blue-700",nav:"space-x-2 flex items-center",nav_button:f(D({variant:"outline"}),"h-9 w-9 bg-white border border-blue-200 text-blue-600 shadow hover:bg-blue-100 hover:text-blue-800 transition-colors duration-150 rounded-full flex items-center justify-center"),nav_button_previous:"absolute left-2",nav_button_next:"absolute right-2",table:"w-full border-collapse space-y-2",head_row:"flex",head_cell:"text-blue-400 rounded-md w-10 font-semibold text-[0.95rem] uppercase tracking-wider py-1",row:"flex w-full mt-2",cell:"h-10 w-10 text-center text-base p-0 relative transition-all duration-200 ease-in-out cursor-pointer [&:has([aria-selected].day-range-end)]:rounded-r-xl [&:has([aria-selected].day-outside)]:bg-purple-100/40 [&:has([aria-selected])]:bg-blue-200/80 first:[&:has([aria-selected])]:rounded-l-xl last:[&:has([aria-selected])]:rounded-r-xl focus-within:relative focus-within:z-20",day:f(D({variant:"ghost"}),"h-10 w-10 p-0 font-medium aria-selected:opacity-100 rounded-full transition-all duration-200"),day_range_end:"day-range-end",day_selected:"bg-gradient-to-br from-blue-500 to-purple-500 text-white font-bold shadow-lg hover:bg-blue-600 hover:text-white focus:bg-blue-700 focus:text-white scale-110 z-10",day_today:"bg-yellow-200 text-yellow-900 border-2 border-yellow-400 font-bold scale-105",day_outside:"day-outside text-gray-300 opacity-60 aria-selected:bg-purple-100/40 aria-selected:text-gray-400 aria-selected:opacity-30",day_disabled:"text-gray-300 opacity-50",day_range_middle:"aria-selected:bg-blue-100 aria-selected:text-blue-700",day_hidden:"invisible",...c},components:{IconLeft:({...b})=>e.jsx(I,{className:"h-5 w-5"}),IconRight:({...b})=>e.jsx(A,{className:"h-5 w-5"})},...h})}_.displayName="Calendar";const V=()=>{const[x,c]=l.useState([]),[a,h]=l.useState(),[b,u]=l.useState(!1),[s,m]=l.useState({title:"",description:""}),[o,r]=l.useState(!1),[j,n]=l.useState(null),g="http://localhost:5000/api/calendar";l.useEffect(()=>{p()},[]);const p=async()=>{r(!0),n(null);try{const t=await w.get(g);c(Array.isArray(t?.data)?t.data:[])}catch(t){console.error("Error fetching events:",t),n("Failed to load events. Please try again later."),c([])}finally{r(!1)}},E=t=>{h(t),u(!0)},S=async()=>{if(!(!a||!s.title)){r(!0),n(null);try{await w.post(g,{...s,date:a.toISOString()}),m({title:"",description:""}),u(!1),await p(),N.event({category:"Calendar",action:"create_event",label:s.title,value:1})}catch(t){console.error("Error adding event:",t),n("Failed to add event. Please try again.")}finally{r(!1)}}},C=async t=>{try{r(!0),n(null),await w.delete(`${g}/${t}`),await p(),N.event({category:"Calendar",action:"delete_event",label:t,value:1})}catch(i){console.error("Error deleting event:",i),n("Failed to delete event. Please try again.")}finally{r(!1)}},y=(x||[]).reduce((t,i)=>{if(!i?.date)return t;try{const d=new Date(i.date).toDateString();t[d]||(t[d]=[]),t[d].push(i)}catch(d){console.error("Error processing event:",i,d)}return t},{});return e.jsxs("div",{className:"text-gray-900",children:[e.jsx(_,{onDayClick:E,modifiers:{hasEvent:t=>!!y[t.toDateString()]},modifiersClassNames:{hasEvent:"bg-blue-200 border-blue-500 border-2"},className:"rounded-md border"}),o&&e.jsx("div",{className:"mt-4 p-4 bg-blue-50 text-blue-800 rounded-md",children:"Loading..."}),j&&e.jsx("div",{className:"mt-4 p-4 bg-red-50 text-red-800 rounded-md",children:j}),e.jsx(L,{open:b,onOpenChange:u,children:e.jsxs(P,{className:"sm:max-w-[425px]",children:[e.jsxs(R,{children:[e.jsx(B,{className:"text-black",children:"Add New Event"}),e.jsx(T,{className:"text-black",children:"Add a new event to your calendar"})]}),e.jsxs("div",{className:"grid gap-4 py-4",children:[e.jsxs("div",{className:"grid grid-cols-4 items-center gap-4",children:[e.jsx("label",{htmlFor:"title",className:"text-right text-black",children:"Title"}),e.jsx(F,{id:"title",value:s.title,onChange:t=>m({...s,title:t.target.value}),className:"col-span-3"})]}),e.jsxs("div",{className:"grid grid-cols-4 items-center gap-4",children:[e.jsx("label",{htmlFor:"description",className:"text-right text-black",children:"Description"}),e.jsx(z,{id:"description",value:s.description,onChange:t=>m({...s,description:t.target.value}),className:"col-span-3"})]}),a&&e.jsxs("div",{className:"text-sm text-black",children:[e.jsx("strong",{children:"Selected Date:"})," ",a.toDateString()]})]}),e.jsxs("div",{className:"flex justify-end gap-2",children:[e.jsx(v,{variant:"outline",onClick:()=>u(!1),disabled:o,children:"Cancel"}),e.jsx(v,{onClick:S,disabled:!s.title||!a||o,children:o?"Saving...":"Save Event"})]}),a&&y[a.toDateString()]&&e.jsxs("div",{className:"mt-4 pt-4 border-t",children:[e.jsxs("h3",{className:"font-semibold mb-2 text-black",children:["Events on ",a.toDateString()]}),e.jsx("ul",{className:"space-y-2",children:y[a.toDateString()].map(t=>e.jsxs("li",{className:"flex justify-between items-center p-2 bg-gray-50 rounded",children:[e.jsx("span",{className:"text-black",children:t.title}),e.jsx(v,{variant:"destructive",size:"sm",onClick:()=>C(t._id),disabled:o,children:"Delete"})]},t._id))})]})]})})]})};export{V as default};
