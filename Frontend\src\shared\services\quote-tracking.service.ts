import api from './api/axios';

export interface QuoteRequestData {
  serviceName: string;
  subServiceName: string;
  subServiceDescription?: string;
  sessionId: string;
  path?: string;
  userEmail?: string;
  userName?: string;
}

// Track quote request for analytics
export const trackQuoteRequest = async (data: QuoteRequestData): Promise<void> => {
  try {
    console.log('🚀 Sending quote request to API:', {
      ...data,
      path: window.location.pathname
    });

    const response = await api.post('/visitor/quote-request', {
      ...data,
      path: window.location.pathname
    });

    console.log('✅ Quote request API response:', response.data);
    console.log('Quote request tracked successfully');
  } catch (error) {
    console.error('❌ Error tracking quote request:', error);
    console.error('Error details:', error.response?.data || error.message);
    // Don't throw error - tracking should be silent
    throw error; // Temporarily throw to see the error
  }
};

// Get session ID for tracking
export const getSessionId = (): string => {
  let sessionId = sessionStorage.getItem('sessionId');
  if (!sessionId) {
    sessionId = 'session_' + Math.random().toString(36).substr(2, 9) + '_' + Date.now();
    sessionStorage.setItem('sessionId', sessionId);
  }
  return sessionId;
};
