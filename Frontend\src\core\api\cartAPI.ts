import api from '../../shared/services/api/axios';
import { CartItem } from '../contexts/CartContext';

export interface CartResponse {
  success: boolean;
  data: any;
  message?: string;
  sessionId?: string;
}

export const cartAPI = {
  // Get cart
  getCart: async (sessionId: string): Promise<CartResponse> => {
    const response = await api.get('/api/cart', {
      headers: {
        'session-id': sessionId
      }
    });
    return response.data;
  },

  // Get cart summary
  getCartSummary: async (sessionId: string): Promise<CartResponse> => {
    const response = await api.get('/api/cart/summary', {
      headers: {
        'session-id': sessionId
      }
    });
    return response.data;
  },

  // Add item to cart
  addToCart: async (
    sessionId: string,
    item: Omit<CartItem, '_id' | 'addedAt'>
  ): Promise<CartResponse> => {
    const response = await api.post('/api/cart/add', item, {
      headers: {
        'session-id': sessionId
      }
    });
    return response.data;
  },

  // Remove item from cart
  removeFromCart: async (
    sessionId: string,
    serviceId: string,
    subServiceId?: string | null
  ): Promise<CartResponse> => {
    const url = subServiceId
      ? `/api/cart/remove/${serviceId}/${subServiceId}`
      : `/api/cart/remove/${serviceId}`;

    const response = await api.delete(url, {
      headers: {
        'session-id': sessionId
      }
    });
    return response.data;
  },

  // Update cart item quantity
  updateCartItem: async (
    sessionId: string,
    serviceId: string,
    subServiceId: string | null | undefined,
    quantity: number
  ): Promise<CartResponse> => {
    const url = subServiceId
      ? `/api/cart/update/${serviceId}/${subServiceId}`
      : `/api/cart/update/${serviceId}`;

    const response = await api.put(url, { quantity }, {
      headers: {
        'session-id': sessionId
      }
    });
    return response.data;
  },

  // Clear cart
  clearCart: async (sessionId: string): Promise<CartResponse> => {
    const response = await api.delete('/api/cart/clear', {
      headers: {
        'session-id': sessionId
      }
    });
    return response.data;
  }
};
