import{r,j as e,aV as x,F as u,a4 as f}from"./react-vendor-Dq0qSR31.js";import{u as y,a as j,b as N,g as v,B as w}from"./index-CasGuY6o.js";import{b1 as s}from"./vendor-OXu-rwpf.js";import"./utils-vendor-DSNVchvY.js";import"./state-vendor-DU4y5LsH.js";const C=()=>{const{trackClick:b}=y(),{language:n,t:a}=j(),{scrollToTop:i}=N(),[o,g]=r.useState([]),[h,d]=r.useState(!1),[c,m]=r.useState(null);r.useEffect(()=>{i(),p()},[n,i]);const p=async()=>{d(!0),m(null);try{const t=await v({language:n});g(t.data)}catch{m("Failed to fetch blogs")}finally{d(!1)}};return e.jsx("div",{className:"pt-20 min-h-screen relative",children:e.jsx("div",{className:"section-padding",children:e.jsxs("div",{className:"container-custom",children:[e.jsxs(s.div,{initial:{opacity:0,y:30},animate:{opacity:1,y:0},transition:{duration:.8},className:"text-center max-w-4xl mx-auto mb-16",children:[e.jsx("div",{className:"inline-flex items-center justify-center w-20 h-20 bg-gradient-to-br from-brand-purple-600 to-brand-purple-800 rounded-3xl mb-8 glow-effect",children:e.jsx(x,{className:"w-10 h-10 text-white"})}),e.jsx("h1",{className:"text-4xl md:text-6xl font-bold gradient-text mb-6",children:a("blog.title")}),e.jsx("p",{className:"text-xl text-brand-purple-200 mb-10 leading-relaxed",children:a("blog.subtitle")}),e.jsx("div",{className:"w-32 h-1 bg-gradient-to-r from-brand-purple-600 to-brand-purple-800 mx-auto rounded-full"})]}),h?e.jsxs("div",{className:"text-center text-brand-purple-300 py-12",children:[e.jsx("div",{className:"animate-spin w-8 h-8 border-4 border-brand-purple-600 border-t-transparent rounded-full mx-auto mb-4"}),a("common.loading")]}):c?e.jsxs("div",{className:"text-center text-red-500 py-12 bg-red-500/10 rounded-2xl border border-red-500/20",children:[e.jsx("p",{className:"mb-4",children:c}),e.jsx("button",{onClick:p,className:"btn-primary",children:"Try Again"})]}):o.length===0?e.jsxs("div",{className:"text-center text-brand-purple-300 py-12",children:[e.jsx(x,{className:"w-16 h-16 mx-auto mb-4 text-brand-purple-500"}),e.jsx("p",{children:"No blog posts found."})]}):e.jsx("div",{className:"space-y-16",children:o.map((t,l)=>e.jsx(s.div,{initial:{opacity:0,y:50},animate:{opacity:1,y:0},transition:{duration:.6,delay:l*.1},className:`flex flex-col ${l%2===0?"lg:flex-row":"lg:flex-row-reverse"} gap-8 items-center`,children:e.jsx(w,{title:t.title,excerpt:t.excerpt,image:t.image||"/public/placeholder.svg",date:t.publishedAt?new Date(t.publishedAt).toLocaleDateString():new Date(t.createdAt).toLocaleDateString(),readTime:"5 min read",category:t.category||"General",href:`/blog/${t.slug||t.id}`,url:t.url,onClick:()=>b({pageName:"Blog",postTitle:t.title}),index:l})},t.id))}),e.jsxs(s.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.6,delay:.3},className:"flex flex-col sm:flex-row gap-4 justify-center mt-16",children:[e.jsxs(u,{to:"/",className:"bg-gradient-to-r from-brand-purple-600 to-brand-purple-700 hover:from-brand-purple-700 hover:to-brand-purple-800 text-white px-8 py-4 rounded-lg font-semibold flex items-center justify-center transition-all duration-300 glow-effect",children:[a("nav.home"),e.jsx(f,{className:"ml-2 h-5 w-5"})]}),e.jsx(u,{to:"/contact",className:"bg-transparent border-2 border-brand-purple-500 text-brand-purple-300 hover:bg-brand-purple-600 hover:text-white hover:border-brand-purple-400 px-8 py-4 rounded-lg font-semibold transition-all duration-300",children:a("nav.contact")})]})]})})})};export{C as default};
