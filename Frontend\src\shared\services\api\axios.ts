import axios from 'axios';
import { removeToken } from '@/shared/services/api/auth';

const api = axios.create({
  baseURL: import.meta.env.VITE_API_URL,
  headers: {
    'Content-Type': 'application/json',
  },
  withCredentials: true, // This is important for sending cookies with cross-origin requests
});

// Request interceptor
api.interceptors.request.use(
  (config) => {
    // Don't set Authorization header here since we're using HTTP-only cookies
    return config;
  },
  (error) => Promise.reject(error)
);

// List of public routes that should not trigger redirect to /login
const PUBLIC_ROUTES = [
  '/',
  '/about',
  '/services',
  '/contact',
  '/cart',
  '/blog',
  '/login',
  '/register',
  '/forgot-password',
  '/unauthorized',
];

function isPublicRoute(pathname) {
  if (PUBLIC_ROUTES.includes(pathname)) return true;

  // Handle dynamic routes
  if (/^\/reset-password\//.test(pathname)) return true;
  if (/^\/services\//.test(pathname)) return true; // /services/:slug
  if (/^\/blog\//.test(pathname)) return true; // /blog/:slug

  return false;
}

// Response interceptor
api.interceptors.response.use(
  (response) => response,
  async (error) => {
    // Handle 401 Unauthorized errors
    if (error.response?.status === 401) {
      // Clear any client-side auth state
      removeToken();
      
      // Redirect to login if not already there
      if (!isPublicRoute(window.location.pathname)) {
        window.location.href = '/login';
      }
    }
    
    return Promise.reject(error);
  }
);

export default api;
