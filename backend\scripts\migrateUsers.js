import mongoose from 'mongoose';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Import models
import User from '../src/components/user/userModel.js';
import Admin from '../src/components/auth/authModel.js';

const migrateUsers = async () => {
  try {
    // Connect to MongoDB
    await mongoose.connect(process.env.MONGODB_URI);
    console.log('Connected to MongoDB');

    // Get all users from the original users collection
    const originalUsers = await mongoose.connection.db.collection('users').find({}).toArray();
    console.log(`Found ${originalUsers.length} users to migrate`);

    let adminCount = 0;
    let userCount = 0;
    let skippedCount = 0;

    for (const user of originalUsers) {
      try {
        // Check if user is admin/staff
        if (user.role === 'admin' || user.role === 'marketing_responsible') {
          // Check if already exists in admins collection
          const existingAdmin = await Admin.findOne({ email: user.email });
          if (existingAdmin) {
            console.log(`Admin ${user.email} already exists, skipping...`);
            skippedCount++;
            continue;
          }

          // Create admin user (without business fields)
          const adminData = {
            _id: user._id,
            name: user.name,
            email: user.email,
            password: user.password,
            role: user.role,
            resetPasswordToken: user.resetPasswordToken,
            resetPasswordExpire: user.resetPasswordExpire,
            isActive: user.isActive !== undefined ? user.isActive : true,
            isVerified: user.isVerified !== undefined ? user.isVerified : true,
            verificationToken: user.verificationToken,
            verificationTokenExpires: user.verificationTokenExpires,
            registrationIP: user.registrationIP,
            createdAt: user.createdAt,
            updatedAt: user.updatedAt,
            __v: user.__v
          };

          await Admin.create(adminData);
          console.log(`Migrated admin: ${user.email}`);
          adminCount++;
        } else {
          // Check if already exists in users collection
          const existingUser = await User.findOne({ email: user.email });
          if (existingUser) {
            console.log(`User ${user.email} already exists, skipping...`);
            skippedCount++;
            continue;
          }

          // Create regular user (with business fields)
          const userData = {
            _id: user._id,
            name: user.name,
            email: user.email,
            password: user.password,
            role: 'user',
            businessDomain: user.businessDomain,
            clientSegment: user.clientSegment,
            companyName: user.companyName,
            companySize: user.companySize,
            resetPasswordToken: user.resetPasswordToken,
            resetPasswordExpire: user.resetPasswordExpire,
            isActive: user.isActive !== undefined ? user.isActive : true,
            isVerified: user.isVerified !== undefined ? user.isVerified : true,
            verificationToken: user.verificationToken,
            verificationTokenExpires: user.verificationTokenExpires,
            registrationIP: user.registrationIP,
            emailVerified: user.emailVerified,
            createdAt: user.createdAt,
            updatedAt: user.updatedAt,
            __v: user.__v
          };

          await User.create(userData);
          console.log(`Migrated user: ${user.email}`);
          userCount++;
        }
      } catch (error) {
        console.error(`Error migrating user ${user.email}:`, error.message);
      }
    }

    console.log('\n=== Migration Summary ===');
    console.log(`Total users processed: ${originalUsers.length}`);
    console.log(`Admins migrated: ${adminCount}`);
    console.log(`Regular users migrated: ${userCount}`);
    console.log(`Skipped (already exist): ${skippedCount}`);

    
    
  

    console.log('\nMigration completed successfully!');
    
  } catch (error) {
    console.error('Migration failed:', error);
  } finally {
    await mongoose.disconnect();
    console.log('Disconnected from MongoDB');
  }
};

// Run migration
migrateUsers();
