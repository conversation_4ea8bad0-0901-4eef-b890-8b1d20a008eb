import jwt from 'jsonwebtoken';
import async<PERSON>and<PERSON> from 'express-async-handler';
import User from '../user/userModel.js';
import Admin from './authModel.js';
import { JWT_EXPIRE, JWT_COOKIE_EXPIRE, ROLES } from '../../shared/config/constants.js';
import { sendEmail, getVerificationEmailTemplate } from '../../shared/utils/emailService.js';
import { generateVerificationToken } from '../../shared/utils/verificationToken.js';
import crypto from 'crypto';

// @desc    Register a new user
// @route   POST /api/auth/register
// @access  Public
const registerUser = asyncHandler(async (req, res) => {
  try {
    const { name, email, password, role, businessDomain, clientSegment, companyName, companySize } = req.body;

    // Validate input - business domain and client segment are optional for admin/staff
    if (!name || !email || !password) {
      res.status(400);
      throw new Error('Please provide name, email, and password');
    }

    // For regular users (not admin/staff), require business domain and client segment
    if (role === 'user' && (!businessDomain || !clientSegment)) {
      res.status(400);
      throw new Error('Please provide business domain and client segment for user registration');
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      res.status(400);
      throw new Error('Please provide a valid email address');
    }

    // Check if user exists in both collections
    const userExists = await User.findOne({ email });
    const adminExists = await Admin.findOne({ email });
    if (userExists || adminExists) {
      res.status(409).json({
        success: false,
        message: 'An account with this email already exists. Please log in or use a different email.'
      });
      return;
    }

    // Validate role if provided
    const validRoles = Object.values(ROLES);
    if (role && !validRoles.includes(role)) {
      res.status(400);
      throw new Error(`Invalid role. Must be one of: ${validRoles.join(', ')}`);
    }

    // Get IP address for registration tracking (use same logic as visitor tracking)
    const { getClientIP } = await import('../visitor/visitorController.js');
    const registrationIP = getClientIP(req);

    // Create user with the appropriate model based on role
    const verificationToken = generateVerificationToken();
    const verificationTokenExpires = Date.now() + 60 * 60 * 1000; // 1 hour

    let user;
    if (role === 'admin' || role === 'marketing_responsible') {
      // Create admin/staff user (no business fields required)
      user = await Admin.create({
        name,
        email,
        password,
        role: role || 'admin',
        registrationIP,
        isVerified: false,
        verificationToken,
        verificationTokenExpires,
      });
    } else {
      // Create regular user (business fields required)
      user = await User.create({
        name,
        email,
        password,
        role: 'user',
        businessDomain,
        clientSegment,
        companyName,
        companySize,
        registrationIP,
        isVerified: false,
        verificationToken,
        verificationTokenExpires,
      });
    }

    if (!user) {
      res.status(500).json({
        success: false,
        message: 'Failed to create user. Please try again.'
      });
      return;
    }

    // Send verification email
    const verificationUrl = `${req.protocol}://${req.get('host')}/api/auth/verify-email?token=${verificationToken}&email=${encodeURIComponent(email)}`;
    const html = getVerificationEmailTemplate(name, verificationUrl);
    try {
      const emailResult = await sendEmail({
        to: email,
        subject: 'Verify your email address',
        html,
      });} catch (emailError) {
      console.error('Failed to send verification email:', emailError);
      // Optionally, you can delete the user if email fails
      await User.deleteOne({ _id: user._id });
      res.status(500).json({
        success: false,
        message: 'Registration failed: Unable to send verification email. Please try again later.'
      });
      return;
    }

    // Link user to visitor using new system
    try {
      const { linkUserToVisitor } = await import('../visitor/visitorController.js');
      await linkUserToVisitor(registrationIP, user);} catch (error) {}
    res.status(201).json({
      success: true,
      message: 'Registration successful! Please check your email to verify your account before logging in.'
    });
    return; // Ensure no further code is executed
  } catch (error) {
    console.error('Registration error:', error);
    throw error; // This will be caught by asyncHandler
  }
});

// @desc    Auth user & get token
// @route   POST /api/auth/login
// @access  Public
const loginUser = asyncHandler(async (req, res) => {
  try {
    const { email, password } = req.body;

    // Validate input
    if (!email || !password) {
      res.status(400);
      throw new Error('Please provide email and password');
    }

    // Check for user in both collections
    let user = await User.findOne({ email }).select('+password');
    let isAdmin = false;

    if (!user) {
      user = await Admin.findOne({ email }).select('+password');
      isAdmin = true;
    }

    if (!user) {
      res.status(401);
      throw new Error('Invalid credentials');
    }

    // Check if email is verified
    if (!user.isVerified) {
      res.status(401);
      throw new Error('Please verify your email before logging in.');
    }

    // Check if password matches
    const isMatch = await user.matchPassword(password);
    if (!isMatch) {
      res.status(401);
      throw new Error('Invalid credentials');
    }

    // Check if user is active
    if (!user.isActive) {
      res.status(403);
      throw new Error('Account is deactivated. Please contact support.');
    }

    // Link anonymous visitor data to user account (only for regular users, not admin/staff)
    try {
      // Only link visitors for regular users (not admin/staff)
      if (user.role === 'user') {
        // Get user's IP address (use same logic as visitor tracking)
        const { getClientIP } = await import('../visitor/visitorController.js');
        let ip = getClientIP(req);

        // Use new visitor linking system
        const { linkUserToVisitor } = await import('../visitor/visitorController.js');
        await linkUserToVisitor(ip, user);
      }
    } catch (visitorLinkError) {
      // Don't fail login if visitor linking fails
    }

    generateTokenResponse(user, 200, res);
  } catch (error) {
    console.error('Login error:', error);
    throw error; // This will be caught by asyncHandler
  }
});

// @desc    Get user profile
// @route   GET /api/auth/me
// @access  Private
const getMe = asyncHandler(async (req, res) => {
  try {if (!req.user?.id) {
      console.error('No user ID in request');
      return res.status(401).json({
        success: false,
        message: 'Not authorized, no user ID found',
      });
    }

    // Check both collections for the user
    let user = await User.findById(req.user.id).select('-password');

    if (!user) {
      user = await Admin.findById(req.user.id).select('-password');
    }

    if (!user) {
      console.error('User not found for ID:', req.user.id);
      return res.status(404).json({
        success: false,
        message: 'User not found',
      });
    }
    
    // For clients that might need the token in the response
    const token = req.cookies.token || 
                 (req.headers.authorization && req.headers.authorization.split(' ')[1]);
    
    const response = {
      _id: user._id,
      name: user.name,
      email: user.email,
      role: user.role,
      isActive: user.isActive,
      createdAt: user.createdAt,
      updatedAt: user.updatedAt,
    };
    
    // Only include token in response if it exists and not in cookie
    if (token && !req.cookies.token) {response.token = token;
    }res.status(200).json({
      success: true,
      data: response,
    });
  } catch (error) {
    console.error('Error in getMe controller:', {
      error: error.message,
      stack: error.stack,
      userId: req.user?.id,
    });
    
    res.status(500).json({
      success: false,
      message: 'Server error while fetching user profile',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined,
    });
  }
});

export const logout = asyncHandler(async (req, res) => {
  // Clear the token cookie with the same options used when setting it
  res.clearCookie('token', {
    httpOnly: true,
    secure: process.env.NODE_ENV === 'production',
    sameSite: process.env.NODE_ENV === 'production' ? 'none' : 'lax',
    domain: process.env.COOKIE_DOMAIN || undefined,
    path: '/',
  });

  res.status(200).json({
    success: true,
    message: 'Successfully logged out',
  });
});

// @desc    Update user profile
// @route   PUT /api/auth/me
// @access  Private
const updateProfile = asyncHandler(async (req, res) => {
  // Check both collections for the user
  let user = await User.findById(req.user.id);

  if (!user) {
    user = await Admin.findById(req.user.id);
  }

  if (user) {
    user.name = req.body.name || user.name;
    user.email = req.body.email || user.email;

    if (req.body.password) {
      user.password = req.body.password;
    }

    const updatedUser = await user.save();

    res.status(200).json({
      success: true,
      data: {
        _id: updatedUser._id,
        name: updatedUser.name,
        email: updatedUser.email,
        role: updatedUser.role,
      },
    });
  } else {
    res.status(404);
    throw new Error('User not found');
  }
});

// @desc    Forgot password
// @route   POST /api/auth/forgotpassword
// @access  Public
const forgotPassword = asyncHandler(async (req, res) => {
  const { email } = req.body;

  // Check both collections for the user
  let user = await User.findOne({ email });

  if (!user) {
    user = await Admin.findOne({ email });
  }

  if (!user) {
    res.status(404).json({ success: false, message: 'No user with that email' });
    return;
  }
  // Get reset token
  const resetToken = user.getResetPasswordToken();
  await user.save({ validateBeforeSave: false });
  // Create reset URL (use frontend URL, only first if comma-separated)
  let FRONTEND_URL = process.env.FRONTEND_URL || 'http://localhost:8080';
  if (FRONTEND_URL.includes(',')) {
    FRONTEND_URL = FRONTEND_URL.split(',')[1] || FRONTEND_URL.split(',')[0];
  }
  const resetUrl = `${FRONTEND_URL}/reset-password/${resetToken}`;
  // Send email
  const { getResetPasswordEmailTemplate, sendEmail } = await import('../../shared/utils/emailService.js');
  const html = getResetPasswordEmailTemplate(user.name, resetUrl);
  try {
    await sendEmail({
      to: user.email,
      subject: 'Password Reset Request',
      html,
    });
    res.status(200).json({ success: true, message: 'Password reset email sent. Please check your inbox.' });
  } catch (err) {
    user.resetPasswordToken = undefined;
    user.resetPasswordExpire = undefined;
    await user.save({ validateBeforeSave: false });
    res.status(500).json({ success: false, message: 'Email could not be sent' });
  }
});

// @desc    Reset password
// @route   PUT /api/auth/resetpassword/:resettoken
// @access  Public
const resetPassword = asyncHandler(async (req, res) => {
  const resetPasswordToken = crypto.createHash('sha256').update(req.params.resettoken).digest('hex');

  // Check both collections for the user
  let user = await User.findOne({
    resetPasswordToken,
    resetPasswordExpire: { $gt: Date.now() },
  });

  if (!user) {
    user = await Admin.findOne({
      resetPasswordToken,
      resetPasswordExpire: { $gt: Date.now() },
    });
  }

  if (!user) {
    res.status(400).json({ success: false, message: 'Invalid or expired token' });
    return;
  }

  user.password = req.body.password;
  user.resetPasswordToken = undefined;
  user.resetPasswordExpire = undefined;
  await user.save();
  res.status(200).json({ success: true, message: 'Password has been reset successfully. You can now log in.' });
});

// @desc    Verify email
// @route   GET /api/auth/verify-email
// @access  Public
const verifyEmail = asyncHandler(async (req, res) => {
  const { token, email } = req.query;
  if (!token || !email) {
    res.status(400);
    throw new Error('Invalid verification link');
  }

  // Check both collections for the user
  let user = await User.findOne({ email, verificationToken: token, verificationTokenExpires: { $gt: Date.now() } });

  if (!user) {
    user = await Admin.findOne({ email, verificationToken: token, verificationTokenExpires: { $gt: Date.now() } });
  }

  if (!user) {
    res.status(400);
    throw new Error('Invalid or expired verification token');
  }

  user.isVerified = true;
  user.verificationToken = undefined;
  user.verificationTokenExpires = undefined;
  await user.save();
  res.status(200).json({ success: true, message: 'Email verified successfully. You can now log in.' });
});

// @desc    Resend verification email
// @route   POST /api/auth/resend-verification
// @access  Public
const resendVerificationEmail = asyncHandler(async (req, res) => {
  const { email } = req.body;
  if (!email) {
    res.status(400);
    throw new Error('Email is required');
  }

  // Check both collections for the user
  let user = await User.findOne({ email });

  if (!user) {
    user = await Admin.findOne({ email });
  }

  if (!user) {
    res.status(404);
    throw new Error('User not found');
  }

  if (user.isVerified) {
    res.status(400);
    throw new Error('Email is already verified');
  }

  const verificationToken = generateVerificationToken();
  user.verificationToken = verificationToken;
  user.verificationTokenExpires = Date.now() + 60 * 60 * 1000; // 1 hour
  await user.save();
  const verificationUrl = `${req.protocol}://${req.get('host')}/api/auth/verify-email?token=${verificationToken}&email=${encodeURIComponent(email)}`;
  const html = getVerificationEmailTemplate(user.name, verificationUrl);
  await sendEmail({
    to: email,
    subject: 'Verify your email address',
    html,
  });
  res.status(200).json({ success: true, message: 'Verification email resent. Please check your inbox.' });
});

// Generate token and send response
const generateTokenResponse = (user, statusCode, res) => {
  // Create token
  const token = user.getSignedJwtToken();
  
  // Calculate expiration date
  const expiresIn = JWT_COOKIE_EXPIRE * 24 * 60 * 60 * 1000; // Convert days to milliseconds
  const expires = new Date(Date.now() + expiresIn);
  
  // Cookie options
  const isProduction = process.env.NODE_ENV === 'production';
  const domain = process.env.COOKIE_DOMAIN || undefined;
  
  const cookieOptions = {
    expires,
    httpOnly: true,
    secure: isProduction, // Only send over HTTPS in production
    sameSite: isProduction ? 'none' : 'lax', // Required for cross-site cookies in production
    domain, // Domain where the cookie is accessible
    path: '/', // Accessible on all paths
  };
  
  // Prepare user data for response
  const userData = {
    _id: user._id,
    name: user.name,
    email: user.email,
    role: user.role,
  };
  
  // Set the cookie and send response
  res
    .status(statusCode)
    .cookie('token', token, cookieOptions)
    .json({
      success: true,
      token: isProduction ? undefined : token, // Only include token in response in development
      data: userData,
    });};

// @desc    Debug user status
// @route   GET /api/auth/debug/:email
// @access  Public (for debugging only)
const debugUserStatus = asyncHandler(async (req, res) => {
  const { email } = req.params;

  // Check both collections
  const regularUser = await User.findOne({ email });
  const adminUser = await Admin.findOne({ email });

  res.json({
    email,
    regularUser: regularUser ? {
      id: regularUser._id,
      name: regularUser.name,
      role: regularUser.role,
      isVerified: regularUser.isVerified,
      verificationToken: regularUser.verificationToken ? 'exists' : 'none',
      verificationTokenExpires: regularUser.verificationTokenExpires,
      collection: 'users'
    } : null,
    adminUser: adminUser ? {
      id: adminUser._id,
      name: adminUser.name,
      role: adminUser.role,
      isVerified: adminUser.isVerified,
      verificationToken: adminUser.verificationToken ? 'exists' : 'none',
      verificationTokenExpires: adminUser.verificationTokenExpires,
      collection: 'admins'
    } : null
  });
});

// @desc    Manually verify user (for testing only)
// @route   POST /api/auth/manual-verify/:email
// @access  Public (for debugging only)
const manualVerifyUser = asyncHandler(async (req, res) => {
  const { email } = req.params;

  // Check both collections
  let user = await User.findOne({ email });

  if (!user) {
    user = await Admin.findOne({ email });
  }

  if (!user) {
    res.status(404);
    throw new Error('User not found');
  }

  user.isVerified = true;
  user.verificationToken = undefined;
  user.verificationTokenExpires = undefined;
  await user.save();

  res.json({
    success: true,
    message: 'User manually verified',
    user: {
      email: user.email,
      isVerified: user.isVerified,
      collection: user.role === 'user' ? 'users' : 'admins'
    }
  });
});

// @desc    Fix visitor records missing user info
// @route   POST /api/auth/fix-visitors
// @access  Public (for debugging only)
const fixVisitorRecords = asyncHandler(async (req, res) => {
  const Visitor = (await import('../visitor/visitorModel.js')).default;

  // Find all visitors that have userId but missing visitorName or visitorEmail
  const visitorsToFix = await Visitor.find({
    userId: { $exists: true, $ne: null },
    $or: [
      { visitorName: { $exists: false } },
      { visitorName: null },
      { visitorName: "" },
      { visitorEmail: { $exists: false } },
      { visitorEmail: null },
      { visitorEmail: "" }
    ]
  });const results = [];

  for (const visitor of visitorsToFix) {
    try {
      // Find the user in both collections
      let user = await User.findById(visitor.userId);
      if (!user) {
        user = await Admin.findById(visitor.userId);
      }

      if (user) {
        // Update visitor with user information
        visitor.visitorName = user.name;
        visitor.visitorEmail = user.email;

        // Copy business information if available
        if (user.businessDomain) visitor.businessDomain = user.businessDomain;
        if (user.clientSegment) visitor.clientSegment = user.clientSegment;

        await visitor.save();

        results.push({
          visitorId: visitor._id,
          userId: user._id,
          name: user.name,
          email: user.email,
          status: 'fixed'
        });
      } else {
        results.push({
          visitorId: visitor._id,
          userId: visitor.userId,
          status: 'user_not_found'
        });
      }
    } catch (error) {
      results.push({
        visitorId: visitor._id,
        userId: visitor.userId,
        status: 'error',
        error: error.message
      });
    }
  }

  res.json({
    success: true,
    message: `Fixed ${results.filter(r => r.status === 'fixed').length} visitor records`,
    results
  });
});

// @desc    Fix specific visitor record
// @route   POST /api/auth/fix-visitor/:userId
// @access  Public (for debugging only)
const fixSpecificVisitor = asyncHandler(async (req, res) => {
  const { userId } = req.params;
  const Visitor = (await import('../visitor/visitorModel.js')).default;

  // Find visitor by userId
  const visitor = await Visitor.findOne({ userId });

  if (!visitor) {
    return res.status(404).json({
      success: false,
      message: 'Visitor not found'
    });
  }

  // Find the user in both collections
  let user = await User.findById(userId);
  if (!user) {
    user = await Admin.findById(userId);
  }

  if (!user) {
    return res.status(404).json({
      success: false,
      message: 'User not found'
    });
  }

  // Update visitor with user information
  visitor.visitorName = user.name;
  visitor.visitorEmail = user.email;

  // Copy business information if available
  if (user.businessDomain) visitor.businessDomain = user.businessDomain;
  if (user.clientSegment) visitor.clientSegment = user.clientSegment;

  await visitor.save();

  res.json({
    success: true,
    message: 'Visitor record updated successfully',
    visitor: {
      id: visitor._id,
      userId: visitor.userId,
      visitorName: visitor.visitorName,
      visitorEmail: visitor.visitorEmail,
      businessDomain: visitor.businessDomain,
      clientSegment: visitor.clientSegment
    }
  });
});

export {
  registerUser,
  loginUser,
  getMe,
  updateProfile,
  forgotPassword,
  resetPassword,
  verifyEmail,
  resendVerificationEmail,
  debugUserStatus,
  manualVerifyUser,
  fixVisitorRecords,
  fixSpecificVisitor,
};
