import React from 'react';
import { motion } from 'framer-motion';
import { ArrowLeft, Plus, Minus, Trash2, ShoppingBag, CreditCard, LogIn } from 'lucide-react';
import { useCart } from '../../core/contexts/CartContext';
import { useAuth } from '../../core/providers/AuthContext';
import { useNavigate } from 'react-router-dom';
import { trackQuoteRequest, getSessionId } from '../../shared/services/quote-tracking.service';
import { useAppSelector } from '../../store/hooks';

const CartPage: React.FC = () => {
  const { items, totalItems, totalPrice, removeFromCart, updateQuantity, clearCart, isAuthenticated } = useCart();
  const { user } = useAuth();
  const reduxUser = useAppSelector((state) => state.auth.user);
  const navigate = useNavigate();
  const [isSubmittingQuote, setIsSubmittingQuote] = React.useState(false);

  // Test function - you can call this from browser console: window.testQuoteRequest()
  React.useEffect(() => {
    (window as any).testQuoteRequest = () => {
      console.log('🧪 Testing quote request...');
      console.log('Items:', items);
      console.log('User:', user || reduxUser);
      handleRequestQuote();
    };
  }, [items, user, reduxUser]);

  // Handle quote request
  const handleRequestQuote = async () => {
    console.log('🔥 Request Quote button clicked!');
    console.log('Cart items:', items);
    console.log('User:', user || reduxUser);

    setIsSubmittingQuote(true);

    try {
      if (items.length === 0) {
        alert('Your cart is empty. Please add some services first.');
        return;
      }

      console.log('📤 Starting to track quote requests...');

      // Track each item in the cart as a quote request
      for (const item of items) {
        console.log('📝 Tracking quote for:', item);

        await trackQuoteRequest({
          serviceName: item.serviceName,
          subServiceName: item.subServiceName,
          subServiceDescription: item.description,
          sessionId: getSessionId(),
          userEmail: (user || reduxUser)?.email,
          userName: (user || reduxUser)?.name
        });

        console.log('✅ Quote tracked for:', item.subServiceName);
      }

      console.log('🎉 All quote requests tracked successfully');

      // Show success message
      alert('Quote request submitted! We will contact you soon with a detailed proposal.');

    } catch (error) {
      console.error('❌ Error submitting quote request:', error);
      alert('There was an error submitting your quote request. Please try again.');
    } finally {
      setIsSubmittingQuote(false);
    }
  };

  // Redirect to login if not authenticated
  if (!isAuthenticated) {
    return (
      <div className="pt-20 min-h-screen bg-gradient-to-br from-brand-black via-brand-purple-900 to-brand-black flex items-center justify-center">
        <div className="text-center max-w-md mx-auto p-8">
          <LogIn className="w-16 h-16 text-brand-purple-400 mx-auto mb-6" />
          <h1 className="text-3xl font-bold text-white mb-4">Login Required</h1>
          <p className="text-brand-grey-300 mb-8">
            Please log in to view your cart and manage your selected services.
          </p>
          <div className="space-y-4">
            <button
              onClick={() => navigate('/login', { state: { from: '/cart' } })}
              className="w-full bg-brand-purple-600 hover:bg-brand-purple-700 text-white font-semibold py-3 px-6 rounded-lg transition-colors duration-300"
            >
              Login to Continue
            </button>
            <button
              onClick={() => navigate('/services')}
              className="w-full bg-transparent border border-brand-grey-500 text-brand-grey-300 hover:bg-brand-grey-500/10 hover:text-white font-medium py-3 px-6 rounded-lg transition-all duration-200"
            >
              Browse Services
            </button>
          </div>
        </div>
      </div>
    );
  }

  const handleQuantityChange = async (serviceId: string, subServiceId: string | undefined, newQuantity: number) => {
    await updateQuantity(serviceId, subServiceId, newQuantity);
  };

  const handleRemoveItem = async (serviceId: string, subServiceId?: string) => {
    console.log('🗑️ Removing item:', { serviceId, subServiceId });
    try {
      await removeFromCart(serviceId, subServiceId);
      console.log('✅ Item removed successfully');
    } catch (error) {
      console.error('❌ Error removing item:', error);
      alert('Error removing item from cart');
    }
  };

  const handleClearCart = async () => {
    if (window.confirm('Are you sure you want to clear your cart?')) {
      await clearCart();
    }
  };

  return (
    <div className="pt-20 min-h-screen bg-brand-black overflow-y-auto">
      <div className="container-custom py-4 sm:py-8 lg:py-12 pb-20 max-h-screen overflow-y-auto">
        {/* Header */}
        <div className="flex items-center gap-4 mb-8">
          <button
            onClick={() => navigate(-1)}
            className="p-2 hover:bg-brand-purple-500/20 rounded-lg transition-colors"
          >
            <ArrowLeft className="w-6 h-6 text-brand-grey-300" />
          </button>
          <div className="flex items-center gap-3">
            <span className="text-3xl">🛒</span>
            <h1 className="text-3xl font-bold text-white">My Cart</h1>
            {totalItems > 0 && (
              <span className="bg-brand-purple-500 text-white text-lg px-3 py-1 rounded-full">
                {totalItems}
              </span>
            )}
          </div>
        </div>

        {items.length === 0 ? (
          <motion.div 
            className="flex flex-col items-center justify-center h-64 text-center"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
          >
            <ShoppingBag className="w-24 h-24 text-brand-grey-500 mb-6" />
            <h2 className="text-2xl font-semibold text-brand-grey-300 mb-4">Your cart is empty</h2>
            <p className="text-brand-grey-500 mb-6">Discover our amazing services and add them to your cart!</p>
            <button
              onClick={() => navigate('/services')}
              className="bg-gradient-to-r from-brand-purple-500 to-brand-purple-600 hover:from-brand-purple-400 hover:to-brand-purple-500 text-white font-semibold py-3 px-6 rounded-lg transition-all duration-200 shadow-lg"
            >
              Browse Services
            </button>
          </motion.div>
        ) : (
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 lg:gap-8">
            {/* Cart Items */}
            <div className="lg:col-span-2 space-y-4 max-h-[80vh] overflow-y-auto scrollbar-thin scrollbar-thumb-brand-purple-500 scrollbar-track-brand-grey-800">
              <div className="flex justify-between items-center mb-6">
                <h2 className="text-xl font-semibold text-white">Cart Items</h2>
                <button
                  onClick={handleClearCart}
                  className="text-red-400 hover:text-red-300 text-sm font-medium transition-colors"
                >
                  Clear All
                </button>
              </div>

              {items.map((item, index) => (
                <motion.div
                  key={item.id}
                  className="bg-brand-purple-900/20 rounded-xl p-4 sm:p-6 border border-brand-purple-500/20"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.1 }}
                >
                  <div className="flex flex-col sm:flex-row sm:justify-between sm:items-start gap-4 mb-4">
                    <div className="flex-1">
                      <h3 className="font-semibold text-white text-base sm:text-lg mb-1">
                        {item.serviceName}
                      </h3>
                      {item.subServiceName && (
                        <p className="text-brand-purple-300 text-sm mb-2">
                          {item.subServiceName}
                        </p>
                      )}
                      {item.description && (
                        <p className="text-brand-grey-400 text-sm">
                          {item.description}
                        </p>
                      )}
                    </div>
                    <button
                      onClick={() => handleRemoveItem(item.serviceId, item.subServiceId)}
                      className="p-2 hover:bg-red-500/20 rounded-lg text-red-400 hover:text-red-300 transition-colors self-start sm:self-auto"
                      title="Remove item"
                    >
                      <Trash2 className="w-5 h-5" />
                    </button>
                  </div>

                  <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
                    <div className="flex items-center gap-3">
                      <button
                        onClick={() => handleQuantityChange(item.serviceId, item.subServiceId, item.quantity - 1)}
                        className="p-2 hover:bg-brand-purple-500/20 rounded-lg text-brand-grey-300 hover:text-white transition-colors"
                        disabled={item.quantity <= 1}
                      >
                        <Minus className="w-4 h-4" />
                      </button>
                      <span className="text-white font-medium text-lg min-w-[3rem] text-center">
                        {item.quantity}
                      </span>
                      <button
                        onClick={() => handleQuantityChange(item.serviceId, item.subServiceId, item.quantity + 1)}
                        className="p-2 hover:bg-brand-purple-500/20 rounded-lg text-brand-grey-300 hover:text-white transition-colors"
                      >
                        <Plus className="w-4 h-4" />
                      </button>
                    </div>
                    <div className="text-right">
                      <p className="text-brand-purple-300 font-semibold text-lg">
                        {item.price > 0 ? `$${(item.price * item.quantity).toFixed(2)}` : 'Contact for Quote'}
                      </p>
                      {item.quantity > 1 && item.price > 0 && (
                        <p className="text-brand-grey-500 text-sm">
                          ${item.price.toFixed(2)} each
                        </p>
                      )}
                    </div>
                  </div>
                </motion.div>
              ))}
            </div>

            {/* Order Summary */}
            <div className="lg:col-span-1">
              <motion.div
                className="bg-brand-purple-900/30 rounded-xl p-4 sm:p-6 border border-brand-purple-500/20 lg:sticky lg:top-24"
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: 0.3 }}
              >
                <h3 className="text-lg sm:text-xl font-semibold text-white mb-4 sm:mb-6">Order Summary</h3>
                
                <div className="space-y-3 mb-6">
                  <div className="flex justify-between text-brand-grey-300">
                    <span>Items ({totalItems})</span>
                    <span>{totalPrice > 0 ? `$${totalPrice.toFixed(2)}` : 'Contact for Quote'}</span>
                  </div>
                  <div className="flex justify-between text-brand-grey-300">
                    <span>Shipping</span>
                    <span className="text-green-400">Free</span>
                  </div>
                  <div className="border-t border-brand-purple-500/20 pt-3">
                    <div className="flex justify-between text-white font-semibold text-lg">
                      <span>Total</span>
                      <span className="text-brand-purple-300">{totalPrice > 0 ? `$${totalPrice.toFixed(2)}` : 'Contact for Quote'}</span>
                    </div>
                  </div>
                </div>

                <div className="space-y-3">
                  {/* Test Button */}
                  <button
                    onClick={() => alert('TEST BUTTON WORKS!')}
                    className="w-full bg-red-500 hover:bg-red-600 text-white font-semibold py-3 px-4 rounded-lg text-sm sm:text-base"
                  >
                    🧪 TEST BUTTON - CLICK ME
                  </button>

                  <button
                    onClick={() => {
                      alert('Quote successfully sent! We will contact you soon.');
                    }}
                    className="w-full bg-gradient-to-r from-brand-purple-500 to-brand-purple-600 hover:from-brand-purple-400 hover:to-brand-purple-500 text-white font-semibold py-3 sm:py-4 px-4 rounded-lg transition-all duration-200 shadow-lg flex items-center justify-center gap-2 cursor-pointer text-sm sm:text-base"
                    style={{ pointerEvents: 'auto' }}
                  >
                    <CreditCard className="w-4 h-4 sm:w-5 sm:h-5" />
                    {totalPrice > 0 ? 'Proceed to Checkout' : 'Request Quote'}
                  </button>
                  <button
                    onClick={() => navigate('/services')}
                    className="w-full bg-transparent border border-brand-grey-500 text-brand-grey-300 hover:bg-brand-grey-500/10 hover:text-white font-medium py-3 px-4 rounded-lg transition-all duration-200"
                  >
                    Continue Shopping
                  </button>
                </div>
              </motion.div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default CartPage;
