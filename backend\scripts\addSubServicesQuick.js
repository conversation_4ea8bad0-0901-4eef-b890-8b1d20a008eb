import mongoose from 'mongoose';
import dotenv from 'dotenv';
import Service from '../src/components/service/serviceModel.js';

// Load environment variables
dotenv.config();

const quickSubServices = [
  {
    title: 'Social Media Management',
    description: 'Complete social media strategy and management across all platforms. We create engaging content, manage your community, run targeted campaigns, and provide detailed analytics to grow your online presence and drive meaningful engagement with your audience.',
    image: 'https://images.unsplash.com/photo-1611224923853-80b023f02d71?w=500&h=300&fit=crop'
  },
  {
    title: 'Search Engine Optimization',
    description: 'Comprehensive SEO services to improve your website\'s visibility and ranking on search engines. We conduct thorough keyword research, optimize your content, improve technical SEO, and build high-quality backlinks to drive organic traffic and increase conversions.',
    image: 'https://images.unsplash.com/photo-1460925895917-afdab827c52f?w=500&h=300&fit=crop'
  },
  {
    title: 'Pay-Per-Click Advertising',
    description: 'Strategic PPC campaigns across Google Ads, Facebook, LinkedIn, and other platforms. We create compelling ad copy, optimize targeting, manage budgets effectively, and continuously monitor performance to maximize your return on investment and drive qualified leads.',
    image: 'https://images.unsplash.com/photo-1556742049-0cfed4f6a45d?w=500&h=300&fit=crop'
  }
];

const addQuickSubServices = async () => {
  try {
    // Connect to MongoDB
    await mongoose.connect(process.env.MONGODB_URI);
    console.log('Connected to MongoDB');

    // Get all services
    const services = await Service.find({});
    console.log(`Found ${services.length} services`);

    for (const service of services) {
      // Only add if service doesn't already have sub-services
      if (!service.subServices || service.subServices.length === 0) {
        service.subServices = quickSubServices;
        await service.save();
        console.log(`Added sub-services to: ${service.title}`);
      } else {
        console.log(`${service.title} already has sub-services`);
      }
    }

    console.log('Quick sub-services addition completed!');
    process.exit(0);
  } catch (error) {
    console.error('Error:', error);
    process.exit(1);
  }
};

addQuickSubServices();
