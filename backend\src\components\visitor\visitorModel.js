import mongoose from 'mongoose';

// Simple page visit schema
const PageVisitSchema = new mongoose.Schema({
  path: { type: String, required: true },
  pageName: { type: String },
  timestamp: { type: Date, default: Date.now },
  duration: { type: Number, default: 0 }, // in milliseconds
  sessionId: { type: String, required: true }
}, { _id: false });

// Simple click tracking schema
const ClickSchema = new mongoose.Schema({
  buttonLabel: { type: String },
  component: { type: String },
  path: { type: String },
  timestamp: { type: Date, default: Date.now },
  sessionId: { type: String, required: true }
}, { _id: false });

// Quote request tracking schema
const QuoteRequestSchema = new mongoose.Schema({
  serviceName: { type: String, required: true },
  subServiceName: { type: String, required: true },
  subServiceDescription: { type: String },
  timestamp: { type: Date, default: Date.now },
  sessionId: { type: String, required: true },
  path: { type: String },
  userEmail: { type: String },
  userName: { type: String }
}, { _id: false });

// Cart action tracking schema
const CartActionSchema = new mongoose.Schema({
  action: { type: String, enum: ['add', 'remove'], required: true },
  serviceName: { type: String, required: true },
  subServiceName: { type: String, required: true },
  subServiceDescription: { type: String },
  timestamp: { type: Date, default: Date.now },
  sessionId: { type: String, required: true },
  path: { type: String },
  userEmail: { type: String },
  userName: { type: String }
}, { _id: false });

// Clean Visitor schema - IP-based tracking only
const VisitorSchema = new mongoose.Schema({
  // Primary identifier - one visitor per IP
  ipAddress: { 
    type: String, 
    required: true, 
    unique: true,
    index: true 
  },
  
  // User association (when visitor creates account or logs in)
  userId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    default: null
  },

  // Visitor information (copied from user when linked)
  visitorName: {
    type: String,
    default: null
  },
  visitorEmail: {
    type: String,
    default: null
  },
  
  // Basic visit tracking
  firstVisit: { type: Date, default: Date.now },
  lastVisit: { type: Date, default: Date.now },
  totalVisits: { type: Number, default: 1 },
  totalPageViews: { type: Number, default: 0 },
  totalClicks: { type: Number, default: 0 },
  totalQuoteRequests: { type: Number, default: 0 },
  totalCartActions: { type: Number, default: 0 },
  totalTimeSpent: { type: Number, default: 0 }, // in milliseconds
  
  // Location data (from IP geolocation)
  location: {
    country: { type: String },
    region: { type: String },
    city: { type: String }
  },
  
  // Page visits, clicks, quotes, and cart actions
  pageVisits: [PageVisitSchema],
  clicks: [ClickSchema],
  quoteRequests: [QuoteRequestSchema],
  cartActions: [CartActionSchema],
  
  // Lead scoring
  leadScore: { 
    type: Number, 
    default: 0,
    min: 0,
    max: 100 
  },
  leadStatus: {
    type: String,
    enum: ['new', 'warm', 'hot', 'converted'],
    default: 'new'
  },

  // Visitor classification
  classification: {
    type: String,
    enum: ['new visitor', 'returning visitor', 'registered user', 'customer'],
    default: 'new visitor'
  },
  
  // Business information (from user registration)
  businessDomain: {
    type: String,
    default: null
  },
  clientSegment: {
    type: String,
    default: null
  },
  
  // Device and browser info
  lastUserAgent: { type: String },
  lastReferrer: { type: String },
  
  // System fields
  isActive: { type: Boolean, default: true }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Indexes for performance
VisitorSchema.index({ ipAddress: 1 });
VisitorSchema.index({ lastVisit: -1 });
VisitorSchema.index({ leadScore: -1 });
VisitorSchema.index({ userId: 1 }, { sparse: true });

// Virtual for visitor type classification
VisitorSchema.virtual('visitorType').get(function() {
  if (this.leadStatus === 'converted') return 'customer';
  if (this.leadScore >= 70) return 'hot_lead';
  if (this.leadScore >= 40) return 'warm_lead';
  if (this.totalVisits > 1) return 'returning_visitor';
  return 'new_visitor';
});

// Method to calculate lead score
VisitorSchema.methods.calculateLeadScore = function() {
  let score = 0;
  
  // Base scoring
  score += Math.min(this.totalPageViews * 2, 20); // Max 20 points for page views
  score += Math.min(this.totalClicks * 3, 15); // Max 15 points for clicks
  score += Math.min(this.totalQuoteRequests * 10, 30); // Max 30 points for quote requests (high value)
  score += Math.min(this.totalCartActions * 2, 20); // Max 20 points for cart actions
  score += Math.min(this.totalVisits * 5, 25); // Max 25 points for visits
  score += Math.min(this.totalTimeSpent / 60000 * 2, 20); // Max 20 points for time (2 points per minute)
  
  // User registration bonus
  if (this.userId) score += 20;
  
  // Business information bonus
  if (this.businessDomain) score += 10;
  if (this.clientSegment) score += 10;
  
  this.leadScore = Math.min(score, 100);
  
  // Update lead status based on score
  if (this.leadScore >= 70) this.leadStatus = 'hot';
  else if (this.leadScore >= 40) this.leadStatus = 'warm';
  else this.leadStatus = 'new';
  
  return this.leadScore;
};

// Method to add page visit
VisitorSchema.methods.addPageVisit = function(pageData) {
  this.pageVisits.push(pageData);
  this.totalPageViews += 1;
  this.lastVisit = new Date();
  
  // Update total time spent
  if (pageData.duration) {
    this.totalTimeSpent += pageData.duration;
  }
  
  this.calculateLeadScore();
};

// Method to add click
VisitorSchema.methods.addClick = function(clickData) {
  this.clicks.push(clickData);
  this.totalClicks += 1;
  this.lastVisit = new Date();
  this.calculateLeadScore();
};

// Method to add quote request
VisitorSchema.methods.addQuoteRequest = function(quoteData) {
  this.quoteRequests.push(quoteData);
  this.totalQuoteRequests += 1;
  this.lastVisit = new Date();
  this.calculateLeadScore();
};

// Method to add cart action
VisitorSchema.methods.addCartAction = function(cartActionData) {
  this.cartActions.push(cartActionData);
  this.totalCartActions += 1;
  this.lastVisit = new Date();
  this.calculateLeadScore();
};

// Method to increment visit count
VisitorSchema.methods.incrementVisit = function() {
  this.totalVisits += 1;
  this.lastVisit = new Date();
  this.calculateLeadScore();
};

// Method to link user account
VisitorSchema.methods.linkUser = function(user) {
  this.userId = user._id;

  // Copy user information
  this.visitorName = user.name;
  this.visitorEmail = user.email;

  // Copy business information from user
  if (user.businessDomain) this.businessDomain = user.businessDomain;
  if (user.clientSegment) this.clientSegment = user.clientSegment;

  this.calculateLeadScore();
};

const Visitor = mongoose.model('Visitor', VisitorSchema);

export default Visitor;
