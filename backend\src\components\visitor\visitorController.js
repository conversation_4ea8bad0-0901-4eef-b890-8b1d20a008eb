import Visitor from './visitorModel.js';
import User from '../user/userModel.js';

// Helper function to get IP address with development simulation
export const getClientIP = (req, sessionId = null) => {
  // Safely get IP address with fallbacks
  let ip = '127.0.0.1'; // Default fallback

  try {
    // Try multiple sources for IP address (prioritize real public IPs)
    ip = req.headers['x-forwarded-for'] ||
         req.headers['x-real-ip'] ||
         req.headers['x-client-ip'] ||
         req.headers['cf-connecting-ip'] || // Cloudflare
         req.headers['x-cluster-client-ip'] ||
         req.connection?.remoteAddress ||
         req.socket?.remoteAddress ||
         req.ip ||
         '127.0.0.1';

    // Handle comma-separated IPs (x-forwarded-for can contain multiple IPs)
    if (ip && ip.includes(',')) {
      // Take the first IP (original client IP)
      ip = ip.split(',')[0].trim();
    }

    // Clean IPv6 localhost
    if (ip === '::1') {
      ip = '127.0.0.1';
    }

    // Remove IPv6 prefix if present
    if (ip && ip.startsWith('::ffff:')) {
      ip = ip.substring(7);
    }



  } catch (error) {
    console.error('[ERROR] Failed to get client IP:', error.message);
    ip = 'unknown';
  }

  return ip;
};

// Helper function to apply Tunisia-specific location corrections
const applyTunisiaLocationCorrections = (location, ip) => {
  console.log(`[DEBUG] Applying Tunisia corrections for:`, location);

  // Known IP ranges and corrections for Tunisian locations
  const tunisianIPCorrections = {
    // Specific IP ranges for better accuracy
    '102.169.133': ['Mahdia Governorate', 'Mahdia'], // Your IP range
    '102.169.134': ['Mahdia Governorate', 'Mahdia'],
    '102.169.135': ['Mahdia Governorate', 'Mahdia'],
  };

  // Check if IP matches known ranges
  const ipPrefix = ip.substring(0, ip.lastIndexOf('.'));
  if (tunisianIPCorrections[ipPrefix]) {
    const [correctRegion, correctCity] = tunisianIPCorrections[ipPrefix];
    console.log(`[DEBUG] IP ${ip} matched known range ${ipPrefix} -> ${correctRegion}, ${correctCity}`);
    return {
      ...location,
      region: correctRegion,
      city: correctCity
    };
  }

  // Known city corrections
  const tunisianCityCorrections = {
    // Major cities with known patterns
    'Tunis': ['Tunis Governorate', 'Tunis'],
    'Sfax': ['Sfax Governorate', 'Sfax'],
    'Sousse': ['Sousse Governorate', 'Sousse'],
    'Kairouan': ['Kairouan Governorate', 'Kairouan'],
    'Bizerte': ['Bizerte Governorate', 'Bizerte'],
    'Gabès': ['Gabès Governorate', 'Gabès'],
    'Ariana': ['Ariana Governorate', 'Ariana'],
    'Gafsa': ['Gafsa Governorate', 'Gafsa'],
    'Monastir': ['Monastir Governorate', 'Monastir'],
    'Ben Arous': ['Ben Arous Governorate', 'Ben Arous'],
    'Kasserine': ['Kasserine Governorate', 'Kasserine'],
    'Medenine': ['Medenine Governorate', 'Medenine'],
    'Nabeul': ['Nabeul Governorate', 'Nabeul'],
    'Tataouine': ['Tataouine Governorate', 'Tataouine'],
    'Beja': ['Beja Governorate', 'Beja'],
    'Jendouba': ['Jendouba Governorate', 'Jendouba'],
    'Mahdia': ['Mahdia Governorate', 'Mahdia'],
    'Sidi Bouzid': ['Sidi Bouzid Governorate', 'Sidi Bouzid'],
    'Siliana': ['Siliana Governorate', 'Siliana'],
    'Kef': ['Kef Governorate', 'El Kef'],
    'Tozeur': ['Tozeur Governorate', 'Tozeur'],
    'Kebili': ['Kebili Governorate', 'Kebili'],
    'Manouba': ['Manouba Governorate', 'Manouba'],
    'Zaghouan': ['Zaghouan Governorate', 'Zaghouan']
  };

  // If the detected city is in our corrections map, use it
  if (location.city && tunisianCityCorrections[location.city]) {
    const [correctRegion, correctCity] = tunisianCityCorrections[location.city];
    console.log(`[DEBUG] Corrected ${location.city} to ${correctRegion}, ${correctCity}`);
    return {
      ...location,
      region: correctRegion,
      city: correctCity
    };
  }

  // If region contains a known governorate name, standardize it
  if (location.region) {
    for (const [city, [governorate, cityName]] of Object.entries(tunisianCityCorrections)) {
      if (location.region.toLowerCase().includes(city.toLowerCase()) ||
          location.region.toLowerCase().includes(governorate.toLowerCase())) {
        console.log(`[DEBUG] Matched region ${location.region} to ${governorate}, ${cityName}`);
        return {
          ...location,
          region: governorate,
          city: cityName
        };
      }
    }
  }

  // Default: keep original but ensure proper formatting
  return {
    ...location,
    region: location.region || 'Unknown Governorate',
    city: location.city || 'Unknown City'
  };
};

// Helper function to choose the best location result from multiple services
const chooseBestLocationResult = (results, ip) => {
  console.log(`[DEBUG] Choosing best result from ${results.length} services for IP: ${ip}`);

  // Score each result based on completeness and accuracy
  const scoredResults = results.map(result => {
    let score = 0;

    // Accuracy bonus (higher accuracy services get priority)
    if (result.accuracy === 'high') score += 30;
    else if (result.accuracy === 'medium') score += 20;
    else if (result.accuracy === 'low') score += 10;

    // Completeness bonus
    if (result.country && result.country !== 'Unknown') score += 10;
    if (result.region && result.region !== 'Unknown') score += 10;
    if (result.city && result.city !== 'Unknown') score += 10;
    if (result.lat && result.lon) score += 10;
    if (result.isp) score += 5;

    // Tunisia-specific bonuses
    if (result.country === 'Tunisia') {
      score += 15; // Prefer Tunisia results since we know the user is in Tunisia

      // Bonus for specific Tunisian cities
      const tunisianCities = ['Mahdia', 'Tunis', 'Sfax', 'Sousse', 'Monastir', 'Nabeul', 'Kairouan'];
      if (result.city && tunisianCities.some(city =>
        result.city.toLowerCase().includes(city.toLowerCase()) ||
        city.toLowerCase().includes(result.city.toLowerCase())
      )) {
        score += 20;
      }

      // Bonus for governorate regions
      if (result.region && result.region.includes('Governorate')) {
        score += 15;
      }
    }

    // Penalty for obviously wrong results
    if (result.city === 'El Kef' || result.region === 'Kef Governorate') {
      score -= 25; // This was wrong for the user
    }

    console.log(`[DEBUG] ${result.service}: ${result.city}, ${result.region} - Score: ${score}`);

    return { ...result, score };
  });

  // Sort by score (highest first)
  scoredResults.sort((a, b) => b.score - a.score);

  const bestResult = scoredResults[0];
  console.log(`[DEBUG] Best result: ${bestResult.service} - ${bestResult.city}, ${bestResult.region} (Score: ${bestResult.score})`);

  return bestResult;
};

// Helper function to get location data from IP
const getLocationFromIP = async (ip) => {
  try {
    console.log(`[DEBUG] Getting geolocation for IP: ${ip}`);

    // For localhost/private IPs, try to get the real public IP first
    if (ip === '127.0.0.1' || ip === '::1' || ip.startsWith('192.168.') || ip.startsWith('10.') || ip === 'localhost') {
      console.log(`[DEBUG] Private IP detected (${ip}), trying to get real public IP...`);

      try {
        // Try to get the real public IP
        const publicIpResponse = await fetch('https://api.ipify.org?format=json');
        const publicIpData = await publicIpResponse.json();
        const realIp = publicIpData.ip;

        if (realIp && realIp !== ip) {
          console.log(`[DEBUG] Found real public IP: ${realIp}, using it for geolocation`);
          ip = realIp; // Use the real public IP
        } else {
          console.log(`[DEBUG] Could not get real public IP, will try geolocation anyway`);
        }
      } catch (publicIpError) {
        console.warn(`[DEBUG] Failed to get public IP:`, publicIpError.message);
      }
    }

    // Try multiple geolocation services with better accuracy for Tunisia
    const services = [
      {
        name: 'ipgeolocation.io',
        url: `https://api.ipgeolocation.io/ipgeo?apiKey=********************************&ip=${ip}`,
        parser: (data) => data.country_name ? {
          country: data.country_name,
          region: data.state_prov,
          city: data.city,
          lat: parseFloat(data.latitude),
          lon: parseFloat(data.longitude),
          isp: data.isp,
          accuracy: 'high'
        } : null
      },
      {
        name: 'ip-api',
        url: `http://ip-api.com/json/${ip}?fields=status,message,country,regionName,city,lat,lon,isp,query`,
        parser: (data) => data.status === 'success' ? {
          country: data.country,
          region: data.regionName,
          city: data.city,
          lat: data.lat,
          lon: data.lon,
          isp: data.isp,
          accuracy: 'medium'
        } : null
      },
      {
        name: 'ipapi.co',
        url: `https://ipapi.co/${ip}/json/`,
        parser: (data) => data.country_name ? {
          country: data.country_name,
          region: data.region,
          city: data.city,
          lat: data.latitude,
          lon: data.longitude,
          isp: data.org,
          accuracy: 'medium'
        } : null
      },
      {
        name: 'ipinfo.io',
        url: `https://ipinfo.io/${ip}/json?token=4d8b4b4c7c4e4a`,
        parser: (data) => data.country ? {
          country: data.country === 'TN' ? 'Tunisia' : data.country,
          region: data.region,
          city: data.city,
          lat: data.loc ? parseFloat(data.loc.split(',')[0]) : null,
          lon: data.loc ? parseFloat(data.loc.split(',')[1]) : null,
          isp: data.org,
          accuracy: 'low'
        } : null
      }
    ];

    let bestResult = null;
    let allResults = [];

    for (const service of services) {
      try {
        console.log(`[DEBUG] Trying ${service.name} for IP: ${ip}`);
        const response = await fetch(service.url);
        const data = await response.json();
        const parsed = service.parser(data);

        console.log(`[DEBUG] ${service.name} response:`, parsed);

        if (parsed) {
          allResults.push({ service: service.name, ...parsed });
        }
      } catch (error) {
        console.warn(`[DEBUG] ${service.name} failed:`, error.message);
      }
    }

    console.log(`[DEBUG] All geolocation results for IP ${ip}:`, allResults);

    if (allResults.length > 0) {
      // Choose the best result based on accuracy and completeness
      bestResult = chooseBestLocationResult(allResults, ip);

      // For Tunisia, apply manual corrections for known inaccurate results
      if (bestResult && bestResult.country === 'Tunisia') {
        bestResult = applyTunisiaLocationCorrections(bestResult, ip);
      }

      return {
        country: bestResult?.country || 'Unknown',
        region: bestResult?.region || 'Unknown',
        city: bestResult?.city || 'Unknown'
      };
    } else {
      throw new Error('All geolocation services failed');
    }
  } catch (error) {
    console.warn(`[DEBUG] Geolocation failed for IP ${ip}:`, error.message);
    return {
      country: 'Unknown',
      region: 'Unknown',
      city: 'Unknown'
    };
  }
};

// Helper function to check if user is staff
const isStaffUser = (user) => {
  return user && ['admin', 'marketing_responsible'].includes(user.role);
};

// POST /api/visitor/track - Main visitor tracking endpoint
export const trackVisitor = async (req, res) => {
  try {
    const { 
      path, 
      duration, 
      timestamp, 
      referrer, 
      userAgent, 
      source, 
      sessionId, 
      type, 
      pageName, 
      buttonLabel,
      country,
      region 
    } = req.body;

    // Exclude admin routes from tracking
    const adminRoutes = ['/login', '/dashboard', '/admin'];
    const isAdminRoute = adminRoutes.some(route => path.startsWith(route));

    if (isAdminRoute) {
      console.log(`[DEBUG] Skipping tracking for admin route: ${path}`);
      return res.json({ success: true, message: 'Admin route excluded from tracking' });
    }

    console.log(`[DEBUG] Visitor tracking request:`, {
      type,
      path,
      sessionId: sessionId?.slice(0, 8) + '...',
      timestamp
    });

    // Validate required fields
    if (!sessionId) {
      return res.status(400).json({ success: false, error: 'sessionId required' });
    }

    // Get IP address
    const ip = getClientIP(req, sessionId);
    console.log(`[DEBUG] Tracking IP: ${ip}`);

    // Check if this IP belongs to a staff user (exclude from tracking)
    const existingVisitor = await Visitor.findOne({ ipAddress: ip }).populate('userId', 'role email');
    if (existingVisitor && existingVisitor.userId && isStaffUser(existingVisitor.userId)) {
      console.log(`[DEBUG] Skipping tracking for staff user: ${existingVisitor.userId.email} (${existingVisitor.userId.role})`);
      return res.status(200).json({ success: true, message: 'Staff user excluded from tracking' });
    }

    // Find or create visitor
    let visitor = existingVisitor;
    if (!visitor) {
      // Get location data
      const location = country && region ? { country, region } : await getLocationFromIP(ip);
      
      // Create new visitor
      visitor = new Visitor({
        ipAddress: ip,
        location,
        lastUserAgent: userAgent,
        lastReferrer: referrer
      });
      
      console.log(`[DEBUG] Created new visitor for IP: ${ip}`);
    }

    // Handle different tracking types
    if (type === 'visit') {
      // New visit - increment visit count
      visitor.incrementVisit();
      console.log(`[DEBUG] New visit recorded for IP: ${ip}, total visits: ${visitor.totalVisits}`);
    }

    if (type === 'visit' || type === 'pageview') {
      // Add page visit manually
      const pageData = {
        path,
        pageName,
        timestamp: new Date(timestamp),
        duration: duration || 0,
        sessionId
      };
      visitor.pageVisits.push(pageData);
      visitor.totalPageViews += 1;
      visitor.lastVisit = new Date();

      // Update total time spent
      if (pageData.duration) {
        visitor.totalTimeSpent += pageData.duration;
      }

      console.log(`[DEBUG] Page visit added: ${path}, total pages: ${visitor.totalPageViews}`);
    }

    if (type === 'click') {
      // Add click tracking manually
      const clickData = {
        buttonLabel,
        component: req.body.component,
        path,
        timestamp: new Date(timestamp),
        sessionId
      };
      visitor.clicks.push(clickData);
      visitor.totalClicks += 1;
      visitor.lastVisit = new Date();
      console.log(`[DEBUG] Click tracked: ${buttonLabel || 'unknown'}, total clicks: ${visitor.totalClicks}`);
    }

    // Update last visit time and user agent
    visitor.lastVisit = new Date(timestamp);
    if (userAgent) visitor.lastUserAgent = userAgent;
    if (referrer) visitor.lastReferrer = referrer;

    // Save visitor with detailed error handling
    try {
      console.log(`[DEBUG] About to save visitor:`, {
        ip: visitor.ipAddress,
        isNew: visitor.isNew,
        totalVisits: visitor.totalVisits,
        totalPageViews: visitor.totalPageViews
      });

      const savedVisitor = await visitor.save();
      console.log(`[DEBUG] Visitor saved successfully:`, {
        id: savedVisitor._id,
        ip: savedVisitor.ipAddress,
        totalVisits: savedVisitor.totalVisits,
        totalPageViews: savedVisitor.totalPageViews,
        leadScore: savedVisitor.leadScore,
        userId: savedVisitor.userId || 'Anonymous'
      });

      res.status(200).json({ success: true });
    } catch (saveError) {
      console.error('[ERROR] Failed to save visitor:', saveError);
      throw saveError;
    }

  } catch (error) {
    console.error('[ERROR] Visitor tracking failed:', error);
    res.status(500).json({ success: false, error: error.message });
  }
};

// GET /api/visitor/list - Get paginated visitor list
export const getVisitorList = async (req, res) => {
  try {
    const { 
      page = 1, 
      limit = 20, 
      sortBy = 'lastVisit', 
      sortOrder = 'desc',
      leadStatus,
      businessDomain,
      minLeadScore 
    } = req.query;

    console.log(`[DEBUG] Getting visitor list - page: ${page}, limit: ${limit}`);

    // Build query
    const query = {};
    if (leadStatus) query.leadStatus = leadStatus;
    if (businessDomain) query.businessDomain = businessDomain;
    if (minLeadScore) query.leadScore = { $gte: parseInt(minLeadScore) };

    // Build sort
    const sort = {};
    sort[sortBy] = sortOrder === 'desc' ? -1 : 1;

    // Get visitors with user information
    const visitors = await Visitor.find(query)
      .populate('userId', 'name email role businessDomain clientSegment')
      .sort(sort)
      .limit(limit * 1)
      .skip((page - 1) * limit)
      .exec();

    const total = await Visitor.countDocuments(query);

    console.log(`[DEBUG] Found ${visitors.length} visitors out of ${total} total`);

    // Debug: Log first visitor structure
    if (visitors.length > 0) {
      console.log('[DEBUG] First visitor structure:', JSON.stringify(visitors[0], null, 2));
    }

    res.json({
      visitors,
      totalPages: Math.ceil(total / limit),
      currentPage: page,
      total
    });

  } catch (error) {
    console.error('[ERROR] Get visitor list failed:', error);
    res.status(500).json({ success: false, error: error.message });
  }
};

// GET /api/visitor/analytics - Get visitor analytics
export const getVisitorAnalytics = async (req, res) => {
  try {
    const { period = '30d' } = req.query;
    
    // Calculate date range
    const now = new Date();
    let startDate;
    
    switch (period) {
      case '7d':
        startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
        break;
      case '30d':
        startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
        break;
      case '90d':
        startDate = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000);
        break;
      default:
        startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
    }

    // Build query for the period
    const query = {
      lastVisit: { $gte: startDate, $lte: now }
    };

    // Get analytics data
    const totalVisitors = await Visitor.countDocuments(query);
    
    const visitorsByStatus = await Visitor.aggregate([
      { $match: query },
      { $group: { _id: '$leadStatus', count: { $sum: 1 } } }
    ]);

    const visitorsByRegion = await Visitor.aggregate([
      { $match: query },
      { 
        $group: { 
          _id: { 
            country: '$location.country', 
            region: '$location.region' 
          }, 
          count: { $sum: 1 },
          avgLeadScore: { $avg: '$leadScore' }
        } 
      },
      { $sort: { count: -1 } }
    ]);

    const leadScoreDistribution = await Visitor.aggregate([
      { $match: query },
      {
        $bucket: {
          groupBy: '$leadScore',
          boundaries: [0, 25, 50, 75, 100],
          default: 'other',
          output: { count: { $sum: 1 } }
        }
      }
    ]);

    // Calculate business domain statistics
    const businessDomainStats = await Visitor.aggregate([
      { $match: query },
      {
        $lookup: {
          from: 'users',
          localField: 'userId',
          foreignField: '_id',
          as: 'user'
        }
      },
      {
        $addFields: {
          businessDomain: {
            $cond: {
              if: { $ne: ['$businessDomain', null] },
              then: '$businessDomain',
              else: { $arrayElemAt: ['$user.businessDomain', 0] }
            }
          }
        }
      },
      {
        $group: {
          _id: '$businessDomain',
          count: { $sum: 1 }
        }
      },
      { $sort: { count: -1 } },
      { $limit: 10 }
    ]);

    // Calculate client segment statistics
    const clientSegmentStats = await Visitor.aggregate([
      { $match: query },
      {
        $lookup: {
          from: 'users',
          localField: 'userId',
          foreignField: '_id',
          as: 'user'
        }
      },
      {
        $addFields: {
          clientSegment: {
            $cond: {
              if: { $ne: ['$clientSegment', null] },
              then: '$clientSegment',
              else: { $arrayElemAt: ['$user.clientSegment', 0] }
            }
          }
        }
      },
      {
        $group: {
          _id: '$clientSegment',
          count: { $sum: 1 }
        }
      },
      { $sort: { count: -1 } },
      { $limit: 10 }
    ]);

    res.json({
      totalVisitors,
      visitorsByStatus,
      visitorsByRegion,
      leadScoreDistribution,
      businessDomainStats,
      clientSegmentStats,
      period
    });

  } catch (error) {
    console.error('[ERROR] Get visitor analytics failed:', error);
    res.status(500).json({ success: false, error: error.message });
  }
};

// PUT /api/visitor/:id/update-lead-status - Update visitor lead status
export const updateLeadStatus = async (req, res) => {
  try {
    const { id } = req.params;
    const { leadStatus, notes } = req.body;

    const visitor = await Visitor.findById(id);
    if (!visitor) {
      return res.status(404).json({ success: false, error: 'Visitor not found' });
    }

    visitor.leadStatus = leadStatus;
    await visitor.save();

    console.log(`[DEBUG] Updated lead status for visitor ${id}: ${leadStatus}`);

    res.json({ success: true, visitor });

  } catch (error) {
    console.error('[ERROR] Update lead status failed:', error);
    res.status(500).json({ success: false, error: error.message });
  }
};

// Function to link user to visitor (called from auth/user controllers)
export const linkUserToVisitor = async (ip, user) => {
  try {
    // Check if user is staff - if so, delete any existing visitor record
    if (isStaffUser(user)) {
      const existingVisitor = await Visitor.findOne({ ipAddress: ip });
      if (existingVisitor) {
        await Visitor.deleteOne({ _id: existingVisitor._id });
      }
      return null;
    }

    // Find existing visitor for this IP
    const visitor = await Visitor.findOne({ ipAddress: ip });

    if (!visitor) {
      return null;
    }

    if (visitor.userId) {
      return visitor;
    }

    // Link user to visitor manually
    visitor.userId = user._id;

    // Update visitor display information from user account
    visitor.visitorName = user.name;
    visitor.visitorEmail = user.email;

    // Copy business information from user account
    if (user.businessDomain) {
      visitor.businessDomain = user.businessDomain;
    }
    if (user.clientSegment) {
      visitor.clientSegment = user.clientSegment;
    }

    // Copy additional user information for marketing dashboard
    if (user.companyName) {
      visitor.companyName = user.companyName;
    }
    if (user.companySize) {
      visitor.companySize = user.companySize;
    }

    // Update classification to registered user
    visitor.classification = 'registered user';

    // Update lead status if it's still 'new'
    if (visitor.leadStatus === 'new') {
      visitor.leadStatus = 'warm';
    }

    // Recalculate lead score with user bonus
    const oldScore = visitor.leadScore;
    let score = 0;
    score += Math.min(visitor.totalPageViews * 2, 20); // Max 20 points for page views
    score += Math.min(visitor.totalClicks * 3, 15); // Max 15 points for clicks
    score += Math.min(visitor.totalVisits * 5, 25); // Max 25 points for visits
    score += Math.min(visitor.totalTimeSpent / 60000 * 2, 20); // Max 20 points for time
    score += 20; // User registration bonus
    visitor.leadScore = Math.min(score, 100); // Cap at 100

    await visitor.save();
    return visitor;
  } catch (error) {
    throw error;
  }
};

// GET /api/visitor/traffic - Get traffic statistics
export const getVisitorTraffic = async (req, res) => {
  try {
    const { period = '30d' } = req.query;

    // Calculate date range
    const now = new Date();
    let startDate;

    switch (period) {
      case '7d':
        startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
        break;
      case '30d':
        startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
        break;
      case '90d':
        startDate = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000);
        break;
      default:
        startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
    }

    // Get traffic statistics
    const totalVisitors = await Visitor.countDocuments({
      lastVisit: { $gte: startDate, $lte: now }
    });

    const totalVisits = await Visitor.aggregate([
      { $match: { lastVisit: { $gte: startDate, $lte: now } } },
      { $group: { _id: null, totalVisits: { $sum: '$totalVisits' } } }
    ]);

    const totalPageViews = await Visitor.aggregate([
      { $match: { lastVisit: { $gte: startDate, $lte: now } } },
      { $unwind: '$pageVisits' },
      { $match: { 'pageVisits.timestamp': { $gte: startDate, $lte: now } } },
      { $count: 'totalPageViews' }
    ]);

    // Calculate average session duration (simplified)
    const avgSessionDuration = await Visitor.aggregate([
      { $match: { lastVisit: { $gte: startDate, $lte: now } } },
      { $unwind: '$pageVisits' },
      { $match: { 'pageVisits.timestamp': { $gte: startDate, $lte: now } } },
      { $group: { _id: null, avgDuration: { $avg: '$pageVisits.duration' } } }
    ]);

    res.json({
      totalVisits: totalVisits[0]?.totalVisits || 0,
      pageViews: totalPageViews[0]?.totalPageViews || 0,
      avgSessionDuration: avgSessionDuration[0]?.avgDuration || 0,
      uniqueVisitors: totalVisitors
    });

  } catch (error) {
    console.error('[ERROR] Get visitor traffic failed:', error);
    res.status(500).json({ success: false, error: error.message });
  }
};

// GET /api/visitor/country-stats - Get country statistics
export const getVisitorCountryStats = async (req, res) => {
  try {
    const { period = '30d' } = req.query;

    // Calculate date range
    const now = new Date();
    let startDate;

    switch (period) {
      case '7d':
        startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
        break;
      case '30d':
        startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
        break;
      case '90d':
        startDate = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000);
        break;
      default:
        startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
    }

    const countryStats = await Visitor.aggregate([
      { $match: { lastVisit: { $gte: startDate, $lte: now } } },
      {
        $group: {
          _id: {
            country: '$location.country',
            region: '$location.region'
          },
          count: { $sum: 1 },
          visits: { $sum: '$totalVisits' },
          avgLeadScore: { $avg: '$leadScore' }
        }
      },
      { $sort: { count: -1 } }
    ]);

    // Calculate total for percentages
    const total = countryStats.reduce((sum, stat) => sum + stat.count, 0);

    // Add percentage and format data
    const formattedStats = countryStats.map(stat => ({
      country: stat._id.country || 'Unknown',
      region: stat._id.region || 'Unknown',
      visits: stat.count,
      totalVisits: stat.visits,
      percentage: total > 0 ? ((stat.count / total) * 100).toFixed(1) : '0.0',
      avgLeadScore: stat.avgLeadScore ? stat.avgLeadScore.toFixed(1) : '0.0'
    }));

    res.json(formattedStats);

  } catch (error) {
    console.error('[ERROR] Get visitor country stats failed:', error);
    res.status(500).json({ success: false, error: error.message });
  }
};

// GET /api/visitor/debug - Debug endpoint to check visitor tracking data
export const debugVisitorTracking = async (req, res) => {
  try {
    const totalVisitors = await Visitor.countDocuments();
    const visitorsWithUsers = await Visitor.countDocuments({ userId: { $exists: true, $ne: null } });
    const anonymousVisitors = await Visitor.countDocuments({ userId: { $exists: false } });

    // Get sample visitors
    const sampleVisitors = await Visitor.find()
      .populate('userId', 'name email role')
      .sort({ lastVisit: -1 })
      .limit(10);

    // Get IP distribution
    const ipDistribution = await Visitor.aggregate([
      { $group: { _id: '$ipAddress', count: { $sum: 1 } } },
      { $sort: { count: -1 } },
      { $limit: 10 }
    ]);

    console.log(`[DEBUG] Visitor tracking debug - Total: ${totalVisitors}, With Users: ${visitorsWithUsers}, Anonymous: ${anonymousVisitors}`);

    res.json({
      success: true,
      summary: {
        totalVisitors,
        visitorsWithUsers,
        anonymousVisitors,
        ipDistribution
      },
      sampleVisitors: sampleVisitors.map(v => ({
        id: v._id,
        ipAddress: v.ipAddress,
        totalVisits: v.totalVisits,
        leadScore: v.leadScore,
        lastVisit: v.lastVisit,
        user: v.userId ? {
          name: v.userId.name,
          email: v.userId.email,
          role: v.userId.role
        } : null,
        location: v.location,
        // Show raw data for debugging
        rawData: {
          hasOldStructure: !!(v.visitHistory || v.engagementMetrics),
          pageVisitsCount: v.pageVisits ? v.pageVisits.length : 0,
          clicksCount: v.clicks ? v.clicks.length : 0
        }
      }))
    });

  } catch (error) {
    console.error('[ERROR] Debug visitor tracking failed:', error);
    res.status(500).json({ success: false, error: error.message });
  }
};

// GET /api/visitor/cleanup - Clean up old visitor data (development only)
export const cleanupOldVisitorData = async (req, res) => {
  try {
    if (process.env.NODE_ENV !== 'development') {
      return res.status(403).json({ success: false, error: 'Cleanup only allowed in development' });
    }

    console.log('[DEBUG] Starting visitor data cleanup...');

    // Remove all old visitor records
    const deleteResult = await Visitor.deleteMany({});
    console.log(`[DEBUG] Deleted ${deleteResult.deletedCount} old visitor records`);

    res.json({
      success: true,
      message: `Cleaned up ${deleteResult.deletedCount} old visitor records`,
      deletedCount: deleteResult.deletedCount
    });

  } catch (error) {
    console.error('[ERROR] Cleanup visitor data failed:', error);
    res.status(500).json({ success: false, error: error.message });
  }
};

// GET /api/visitor/fix-locations - Fix hardcoded locations for existing visitors
export const fixVisitorLocations = async (req, res) => {
  try {
    console.log('[DEBUG] Starting location fix for existing visitors...');

    // Find visitors with hardcoded Sfax location
    const visitorsToFix = await Visitor.find({
      'location.region': 'Sfax Governorate'
    });

    console.log(`[DEBUG] Found ${visitorsToFix.length} visitors with hardcoded Sfax location`);

    let fixedCount = 0;
    let errorCount = 0;

    for (const visitor of visitorsToFix) {
      try {
        console.log(`[DEBUG] Fixing location for visitor ${visitor._id} with IP: ${visitor.ipAddress}`);

        // Get real location for this IP
        const newLocation = await getLocationFromIP(visitor.ipAddress);

        // Update visitor location
        visitor.location = newLocation;
        await visitor.save();

        fixedCount++;
        console.log(`[DEBUG] Fixed location for ${visitor.ipAddress}: ${newLocation.country}, ${newLocation.region}`);

        // Add small delay to avoid rate limiting
        await new Promise(resolve => setTimeout(resolve, 100));

      } catch (error) {
        console.error(`[ERROR] Failed to fix location for visitor ${visitor._id}:`, error.message);
        errorCount++;
      }
    }

    console.log(`[DEBUG] Location fix completed: ${fixedCount} fixed, ${errorCount} errors`);

    res.json({
      success: true,
      message: `Fixed locations for ${fixedCount} visitors`,
      fixedCount,
      errorCount,
      totalProcessed: visitorsToFix.length
    });

  } catch (error) {
    console.error('[ERROR] Location fix failed:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
};

// GET /api/visitor/geocode - Geocode region/country to coordinates
export const geocodeLocation = async (req, res) => {
  try {
    const { region, country } = req.query;

    if (!region || !country) {
      return res.status(400).json({
        success: false,
        error: 'Region and country parameters are required'
      });
    }

    console.log(`[DEBUG] Geocoding request: ${region}, ${country}`);

    // Try multiple geocoding services
    let coordinates = null;

    // Primary: Nominatim (OpenStreetMap)
    try {
      const query = encodeURIComponent(`${region}, ${country}`);
      const nominatimUrl = `https://nominatim.openstreetmap.org/search?format=json&q=${query}&limit=1`;

      const response = await fetch(nominatimUrl, {
        headers: {
          'User-Agent': 'ClickForYou-Analytics/1.0',
          'Accept-Language': 'en'
        }
      });

      if (response.ok) {
        const data = await response.json();
        if (data && data.length > 0) {
          coordinates = [parseFloat(data[0].lat), parseFloat(data[0].lon)];
          console.log(`[DEBUG] Nominatim geocoded ${region}, ${country} to:`, coordinates);
        }
      }
    } catch (nominatimError) {
      console.warn(`[DEBUG] Nominatim geocoding failed:`, nominatimError.message);
    }

    // Fallback: Use the same IP geolocation services to get coordinates
    if (!coordinates) {
      try {
        // Try to get coordinates from a different service
        const fallbackResponse = await fetch(`https://api.opencagedata.com/geocode/v1/json?q=${encodeURIComponent(`${region}, ${country}`)}&key=demo&limit=1`);

        if (fallbackResponse.ok) {
          const fallbackData = await fallbackResponse.json();
          if (fallbackData.results && fallbackData.results.length > 0) {
            const result = fallbackData.results[0];
            coordinates = [result.geometry.lat, result.geometry.lng];
            console.log(`[DEBUG] OpenCage geocoded ${region}, ${country} to:`, coordinates);
          }
        }
      } catch (fallbackError) {
        console.warn(`[DEBUG] Fallback geocoding failed:`, fallbackError.message);
      }
    }

    // Last resort: Use hardcoded coordinates for common locations
    if (!coordinates) {
      const commonLocations = {
        'Tunisia,Tunis Governorate': [36.8065, 10.1815],
        'Tunisia,Sfax Governorate': [34.7406, 10.7603],
        'United States,California': [36.7783, -119.4179],
        'France,Île-de-France': [48.8566, 2.3522],
        'Germany,Bavaria': [48.7904, 11.4979],
        'United Kingdom,England': [52.3555, -1.1743],
        'Canada,Ontario': [51.2538, -85.3232],
        'Australia,New South Wales': [-31.2532, 146.9211]
      };

      const locationKey = `${country},${region}`;
      coordinates = commonLocations[locationKey];

      if (coordinates) {
        console.log(`[DEBUG] Used hardcoded coordinates for ${region}, ${country}:`, coordinates);
      }
    }

    if (coordinates) {
      res.json({
        success: true,
        coordinates,
        region,
        country
      });
    } else {
      console.warn(`[DEBUG] No coordinates found for ${region}, ${country}`);
      res.status(404).json({
        success: false,
        error: `No coordinates found for ${region}, ${country}`
      });
    }

  } catch (error) {
    console.error('[ERROR] Geocoding failed:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
};

// POST /api/visitor/override-location - Manually override location for an IP
export const overrideLocation = async (req, res) => {
  try {
    const { ip, country, region, city } = req.body;

    if (!ip || !country || !region || !city) {
      return res.status(400).json({
        success: false,
        error: 'IP, country, region, and city are required'
      });
    }

    console.log(`[DEBUG] Overriding location for IP ${ip} to: ${city}, ${region}, ${country}`);

    // Find all visitors with this IP and update their location
    const visitors = await Visitor.find({ ipAddress: ip });

    let updatedCount = 0;
    for (const visitor of visitors) {
      visitor.location = { country, region, city };
      await visitor.save();
      updatedCount++;
    }

    console.log(`[DEBUG] Updated location for ${updatedCount} visitors with IP ${ip}`);

    res.json({
      success: true,
      message: `Updated location for ${updatedCount} visitors`,
      updatedCount,
      newLocation: { country, region, city }
    });

  } catch (error) {
    console.error('[ERROR] Location override failed:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
};

// GET /api/visitor/test-ip - Test IP detection and geolocation
export const testIPDetection = async (req, res) => {
  try {
    // Get the detected IP
    const detectedIP = getClientIP(req);

    // Get location for this IP
    const location = await getLocationFromIP(detectedIP);

    // Also try to get real public IP
    let realPublicIP = null;
    try {
      const publicIpResponse = await fetch('https://api.ipify.org?format=json');
      const publicIpData = await publicIpResponse.json();
      realPublicIP = publicIpData.ip;
    } catch (error) {
      // Ignore error
    }

    // Get location for real public IP if different
    let realLocation = null;
    if (realPublicIP && realPublicIP !== detectedIP) {
      realLocation = await getLocationFromIP(realPublicIP);
    }

    res.json({
      success: true,
      test: 'IP Detection and Geolocation',
      results: {
        detectedIP,
        detectedLocation: location,
        realPublicIP,
        realLocation,
        headers: {
          'x-forwarded-for': req.headers['x-forwarded-for'],
          'x-real-ip': req.headers['x-real-ip'],
          'x-client-ip': req.headers['x-client-ip'],
          'cf-connecting-ip': req.headers['cf-connecting-ip'],
          'user-agent': req.headers['user-agent']
        },
        connectionInfo: {
          remoteAddress: req.connection?.remoteAddress,
          socketRemoteAddress: req.socket?.remoteAddress,
          reqIP: req.ip
        }
      }
    });

  } catch (error) {
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
};

// GET /api/visitor/test-linking - Test user-visitor linking
export const testUserVisitorLinking = async (req, res) => {
  try {
    const detectedIP = getClientIP(req);

    // Find visitor for this IP
    const visitor = await Visitor.findOne({ ipAddress: detectedIP });

    // Find user if authenticated
    let user = null;
    if (req.user) {
      user = await User.findById(req.user.id);
    }

    res.json({
      success: true,
      test: 'User-Visitor Linking',
      results: {
        detectedIP,
        visitor: visitor ? {
          id: visitor._id,
          userId: visitor.userId,
          visitorName: visitor.visitorName,
          visitorEmail: visitor.visitorEmail,
          businessDomain: visitor.businessDomain,
          clientSegment: visitor.clientSegment,
          leadScore: visitor.leadScore,
          totalVisits: visitor.totalVisits,
          totalPageViews: visitor.totalPageViews
        } : null,
        user: user ? {
          id: user._id,
          name: user.name,
          email: user.email,
          role: user.role,
          businessDomain: user.businessDomain,
          clientSegment: user.clientSegment
        } : null,
        isLinked: visitor && user && visitor.userId?.toString() === user._id.toString()
      }
    });

  } catch (error) {
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
};

// POST /api/visitor/force-link - Force link current user to visitor (development only)
export const forceLinkUserToVisitor = async (req, res) => {
  try {
    if (!req.user) {
      return res.status(401).json({
        success: false,
        error: 'User not authenticated'
      });
    }

    const { businessDomain, clientSegment } = req.body;
    const detectedIP = getClientIP(req);

    // Find user
    const user = await User.findById(req.user.id);
    if (!user) {
      return res.status(404).json({
        success: false,
        error: 'User not found'
      });
    }

    // Update user with business information if provided
    if (businessDomain) {
      user.businessDomain = businessDomain;
    }
    if (clientSegment) {
      user.clientSegment = clientSegment;
    }
    await user.save();

    // Find visitor
    const visitor = await Visitor.findOne({ ipAddress: detectedIP });
    if (!visitor) {
      return res.status(404).json({
        success: false,
        error: 'No visitor found for your IP'
      });
    }

    // Force link
    const linkedVisitor = await linkUserToVisitor(detectedIP, user);

    res.json({
      success: true,
      message: 'User forcefully linked to visitor',
      results: {
        user: {
          id: user._id,
          name: user.name,
          email: user.email,
          businessDomain: user.businessDomain,
          clientSegment: user.clientSegment
        },
        visitor: {
          id: linkedVisitor._id,
          userId: linkedVisitor.userId,
          visitorName: linkedVisitor.visitorName,
          visitorEmail: linkedVisitor.visitorEmail,
          businessDomain: linkedVisitor.businessDomain,
          clientSegment: linkedVisitor.clientSegment,
          classification: linkedVisitor.classification,
          leadScore: linkedVisitor.leadScore
        }
      }
    });

  } catch (error) {
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
};

// POST /api/visitor/link-current-user - Link current logged-in user to visitor (simpler version)
export const linkCurrentUser = async (req, res) => {
  try {
    if (!req.user) {
      return res.status(401).json({
        success: false,
        error: 'User not authenticated',
        debug: {
          headers: req.headers.authorization ? 'Has auth header' : 'No auth header',
          cookies: req.cookies ? Object.keys(req.cookies) : 'No cookies'
        }
      });
    }

    const detectedIP = getClientIP(req);

    // Find user
    const user = await User.findById(req.user.id);
    if (!user) {
      return res.status(404).json({
        success: false,
        error: 'User not found'
      });
    }

    // Find visitor
    const visitor = await Visitor.findOne({ ipAddress: detectedIP });
    if (!visitor) {
      return res.status(404).json({
        success: false,
        error: 'No visitor found for your IP',
        debug: {
          detectedIP,
          userEmail: user.email
        }
      });
    }

    // Link user to visitor
    const linkedVisitor = await linkUserToVisitor(detectedIP, user);

    res.json({
      success: true,
      message: 'Current user linked to visitor successfully',
      results: {
        visitor: {
          id: linkedVisitor._id,
          userId: linkedVisitor.userId,
          visitorName: linkedVisitor.visitorName,
          visitorEmail: linkedVisitor.visitorEmail,
          businessDomain: linkedVisitor.businessDomain,
          clientSegment: linkedVisitor.clientSegment,
          classification: linkedVisitor.classification,
          leadScore: linkedVisitor.leadScore
        }
      }
    });

  } catch (error) {
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
};

// POST /api/visitor/fix-user-ip - Fix user registration IP to match visitor IP
export const fixUserRegistrationIP = async (req, res) => {
  try {
    // Find the user by email
    const user = await User.findOne({ email: '<EMAIL>' });
    if (!user) {
      return res.status(404).json({
        success: false,
        error: 'User not found'
      });
    }

    // Update user's registration IP to match visitor IP format
    user.registrationIP = '127.0.0.1';
    await user.save();

    // Now try to link the visitor
    const visitor = await Visitor.findOne({ ipAddress: '127.0.0.1' });
    if (visitor) {
      // Link user to visitor
      const linkedVisitor = await linkUserToVisitor('127.0.0.1', user);

      res.json({
        success: true,
        message: 'User IP fixed and visitor linked successfully',
        user: {
          id: user._id,
          email: user.email,
          name: user.name,
          registrationIP: user.registrationIP,
          businessDomain: user.businessDomain,
          clientSegment: user.clientSegment
        },
        visitor: {
          id: linkedVisitor._id,
          userId: linkedVisitor.userId,
          visitorName: linkedVisitor.visitorName,
          visitorEmail: linkedVisitor.visitorEmail,
          businessDomain: linkedVisitor.businessDomain,
          clientSegment: linkedVisitor.clientSegment,
          classification: linkedVisitor.classification,
          leadStatus: linkedVisitor.leadStatus,
          leadScore: linkedVisitor.leadScore
        }
      });
    } else {
      res.json({
        success: true,
        message: 'User IP fixed but no visitor found to link',
        user: {
          id: user._id,
          email: user.email,
          registrationIP: user.registrationIP
        }
      });
    }

  } catch (error) {
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
};

// POST /api/visitor/migrate-schema - Add missing fields to existing visitor records
export const migrateVisitorSchema = async (req, res) => {
  try {
    // Update all visitor records to include the new fields with default values
    const result = await Visitor.updateMany(
      {}, // Update all documents
      {
        $set: {
          visitorName: null,
          visitorEmail: null,
          businessDomain: null,
          clientSegment: null,
          classification: 'new visitor'
        }
      },
      {
        upsert: false, // Don't create new documents
        strict: false // Allow fields not in schema (in case of version differences)
      }
    );

    res.json({
      success: true,
      message: 'Visitor schema migration completed',
      modifiedCount: result.modifiedCount,
      matchedCount: result.matchedCount
    });

  } catch (error) {
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
};

// POST /api/visitor/fix-current-visitor - Fix current visitor record with user account info
export const fixCurrentVisitorRecord = async (req, res) => {
  try {
    const detectedIP = getClientIP(req);

    // Find visitor for current IP
    const visitor = await Visitor.findOne({ ipAddress: detectedIP });
    if (!visitor) {
      return res.status(404).json({
        success: false,
        error: 'No visitor found for your IP address'
      });
    }

    // Get user by email (since we know it from the logs)
    const userEmail = '<EMAIL>';
    const user = await User.findOne({ email: userEmail });
    if (!user) {
      return res.status(404).json({
        success: false,
        error: 'User not found'
      });
    }

    // Update visitor with user information
    visitor.userId = user._id;
    visitor.visitorName = user.name;
    visitor.visitorEmail = user.email;

    // Set business information (you can customize these)
    visitor.businessDomain = user.businessDomain || 'technology';
    visitor.clientSegment = user.clientSegment || 'startup';

    // Update classification and lead status
    visitor.classification = 'registered user';
    if (visitor.leadStatus === 'new') {
      visitor.leadStatus = 'warm';
    }

    // Recalculate lead score with user bonus
    let score = 0;
    score += Math.min(visitor.totalPageViews * 2, 20); // Max 20 points for page views
    score += Math.min(visitor.totalClicks * 3, 15); // Max 15 points for clicks
    score += Math.min(visitor.totalVisits * 5, 25); // Max 25 points for visits
    score += Math.min(visitor.totalTimeSpent / 60000 * 2, 20); // Max 20 points for time
    score += 20; // User registration bonus
    visitor.leadScore = Math.min(score, 100); // Cap at 100

    await visitor.save();

    res.json({
      success: true,
      message: 'Visitor record updated with user account information',
      visitor: {
        id: visitor._id,
        ipAddress: visitor.ipAddress,
        userId: visitor.userId,
        visitorName: visitor.visitorName,
        visitorEmail: visitor.visitorEmail,
        businessDomain: visitor.businessDomain,
        clientSegment: visitor.clientSegment,
        classification: visitor.classification,
        leadStatus: visitor.leadStatus,
        leadScore: visitor.leadScore,
        totalVisits: visitor.totalVisits,
        totalPageViews: visitor.totalPageViews
      }
    });

  } catch (error) {
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
};

// POST /api/visitor/test - Test visitor creation (development only)
export const testVisitorCreation = async (req, res) => {
  try {
    if (process.env.NODE_ENV !== 'development') {
      return res.status(403).json({ success: false, error: 'Test only allowed in development' });
    }

    console.log('[DEBUG] Testing visitor creation...');

    // Create a test visitor
    const testVisitor = new Visitor({
      ipAddress: '192.168.1.999',
      location: { country: 'TN', region: 'Test Region' },
      totalVisits: 1,
      totalPageViews: 1,
      totalClicks: 0,
      totalTimeSpent: 5000
    });

    // Test the methods
    testVisitor.addPageVisit({
      path: '/test',
      pageName: 'Test Page',
      timestamp: new Date(),
      duration: 5000,
      sessionId: 'test_session'
    });

    const savedVisitor = await testVisitor.save();
    console.log('[DEBUG] Test visitor saved:', savedVisitor._id);

    res.json({
      success: true,
      message: 'Test visitor created successfully',
      visitor: {
        id: savedVisitor._id,
        ipAddress: savedVisitor.ipAddress,
        totalVisits: savedVisitor.totalVisits,
        totalPageViews: savedVisitor.totalPageViews,
        leadScore: savedVisitor.leadScore
      }
    });

  } catch (error) {
    console.error('[ERROR] Test visitor creation failed:', error);
    res.status(500).json({ success: false, error: error.message });
  }
};

// POST /api/visitor/quote-request - Track quote requests
export const trackQuoteRequest = async (req, res) => {
  try {
    const {
      serviceName,
      subServiceName,
      subServiceDescription,
      sessionId,
      path,
      userEmail,
      userName
    } = req.body;

    console.log(`[DEBUG] Quote request tracking:`, {
      serviceName,
      subServiceName,
      sessionId: sessionId?.slice(0, 8) + '...',
      userEmail
    });

    // Validate required fields
    if (!sessionId || !serviceName || !subServiceName) {
      return res.status(400).json({
        success: false,
        error: 'sessionId, serviceName, and subServiceName are required'
      });
    }

    // Get IP address
    const ip = getClientIP(req, sessionId);
    console.log(`[DEBUG] Quote request from IP: ${ip}`);

    // Check if this IP belongs to a staff user (exclude from tracking)
    const existingVisitor = await Visitor.findOne({ ipAddress: ip }).populate('userId', 'role email');
    if (existingVisitor && existingVisitor.userId && isStaffUser(existingVisitor.userId)) {
      console.log(`[DEBUG] Skipping quote request tracking for staff user: ${existingVisitor.userId.email}`);
      return res.status(200).json({ success: true, message: 'Staff user excluded from tracking' });
    }

    // Find or create visitor
    let visitor = existingVisitor;
    if (!visitor) {
      // Get location data
      const location = await getLocationFromIP(ip);

      // Create new visitor
      visitor = new Visitor({
        ipAddress: ip,
        location
      });

      console.log(`[DEBUG] Created new visitor for quote request from IP: ${ip}`);
    }

    // Add quote request
    const quoteData = {
      serviceName,
      subServiceName,
      subServiceDescription,
      timestamp: new Date(),
      sessionId,
      path,
      userEmail,
      userName
    };

    visitor.addQuoteRequest(quoteData);

    // Save visitor
    await visitor.save();

    console.log(`[DEBUG] Quote request tracked successfully for ${serviceName} - ${subServiceName}`);

    res.status(200).json({
      success: true,
      message: 'Quote request tracked successfully',
      data: {
        totalQuoteRequests: visitor.totalQuoteRequests,
        leadScore: visitor.leadScore
      }
    });

  } catch (error) {
    console.error('Error tracking quote request:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to track quote request'
    });
  }
};

// POST /api/visitor/cart-action - Track cart actions (add/remove)
export const trackCartAction = async (req, res) => {
  try {
    const {
      action,
      serviceName,
      subServiceName,
      subServiceDescription,
      sessionId,
      path,
      userEmail,
      userName
    } = req.body;

    console.log(`[DEBUG] Cart action tracking:`, {
      action,
      serviceName,
      subServiceName,
      sessionId: sessionId?.slice(0, 8) + '...',
      userEmail
    });

    // Validate required fields
    if (!sessionId || !action || !serviceName || !subServiceName) {
      return res.status(400).json({
        success: false,
        error: 'sessionId, action, serviceName, and subServiceName are required'
      });
    }

    if (!['add', 'remove'].includes(action)) {
      return res.status(400).json({
        success: false,
        error: 'action must be either "add" or "remove"'
      });
    }

    // Get IP address
    const ip = getClientIP(req, sessionId);
    console.log(`[DEBUG] Cart action from IP: ${ip}`);

    // Check if this IP belongs to a staff user (exclude from tracking)
    const existingVisitor = await Visitor.findOne({ ipAddress: ip }).populate('userId', 'role email');
    if (existingVisitor && existingVisitor.userId && isStaffUser(existingVisitor.userId)) {
      console.log(`[DEBUG] Skipping cart action tracking for staff user: ${existingVisitor.userId.email}`);
      return res.status(200).json({ success: true, message: 'Staff user excluded from tracking' });
    }

    // Find or create visitor
    let visitor = existingVisitor;
    if (!visitor) {
      // Get location data
      const location = await getLocationFromIP(ip);

      // Create new visitor
      visitor = new Visitor({
        ipAddress: ip,
        location
      });

      console.log(`[DEBUG] Created new visitor for cart action from IP: ${ip}`);
    }

    // Add cart action
    const cartActionData = {
      action,
      serviceName,
      subServiceName,
      subServiceDescription,
      timestamp: new Date(),
      sessionId,
      path,
      userEmail,
      userName
    };

    visitor.addCartAction(cartActionData);

    // Save visitor
    await visitor.save();

    console.log(`[DEBUG] Cart action tracked successfully: ${action} - ${serviceName} - ${subServiceName}`);

    res.status(200).json({
      success: true,
      message: 'Cart action tracked successfully',
      data: {
        totalCartActions: visitor.totalCartActions,
        leadScore: visitor.leadScore
      }
    });

  } catch (error) {
    console.error('Error tracking cart action:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to track cart action'
    });
  }
};
