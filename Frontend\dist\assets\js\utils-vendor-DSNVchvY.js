function Xe(e){var t,n,r="";if(typeof e=="string"||typeof e=="number")r+=e;else if(typeof e=="object")if(Array.isArray(e)){var s=e.length;for(t=0;t<s;t++)e[t]&&(n=Xe(e[t]))&&(r&&(r+=" "),r+=n)}else for(n in e)e[n]&&(r&&(r+=" "),r+=n);return r}function Rt(){for(var e,t,n=0,r="",s=arguments.length;n<s;n++)(e=arguments[n])&&(t=Xe(e))&&(r&&(r+=" "),r+=t);return r}const Ae=e=>typeof e=="boolean"?`${e}`:e===0?"0":e,Me=Rt,ms=(e,t)=>n=>{var r;if(t?.variants==null)return Me(e,n?.class,n?.className);const{variants:s,defaultVariants:o}=t,a=Object.keys(s).map(l=>{const u=n?.[l],f=o?.[l];if(u===null)return null;const g=Ae(u)||Ae(f);return s[l][g]}),c=n&&Object.entries(n).reduce((l,u)=>{let[f,g]=u;return g===void 0||(l[f]=g),l},{}),d=t==null||(r=t.compoundVariants)===null||r===void 0?void 0:r.reduce((l,u)=>{let{class:f,className:g,...O}=u;return Object.entries(O).every(h=>{let[p,m]=h;return Array.isArray(m)?m.includes({...o,...c}[p]):{...o,...c}[p]===m})?[...l,f,g]:l},[]);return Me(e,a,d,n?.class,n?.className)};function Qe(e,t){return function(){return e.apply(t,arguments)}}const{toString:Pt}=Object.prototype,{getPrototypeOf:xe}=Object,{iterator:ie,toStringTag:Ge}=Symbol,ce=(e=>t=>{const n=Pt.call(t);return e[n]||(e[n]=n.slice(8,-1).toLowerCase())})(Object.create(null)),M=e=>(e=e.toLowerCase(),t=>ce(t)===e),ue=e=>t=>typeof t===e,{isArray:Y}=Array,z=ue("undefined");function Dt(e){return e!==null&&!z(e)&&e.constructor!==null&&!z(e.constructor)&&k(e.constructor.isBuffer)&&e.constructor.isBuffer(e)}const Ke=M("ArrayBuffer");function kt(e){let t;return typeof ArrayBuffer<"u"&&ArrayBuffer.isView?t=ArrayBuffer.isView(e):t=e&&e.buffer&&Ke(e.buffer),t}const At=ue("string"),k=ue("function"),Ze=ue("number"),le=e=>e!==null&&typeof e=="object",Mt=e=>e===!0||e===!1,Z=e=>{if(ce(e)!=="object")return!1;const t=xe(e);return(t===null||t===Object.prototype||Object.getPrototypeOf(t)===null)&&!(Ge in e)&&!(ie in e)},Nt=M("Date"),Ct=M("File"),Ft=M("Blob"),_t=M("FileList"),vt=e=>le(e)&&k(e.pipe),Wt=e=>{let t;return e&&(typeof FormData=="function"&&e instanceof FormData||k(e.append)&&((t=ce(e))==="formdata"||t==="object"&&k(e.toString)&&e.toString()==="[object FormData]"))},Lt=M("URLSearchParams"),[Bt,qt,Ut,jt]=["ReadableStream","Request","Response","Headers"].map(M),Yt=e=>e.trim?e.trim():e.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,"");function X(e,t,{allOwnKeys:n=!1}={}){if(e===null||typeof e>"u")return;let r,s;if(typeof e!="object"&&(e=[e]),Y(e))for(r=0,s=e.length;r<s;r++)t.call(null,e[r],r,e);else{const o=n?Object.getOwnPropertyNames(e):Object.keys(e),a=o.length;let c;for(r=0;r<a;r++)c=o[r],t.call(null,e[c],c,e)}}function et(e,t){t=t.toLowerCase();const n=Object.keys(e);let r=n.length,s;for(;r-- >0;)if(s=n[r],t===s.toLowerCase())return s;return null}const L=typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:global,tt=e=>!z(e)&&e!==L;function we(){const{caseless:e}=tt(this)&&this||{},t={},n=(r,s)=>{const o=e&&et(t,s)||s;Z(t[o])&&Z(r)?t[o]=we(t[o],r):Z(r)?t[o]=we({},r):Y(r)?t[o]=r.slice():t[o]=r};for(let r=0,s=arguments.length;r<s;r++)arguments[r]&&X(arguments[r],n);return t}const Ht=(e,t,n,{allOwnKeys:r}={})=>(X(t,(s,o)=>{n&&k(s)?e[o]=Qe(s,n):e[o]=s},{allOwnKeys:r}),e),It=e=>(e.charCodeAt(0)===65279&&(e=e.slice(1)),e),Vt=(e,t,n,r)=>{e.prototype=Object.create(t.prototype,r),e.prototype.constructor=e,Object.defineProperty(e,"super",{value:t.prototype}),n&&Object.assign(e.prototype,n)},$t=(e,t,n,r)=>{let s,o,a;const c={};if(t=t||{},e==null)return t;do{for(s=Object.getOwnPropertyNames(e),o=s.length;o-- >0;)a=s[o],(!r||r(a,e,t))&&!c[a]&&(t[a]=e[a],c[a]=!0);e=n!==!1&&xe(e)}while(e&&(!n||n(e,t))&&e!==Object.prototype);return t},Jt=(e,t,n)=>{e=String(e),(n===void 0||n>e.length)&&(n=e.length),n-=t.length;const r=e.indexOf(t,n);return r!==-1&&r===n},zt=e=>{if(!e)return null;if(Y(e))return e;let t=e.length;if(!Ze(t))return null;const n=new Array(t);for(;t-- >0;)n[t]=e[t];return n},Xt=(e=>t=>e&&t instanceof e)(typeof Uint8Array<"u"&&xe(Uint8Array)),Qt=(e,t)=>{const r=(e&&e[ie]).call(e);let s;for(;(s=r.next())&&!s.done;){const o=s.value;t.call(e,o[0],o[1])}},Gt=(e,t)=>{let n;const r=[];for(;(n=e.exec(t))!==null;)r.push(n);return r},Kt=M("HTMLFormElement"),Zt=e=>e.toLowerCase().replace(/[-_\s]([a-z\d])(\w*)/g,function(n,r,s){return r.toUpperCase()+s}),Ne=(({hasOwnProperty:e})=>(t,n)=>e.call(t,n))(Object.prototype),en=M("RegExp"),nt=(e,t)=>{const n=Object.getOwnPropertyDescriptors(e),r={};X(n,(s,o)=>{let a;(a=t(s,o,e))!==!1&&(r[o]=a||s)}),Object.defineProperties(e,r)},tn=e=>{nt(e,(t,n)=>{if(k(e)&&["arguments","caller","callee"].indexOf(n)!==-1)return!1;const r=e[n];if(k(r)){if(t.enumerable=!1,"writable"in t){t.writable=!1;return}t.set||(t.set=()=>{throw Error("Can not rewrite read-only method '"+n+"'")})}})},nn=(e,t)=>{const n={},r=s=>{s.forEach(o=>{n[o]=!0})};return Y(e)?r(e):r(String(e).split(t)),n},rn=()=>{},sn=(e,t)=>e!=null&&Number.isFinite(e=+e)?e:t;function on(e){return!!(e&&k(e.append)&&e[Ge]==="FormData"&&e[ie])}const an=e=>{const t=new Array(10),n=(r,s)=>{if(le(r)){if(t.indexOf(r)>=0)return;if(!("toJSON"in r)){t[s]=r;const o=Y(r)?[]:{};return X(r,(a,c)=>{const d=n(a,s+1);!z(d)&&(o[c]=d)}),t[s]=void 0,o}}return r};return n(e,0)},cn=M("AsyncFunction"),un=e=>e&&(le(e)||k(e))&&k(e.then)&&k(e.catch),rt=((e,t)=>e?setImmediate:t?((n,r)=>(L.addEventListener("message",({source:s,data:o})=>{s===L&&o===n&&r.length&&r.shift()()},!1),s=>{r.push(s),L.postMessage(n,"*")}))(`axios@${Math.random()}`,[]):n=>setTimeout(n))(typeof setImmediate=="function",k(L.postMessage)),ln=typeof queueMicrotask<"u"?queueMicrotask.bind(L):typeof process<"u"&&process.nextTick||rt,fn=e=>e!=null&&k(e[ie]),i={isArray:Y,isArrayBuffer:Ke,isBuffer:Dt,isFormData:Wt,isArrayBufferView:kt,isString:At,isNumber:Ze,isBoolean:Mt,isObject:le,isPlainObject:Z,isReadableStream:Bt,isRequest:qt,isResponse:Ut,isHeaders:jt,isUndefined:z,isDate:Nt,isFile:Ct,isBlob:Ft,isRegExp:en,isFunction:k,isStream:vt,isURLSearchParams:Lt,isTypedArray:Xt,isFileList:_t,forEach:X,merge:we,extend:Ht,trim:Yt,stripBOM:It,inherits:Vt,toFlatObject:$t,kindOf:ce,kindOfTest:M,endsWith:Jt,toArray:zt,forEachEntry:Qt,matchAll:Gt,isHTMLForm:Kt,hasOwnProperty:Ne,hasOwnProp:Ne,reduceDescriptors:nt,freezeMethods:tn,toObjectSet:nn,toCamelCase:Zt,noop:rn,toFiniteNumber:sn,findKey:et,global:L,isContextDefined:tt,isSpecCompliantForm:on,toJSONObject:an,isAsyncFn:cn,isThenable:un,setImmediate:rt,asap:ln,isIterable:fn};function y(e,t,n,r,s){Error.call(this),Error.captureStackTrace?Error.captureStackTrace(this,this.constructor):this.stack=new Error().stack,this.message=e,this.name="AxiosError",t&&(this.code=t),n&&(this.config=n),r&&(this.request=r),s&&(this.response=s,this.status=s.status?s.status:null)}i.inherits(y,Error,{toJSON:function(){return{message:this.message,name:this.name,description:this.description,number:this.number,fileName:this.fileName,lineNumber:this.lineNumber,columnNumber:this.columnNumber,stack:this.stack,config:i.toJSONObject(this.config),code:this.code,status:this.status}}});const st=y.prototype,ot={};["ERR_BAD_OPTION_VALUE","ERR_BAD_OPTION","ECONNABORTED","ETIMEDOUT","ERR_NETWORK","ERR_FR_TOO_MANY_REDIRECTS","ERR_DEPRECATED","ERR_BAD_RESPONSE","ERR_BAD_REQUEST","ERR_CANCELED","ERR_NOT_SUPPORT","ERR_INVALID_URL"].forEach(e=>{ot[e]={value:e}});Object.defineProperties(y,ot);Object.defineProperty(st,"isAxiosError",{value:!0});y.from=(e,t,n,r,s,o)=>{const a=Object.create(st);return i.toFlatObject(e,a,function(d){return d!==Error.prototype},c=>c!=="isAxiosError"),y.call(a,e.message,t,n,r,s),a.cause=e,a.name=e.name,o&&Object.assign(a,o),a};const dn=null;function be(e){return i.isPlainObject(e)||i.isArray(e)}function at(e){return i.endsWith(e,"[]")?e.slice(0,-2):e}function Ce(e,t,n){return e?e.concat(t).map(function(s,o){return s=at(s),!n&&o?"["+s+"]":s}).join(n?".":""):t}function hn(e){return i.isArray(e)&&!e.some(be)}const mn=i.toFlatObject(i,{},null,function(t){return/^is[A-Z]/.test(t)});function fe(e,t,n){if(!i.isObject(e))throw new TypeError("target must be an object");t=t||new FormData,n=i.toFlatObject(n,{metaTokens:!0,dots:!1,indexes:!1},!1,function(p,m){return!i.isUndefined(m[p])});const r=n.metaTokens,s=n.visitor||u,o=n.dots,a=n.indexes,d=(n.Blob||typeof Blob<"u"&&Blob)&&i.isSpecCompliantForm(t);if(!i.isFunction(s))throw new TypeError("visitor must be a function");function l(h){if(h===null)return"";if(i.isDate(h))return h.toISOString();if(i.isBoolean(h))return h.toString();if(!d&&i.isBlob(h))throw new y("Blob is not supported. Use a Buffer instead.");return i.isArrayBuffer(h)||i.isTypedArray(h)?d&&typeof Blob=="function"?new Blob([h]):Buffer.from(h):h}function u(h,p,m){let S=h;if(h&&!m&&typeof h=="object"){if(i.endsWith(p,"{}"))p=r?p:p.slice(0,-2),h=JSON.stringify(h);else if(i.isArray(h)&&hn(h)||(i.isFileList(h)||i.endsWith(p,"[]"))&&(S=i.toArray(h)))return p=at(p),S.forEach(function(x,C){!(i.isUndefined(x)||x===null)&&t.append(a===!0?Ce([p],C,o):a===null?p:p+"[]",l(x))}),!1}return be(h)?!0:(t.append(Ce(m,p,o),l(h)),!1)}const f=[],g=Object.assign(mn,{defaultVisitor:u,convertValue:l,isVisitable:be});function O(h,p){if(!i.isUndefined(h)){if(f.indexOf(h)!==-1)throw Error("Circular reference detected in "+p.join("."));f.push(h),i.forEach(h,function(S,T){(!(i.isUndefined(S)||S===null)&&s.call(t,S,i.isString(T)?T.trim():T,p,g))===!0&&O(S,p?p.concat(T):[T])}),f.pop()}}if(!i.isObject(e))throw new TypeError("data must be an object");return O(e),t}function Fe(e){const t={"!":"%21","'":"%27","(":"%28",")":"%29","~":"%7E","%20":"+","%00":"\0"};return encodeURIComponent(e).replace(/[!'()~]|%20|%00/g,function(r){return t[r]})}function Re(e,t){this._pairs=[],e&&fe(e,this,t)}const it=Re.prototype;it.append=function(t,n){this._pairs.push([t,n])};it.toString=function(t){const n=t?function(r){return t.call(this,r,Fe)}:Fe;return this._pairs.map(function(s){return n(s[0])+"="+n(s[1])},"").join("&")};function pn(e){return encodeURIComponent(e).replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}function ct(e,t,n){if(!t)return e;const r=n&&n.encode||pn;i.isFunction(n)&&(n={serialize:n});const s=n&&n.serialize;let o;if(s?o=s(t,n):o=i.isURLSearchParams(t)?t.toString():new Re(t,n).toString(r),o){const a=e.indexOf("#");a!==-1&&(e=e.slice(0,a)),e+=(e.indexOf("?")===-1?"?":"&")+o}return e}class _e{constructor(){this.handlers=[]}use(t,n,r){return this.handlers.push({fulfilled:t,rejected:n,synchronous:r?r.synchronous:!1,runWhen:r?r.runWhen:null}),this.handlers.length-1}eject(t){this.handlers[t]&&(this.handlers[t]=null)}clear(){this.handlers&&(this.handlers=[])}forEach(t){i.forEach(this.handlers,function(r){r!==null&&t(r)})}}const ut={silentJSONParsing:!0,forcedJSONParsing:!0,clarifyTimeoutError:!1},yn=typeof URLSearchParams<"u"?URLSearchParams:Re,gn=typeof FormData<"u"?FormData:null,wn=typeof Blob<"u"?Blob:null,bn={isBrowser:!0,classes:{URLSearchParams:yn,FormData:gn,Blob:wn},protocols:["http","https","file","blob","url","data"]},Pe=typeof window<"u"&&typeof document<"u",Oe=typeof navigator=="object"&&navigator||void 0,On=Pe&&(!Oe||["ReactNative","NativeScript","NS"].indexOf(Oe.product)<0),Sn=typeof WorkerGlobalScope<"u"&&self instanceof WorkerGlobalScope&&typeof self.importScripts=="function",En=Pe&&window.location.href||"http://localhost",Tn=Object.freeze(Object.defineProperty({__proto__:null,hasBrowserEnv:Pe,hasStandardBrowserEnv:On,hasStandardBrowserWebWorkerEnv:Sn,navigator:Oe,origin:En},Symbol.toStringTag,{value:"Module"})),P={...Tn,...bn};function xn(e,t){return fe(e,new P.classes.URLSearchParams,Object.assign({visitor:function(n,r,s,o){return P.isNode&&i.isBuffer(n)?(this.append(r,n.toString("base64")),!1):o.defaultVisitor.apply(this,arguments)}},t))}function Rn(e){return i.matchAll(/\w+|\[(\w*)]/g,e).map(t=>t[0]==="[]"?"":t[1]||t[0])}function Pn(e){const t={},n=Object.keys(e);let r;const s=n.length;let o;for(r=0;r<s;r++)o=n[r],t[o]=e[o];return t}function lt(e){function t(n,r,s,o){let a=n[o++];if(a==="__proto__")return!0;const c=Number.isFinite(+a),d=o>=n.length;return a=!a&&i.isArray(s)?s.length:a,d?(i.hasOwnProp(s,a)?s[a]=[s[a],r]:s[a]=r,!c):((!s[a]||!i.isObject(s[a]))&&(s[a]=[]),t(n,r,s[a],o)&&i.isArray(s[a])&&(s[a]=Pn(s[a])),!c)}if(i.isFormData(e)&&i.isFunction(e.entries)){const n={};return i.forEachEntry(e,(r,s)=>{t(Rn(r),s,n,0)}),n}return null}function Dn(e,t,n){if(i.isString(e))try{return(t||JSON.parse)(e),i.trim(e)}catch(r){if(r.name!=="SyntaxError")throw r}return(n||JSON.stringify)(e)}const Q={transitional:ut,adapter:["xhr","http","fetch"],transformRequest:[function(t,n){const r=n.getContentType()||"",s=r.indexOf("application/json")>-1,o=i.isObject(t);if(o&&i.isHTMLForm(t)&&(t=new FormData(t)),i.isFormData(t))return s?JSON.stringify(lt(t)):t;if(i.isArrayBuffer(t)||i.isBuffer(t)||i.isStream(t)||i.isFile(t)||i.isBlob(t)||i.isReadableStream(t))return t;if(i.isArrayBufferView(t))return t.buffer;if(i.isURLSearchParams(t))return n.setContentType("application/x-www-form-urlencoded;charset=utf-8",!1),t.toString();let c;if(o){if(r.indexOf("application/x-www-form-urlencoded")>-1)return xn(t,this.formSerializer).toString();if((c=i.isFileList(t))||r.indexOf("multipart/form-data")>-1){const d=this.env&&this.env.FormData;return fe(c?{"files[]":t}:t,d&&new d,this.formSerializer)}}return o||s?(n.setContentType("application/json",!1),Dn(t)):t}],transformResponse:[function(t){const n=this.transitional||Q.transitional,r=n&&n.forcedJSONParsing,s=this.responseType==="json";if(i.isResponse(t)||i.isReadableStream(t))return t;if(t&&i.isString(t)&&(r&&!this.responseType||s)){const a=!(n&&n.silentJSONParsing)&&s;try{return JSON.parse(t)}catch(c){if(a)throw c.name==="SyntaxError"?y.from(c,y.ERR_BAD_RESPONSE,this,null,this.response):c}}return t}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,maxBodyLength:-1,env:{FormData:P.classes.FormData,Blob:P.classes.Blob},validateStatus:function(t){return t>=200&&t<300},headers:{common:{Accept:"application/json, text/plain, */*","Content-Type":void 0}}};i.forEach(["delete","get","head","post","put","patch"],e=>{Q.headers[e]={}});const kn=i.toObjectSet(["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"]),An=e=>{const t={};let n,r,s;return e&&e.split(`
`).forEach(function(a){s=a.indexOf(":"),n=a.substring(0,s).trim().toLowerCase(),r=a.substring(s+1).trim(),!(!n||t[n]&&kn[n])&&(n==="set-cookie"?t[n]?t[n].push(r):t[n]=[r]:t[n]=t[n]?t[n]+", "+r:r)}),t},ve=Symbol("internals");function V(e){return e&&String(e).trim().toLowerCase()}function ee(e){return e===!1||e==null?e:i.isArray(e)?e.map(ee):String(e)}function Mn(e){const t=Object.create(null),n=/([^\s,;=]+)\s*(?:=\s*([^,;]+))?/g;let r;for(;r=n.exec(e);)t[r[1]]=r[2];return t}const Nn=e=>/^[-_a-zA-Z0-9^`|~,!#$%&'*+.]+$/.test(e.trim());function me(e,t,n,r,s){if(i.isFunction(r))return r.call(this,t,n);if(s&&(t=n),!!i.isString(t)){if(i.isString(r))return t.indexOf(r)!==-1;if(i.isRegExp(r))return r.test(t)}}function Cn(e){return e.trim().toLowerCase().replace(/([a-z\d])(\w*)/g,(t,n,r)=>n.toUpperCase()+r)}function Fn(e,t){const n=i.toCamelCase(" "+t);["get","set","has"].forEach(r=>{Object.defineProperty(e,r+n,{value:function(s,o,a){return this[r].call(this,t,s,o,a)},configurable:!0})})}let A=class{constructor(t){t&&this.set(t)}set(t,n,r){const s=this;function o(c,d,l){const u=V(d);if(!u)throw new Error("header name must be a non-empty string");const f=i.findKey(s,u);(!f||s[f]===void 0||l===!0||l===void 0&&s[f]!==!1)&&(s[f||d]=ee(c))}const a=(c,d)=>i.forEach(c,(l,u)=>o(l,u,d));if(i.isPlainObject(t)||t instanceof this.constructor)a(t,n);else if(i.isString(t)&&(t=t.trim())&&!Nn(t))a(An(t),n);else if(i.isObject(t)&&i.isIterable(t)){let c={},d,l;for(const u of t){if(!i.isArray(u))throw TypeError("Object iterator must return a key-value pair");c[l=u[0]]=(d=c[l])?i.isArray(d)?[...d,u[1]]:[d,u[1]]:u[1]}a(c,n)}else t!=null&&o(n,t,r);return this}get(t,n){if(t=V(t),t){const r=i.findKey(this,t);if(r){const s=this[r];if(!n)return s;if(n===!0)return Mn(s);if(i.isFunction(n))return n.call(this,s,r);if(i.isRegExp(n))return n.exec(s);throw new TypeError("parser must be boolean|regexp|function")}}}has(t,n){if(t=V(t),t){const r=i.findKey(this,t);return!!(r&&this[r]!==void 0&&(!n||me(this,this[r],r,n)))}return!1}delete(t,n){const r=this;let s=!1;function o(a){if(a=V(a),a){const c=i.findKey(r,a);c&&(!n||me(r,r[c],c,n))&&(delete r[c],s=!0)}}return i.isArray(t)?t.forEach(o):o(t),s}clear(t){const n=Object.keys(this);let r=n.length,s=!1;for(;r--;){const o=n[r];(!t||me(this,this[o],o,t,!0))&&(delete this[o],s=!0)}return s}normalize(t){const n=this,r={};return i.forEach(this,(s,o)=>{const a=i.findKey(r,o);if(a){n[a]=ee(s),delete n[o];return}const c=t?Cn(o):String(o).trim();c!==o&&delete n[o],n[c]=ee(s),r[c]=!0}),this}concat(...t){return this.constructor.concat(this,...t)}toJSON(t){const n=Object.create(null);return i.forEach(this,(r,s)=>{r!=null&&r!==!1&&(n[s]=t&&i.isArray(r)?r.join(", "):r)}),n}[Symbol.iterator](){return Object.entries(this.toJSON())[Symbol.iterator]()}toString(){return Object.entries(this.toJSON()).map(([t,n])=>t+": "+n).join(`
`)}getSetCookie(){return this.get("set-cookie")||[]}get[Symbol.toStringTag](){return"AxiosHeaders"}static from(t){return t instanceof this?t:new this(t)}static concat(t,...n){const r=new this(t);return n.forEach(s=>r.set(s)),r}static accessor(t){const r=(this[ve]=this[ve]={accessors:{}}).accessors,s=this.prototype;function o(a){const c=V(a);r[c]||(Fn(s,a),r[c]=!0)}return i.isArray(t)?t.forEach(o):o(t),this}};A.accessor(["Content-Type","Content-Length","Accept","Accept-Encoding","User-Agent","Authorization"]);i.reduceDescriptors(A.prototype,({value:e},t)=>{let n=t[0].toUpperCase()+t.slice(1);return{get:()=>e,set(r){this[n]=r}}});i.freezeMethods(A);function pe(e,t){const n=this||Q,r=t||n,s=A.from(r.headers);let o=r.data;return i.forEach(e,function(c){o=c.call(n,o,s.normalize(),t?t.status:void 0)}),s.normalize(),o}function ft(e){return!!(e&&e.__CANCEL__)}function H(e,t,n){y.call(this,e??"canceled",y.ERR_CANCELED,t,n),this.name="CanceledError"}i.inherits(H,y,{__CANCEL__:!0});function dt(e,t,n){const r=n.config.validateStatus;!n.status||!r||r(n.status)?e(n):t(new y("Request failed with status code "+n.status,[y.ERR_BAD_REQUEST,y.ERR_BAD_RESPONSE][Math.floor(n.status/100)-4],n.config,n.request,n))}function _n(e){const t=/^([-+\w]{1,25})(:?\/\/|:)/.exec(e);return t&&t[1]||""}function vn(e,t){e=e||10;const n=new Array(e),r=new Array(e);let s=0,o=0,a;return t=t!==void 0?t:1e3,function(d){const l=Date.now(),u=r[o];a||(a=l),n[s]=d,r[s]=l;let f=o,g=0;for(;f!==s;)g+=n[f++],f=f%e;if(s=(s+1)%e,s===o&&(o=(o+1)%e),l-a<t)return;const O=u&&l-u;return O?Math.round(g*1e3/O):void 0}}function Wn(e,t){let n=0,r=1e3/t,s,o;const a=(l,u=Date.now())=>{n=u,s=null,o&&(clearTimeout(o),o=null),e.apply(null,l)};return[(...l)=>{const u=Date.now(),f=u-n;f>=r?a(l,u):(s=l,o||(o=setTimeout(()=>{o=null,a(s)},r-f)))},()=>s&&a(s)]}const ne=(e,t,n=3)=>{let r=0;const s=vn(50,250);return Wn(o=>{const a=o.loaded,c=o.lengthComputable?o.total:void 0,d=a-r,l=s(d),u=a<=c;r=a;const f={loaded:a,total:c,progress:c?a/c:void 0,bytes:d,rate:l||void 0,estimated:l&&c&&u?(c-a)/l:void 0,event:o,lengthComputable:c!=null,[t?"download":"upload"]:!0};e(f)},n)},We=(e,t)=>{const n=e!=null;return[r=>t[0]({lengthComputable:n,total:e,loaded:r}),t[1]]},Le=e=>(...t)=>i.asap(()=>e(...t)),Ln=P.hasStandardBrowserEnv?((e,t)=>n=>(n=new URL(n,P.origin),e.protocol===n.protocol&&e.host===n.host&&(t||e.port===n.port)))(new URL(P.origin),P.navigator&&/(msie|trident)/i.test(P.navigator.userAgent)):()=>!0,Bn=P.hasStandardBrowserEnv?{write(e,t,n,r,s,o){const a=[e+"="+encodeURIComponent(t)];i.isNumber(n)&&a.push("expires="+new Date(n).toGMTString()),i.isString(r)&&a.push("path="+r),i.isString(s)&&a.push("domain="+s),o===!0&&a.push("secure"),document.cookie=a.join("; ")},read(e){const t=document.cookie.match(new RegExp("(^|;\\s*)("+e+")=([^;]*)"));return t?decodeURIComponent(t[3]):null},remove(e){this.write(e,"",Date.now()-864e5)}}:{write(){},read(){return null},remove(){}};function qn(e){return/^([a-z][a-z\d+\-.]*:)?\/\//i.test(e)}function Un(e,t){return t?e.replace(/\/?\/$/,"")+"/"+t.replace(/^\/+/,""):e}function ht(e,t,n){let r=!qn(t);return e&&(r||n==!1)?Un(e,t):t}const Be=e=>e instanceof A?{...e}:e;function q(e,t){t=t||{};const n={};function r(l,u,f,g){return i.isPlainObject(l)&&i.isPlainObject(u)?i.merge.call({caseless:g},l,u):i.isPlainObject(u)?i.merge({},u):i.isArray(u)?u.slice():u}function s(l,u,f,g){if(i.isUndefined(u)){if(!i.isUndefined(l))return r(void 0,l,f,g)}else return r(l,u,f,g)}function o(l,u){if(!i.isUndefined(u))return r(void 0,u)}function a(l,u){if(i.isUndefined(u)){if(!i.isUndefined(l))return r(void 0,l)}else return r(void 0,u)}function c(l,u,f){if(f in t)return r(l,u);if(f in e)return r(void 0,l)}const d={url:o,method:o,data:o,baseURL:a,transformRequest:a,transformResponse:a,paramsSerializer:a,timeout:a,timeoutMessage:a,withCredentials:a,withXSRFToken:a,adapter:a,responseType:a,xsrfCookieName:a,xsrfHeaderName:a,onUploadProgress:a,onDownloadProgress:a,decompress:a,maxContentLength:a,maxBodyLength:a,beforeRedirect:a,transport:a,httpAgent:a,httpsAgent:a,cancelToken:a,socketPath:a,responseEncoding:a,validateStatus:c,headers:(l,u,f)=>s(Be(l),Be(u),f,!0)};return i.forEach(Object.keys(Object.assign({},e,t)),function(u){const f=d[u]||s,g=f(e[u],t[u],u);i.isUndefined(g)&&f!==c||(n[u]=g)}),n}const mt=e=>{const t=q({},e);let{data:n,withXSRFToken:r,xsrfHeaderName:s,xsrfCookieName:o,headers:a,auth:c}=t;t.headers=a=A.from(a),t.url=ct(ht(t.baseURL,t.url,t.allowAbsoluteUrls),e.params,e.paramsSerializer),c&&a.set("Authorization","Basic "+btoa((c.username||"")+":"+(c.password?unescape(encodeURIComponent(c.password)):"")));let d;if(i.isFormData(n)){if(P.hasStandardBrowserEnv||P.hasStandardBrowserWebWorkerEnv)a.setContentType(void 0);else if((d=a.getContentType())!==!1){const[l,...u]=d?d.split(";").map(f=>f.trim()).filter(Boolean):[];a.setContentType([l||"multipart/form-data",...u].join("; "))}}if(P.hasStandardBrowserEnv&&(r&&i.isFunction(r)&&(r=r(t)),r||r!==!1&&Ln(t.url))){const l=s&&o&&Bn.read(o);l&&a.set(s,l)}return t},jn=typeof XMLHttpRequest<"u",Yn=jn&&function(e){return new Promise(function(n,r){const s=mt(e);let o=s.data;const a=A.from(s.headers).normalize();let{responseType:c,onUploadProgress:d,onDownloadProgress:l}=s,u,f,g,O,h;function p(){O&&O(),h&&h(),s.cancelToken&&s.cancelToken.unsubscribe(u),s.signal&&s.signal.removeEventListener("abort",u)}let m=new XMLHttpRequest;m.open(s.method.toUpperCase(),s.url,!0),m.timeout=s.timeout;function S(){if(!m)return;const x=A.from("getAllResponseHeaders"in m&&m.getAllResponseHeaders()),D={data:!c||c==="text"||c==="json"?m.responseText:m.response,status:m.status,statusText:m.statusText,headers:x,config:e,request:m};dt(function(v){n(v),p()},function(v){r(v),p()},D),m=null}"onloadend"in m?m.onloadend=S:m.onreadystatechange=function(){!m||m.readyState!==4||m.status===0&&!(m.responseURL&&m.responseURL.indexOf("file:")===0)||setTimeout(S)},m.onabort=function(){m&&(r(new y("Request aborted",y.ECONNABORTED,e,m)),m=null)},m.onerror=function(){r(new y("Network Error",y.ERR_NETWORK,e,m)),m=null},m.ontimeout=function(){let C=s.timeout?"timeout of "+s.timeout+"ms exceeded":"timeout exceeded";const D=s.transitional||ut;s.timeoutErrorMessage&&(C=s.timeoutErrorMessage),r(new y(C,D.clarifyTimeoutError?y.ETIMEDOUT:y.ECONNABORTED,e,m)),m=null},o===void 0&&a.setContentType(null),"setRequestHeader"in m&&i.forEach(a.toJSON(),function(C,D){m.setRequestHeader(D,C)}),i.isUndefined(s.withCredentials)||(m.withCredentials=!!s.withCredentials),c&&c!=="json"&&(m.responseType=s.responseType),l&&([g,h]=ne(l,!0),m.addEventListener("progress",g)),d&&m.upload&&([f,O]=ne(d),m.upload.addEventListener("progress",f),m.upload.addEventListener("loadend",O)),(s.cancelToken||s.signal)&&(u=x=>{m&&(r(!x||x.type?new H(null,e,m):x),m.abort(),m=null)},s.cancelToken&&s.cancelToken.subscribe(u),s.signal&&(s.signal.aborted?u():s.signal.addEventListener("abort",u)));const T=_n(s.url);if(T&&P.protocols.indexOf(T)===-1){r(new y("Unsupported protocol "+T+":",y.ERR_BAD_REQUEST,e));return}m.send(o||null)})},Hn=(e,t)=>{const{length:n}=e=e?e.filter(Boolean):[];if(t||n){let r=new AbortController,s;const o=function(l){if(!s){s=!0,c();const u=l instanceof Error?l:this.reason;r.abort(u instanceof y?u:new H(u instanceof Error?u.message:u))}};let a=t&&setTimeout(()=>{a=null,o(new y(`timeout ${t} of ms exceeded`,y.ETIMEDOUT))},t);const c=()=>{e&&(a&&clearTimeout(a),a=null,e.forEach(l=>{l.unsubscribe?l.unsubscribe(o):l.removeEventListener("abort",o)}),e=null)};e.forEach(l=>l.addEventListener("abort",o));const{signal:d}=r;return d.unsubscribe=()=>i.asap(c),d}},In=function*(e,t){let n=e.byteLength;if(n<t){yield e;return}let r=0,s;for(;r<n;)s=r+t,yield e.slice(r,s),r=s},Vn=async function*(e,t){for await(const n of $n(e))yield*In(n,t)},$n=async function*(e){if(e[Symbol.asyncIterator]){yield*e;return}const t=e.getReader();try{for(;;){const{done:n,value:r}=await t.read();if(n)break;yield r}}finally{await t.cancel()}},qe=(e,t,n,r)=>{const s=Vn(e,t);let o=0,a,c=d=>{a||(a=!0,r&&r(d))};return new ReadableStream({async pull(d){try{const{done:l,value:u}=await s.next();if(l){c(),d.close();return}let f=u.byteLength;if(n){let g=o+=f;n(g)}d.enqueue(new Uint8Array(u))}catch(l){throw c(l),l}},cancel(d){return c(d),s.return()}},{highWaterMark:2})},de=typeof fetch=="function"&&typeof Request=="function"&&typeof Response=="function",pt=de&&typeof ReadableStream=="function",Jn=de&&(typeof TextEncoder=="function"?(e=>t=>e.encode(t))(new TextEncoder):async e=>new Uint8Array(await new Response(e).arrayBuffer())),yt=(e,...t)=>{try{return!!e(...t)}catch{return!1}},zn=pt&&yt(()=>{let e=!1;const t=new Request(P.origin,{body:new ReadableStream,method:"POST",get duplex(){return e=!0,"half"}}).headers.has("Content-Type");return e&&!t}),Ue=64*1024,Se=pt&&yt(()=>i.isReadableStream(new Response("").body)),re={stream:Se&&(e=>e.body)};de&&(e=>{["text","arrayBuffer","blob","formData","stream"].forEach(t=>{!re[t]&&(re[t]=i.isFunction(e[t])?n=>n[t]():(n,r)=>{throw new y(`Response type '${t}' is not supported`,y.ERR_NOT_SUPPORT,r)})})})(new Response);const Xn=async e=>{if(e==null)return 0;if(i.isBlob(e))return e.size;if(i.isSpecCompliantForm(e))return(await new Request(P.origin,{method:"POST",body:e}).arrayBuffer()).byteLength;if(i.isArrayBufferView(e)||i.isArrayBuffer(e))return e.byteLength;if(i.isURLSearchParams(e)&&(e=e+""),i.isString(e))return(await Jn(e)).byteLength},Qn=async(e,t)=>{const n=i.toFiniteNumber(e.getContentLength());return n??Xn(t)},Gn=de&&(async e=>{let{url:t,method:n,data:r,signal:s,cancelToken:o,timeout:a,onDownloadProgress:c,onUploadProgress:d,responseType:l,headers:u,withCredentials:f="same-origin",fetchOptions:g}=mt(e);l=l?(l+"").toLowerCase():"text";let O=Hn([s,o&&o.toAbortSignal()],a),h;const p=O&&O.unsubscribe&&(()=>{O.unsubscribe()});let m;try{if(d&&zn&&n!=="get"&&n!=="head"&&(m=await Qn(u,r))!==0){let D=new Request(t,{method:"POST",body:r,duplex:"half"}),F;if(i.isFormData(r)&&(F=D.headers.get("content-type"))&&u.setContentType(F),D.body){const[v,K]=We(m,ne(Le(d)));r=qe(D.body,Ue,v,K)}}i.isString(f)||(f=f?"include":"omit");const S="credentials"in Request.prototype;h=new Request(t,{...g,signal:O,method:n.toUpperCase(),headers:u.normalize().toJSON(),body:r,duplex:"half",credentials:S?f:void 0});let T=await fetch(h,g);const x=Se&&(l==="stream"||l==="response");if(Se&&(c||x&&p)){const D={};["status","statusText","headers"].forEach(ke=>{D[ke]=T[ke]});const F=i.toFiniteNumber(T.headers.get("content-length")),[v,K]=c&&We(F,ne(Le(c),!0))||[];T=new Response(qe(T.body,Ue,v,()=>{K&&K(),p&&p()}),D)}l=l||"text";let C=await re[i.findKey(re,l)||"text"](T,e);return!x&&p&&p(),await new Promise((D,F)=>{dt(D,F,{data:C,headers:A.from(T.headers),status:T.status,statusText:T.statusText,config:e,request:h})})}catch(S){throw p&&p(),S&&S.name==="TypeError"&&/Load failed|fetch/i.test(S.message)?Object.assign(new y("Network Error",y.ERR_NETWORK,e,h),{cause:S.cause||S}):y.from(S,S&&S.code,e,h)}}),Ee={http:dn,xhr:Yn,fetch:Gn};i.forEach(Ee,(e,t)=>{if(e){try{Object.defineProperty(e,"name",{value:t})}catch{}Object.defineProperty(e,"adapterName",{value:t})}});const je=e=>`- ${e}`,Kn=e=>i.isFunction(e)||e===null||e===!1,gt={getAdapter:e=>{e=i.isArray(e)?e:[e];const{length:t}=e;let n,r;const s={};for(let o=0;o<t;o++){n=e[o];let a;if(r=n,!Kn(n)&&(r=Ee[(a=String(n)).toLowerCase()],r===void 0))throw new y(`Unknown adapter '${a}'`);if(r)break;s[a||"#"+o]=r}if(!r){const o=Object.entries(s).map(([c,d])=>`adapter ${c} `+(d===!1?"is not supported by the environment":"is not available in the build"));let a=t?o.length>1?`since :
`+o.map(je).join(`
`):" "+je(o[0]):"as no adapter specified";throw new y("There is no suitable adapter to dispatch the request "+a,"ERR_NOT_SUPPORT")}return r},adapters:Ee};function ye(e){if(e.cancelToken&&e.cancelToken.throwIfRequested(),e.signal&&e.signal.aborted)throw new H(null,e)}function Ye(e){return ye(e),e.headers=A.from(e.headers),e.data=pe.call(e,e.transformRequest),["post","put","patch"].indexOf(e.method)!==-1&&e.headers.setContentType("application/x-www-form-urlencoded",!1),gt.getAdapter(e.adapter||Q.adapter)(e).then(function(r){return ye(e),r.data=pe.call(e,e.transformResponse,r),r.headers=A.from(r.headers),r},function(r){return ft(r)||(ye(e),r&&r.response&&(r.response.data=pe.call(e,e.transformResponse,r.response),r.response.headers=A.from(r.response.headers))),Promise.reject(r)})}const wt="1.10.0",he={};["object","boolean","number","function","string","symbol"].forEach((e,t)=>{he[e]=function(r){return typeof r===e||"a"+(t<1?"n ":" ")+e}});const He={};he.transitional=function(t,n,r){function s(o,a){return"[Axios v"+wt+"] Transitional option '"+o+"'"+a+(r?". "+r:"")}return(o,a,c)=>{if(t===!1)throw new y(s(a," has been removed"+(n?" in "+n:"")),y.ERR_DEPRECATED);return n&&!He[a]&&(He[a]=!0,console.warn(s(a," has been deprecated since v"+n+" and will be removed in the near future"))),t?t(o,a,c):!0}};he.spelling=function(t){return(n,r)=>(console.warn(`${r} is likely a misspelling of ${t}`),!0)};function Zn(e,t,n){if(typeof e!="object")throw new y("options must be an object",y.ERR_BAD_OPTION_VALUE);const r=Object.keys(e);let s=r.length;for(;s-- >0;){const o=r[s],a=t[o];if(a){const c=e[o],d=c===void 0||a(c,o,e);if(d!==!0)throw new y("option "+o+" must be "+d,y.ERR_BAD_OPTION_VALUE);continue}if(n!==!0)throw new y("Unknown option "+o,y.ERR_BAD_OPTION)}}const te={assertOptions:Zn,validators:he},N=te.validators;let B=class{constructor(t){this.defaults=t||{},this.interceptors={request:new _e,response:new _e}}async request(t,n){try{return await this._request(t,n)}catch(r){if(r instanceof Error){let s={};Error.captureStackTrace?Error.captureStackTrace(s):s=new Error;const o=s.stack?s.stack.replace(/^.+\n/,""):"";try{r.stack?o&&!String(r.stack).endsWith(o.replace(/^.+\n.+\n/,""))&&(r.stack+=`
`+o):r.stack=o}catch{}}throw r}}_request(t,n){typeof t=="string"?(n=n||{},n.url=t):n=t||{},n=q(this.defaults,n);const{transitional:r,paramsSerializer:s,headers:o}=n;r!==void 0&&te.assertOptions(r,{silentJSONParsing:N.transitional(N.boolean),forcedJSONParsing:N.transitional(N.boolean),clarifyTimeoutError:N.transitional(N.boolean)},!1),s!=null&&(i.isFunction(s)?n.paramsSerializer={serialize:s}:te.assertOptions(s,{encode:N.function,serialize:N.function},!0)),n.allowAbsoluteUrls!==void 0||(this.defaults.allowAbsoluteUrls!==void 0?n.allowAbsoluteUrls=this.defaults.allowAbsoluteUrls:n.allowAbsoluteUrls=!0),te.assertOptions(n,{baseUrl:N.spelling("baseURL"),withXsrfToken:N.spelling("withXSRFToken")},!0),n.method=(n.method||this.defaults.method||"get").toLowerCase();let a=o&&i.merge(o.common,o[n.method]);o&&i.forEach(["delete","get","head","post","put","patch","common"],h=>{delete o[h]}),n.headers=A.concat(a,o);const c=[];let d=!0;this.interceptors.request.forEach(function(p){typeof p.runWhen=="function"&&p.runWhen(n)===!1||(d=d&&p.synchronous,c.unshift(p.fulfilled,p.rejected))});const l=[];this.interceptors.response.forEach(function(p){l.push(p.fulfilled,p.rejected)});let u,f=0,g;if(!d){const h=[Ye.bind(this),void 0];for(h.unshift.apply(h,c),h.push.apply(h,l),g=h.length,u=Promise.resolve(n);f<g;)u=u.then(h[f++],h[f++]);return u}g=c.length;let O=n;for(f=0;f<g;){const h=c[f++],p=c[f++];try{O=h(O)}catch(m){p.call(this,m);break}}try{u=Ye.call(this,O)}catch(h){return Promise.reject(h)}for(f=0,g=l.length;f<g;)u=u.then(l[f++],l[f++]);return u}getUri(t){t=q(this.defaults,t);const n=ht(t.baseURL,t.url,t.allowAbsoluteUrls);return ct(n,t.params,t.paramsSerializer)}};i.forEach(["delete","get","head","options"],function(t){B.prototype[t]=function(n,r){return this.request(q(r||{},{method:t,url:n,data:(r||{}).data}))}});i.forEach(["post","put","patch"],function(t){function n(r){return function(o,a,c){return this.request(q(c||{},{method:t,headers:r?{"Content-Type":"multipart/form-data"}:{},url:o,data:a}))}}B.prototype[t]=n(),B.prototype[t+"Form"]=n(!0)});let er=class bt{constructor(t){if(typeof t!="function")throw new TypeError("executor must be a function.");let n;this.promise=new Promise(function(o){n=o});const r=this;this.promise.then(s=>{if(!r._listeners)return;let o=r._listeners.length;for(;o-- >0;)r._listeners[o](s);r._listeners=null}),this.promise.then=s=>{let o;const a=new Promise(c=>{r.subscribe(c),o=c}).then(s);return a.cancel=function(){r.unsubscribe(o)},a},t(function(o,a,c){r.reason||(r.reason=new H(o,a,c),n(r.reason))})}throwIfRequested(){if(this.reason)throw this.reason}subscribe(t){if(this.reason){t(this.reason);return}this._listeners?this._listeners.push(t):this._listeners=[t]}unsubscribe(t){if(!this._listeners)return;const n=this._listeners.indexOf(t);n!==-1&&this._listeners.splice(n,1)}toAbortSignal(){const t=new AbortController,n=r=>{t.abort(r)};return this.subscribe(n),t.signal.unsubscribe=()=>this.unsubscribe(n),t.signal}static source(){let t;return{token:new bt(function(s){t=s}),cancel:t}}};function tr(e){return function(n){return e.apply(null,n)}}function nr(e){return i.isObject(e)&&e.isAxiosError===!0}const Te={Continue:100,SwitchingProtocols:101,Processing:102,EarlyHints:103,Ok:200,Created:201,Accepted:202,NonAuthoritativeInformation:203,NoContent:204,ResetContent:205,PartialContent:206,MultiStatus:207,AlreadyReported:208,ImUsed:226,MultipleChoices:300,MovedPermanently:301,Found:302,SeeOther:303,NotModified:304,UseProxy:305,Unused:306,TemporaryRedirect:307,PermanentRedirect:308,BadRequest:400,Unauthorized:401,PaymentRequired:402,Forbidden:403,NotFound:404,MethodNotAllowed:405,NotAcceptable:406,ProxyAuthenticationRequired:407,RequestTimeout:408,Conflict:409,Gone:410,LengthRequired:411,PreconditionFailed:412,PayloadTooLarge:413,UriTooLong:414,UnsupportedMediaType:415,RangeNotSatisfiable:416,ExpectationFailed:417,ImATeapot:418,MisdirectedRequest:421,UnprocessableEntity:422,Locked:423,FailedDependency:424,TooEarly:425,UpgradeRequired:426,PreconditionRequired:428,TooManyRequests:429,RequestHeaderFieldsTooLarge:431,UnavailableForLegalReasons:451,InternalServerError:500,NotImplemented:501,BadGateway:502,ServiceUnavailable:503,GatewayTimeout:504,HttpVersionNotSupported:505,VariantAlsoNegotiates:506,InsufficientStorage:507,LoopDetected:508,NotExtended:510,NetworkAuthenticationRequired:511};Object.entries(Te).forEach(([e,t])=>{Te[t]=e});function Ot(e){const t=new B(e),n=Qe(B.prototype.request,t);return i.extend(n,B.prototype,t,{allOwnKeys:!0}),i.extend(n,t,null,{allOwnKeys:!0}),n.create=function(s){return Ot(q(e,s))},n}const E=Ot(Q);E.Axios=B;E.CanceledError=H;E.CancelToken=er;E.isCancel=ft;E.VERSION=wt;E.toFormData=fe;E.AxiosError=y;E.Cancel=E.CanceledError;E.all=function(t){return Promise.all(t)};E.spread=tr;E.isAxiosError=nr;E.mergeConfig=q;E.AxiosHeaders=A;E.formToJSON=e=>lt(i.isHTMLForm(e)?new FormData(e):e);E.getAdapter=gt.getAdapter;E.HttpStatusCode=Te;E.default=E;const{Axios:gs,AxiosError:ws,CanceledError:bs,isCancel:Os,CancelToken:Ss,VERSION:Es,all:Ts,Cancel:xs,isAxiosError:Rs,spread:Ps,toFormData:Ds,AxiosHeaders:ks,HttpStatusCode:As,formToJSON:Ms,getAdapter:Ns,mergeConfig:Cs}=E,De=6048e5,rr=864e5,Ie=Symbol.for("constructDateFrom");function R(e,t){return typeof e=="function"?e(t):e&&typeof e=="object"&&Ie in e?e[Ie](t):e instanceof Date?new e.constructor(t):new Date(t)}function b(e,t){return R(t||e,e)}function St(e,t,n){const r=b(e,n?.in);return isNaN(t)?R(n?.in||e,NaN):(t&&r.setDate(r.getDate()+t),r)}function sr(e,t,n){const r=b(e,n?.in);if(isNaN(t))return R(n?.in||e,NaN);if(!t)return r;const s=r.getDate(),o=R(n?.in||e,r.getTime());o.setMonth(r.getMonth()+t+1,0);const a=o.getDate();return s>=a?o:(r.setFullYear(o.getFullYear(),o.getMonth(),s),r)}let or={};function G(){return or}function U(e,t){const n=G(),r=t?.weekStartsOn??t?.locale?.options?.weekStartsOn??n.weekStartsOn??n.locale?.options?.weekStartsOn??0,s=b(e,t?.in),o=s.getDay(),a=(o<r?7:0)+o-r;return s.setDate(s.getDate()-a),s.setHours(0,0,0,0),s}function se(e,t){return U(e,{...t,weekStartsOn:1})}function Et(e,t){const n=b(e,t?.in),r=n.getFullYear(),s=R(n,0);s.setFullYear(r+1,0,4),s.setHours(0,0,0,0);const o=se(s),a=R(n,0);a.setFullYear(r,0,4),a.setHours(0,0,0,0);const c=se(a);return n.getTime()>=o.getTime()?r+1:n.getTime()>=c.getTime()?r:r-1}function oe(e){const t=b(e),n=new Date(Date.UTC(t.getFullYear(),t.getMonth(),t.getDate(),t.getHours(),t.getMinutes(),t.getSeconds(),t.getMilliseconds()));return n.setUTCFullYear(t.getFullYear()),+e-+n}function I(e,...t){const n=R.bind(null,e||t.find(r=>typeof r=="object"));return t.map(n)}function ae(e,t){const n=b(e,t?.in);return n.setHours(0,0,0,0),n}function ar(e,t,n){const[r,s]=I(n?.in,e,t),o=ae(r),a=ae(s),c=+o-oe(o),d=+a-oe(a);return Math.round((c-d)/rr)}function ir(e,t){const n=Et(e,t),r=R(e,0);return r.setFullYear(n,0,4),r.setHours(0,0,0,0),se(r)}function Fs(e,t,n){return St(e,t*7,n)}function _s(e,t,n){return sr(e,t*12,n)}function vs(e,t){let n,r=t?.in;return e.forEach(s=>{!r&&typeof s=="object"&&(r=R.bind(null,s));const o=b(s,r);(!n||n<o||isNaN(+o))&&(n=o)}),R(r,n||NaN)}function Ws(e,t){let n,r=t?.in;return e.forEach(s=>{!r&&typeof s=="object"&&(r=R.bind(null,s));const o=b(s,r);(!n||n>o||isNaN(+o))&&(n=o)}),R(r,n||NaN)}function Ls(e,t,n){const[r,s]=I(n?.in,e,t);return+ae(r)==+ae(s)}function cr(e){return e instanceof Date||typeof e=="object"&&Object.prototype.toString.call(e)==="[object Date]"}function ur(e){return!(!cr(e)&&typeof e!="number"||isNaN(+b(e)))}function Bs(e,t,n){const[r,s]=I(n?.in,e,t),o=r.getFullYear()-s.getFullYear(),a=r.getMonth()-s.getMonth();return o*12+a}function lr(e,t,n){const[r,s]=I(n?.in,e,t),o=U(r,n),a=U(s,n),c=+o-oe(o),d=+a-oe(a);return Math.round((c-d)/De)}function qs(e,t){const n=b(e,t?.in),r=n.getMonth();return n.setFullYear(n.getFullYear(),r+1,0),n.setHours(23,59,59,999),n}function fr(e,t){const n=b(e,t?.in);return n.setDate(1),n.setHours(0,0,0,0),n}function dr(e,t){const n=b(e,t?.in);return n.setFullYear(n.getFullYear(),0,1),n.setHours(0,0,0,0),n}function hr(e,t){const n=G(),r=t?.weekStartsOn??t?.locale?.options?.weekStartsOn??n.weekStartsOn??n.locale?.options?.weekStartsOn??0,s=b(e,t?.in),o=s.getDay(),a=(o<r?-7:0)+6-(o-r);return s.setDate(s.getDate()+a),s.setHours(23,59,59,999),s}function Us(e,t){return hr(e,{...t,weekStartsOn:1})}const mr={lessThanXSeconds:{one:"less than a second",other:"less than {{count}} seconds"},xSeconds:{one:"1 second",other:"{{count}} seconds"},halfAMinute:"half a minute",lessThanXMinutes:{one:"less than a minute",other:"less than {{count}} minutes"},xMinutes:{one:"1 minute",other:"{{count}} minutes"},aboutXHours:{one:"about 1 hour",other:"about {{count}} hours"},xHours:{one:"1 hour",other:"{{count}} hours"},xDays:{one:"1 day",other:"{{count}} days"},aboutXWeeks:{one:"about 1 week",other:"about {{count}} weeks"},xWeeks:{one:"1 week",other:"{{count}} weeks"},aboutXMonths:{one:"about 1 month",other:"about {{count}} months"},xMonths:{one:"1 month",other:"{{count}} months"},aboutXYears:{one:"about 1 year",other:"about {{count}} years"},xYears:{one:"1 year",other:"{{count}} years"},overXYears:{one:"over 1 year",other:"over {{count}} years"},almostXYears:{one:"almost 1 year",other:"almost {{count}} years"}},pr=(e,t,n)=>{let r;const s=mr[e];return typeof s=="string"?r=s:t===1?r=s.one:r=s.other.replace("{{count}}",t.toString()),n?.addSuffix?n.comparison&&n.comparison>0?"in "+r:r+" ago":r};function ge(e){return(t={})=>{const n=t.width?String(t.width):e.defaultWidth;return e.formats[n]||e.formats[e.defaultWidth]}}const yr={full:"EEEE, MMMM do, y",long:"MMMM do, y",medium:"MMM d, y",short:"MM/dd/yyyy"},gr={full:"h:mm:ss a zzzz",long:"h:mm:ss a z",medium:"h:mm:ss a",short:"h:mm a"},wr={full:"{{date}} 'at' {{time}}",long:"{{date}} 'at' {{time}}",medium:"{{date}}, {{time}}",short:"{{date}}, {{time}}"},br={date:ge({formats:yr,defaultWidth:"full"}),time:ge({formats:gr,defaultWidth:"full"}),dateTime:ge({formats:wr,defaultWidth:"full"})},Or={lastWeek:"'last' eeee 'at' p",yesterday:"'yesterday at' p",today:"'today at' p",tomorrow:"'tomorrow at' p",nextWeek:"eeee 'at' p",other:"P"},Sr=(e,t,n,r)=>Or[e];function $(e){return(t,n)=>{const r=n?.context?String(n.context):"standalone";let s;if(r==="formatting"&&e.formattingValues){const a=e.defaultFormattingWidth||e.defaultWidth,c=n?.width?String(n.width):a;s=e.formattingValues[c]||e.formattingValues[a]}else{const a=e.defaultWidth,c=n?.width?String(n.width):e.defaultWidth;s=e.values[c]||e.values[a]}const o=e.argumentCallback?e.argumentCallback(t):t;return s[o]}}const Er={narrow:["B","A"],abbreviated:["BC","AD"],wide:["Before Christ","Anno Domini"]},Tr={narrow:["1","2","3","4"],abbreviated:["Q1","Q2","Q3","Q4"],wide:["1st quarter","2nd quarter","3rd quarter","4th quarter"]},xr={narrow:["J","F","M","A","M","J","J","A","S","O","N","D"],abbreviated:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],wide:["January","February","March","April","May","June","July","August","September","October","November","December"]},Rr={narrow:["S","M","T","W","T","F","S"],short:["Su","Mo","Tu","We","Th","Fr","Sa"],abbreviated:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],wide:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"]},Pr={narrow:{am:"a",pm:"p",midnight:"mi",noon:"n",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},abbreviated:{am:"AM",pm:"PM",midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},wide:{am:"a.m.",pm:"p.m.",midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"}},Dr={narrow:{am:"a",pm:"p",midnight:"mi",noon:"n",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"},abbreviated:{am:"AM",pm:"PM",midnight:"midnight",noon:"noon",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"},wide:{am:"a.m.",pm:"p.m.",midnight:"midnight",noon:"noon",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"}},kr=(e,t)=>{const n=Number(e),r=n%100;if(r>20||r<10)switch(r%10){case 1:return n+"st";case 2:return n+"nd";case 3:return n+"rd"}return n+"th"},Ar={ordinalNumber:kr,era:$({values:Er,defaultWidth:"wide"}),quarter:$({values:Tr,defaultWidth:"wide",argumentCallback:e=>e-1}),month:$({values:xr,defaultWidth:"wide"}),day:$({values:Rr,defaultWidth:"wide"}),dayPeriod:$({values:Pr,defaultWidth:"wide",formattingValues:Dr,defaultFormattingWidth:"wide"})};function J(e){return(t,n={})=>{const r=n.width,s=r&&e.matchPatterns[r]||e.matchPatterns[e.defaultMatchWidth],o=t.match(s);if(!o)return null;const a=o[0],c=r&&e.parsePatterns[r]||e.parsePatterns[e.defaultParseWidth],d=Array.isArray(c)?Nr(c,f=>f.test(a)):Mr(c,f=>f.test(a));let l;l=e.valueCallback?e.valueCallback(d):d,l=n.valueCallback?n.valueCallback(l):l;const u=t.slice(a.length);return{value:l,rest:u}}}function Mr(e,t){for(const n in e)if(Object.prototype.hasOwnProperty.call(e,n)&&t(e[n]))return n}function Nr(e,t){for(let n=0;n<e.length;n++)if(t(e[n]))return n}function Cr(e){return(t,n={})=>{const r=t.match(e.matchPattern);if(!r)return null;const s=r[0],o=t.match(e.parsePattern);if(!o)return null;let a=e.valueCallback?e.valueCallback(o[0]):o[0];a=n.valueCallback?n.valueCallback(a):a;const c=t.slice(s.length);return{value:a,rest:c}}}const Fr=/^(\d+)(th|st|nd|rd)?/i,_r=/\d+/i,vr={narrow:/^(b|a)/i,abbreviated:/^(b\.?\s?c\.?|b\.?\s?c\.?\s?e\.?|a\.?\s?d\.?|c\.?\s?e\.?)/i,wide:/^(before christ|before common era|anno domini|common era)/i},Wr={any:[/^b/i,/^(a|c)/i]},Lr={narrow:/^[1234]/i,abbreviated:/^q[1234]/i,wide:/^[1234](th|st|nd|rd)? quarter/i},Br={any:[/1/i,/2/i,/3/i,/4/i]},qr={narrow:/^[jfmasond]/i,abbreviated:/^(jan|feb|mar|apr|may|jun|jul|aug|sep|oct|nov|dec)/i,wide:/^(january|february|march|april|may|june|july|august|september|october|november|december)/i},Ur={narrow:[/^j/i,/^f/i,/^m/i,/^a/i,/^m/i,/^j/i,/^j/i,/^a/i,/^s/i,/^o/i,/^n/i,/^d/i],any:[/^ja/i,/^f/i,/^mar/i,/^ap/i,/^may/i,/^jun/i,/^jul/i,/^au/i,/^s/i,/^o/i,/^n/i,/^d/i]},jr={narrow:/^[smtwf]/i,short:/^(su|mo|tu|we|th|fr|sa)/i,abbreviated:/^(sun|mon|tue|wed|thu|fri|sat)/i,wide:/^(sunday|monday|tuesday|wednesday|thursday|friday|saturday)/i},Yr={narrow:[/^s/i,/^m/i,/^t/i,/^w/i,/^t/i,/^f/i,/^s/i],any:[/^su/i,/^m/i,/^tu/i,/^w/i,/^th/i,/^f/i,/^sa/i]},Hr={narrow:/^(a|p|mi|n|(in the|at) (morning|afternoon|evening|night))/i,any:/^([ap]\.?\s?m\.?|midnight|noon|(in the|at) (morning|afternoon|evening|night))/i},Ir={any:{am:/^a/i,pm:/^p/i,midnight:/^mi/i,noon:/^no/i,morning:/morning/i,afternoon:/afternoon/i,evening:/evening/i,night:/night/i}},Vr={ordinalNumber:Cr({matchPattern:Fr,parsePattern:_r,valueCallback:e=>parseInt(e,10)}),era:J({matchPatterns:vr,defaultMatchWidth:"wide",parsePatterns:Wr,defaultParseWidth:"any"}),quarter:J({matchPatterns:Lr,defaultMatchWidth:"wide",parsePatterns:Br,defaultParseWidth:"any",valueCallback:e=>e+1}),month:J({matchPatterns:qr,defaultMatchWidth:"wide",parsePatterns:Ur,defaultParseWidth:"any"}),day:J({matchPatterns:jr,defaultMatchWidth:"wide",parsePatterns:Yr,defaultParseWidth:"any"}),dayPeriod:J({matchPatterns:Hr,defaultMatchWidth:"any",parsePatterns:Ir,defaultParseWidth:"any"})},$r={code:"en-US",formatDistance:pr,formatLong:br,formatRelative:Sr,localize:Ar,match:Vr,options:{weekStartsOn:0,firstWeekContainsDate:1}};function Jr(e,t){const n=b(e,t?.in);return ar(n,dr(n))+1}function zr(e,t){const n=b(e,t?.in),r=+se(n)-+ir(n);return Math.round(r/De)+1}function Tt(e,t){const n=b(e,t?.in),r=n.getFullYear(),s=G(),o=t?.firstWeekContainsDate??t?.locale?.options?.firstWeekContainsDate??s.firstWeekContainsDate??s.locale?.options?.firstWeekContainsDate??1,a=R(t?.in||e,0);a.setFullYear(r+1,0,o),a.setHours(0,0,0,0);const c=U(a,t),d=R(t?.in||e,0);d.setFullYear(r,0,o),d.setHours(0,0,0,0);const l=U(d,t);return+n>=+c?r+1:+n>=+l?r:r-1}function Xr(e,t){const n=G(),r=t?.firstWeekContainsDate??t?.locale?.options?.firstWeekContainsDate??n.firstWeekContainsDate??n.locale?.options?.firstWeekContainsDate??1,s=Tt(e,t),o=R(t?.in||e,0);return o.setFullYear(s,0,r),o.setHours(0,0,0,0),U(o,t)}function Qr(e,t){const n=b(e,t?.in),r=+U(n,t)-+Xr(n,t);return Math.round(r/De)+1}function w(e,t){const n=e<0?"-":"",r=Math.abs(e).toString().padStart(t,"0");return n+r}const _={y(e,t){const n=e.getFullYear(),r=n>0?n:1-n;return w(t==="yy"?r%100:r,t.length)},M(e,t){const n=e.getMonth();return t==="M"?String(n+1):w(n+1,2)},d(e,t){return w(e.getDate(),t.length)},a(e,t){const n=e.getHours()/12>=1?"pm":"am";switch(t){case"a":case"aa":return n.toUpperCase();case"aaa":return n;case"aaaaa":return n[0];case"aaaa":default:return n==="am"?"a.m.":"p.m."}},h(e,t){return w(e.getHours()%12||12,t.length)},H(e,t){return w(e.getHours(),t.length)},m(e,t){return w(e.getMinutes(),t.length)},s(e,t){return w(e.getSeconds(),t.length)},S(e,t){const n=t.length,r=e.getMilliseconds(),s=Math.trunc(r*Math.pow(10,n-3));return w(s,t.length)}},j={midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},Ve={G:function(e,t,n){const r=e.getFullYear()>0?1:0;switch(t){case"G":case"GG":case"GGG":return n.era(r,{width:"abbreviated"});case"GGGGG":return n.era(r,{width:"narrow"});case"GGGG":default:return n.era(r,{width:"wide"})}},y:function(e,t,n){if(t==="yo"){const r=e.getFullYear(),s=r>0?r:1-r;return n.ordinalNumber(s,{unit:"year"})}return _.y(e,t)},Y:function(e,t,n,r){const s=Tt(e,r),o=s>0?s:1-s;if(t==="YY"){const a=o%100;return w(a,2)}return t==="Yo"?n.ordinalNumber(o,{unit:"year"}):w(o,t.length)},R:function(e,t){const n=Et(e);return w(n,t.length)},u:function(e,t){const n=e.getFullYear();return w(n,t.length)},Q:function(e,t,n){const r=Math.ceil((e.getMonth()+1)/3);switch(t){case"Q":return String(r);case"QQ":return w(r,2);case"Qo":return n.ordinalNumber(r,{unit:"quarter"});case"QQQ":return n.quarter(r,{width:"abbreviated",context:"formatting"});case"QQQQQ":return n.quarter(r,{width:"narrow",context:"formatting"});case"QQQQ":default:return n.quarter(r,{width:"wide",context:"formatting"})}},q:function(e,t,n){const r=Math.ceil((e.getMonth()+1)/3);switch(t){case"q":return String(r);case"qq":return w(r,2);case"qo":return n.ordinalNumber(r,{unit:"quarter"});case"qqq":return n.quarter(r,{width:"abbreviated",context:"standalone"});case"qqqqq":return n.quarter(r,{width:"narrow",context:"standalone"});case"qqqq":default:return n.quarter(r,{width:"wide",context:"standalone"})}},M:function(e,t,n){const r=e.getMonth();switch(t){case"M":case"MM":return _.M(e,t);case"Mo":return n.ordinalNumber(r+1,{unit:"month"});case"MMM":return n.month(r,{width:"abbreviated",context:"formatting"});case"MMMMM":return n.month(r,{width:"narrow",context:"formatting"});case"MMMM":default:return n.month(r,{width:"wide",context:"formatting"})}},L:function(e,t,n){const r=e.getMonth();switch(t){case"L":return String(r+1);case"LL":return w(r+1,2);case"Lo":return n.ordinalNumber(r+1,{unit:"month"});case"LLL":return n.month(r,{width:"abbreviated",context:"standalone"});case"LLLLL":return n.month(r,{width:"narrow",context:"standalone"});case"LLLL":default:return n.month(r,{width:"wide",context:"standalone"})}},w:function(e,t,n,r){const s=Qr(e,r);return t==="wo"?n.ordinalNumber(s,{unit:"week"}):w(s,t.length)},I:function(e,t,n){const r=zr(e);return t==="Io"?n.ordinalNumber(r,{unit:"week"}):w(r,t.length)},d:function(e,t,n){return t==="do"?n.ordinalNumber(e.getDate(),{unit:"date"}):_.d(e,t)},D:function(e,t,n){const r=Jr(e);return t==="Do"?n.ordinalNumber(r,{unit:"dayOfYear"}):w(r,t.length)},E:function(e,t,n){const r=e.getDay();switch(t){case"E":case"EE":case"EEE":return n.day(r,{width:"abbreviated",context:"formatting"});case"EEEEE":return n.day(r,{width:"narrow",context:"formatting"});case"EEEEEE":return n.day(r,{width:"short",context:"formatting"});case"EEEE":default:return n.day(r,{width:"wide",context:"formatting"})}},e:function(e,t,n,r){const s=e.getDay(),o=(s-r.weekStartsOn+8)%7||7;switch(t){case"e":return String(o);case"ee":return w(o,2);case"eo":return n.ordinalNumber(o,{unit:"day"});case"eee":return n.day(s,{width:"abbreviated",context:"formatting"});case"eeeee":return n.day(s,{width:"narrow",context:"formatting"});case"eeeeee":return n.day(s,{width:"short",context:"formatting"});case"eeee":default:return n.day(s,{width:"wide",context:"formatting"})}},c:function(e,t,n,r){const s=e.getDay(),o=(s-r.weekStartsOn+8)%7||7;switch(t){case"c":return String(o);case"cc":return w(o,t.length);case"co":return n.ordinalNumber(o,{unit:"day"});case"ccc":return n.day(s,{width:"abbreviated",context:"standalone"});case"ccccc":return n.day(s,{width:"narrow",context:"standalone"});case"cccccc":return n.day(s,{width:"short",context:"standalone"});case"cccc":default:return n.day(s,{width:"wide",context:"standalone"})}},i:function(e,t,n){const r=e.getDay(),s=r===0?7:r;switch(t){case"i":return String(s);case"ii":return w(s,t.length);case"io":return n.ordinalNumber(s,{unit:"day"});case"iii":return n.day(r,{width:"abbreviated",context:"formatting"});case"iiiii":return n.day(r,{width:"narrow",context:"formatting"});case"iiiiii":return n.day(r,{width:"short",context:"formatting"});case"iiii":default:return n.day(r,{width:"wide",context:"formatting"})}},a:function(e,t,n){const s=e.getHours()/12>=1?"pm":"am";switch(t){case"a":case"aa":return n.dayPeriod(s,{width:"abbreviated",context:"formatting"});case"aaa":return n.dayPeriod(s,{width:"abbreviated",context:"formatting"}).toLowerCase();case"aaaaa":return n.dayPeriod(s,{width:"narrow",context:"formatting"});case"aaaa":default:return n.dayPeriod(s,{width:"wide",context:"formatting"})}},b:function(e,t,n){const r=e.getHours();let s;switch(r===12?s=j.noon:r===0?s=j.midnight:s=r/12>=1?"pm":"am",t){case"b":case"bb":return n.dayPeriod(s,{width:"abbreviated",context:"formatting"});case"bbb":return n.dayPeriod(s,{width:"abbreviated",context:"formatting"}).toLowerCase();case"bbbbb":return n.dayPeriod(s,{width:"narrow",context:"formatting"});case"bbbb":default:return n.dayPeriod(s,{width:"wide",context:"formatting"})}},B:function(e,t,n){const r=e.getHours();let s;switch(r>=17?s=j.evening:r>=12?s=j.afternoon:r>=4?s=j.morning:s=j.night,t){case"B":case"BB":case"BBB":return n.dayPeriod(s,{width:"abbreviated",context:"formatting"});case"BBBBB":return n.dayPeriod(s,{width:"narrow",context:"formatting"});case"BBBB":default:return n.dayPeriod(s,{width:"wide",context:"formatting"})}},h:function(e,t,n){if(t==="ho"){let r=e.getHours()%12;return r===0&&(r=12),n.ordinalNumber(r,{unit:"hour"})}return _.h(e,t)},H:function(e,t,n){return t==="Ho"?n.ordinalNumber(e.getHours(),{unit:"hour"}):_.H(e,t)},K:function(e,t,n){const r=e.getHours()%12;return t==="Ko"?n.ordinalNumber(r,{unit:"hour"}):w(r,t.length)},k:function(e,t,n){let r=e.getHours();return r===0&&(r=24),t==="ko"?n.ordinalNumber(r,{unit:"hour"}):w(r,t.length)},m:function(e,t,n){return t==="mo"?n.ordinalNumber(e.getMinutes(),{unit:"minute"}):_.m(e,t)},s:function(e,t,n){return t==="so"?n.ordinalNumber(e.getSeconds(),{unit:"second"}):_.s(e,t)},S:function(e,t){return _.S(e,t)},X:function(e,t,n){const r=e.getTimezoneOffset();if(r===0)return"Z";switch(t){case"X":return Je(r);case"XXXX":case"XX":return W(r);case"XXXXX":case"XXX":default:return W(r,":")}},x:function(e,t,n){const r=e.getTimezoneOffset();switch(t){case"x":return Je(r);case"xxxx":case"xx":return W(r);case"xxxxx":case"xxx":default:return W(r,":")}},O:function(e,t,n){const r=e.getTimezoneOffset();switch(t){case"O":case"OO":case"OOO":return"GMT"+$e(r,":");case"OOOO":default:return"GMT"+W(r,":")}},z:function(e,t,n){const r=e.getTimezoneOffset();switch(t){case"z":case"zz":case"zzz":return"GMT"+$e(r,":");case"zzzz":default:return"GMT"+W(r,":")}},t:function(e,t,n){const r=Math.trunc(+e/1e3);return w(r,t.length)},T:function(e,t,n){return w(+e,t.length)}};function $e(e,t=""){const n=e>0?"-":"+",r=Math.abs(e),s=Math.trunc(r/60),o=r%60;return o===0?n+String(s):n+String(s)+t+w(o,2)}function Je(e,t){return e%60===0?(e>0?"-":"+")+w(Math.abs(e)/60,2):W(e,t)}function W(e,t=""){const n=e>0?"-":"+",r=Math.abs(e),s=w(Math.trunc(r/60),2),o=w(r%60,2);return n+s+t+o}const ze=(e,t)=>{switch(e){case"P":return t.date({width:"short"});case"PP":return t.date({width:"medium"});case"PPP":return t.date({width:"long"});case"PPPP":default:return t.date({width:"full"})}},xt=(e,t)=>{switch(e){case"p":return t.time({width:"short"});case"pp":return t.time({width:"medium"});case"ppp":return t.time({width:"long"});case"pppp":default:return t.time({width:"full"})}},Gr=(e,t)=>{const n=e.match(/(P+)(p+)?/)||[],r=n[1],s=n[2];if(!s)return ze(e,t);let o;switch(r){case"P":o=t.dateTime({width:"short"});break;case"PP":o=t.dateTime({width:"medium"});break;case"PPP":o=t.dateTime({width:"long"});break;case"PPPP":default:o=t.dateTime({width:"full"});break}return o.replace("{{date}}",ze(r,t)).replace("{{time}}",xt(s,t))},Kr={p:xt,P:Gr},Zr=/^D+$/,es=/^Y+$/,ts=["D","DD","YY","YYYY"];function ns(e){return Zr.test(e)}function rs(e){return es.test(e)}function ss(e,t,n){const r=os(e,t,n);if(console.warn(r),ts.includes(e))throw new RangeError(r)}function os(e,t,n){const r=e[0]==="Y"?"years":"days of the month";return`Use \`${e.toLowerCase()}\` instead of \`${e}\` (in \`${t}\`) for formatting ${r} to the input \`${n}\`; see: https://github.com/date-fns/date-fns/blob/master/docs/unicodeTokens.md`}const as=/[yYQqMLwIdDecihHKkms]o|(\w)\1*|''|'(''|[^'])+('|$)|./g,is=/P+p+|P+|p+|''|'(''|[^'])+('|$)|./g,cs=/^'([^]*?)'?$/,us=/''/g,ls=/[a-zA-Z]/;function js(e,t,n){const r=G(),s=n?.locale??r.locale??$r,o=n?.firstWeekContainsDate??n?.locale?.options?.firstWeekContainsDate??r.firstWeekContainsDate??r.locale?.options?.firstWeekContainsDate??1,a=n?.weekStartsOn??n?.locale?.options?.weekStartsOn??r.weekStartsOn??r.locale?.options?.weekStartsOn??0,c=b(e,n?.in);if(!ur(c))throw new RangeError("Invalid time value");let d=t.match(is).map(u=>{const f=u[0];if(f==="p"||f==="P"){const g=Kr[f];return g(u,s.formatLong)}return u}).join("").match(as).map(u=>{if(u==="''")return{isToken:!1,value:"'"};const f=u[0];if(f==="'")return{isToken:!1,value:fs(u)};if(Ve[f])return{isToken:!0,value:u};if(f.match(ls))throw new RangeError("Format string contains an unescaped latin alphabet character `"+f+"`");return{isToken:!1,value:u}});s.localize.preprocessor&&(d=s.localize.preprocessor(c,d));const l={firstWeekContainsDate:o,weekStartsOn:a,locale:s};return d.map(u=>{if(!u.isToken)return u.value;const f=u.value;(!n?.useAdditionalWeekYearTokens&&rs(f)||!n?.useAdditionalDayOfYearTokens&&ns(f))&&ss(f,t,String(e));const g=Ve[f[0]];return g(c,f,s.localize,l)}).join("")}function fs(e){const t=e.match(cs);return t?t[1].replace(us,"'"):e}function ds(e,t){const n=b(e,t?.in),r=n.getFullYear(),s=n.getMonth(),o=R(n,0);return o.setFullYear(r,s+1,0),o.setHours(0,0,0,0),o.getDate()}function Ys(e){return Math.trunc(+b(e)/1e3)}function hs(e,t){const n=b(e,t?.in),r=n.getMonth();return n.setFullYear(n.getFullYear(),r+1,0),n.setHours(0,0,0,0),b(n,t?.in)}function Hs(e,t){const n=b(e,t?.in);return lr(hs(n,t),fr(n,t),t)+1}function Is(e,t){return+b(e)>+b(t)}function Vs(e,t){return+b(e)<+b(t)}function $s(e,t,n){const[r,s]=I(n?.in,e,t);return r.getFullYear()===s.getFullYear()&&r.getMonth()===s.getMonth()}function Js(e,t,n){const[r,s]=I(n?.in,e,t);return r.getFullYear()===s.getFullYear()}function zs(e,t,n){return St(e,-t,n)}function Xs(e,t,n){const r=b(e,n?.in),s=r.getFullYear(),o=r.getDate(),a=R(e,0);a.setFullYear(s,t,15),a.setHours(0,0,0,0);const c=ds(a);return r.setMonth(t,Math.min(o,c)),r}function Qs(e,t,n){const r=b(e,n?.in);return isNaN(+r)?R(e,NaN):(r.setFullYear(t),r)}export{hr as A,se as B,U as C,zr as D,Qr as E,cr as F,ms as G,E as H,ae as a,$r as b,Rt as c,sr as d,qs as e,Bs as f,Vs as g,Ls as h,$s as i,St as j,zs as k,ar as l,Hs as m,Fs as n,Ys as o,js as p,Is as q,_s as r,fr as s,vs as t,Ws as u,Js as v,Xs as w,Qs as x,dr as y,Us as z};
