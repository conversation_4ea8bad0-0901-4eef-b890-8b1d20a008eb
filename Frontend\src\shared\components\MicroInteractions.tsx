import React, { useState, useRef, useEffect } from 'react';
import { motion, useAnimation, useMotionValue, useTransform } from 'framer-motion';
import { useAnimationContext } from '@/core/providers/AnimationContext';

// Magnetic Button Component
interface MagneticButtonProps {
  children: React.ReactNode;
  className?: string;
  onClick?: () => void;
  strength?: number;
}

export const MagneticButton: React.FC<MagneticButtonProps> = ({
  children,
  className = '',
  onClick,
  strength = 0.3
}) => {
  const ref = useRef<HTMLButtonElement>(null);
  const [isHovered, setIsHovered] = useState(false);
  const { mousePosition, isReducedMotion } = useAnimationContext();
  
  const x = useMotionValue(0);
  const y = useMotionValue(0);

  useEffect(() => {
    if (!ref.current || isReducedMotion) return;

    const rect = ref.current.getBoundingClientRect();
    const centerX = rect.left + rect.width / 2;
    const centerY = rect.top + rect.height / 2;
    
    if (isHovered) {
      const deltaX = (mousePosition.x - centerX) * strength;
      const deltaY = (mousePosition.y - centerY) * strength;
      
      x.set(deltaX);
      y.set(deltaY);
    } else {
      x.set(0);
      y.set(0);
    }
  }, [mousePosition, isHovered, strength, x, y, isReducedMotion]);

  return (
    <motion.button
      ref={ref}
      className={className}
      style={{ x, y }}
      onClick={onClick}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
      whileHover={{ scale: 1.05 }}
      whileTap={{ scale: 0.95 }}
      transition={{ type: "spring", stiffness: 300, damping: 30 }}
    >
      {children}
    </motion.button>
  );
};

// Ripple Effect Component
interface RippleEffectProps {
  children: React.ReactNode;
  className?: string;
  onClick?: (e: React.MouseEvent) => void;
  color?: string;
}

export const RippleEffect: React.FC<RippleEffectProps> = ({
  children,
  className = '',
  onClick,
  color = 'rgba(147, 51, 234, 0.3)'
}) => {
  const [ripples, setRipples] = useState<Array<{ id: string; x: number; y: number }>>([]);
  const { isReducedMotion } = useAnimationContext();
  const rippleIdCounter = useRef(0);

  const handleClick = (e: React.MouseEvent) => {
    if (!isReducedMotion) {
      const rect = e.currentTarget.getBoundingClientRect();
      const x = e.clientX - rect.left;
      const y = e.clientY - rect.top;

      rippleIdCounter.current += 1;
      const newRipple = { id: `ripple-${Date.now()}-${rippleIdCounter.current}`, x, y };
      setRipples(prev => [...prev, newRipple]);

      setTimeout(() => {
        setRipples(prev => prev.filter(ripple => ripple.id !== newRipple.id));
      }, 600);
    }

    onClick?.(e);
  };

  return (
    <div className={`relative overflow-hidden ${className}`} onClick={handleClick}>
      {children}
      {ripples.map(ripple => (
        <motion.div
          key={ripple.id}
          className="absolute rounded-full pointer-events-none"
          style={{
            left: ripple.x,
            top: ripple.y,
            backgroundColor: color,
          }}
          initial={{ width: 0, height: 0, opacity: 0.8 }}
          animate={{ 
            width: 300, 
            height: 300, 
            opacity: 0,
            x: -150,
            y: -150
          }}
          transition={{ duration: 0.6, ease: "easeOut" }}
        />
      ))}
    </div>
  );
};

// Tilt Card Component
interface TiltCardProps {
  children: React.ReactNode;
  className?: string;
  maxTilt?: number;
  perspective?: number;
}

export const TiltCard: React.FC<TiltCardProps> = ({
  children,
  className = '',
  maxTilt = 15,
  perspective = 1000
}) => {
  const ref = useRef<HTMLDivElement>(null);
  const [isHovered, setIsHovered] = useState(false);
  const { mousePosition, isReducedMotion } = useAnimationContext();
  
  const rotateX = useMotionValue(0);
  const rotateY = useMotionValue(0);
  
  const transform = useTransform(
    [rotateX, rotateY],
    ([x, y]) => `perspective(${perspective}px) rotateX(${x}deg) rotateY(${y}deg)`
  );

  useEffect(() => {
    if (!ref.current || isReducedMotion) return;

    const rect = ref.current.getBoundingClientRect();
    const centerX = rect.left + rect.width / 2;
    const centerY = rect.top + rect.height / 2;
    
    if (isHovered) {
      const deltaX = (mousePosition.y - centerY) / rect.height;
      const deltaY = (mousePosition.x - centerX) / rect.width;
      
      rotateX.set(deltaX * maxTilt);
      rotateY.set(deltaY * maxTilt);
    } else {
      rotateX.set(0);
      rotateY.set(0);
    }
  }, [mousePosition, isHovered, maxTilt, rotateX, rotateY, isReducedMotion]);

  return (
    <motion.div
      ref={ref}
      className={className}
      style={{ transform }}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
      transition={{ type: "spring", stiffness: 300, damping: 30 }}
    >
      {children}
    </motion.div>
  );
};

// Morphing Shape Component
interface MorphingShapeProps {
  shapes: string[];
  className?: string;
  duration?: number;
  autoPlay?: boolean;
}

export const MorphingShape: React.FC<MorphingShapeProps> = ({
  shapes,
  className = '',
  duration = 3,
  autoPlay = true
}) => {
  const [currentShape, setCurrentShape] = useState(0);
  const { isReducedMotion } = useAnimationContext();

  useEffect(() => {
    if (!autoPlay || isReducedMotion) return;

    const interval = setInterval(() => {
      setCurrentShape(prev => (prev + 1) % shapes.length);
    }, duration * 1000);

    return () => clearInterval(interval);
  }, [shapes.length, duration, autoPlay, isReducedMotion]);

  return (
    <svg className={className} viewBox="0 0 100 100">
      <motion.path
        d={shapes[currentShape]}
        fill="currentColor"
        animate={{ d: shapes[currentShape] }}
        transition={{ duration: duration * 0.8, ease: "easeInOut" }}
      />
    </svg>
  );
};

// Glitch Text Effect
interface GlitchTextProps {
  text: string;
  className?: string;
  trigger?: 'hover' | 'auto' | 'click';
  intensity?: number;
}

export const GlitchText: React.FC<GlitchTextProps> = ({
  text,
  className = '',
  trigger = 'hover',
  intensity = 3
}) => {
  const [isGlitching, setIsGlitching] = useState(false);
  const { isReducedMotion } = useAnimationContext();

  const glitchVariants = {
    normal: { x: 0, y: 0, opacity: 1 },
    glitch: {
      x: [0, -intensity, intensity, 0],
      y: [0, intensity, -intensity, 0],
      opacity: [1, 0.8, 1, 0.9, 1],
      transition: { duration: 0.3, repeat: 2 }
    }
  };

  useEffect(() => {
    if (trigger === 'auto' && !isReducedMotion) {
      const interval = setInterval(() => {
        setIsGlitching(true);
        setTimeout(() => setIsGlitching(false), 900);
      }, 5000);

      return () => clearInterval(interval);
    }
  }, [trigger, isReducedMotion]);

  const handleInteraction = () => {
    if (!isReducedMotion) {
      setIsGlitching(true);
      setTimeout(() => setIsGlitching(false), 900);
    }
  };

  return (
    <motion.span
      className={`relative inline-block ${className}`}
      variants={glitchVariants}
      animate={isGlitching ? 'glitch' : 'normal'}
      onMouseEnter={trigger === 'hover' ? handleInteraction : undefined}
      onClick={trigger === 'click' ? handleInteraction : undefined}
    >
      {text}
      {isGlitching && !isReducedMotion && (
        <>
          <motion.span
            className="absolute inset-0 text-red-500 opacity-70"
            style={{ clipPath: 'inset(0 0 50% 0)' }}
            animate={{ x: [-2, 2, -2] }}
            transition={{ duration: 0.1, repeat: Infinity }}
          >
            {text}
          </motion.span>
          <motion.span
            className="absolute inset-0 text-blue-500 opacity-70"
            style={{ clipPath: 'inset(50% 0 0 0)' }}
            animate={{ x: [2, -2, 2] }}
            transition={{ duration: 0.1, repeat: Infinity, delay: 0.05 }}
          >
            {text}
          </motion.span>
        </>
      )}
    </motion.span>
  );
};

// Pulse Loader
interface PulseLoaderProps {
  size?: number;
  color?: string;
  className?: string;
}

export const PulseLoader: React.FC<PulseLoaderProps> = ({
  size = 40,
  color = '#9333ea',
  className = ''
}) => {
  const { isReducedMotion } = useAnimationContext();

  return (
    <div className={`flex items-center justify-center ${className}`}>
      {[0, 1, 2].map(i => (
        <motion.div
          key={i}
          className="rounded-full"
          style={{
            width: size / 4,
            height: size / 4,
            backgroundColor: color,
            marginRight: i < 2 ? size / 8 : 0
          }}
          animate={isReducedMotion ? {} : {
            scale: [1, 1.5, 1],
            opacity: [0.7, 1, 0.7]
          }}
          transition={{
            duration: 1.5,
            repeat: Infinity,
            delay: i * 0.2,
            ease: "easeInOut"
          }}
        />
      ))}
    </div>
  );
};
