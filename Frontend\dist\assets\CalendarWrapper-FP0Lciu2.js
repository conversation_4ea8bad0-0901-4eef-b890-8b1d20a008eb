const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["./EventCalendar-Bmp-lSaA.js","./index-hEW_vQ3f.js","./dialog-DaFUYITg.js","./index-lne2Edaq.js"])))=>i.map(i=>d[i]);
import{j as e,r,k as s,_ as t}from"./index-hEW_vQ3f.js";const l=s.lazy(()=>t(()=>import("./EventCalendar-Bmp-lSaA.js"),__vite__mapDeps([0,1,2,3]),import.meta.url).catch(()=>({default:()=>e.jsx("div",{className:"p-8 text-center",children:e.jsx("p",{className:"text-gray-500",children:"Calendar component is temporarily unavailable."})})}))),n=()=>e.jsxs("div",{className:"flex h-64 w-full items-center justify-center",children:[e.jsx("div",{className:"h-8 w-8 animate-spin rounded-full border-2 border-gray-300 border-t-blue-500"}),e.jsx("span",{className:"ml-2 text-gray-600",children:"Loading calendar..."})]}),c=({className:a})=>e.jsx("div",{className:a,children:e.jsx(r.Suspense,{fallback:e.jsx(n,{}),children:e.jsx(l,{})})});export{c as default};
