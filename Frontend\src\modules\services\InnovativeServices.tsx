import { useState, useRef, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { useNavigate, useParams } from "react-router-dom";
import { useClickTracking } from "@/core/providers/ClickTrackingContext";
import { useAuth } from "@/core/providers/AuthContext";
import { useUserAuthModal } from "@/core/contexts/UserAuthModalContext";
import { useAppSelector } from "@/store/hooks";
import { ChevronLeft, ChevronRight, ArrowRight, Star, Sparkles, Target, Rocket, Eye, Zap, Bot, Cpu, ShoppingCart, Globe, Smartphone, Code, Palette, TrendingUp, Users, LogIn } from "lucide-react";
import { useCart } from "../../core/contexts/CartContext";
import { Service, SubService } from "@/core/types";
import { getServices } from "@/shared/services/service.service";

const InnovativeServices = () => {
  const { trackClick } = useClickTracking();
  const navigate = useNavigate();
  const { slug } = useParams();
  const { addToCart } = useCart();
  const { user } = useAuth();
  const { openModal: openUserAuthModal } = useUserAuthModal();
  const reduxUser = useAppSelector((state) => state.auth.user);
  const reduxIsAuthenticated = useAppSelector((state) => state.auth.isAuthenticated);

  // Check if user is authenticated
  const isAuthenticated = Boolean((user || reduxUser) && reduxIsAuthenticated);
  const [services, setServices] = useState<Service[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [currentServiceIndex, setCurrentServiceIndex] = useState(0);
  const [showSubServices, setShowSubServices] = useState(false);
  const containerRef = useRef<HTMLDivElement>(null);

  // Fetch services from API and scroll to top
  useEffect(() => {
    // Scroll to top when component mounts
    window.scrollTo({ top: 0, behavior: 'smooth' });
    fetchServices();
  }, []);

  // Handle URL slug parameter
  useEffect(() => {
    if (slug && services.length > 0) {
      const serviceIndex = services.findIndex(service =>
        service.slug === slug || service.id === slug
      );
      if (serviceIndex !== -1) {
        setCurrentServiceIndex(serviceIndex);
        setShowSubServices(true);
      }
    }
  }, [slug, services]);

  // Handle URL slug parameter to show specific service
  useEffect(() => {
    if (slug && services.length > 0) {
      const serviceIndex = services.findIndex(service =>
        service.slug === slug || service.id === slug
      );

      if (serviceIndex !== -1) {
        setCurrentServiceIndex(serviceIndex);
        setShowSubServices(true);

        // Scroll to the service section
        setTimeout(() => {
          if (containerRef.current) {
            const targetX = (serviceIndex + 1) * window.innerWidth;
            containerRef.current.scrollTo({
              left: targetX,
              behavior: 'smooth'
            });
          }
        }, 100);

        // Track analytics for direct service access
        trackClick({
          pageName: 'Services',
          serviceName: services[serviceIndex].title,
          buttonLabel: 'Direct Service Access',
          component: 'services'
        });
      }
    } else if (!slug) {
      // If no slug, show main services page
      setShowSubServices(false);
      setCurrentServiceIndex(0);
      if (containerRef.current) {
        containerRef.current.scrollTo({
          left: 0,
          behavior: 'smooth'
        });
      }
    }
  }, [slug, services, trackClick]);

  const fetchServices = async () => {
    setLoading(true);
    setError(null);
    try {
      const response = await getServices();
      console.log("Fetched services response:", response);

      // The API returns { data: Service[], pagination: {...} }
      if (response && response.data && Array.isArray(response.data)) {
        setServices(response.data);
      } else {
        console.warn("Services data is not in expected format:", response);
        setServices([]);
      }
    } catch (err) {
      console.error("Error fetching services:", err);
      setError("Failed to load services. Please try again later.");
    } finally {
      setLoading(false);
    }
  };

  // Get icon component based on service title
  const getIconComponent = (serviceTitle: string) => {
    const title = serviceTitle.toLowerCase();
    if (title.includes('marketing') || title.includes('seo')) return TrendingUp;
    if (title.includes('web') || title.includes('development')) return Code;
    if (title.includes('mobile') || title.includes('app')) return Smartphone;
    if (title.includes('design') || title.includes('ui') || title.includes('ux')) return Palette;
    if (title.includes('consulting') || title.includes('strategy')) return Target;
    if (title.includes('analytics') || title.includes('data')) return Eye;
    if (title.includes('automation') || title.includes('ai')) return Bot;
    return Rocket; // Default icon
  };

  // Handle service click - slide to sub-services
  const handleServiceClick = (index: number) => {
    setCurrentServiceIndex(index);
    setShowSubServices(true);

    // Update URL to reflect the current service
    const service = services[index];
    const serviceSlug = service.slug || service.id;
    navigate(`/services/${serviceSlug}`, { replace: true });

    // Scroll to the specific service section with mobile optimization
    if (containerRef.current) {
      const targetX = (index + 1) * window.innerWidth;

      // Use different scroll behavior for mobile vs desktop
      const isMobile = window.innerWidth < 640;
      if (isMobile) {
        // On mobile, scroll immediately without animation for better performance
        containerRef.current.scrollTo({
          left: targetX,
          behavior: 'auto'
        });
        // Then scroll to top of the page
        setTimeout(() => {
          window.scrollTo({ top: 0, behavior: 'smooth' });
        }, 100);
      } else {
        containerRef.current.scrollTo({
          left: targetX,
          behavior: 'smooth'
        });
      }
    }

    // Track analytics
    trackClick({
      pageName: 'Services',
      serviceName: services[index].title,
      buttonLabel: 'Service Click',
      component: 'services'
    });
  };

  // Navigate back to main services with mobile optimization
  const handleBackToServices = () => {
    setShowSubServices(false);

    // Update URL back to main services
    navigate('/services', { replace: true });

    if (containerRef.current) {
      const isMobile = window.innerWidth < 640;
      if (isMobile) {
        // On mobile, scroll immediately and then to top
        containerRef.current.scrollTo({
          left: 0,
          behavior: 'auto'
        });
        setTimeout(() => {
          window.scrollTo({ top: 0, behavior: 'smooth' });
        }, 100);
      } else {
        containerRef.current.scrollTo({
          left: 0,
          behavior: 'smooth'
        });
      }
    }
  };

  // Navigate between services
  const navigateService = (direction: 'prev' | 'next') => {
    const newIndex = direction === 'prev'
      ? Math.max(0, currentServiceIndex - 1)
      : Math.min(services.length - 1, currentServiceIndex + 1);

    setCurrentServiceIndex(newIndex);

    // Update URL
    const service = services[newIndex];
    const serviceSlug = service.slug || service.id;
    navigate(`/services/${serviceSlug}`, { replace: true });

    if (containerRef.current) {
      const targetX = (newIndex + 1) * window.innerWidth;
      containerRef.current.scrollTo({
        left: targetX,
        behavior: 'smooth'
      });
    }
  };

  // Add to cart handler
  const handleAddToCart = async (subService: SubService, serviceName: string) => {
    if (!isAuthenticated) {
      // Open user auth modal with custom message
      openUserAuthModal('Please connect to your account to add services to your cart');
      return;
    }

    try {
      const currentService = services[currentServiceIndex];
      const success = await addToCart({
        serviceId: (currentService as any)._id || currentService.id,
        serviceName: serviceName,
        subServiceId: subService.title, // Use title as identifier since _id doesn't exist
        subServiceName: subService.title,
        description: subService.description,
        price: 0 // No price - will be determined during consultation
      });

      if (success) {
        // Track analytics
        trackClick({
          pageName: 'Services',
          serviceName: serviceName,
          buttonLabel: 'Add to Cart',
          component: 'services'
        });

        // Show success message (optional)
        console.log('Item added to cart successfully');
      }
    } catch (error) {
      console.error('Error adding item to cart:', error);
    }
  };

  // Loading state
  if (loading) {
    return (
      <div className="pt-20 min-h-screen flex items-center justify-center bg-white">
        <div className="text-center">
          <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-brand-purple-500 mx-auto mb-4"></div>
          <p className="text-brand-black text-xl">Loading our amazing services...</p>
        </div>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className="pt-20 min-h-screen flex items-center justify-center bg-white">
        <div className="text-center">
          <p className="text-red-500 text-xl mb-4">{error}</p>
          <button
            onClick={fetchServices}
            className="bg-brand-purple-600 hover:bg-brand-purple-700 text-white px-6 py-3 rounded-lg transition-colors duration-300"
          >
            Try Again
          </button>
        </div>
      </div>
    );
  }

  // No services state
  if (!services || services.length === 0) {
    return (
      <div className="pt-20 min-h-screen flex items-center justify-center bg-white">
        <div className="text-center">
          <p className="text-brand-black text-xl">No services available at the moment.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="pt-16 sm:pt-20 min-h-screen relative overflow-hidden">
      {/* Mobile-First Responsive Container */}
      <div
        ref={containerRef}
        className="horizontal-scroll-container hide-scrollbar"
        style={{
          height: showSubServices ? 'auto' : '100vh',
          minHeight: '100vh',
          scrollbarWidth: 'none',
          msOverflowStyle: 'none'
        }}
      >
        {/* Main Services Section */}
        <div className="horizontal-scroll-item flex flex-col justify-center px-4 sm:px-6 lg:px-16 relative py-8 sm:py-0 bg-gradient-to-b from-black/50 to-transparent">
          <div className="text-center max-w-6xl mx-auto">
            <motion.h1
              className="text-3xl sm:text-4xl md:text-6xl lg:text-7xl xl:text-8xl font-bold text-white mb-6 sm:mb-8 px-2 drop-shadow-2xl"
              initial={{ opacity: 0, y: 50 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
            >
              Our <span className="text-brand-purple-400">Services</span>
            </motion.h1>
            <motion.p
              className="text-base sm:text-lg md:text-xl lg:text-2xl text-white/90 mb-8 sm:mb-12 lg:mb-16 leading-relaxed max-w-4xl mx-auto px-4 drop-shadow-lg"
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.2 }}
            >
              Innovative digital solutions that transform your business
            </motion.p>

            {/* Mobile-Responsive Services Grid */}
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 sm:gap-6 lg:gap-8 max-w-7xl mx-auto px-2 sm:px-0">
              {services.map((service, index) => {
                const IconComponent = getIconComponent(service.title);
                return (
                  <motion.div
                    key={service.id}
                    initial={{ opacity: 0, y: 50 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.6, delay: index * 0.1 }}
                    className="group cursor-pointer"
                    onClick={() => handleServiceClick(index)}
                  >
                    <div className="relative h-64 sm:h-72 lg:h-80 rounded-2xl sm:rounded-3xl overflow-hidden shadow-2xl border border-white/10 group-hover:border-white/30 transition-all duration-500 group-hover:scale-105 touch-manipulation">
                      {/* Service Image as Main Background */}
                      {service.image && service.image !== 'no-photo.jpg' ? (
                        <div className="absolute inset-0">
                          <img
                            src={service.image}
                            alt={service.title}
                            className="w-full h-full object-cover transition-all duration-700 group-hover:scale-110"
                          />
                          <div className="absolute inset-0 bg-gradient-to-t from-black/80 via-black/20 to-transparent" />
                        </div>
                      ) : (
                        <div className="absolute inset-0 bg-gradient-to-br from-brand-purple-600 via-brand-purple-700 to-brand-purple-800" />
                      )}

                      {/* Mobile-Responsive Content */}
                      <div className="relative z-10 h-full flex flex-col justify-between p-4 sm:p-6 lg:p-8">
                        <div className="flex items-start justify-between">
                          <motion.div
                            className="p-2 sm:p-3 lg:p-4 bg-brand-purple-600/90 backdrop-blur-sm rounded-xl sm:rounded-2xl"
                            whileHover={{ scale: 1.1, rotate: 10 }}
                            transition={{ duration: 0.3 }}
                          >
                            <IconComponent className="w-5 h-5 sm:w-6 sm:h-6 lg:w-8 lg:h-8 text-white" />
                          </motion.div>
                          <div className="text-right">
                            <span className="text-brand-purple-300 text-xs sm:text-sm font-semibold">
                              {service.subServices?.length || 0} Services
                            </span>
                          </div>
                        </div>

                        <div>
                          <h3 className="text-lg sm:text-xl lg:text-2xl font-bold text-white mb-2 sm:mb-3 lg:mb-4 group-hover:text-brand-purple-200 transition-colors duration-300 line-clamp-2">
                            {service.title}
                          </h3>
                          <p className="text-brand-grey-300 text-sm sm:text-base line-clamp-2 sm:line-clamp-3 mb-3 sm:mb-4">
                            {service.description}
                          </p>
                          <div className="flex items-center justify-between">
                            <span className="text-brand-purple-300 text-xs sm:text-sm font-semibold">
                              Click to explore →
                            </span>
                            <ArrowRight className="w-4 h-4 sm:w-5 sm:h-5 text-brand-purple-300 group-hover:translate-x-2 transition-transform duration-300" />
                          </div>
                        </div>
                      </div>

                      {/* Hover effects */}
                      <div className="absolute inset-0 rounded-3xl ring-2 ring-brand-purple-500/0 group-hover:ring-brand-purple-500/60 transition-all duration-500" />
                      
                      {/* Sparkle effects */}
                      <div className="absolute top-4 right-4 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                        <Sparkles className="w-6 h-6 text-brand-purple-300 animate-pulse" />
                      </div>
                    </div>
                  </motion.div>
                );
              })}
            </div>
          </div>
        </div>

        {/* Mobile-Responsive Sub-Services Sections */}
        {services.map((service, serviceIndex) => (
          <div key={`sub-${service.id}`} className="horizontal-scroll-item flex flex-col justify-center px-4 sm:px-6 lg:px-16 relative py-8 sm:py-0 bg-gradient-to-b from-black/30 to-transparent">
            {/* Mobile-Responsive Back Button */}
            <motion.button
              onClick={handleBackToServices}
              className="absolute top-4 sm:top-8 left-4 sm:left-8 z-50 bg-brand-purple-600 hover:bg-brand-purple-700 text-white rounded-full p-3 sm:p-4 shadow-lg transition-all duration-300"
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.95 }}
            >
              <ChevronLeft className="w-5 h-5 sm:w-6 sm:h-6" />
            </motion.button>

            {/* Mobile-Hidden Navigation Buttons */}
            {serviceIndex > 0 && (
              <motion.button
                onClick={() => navigateService('prev')}
                className="hidden sm:block absolute top-1/2 left-4 sm:left-8 transform -translate-y-1/2 z-50 bg-brand-purple-600/80 hover:bg-brand-purple-600 text-white rounded-full p-3 shadow-lg transition-all duration-300"
                whileHover={{ scale: 1.1 }}
                whileTap={{ scale: 0.95 }}
              >
                <ChevronLeft className="w-5 h-5" />
              </motion.button>
            )}

            {serviceIndex < services.length - 1 && (
              <motion.button
                onClick={() => navigateService('next')}
                className="hidden sm:block absolute top-1/2 right-4 sm:right-8 transform -translate-y-1/2 z-50 bg-brand-purple-600/80 hover:bg-brand-purple-600 text-white rounded-full p-3 shadow-lg transition-all duration-300"
                whileHover={{ scale: 1.1 }}
                whileTap={{ scale: 0.95 }}
              >
                <ChevronRight className="w-5 h-5" />
              </motion.button>
            )}

            <div className="text-center max-w-7xl mx-auto">
              <motion.h2
                className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl xl:text-6xl font-bold text-white mb-3 sm:mb-4 px-4"
                initial={{ opacity: 0, y: 30 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6 }}
              >
                {service.title}
              </motion.h2>
              <motion.p
                className="text-base sm:text-lg md:text-xl text-brand-grey-300 mb-8 sm:mb-10 lg:mb-12 max-w-3xl mx-auto px-4"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.2 }}
              >
                {service.description}
              </motion.p>

              {/* Mobile-Responsive Sub-Services Grid */}
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 sm:gap-6 lg:gap-8 px-2 sm:px-0">
                {service.subServices?.map((subService, subIndex) => (
                  <motion.div
                    key={subService.id}
                    initial={{ opacity: 0, y: 50 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.6, delay: subIndex * 0.1 }}
                    className="group relative"
                  >
                    <div className="relative h-80 sm:h-88 lg:h-96 rounded-2xl sm:rounded-3xl overflow-hidden shadow-2xl border border-white/10 group-hover:border-white/30 transition-all duration-500 group-hover:scale-105 touch-manipulation">
                      {/* Sub-Service Image as Main Background */}
                      {subService.image && subService.image !== 'no-photo.jpg' ? (
                        <div className="absolute inset-0">
                          <img
                            src={subService.image}
                            alt={subService.title}
                            className="w-full h-full object-cover transition-all duration-700 group-hover:scale-110"
                          />
                          <div className="absolute inset-0 bg-gradient-to-t from-black/80 via-black/20 to-transparent" />
                        </div>
                      ) : (
                        <div className="absolute inset-0 bg-gradient-to-br from-brand-purple-600 via-brand-purple-700 to-brand-purple-800" />
                      )}

                      {/* Mobile-Responsive Content */}
                      <div className="relative z-10 h-full flex flex-col justify-between p-4 sm:p-5 lg:p-6">
                        <div className="flex items-start justify-between">
                          <div className="p-2 sm:p-3 bg-brand-purple-600/90 backdrop-blur-sm rounded-xl sm:rounded-2xl">
                            <Star className="w-4 h-4 sm:w-5 sm:h-5 lg:w-6 lg:h-6 text-white" />
                          </div>
                          <div className="text-right">
                            <span className="text-brand-purple-300 text-xs sm:text-sm font-semibold">
                              Premium Service
                            </span>
                          </div>
                        </div>

                        <div>
                          <h3 className="text-lg sm:text-xl font-bold text-white mb-2 sm:mb-3 group-hover:text-brand-purple-200 transition-colors duration-300 line-clamp-2">
                            {subService.title}
                          </h3>
                          <p className="text-brand-grey-300 text-sm line-clamp-2 sm:line-clamp-3 mb-4 sm:mb-6">
                            {subService.description}
                          </p>

                          {/* Mobile-Responsive Add to Cart Button */}
                          <motion.button
                            onClick={(e) => {
                              e.stopPropagation();
                              handleAddToCart(subService, service.title);
                            }}
                            className={`w-full rounded-xl sm:rounded-2xl py-2.5 sm:py-3 px-3 sm:px-4 font-semibold text-sm sm:text-base transition-all duration-300 flex items-center justify-center gap-2 touch-manipulation ${
                              isAuthenticated
                                ? 'bg-brand-purple-600 hover:bg-brand-purple-700 text-white'
                                : 'bg-brand-grey-600 hover:bg-brand-grey-500 text-white'
                            }`}
                            whileHover={{ scale: 1.05 }}
                            whileTap={{ scale: 0.95 }}
                          >
                            {isAuthenticated ? (
                              <>
                                <ShoppingCart className="w-3 h-3 sm:w-4 sm:h-4" />
                                <span className="hidden sm:inline">Add to Cart</span>
                                <span className="sm:hidden">Add</span>
                              </>
                            ) : (
                              <>
                                <LogIn className="w-3 h-3 sm:w-4 sm:h-4" />
                                <span className="hidden sm:inline">Connect to Add</span>
                                <span className="sm:hidden">Connect</span>
                              </>
                            )}
                          </motion.button>
                        </div>
                      </div>

                      {/* Hover effects */}
                      <div className="absolute inset-0 rounded-3xl ring-2 ring-brand-purple-500/0 group-hover:ring-brand-purple-500/60 transition-all duration-500" />

                      {/* Sparkle effects */}
                      <div className="absolute top-4 right-4 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                        <Sparkles className="w-5 h-5 text-brand-purple-300 animate-pulse" />
                      </div>
                    </div>
                  </motion.div>
                ))}
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Mobile Navigation Buttons */}
      {showSubServices && (
        <div className="sm:hidden fixed bottom-20 left-4 right-4 z-50 flex justify-between">
          {currentServiceIndex > 0 && (
            <motion.button
              onClick={() => navigateService('prev')}
              className="bg-brand-purple-600/90 hover:bg-brand-purple-700 text-white rounded-full p-3 shadow-lg backdrop-blur-sm"
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.95 }}
            >
              <ChevronLeft className="w-5 h-5" />
            </motion.button>
          )}
          <div className="flex-1" />
          {currentServiceIndex < services.length - 1 && (
            <motion.button
              onClick={() => navigateService('next')}
              className="bg-brand-purple-600/90 hover:bg-brand-purple-700 text-white rounded-full p-3 shadow-lg backdrop-blur-sm"
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.95 }}
            >
              <ChevronRight className="w-5 h-5" />
            </motion.button>
          )}
        </div>
      )}

      {/* Mobile-Responsive Scroll Indicator */}
      <div className="fixed bottom-4 sm:bottom-8 left-1/2 transform -translate-x-1/2 z-50">
        <div className="flex space-x-1.5 sm:space-x-2 bg-brand-black/50 backdrop-blur-sm rounded-full px-3 py-2">
          {[...Array(services.length + 1)].map((_, index) => (
            <div
              key={index}
              className={`w-1.5 h-1.5 sm:w-2 sm:h-2 rounded-full transition-all duration-300 ${
                index === 0 && !showSubServices
                  ? 'bg-brand-purple-400 w-6 sm:w-8'
                  : index === currentServiceIndex + 1 && showSubServices
                  ? 'bg-brand-purple-400 w-6 sm:w-8'
                  : 'bg-brand-grey-600'
              }`}
            />
          ))}
        </div>
      </div>
    </div>
  );
};

export default InnovativeServices;
