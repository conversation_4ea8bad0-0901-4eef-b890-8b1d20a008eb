var x=(l,c,a)=>new Promise((m,i)=>{var d=t=>{try{r(a.next(t))}catch(n){i(n)}},g=t=>{try{r(a.throw(t))}catch(n){i(n)}},r=t=>t.done?m(t.value):Promise.resolve(t.value).then(d,g);r((a=a.apply(l,c)).next())});import{c as U,e as I,d as E,r as b,j as e,X as R,Q as y,I as j}from"./index-hEW_vQ3f.js";import{R as L}from"./quill.snow-Crc82pRX.js";import"./isEqual-CATH4cxC.js";/**
 * @license lucide-react v0.336.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const P=U("Upload",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"17 8 12 3 7 8",key:"t8dd8p"}],["line",{x1:"12",x2:"12",y1:"3",y2:"15",key:"widbto"}]]),T=l=>x(null,null,function*(){return new Promise(c=>{setTimeout(()=>c(URL.createObjectURL(l)),1e3)})}),B=()=>{var p,f,v;const l=I(),c=E(),a=(p=c.state)==null?void 0:p.subService,m=(f=c.state)==null?void 0:f.idx,i=(v=c.state)==null?void 0:v.onSave,[d,g]=b.useState((a==null?void 0:a.title)||""),[r,t]=b.useState((a==null?void 0:a.image)||""),[n,N]=b.useState((a==null?void 0:a.description)||""),[u,h]=b.useState(!1),[o,w]=b.useState(!1),S=s=>x(null,null,function*(){h(!0);const C=yield T(s);t(C),h(!1)}),k=()=>{!d.trim()||!n.trim()||(w(!0),i&&i({title:d,image:r,description:n},m),l(-1))};return e.jsxs("div",{className:"min-h-screen bg-brand-grey-950 flex flex-col",children:[e.jsxs("div",{className:"flex items-center justify-between p-6 border-b border-brand-grey-800 bg-brand-grey-950",children:[e.jsx("h1",{className:"text-2xl md:text-3xl font-bold text-brand-accent-100",children:a?"Edit Sub-Service":"Add Sub-Service"}),e.jsx("button",{onClick:()=>l(-1),className:"text-brand-grey-400 hover:text-brand-accent-100",children:e.jsx(R,{className:"w-6 h-6"})})]}),e.jsx("div",{className:"flex-1 flex flex-col items-center justify-center p-6",children:e.jsxs("div",{className:"w-full max-w-2xl bg-brand-grey-900 rounded-2xl shadow-2xl p-8 border border-brand-grey-800 flex flex-col gap-8",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block mb-1 font-semibold text-brand-accent-100 text-lg",children:"Title"}),e.jsx(y,{value:d,onChange:s=>g(s.target.value),placeholder:"Enter sub-service title",disabled:o,className:"bg-brand-grey-900 border-brand-grey-700 text-brand-white placeholder:text-brand-grey-400 focus:ring-2 focus:ring-brand-accent-100 font-semibold text-lg"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block mb-1 font-semibold text-brand-accent-100 text-lg",children:"Image"}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(y,{type:"text",value:r,onChange:s=>t(s.target.value),placeholder:"Paste image URL or upload",disabled:o||u,className:"bg-brand-grey-900 border-brand-grey-700 text-brand-white placeholder:text-brand-grey-400 focus:ring-2 focus:ring-brand-accent-100 flex-1"}),e.jsxs("label",{className:"inline-flex items-center cursor-pointer",children:[e.jsx("input",{type:"file",accept:"image/*",className:"hidden",disabled:o||u,onChange:s=>x(null,null,function*(){s.target.files&&s.target.files[0]&&(yield S(s.target.files[0]))})}),e.jsx("span",{className:"p-2 bg-brand-grey-700 rounded hover:bg-brand-accent-100 hover:text-brand-black transition",children:e.jsx(P,{className:"w-4 h-4"})})]}),u&&e.jsx("span",{className:"ml-2 text-xs text-brand-accent-100 animate-pulse",children:"Uploading..."})]}),r&&e.jsx("img",{src:r,alt:"Preview",className:"mt-2 max-h-32 rounded-xl border border-brand-grey-700 w-full object-cover shadow"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block mb-1 font-semibold text-brand-accent-100 text-lg",children:"Description"}),e.jsx(L,{value:n,onChange:N,theme:"snow",className:"bg-brand-grey-900 text-brand-white rounded-xl",placeholder:"Enter sub-service description",style:{minHeight:120}})]}),e.jsxs("div",{className:"flex flex-col md:flex-row gap-4 justify-end",children:[e.jsx(j,{type:"button",variant:"secondary",onClick:()=>l(-1),disabled:o,className:"btn-secondary text-brand-white bg-brand-grey-800 hover:bg-brand-grey-900 hover:text-brand-accent-100 transition-all duration-200 w-full md:w-auto font-semibold",children:"Cancel"}),e.jsx(j,{type:"button",onClick:k,disabled:o||!d.trim()||!n.trim(),className:"btn-primary bg-brand-accent-100 text-brand-black hover:bg-brand-accent-200 hover:text-brand-black transition-all duration-200 w-full md:w-auto font-bold",children:o?"Saving...":"Save"})]})]})})]})};export{B as default};
