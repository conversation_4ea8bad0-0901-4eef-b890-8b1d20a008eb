import React, { createContext, useContext, useEffect, useState, useCallback } from 'react';
import { useQuery, useQueryClient } from '@tanstack/react-query';
import * as authService from '@/shared/services/auth.service';
// Using the User type from auth.service to avoid type conflicts
import type { User, UserRole } from '@/shared/services/auth.service';
import { removeToken, isAuthenticated as checkAuth } from '@/shared/services/api/auth';

interface RegisterData {
  name: string;
  email: string;
  password: string;
  role: 'user' | 'admin' | 'marketing_responsible';
}

interface AuthContextType {
  user: User | null;
  isLoading: boolean;
  isAuthenticated: () => boolean;
  hasRole: (role: string) => boolean;
  login: (credentials: { email: string; password: string }) => Promise<User>;
  register: (data: RegisterData) => Promise<void>;
  logout: () => Promise<void>;
  refetchUser: () => void;
  checkAuthStatus: () => Promise<void>; // Expose this for ProtectedRoute
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const queryClient = useQueryClient();
  const [isLoading, setIsLoading] = useState(true);

  const { 
    data: user, 
    isLoading: isLoadingUser, 
    isError,
    refetch: refetchUser 
  } = useQuery<User | null>({
    queryKey: ['currentUser'],
    queryFn: async (): Promise<User | null> => {
      try {
        // Try to get the current user - the HTTP-only cookie will be sent automatically
        const currentUser = await authService.getCurrentUser();
        return currentUser || null;
      } catch (error) {
        console.error('Failed to fetch current user:', error);
        removeToken();
        return null;
      }
    },
    retry: 1,
    enabled: false, // Don't run on mount
  });

  // Only check auth status when explicitly requested (e.g., by ProtectedRoute)
  const checkAuthStatus = useCallback(async () => {
    setIsLoading(true);
    try {
      await refetchUser();
    } catch (error) {
      console.error('Auth check failed:', error);
      // On error, clear auth state
      queryClient.setQueryData(['currentUser'], null);
      removeToken();
    } finally {
      setIsLoading(false);
    }
  }, [refetchUser, queryClient]);

  // Handle successful login
  const handleLogin = useCallback(async (credentials: { email: string; password: string }) => {
    try {
      console.log('Starting login process...');
      setIsLoading(true);
      
      try {
        // Clear any existing auth state
        console.log('Clearing existing auth state...');
        queryClient.clear();
        removeToken();
        sessionStorage.removeItem('temp_token');
        
        // 1. Perform login - this will set the HTTP-only cookie
        console.log('Sending login request...');
        const loginResponse = await authService.login(credentials);
        console.log('Login response:', loginResponse);
        
        // 2. Fetch the current user - the cookie should be sent automatically
        console.log('Fetching current user...');
        const currentUser = await authService.getCurrentUser();
        console.log('Current user:', currentUser);
        
        if (!currentUser) {
          console.error('No user data returned after login');
          throw new Error('Failed to load user data after login');
        }
        
        // 3. Update the query cache with the current user
        console.log('Updating query cache with user data');
        queryClient.setQueryData(['currentUser'], currentUser);
        
        // 4. Force a refetch of the current user to ensure consistency
        await queryClient.refetchQueries({ queryKey: ['currentUser'] });
        
        console.log('Login successful, user:', currentUser);
        return currentUser;
      } finally {
        // Always ensure loading is set to false
        setIsLoading(false);
      }
    } catch (error) {
      console.error('Login failed:', error);
      // Clear any partial auth state on failure
      queryClient.setQueryData(['currentUser'], null);
      removeToken();
      sessionStorage.removeItem('temp_token');
      throw error;
    }
  }, [queryClient]);
  
  // Handle logout
  const handleLogout = useCallback(async () => {
    try {
      await authService.logout();
    } catch (error) {
      console.error('Logout failed:', error);
    } finally {
      // Clear all auth state
      queryClient.setQueryData(['currentUser'], null);
      queryClient.clear();
      removeToken();

      // Dispatch logout event for other components
      window.dispatchEvent(new CustomEvent('userLoggedOut'));
    }
  }, [queryClient]);

  const login = handleLogin;
  
  const register = async (data: {
    name: string;
    email: string;
    password: string;
    role: 'user' | 'admin' | 'marketing_responsible';
    businessDomain?: string;
    clientSegment?: string;
    companyName?: string;
  }) => {
    try {
      // 1. Register the new user
      await authService.register({
        name: data.name,
        email: data.email,
        password: data.password,
        role: data.role || 'user',
        businessDomain: data.businessDomain || '',
        clientSegment: data.clientSegment || '',
        companyName: data.companyName,
      });
      // 2. Show a message to check email, do NOT auto-login
      // Optionally, you can set a state or use a notification system here
      // Example: setRegistrationSuccess(true);
      // Do NOT call handleLogin here
    } catch (error) {
      console.error('Registration failed:', error);
      throw error;
    }
  };
  
  const logout = handleLogout;

  // Auto-trigger visitor linking when user becomes authenticated
  useEffect(() => {
    const triggerVisitorLinking = async () => {
      if (user && user.role && !isError) {
        try {
          // Call the visitor linking endpoint
          const response = await fetch('/api/visitor/link-current', {
            method: 'POST',
            credentials: 'include', // Include cookies
            headers: {
              'Content-Type': 'application/json',
            },
          });

          if (response.ok) {
            console.log('✅ Visitor linking triggered successfully');
          } else {
            console.log('⚠️ Visitor linking failed:', response.status);
          }
        } catch (error) {
          console.log('⚠️ Visitor linking error:', error);
        }
      }
    };

    // Only trigger once when user becomes available
    if (user && !isLoading) {
      triggerVisitorLinking();
    }
  }, [user, isLoading, isError]);

  // Check if user is authenticated and has a valid role
  const isAuthenticated = useCallback(() => {
    // Check if we have a user object and it has a role
    const hasValidUser = !!(user && user.role);
    // Check if we have a valid auth token (for token-based auth)
    const hasValidToken = checkAuth();

    // For cookie-based authentication, if we have valid user data from the server,
    // consider the user authenticated even without a client-side token
    const isAuthenticatedViaCookie = hasValidUser && !isError;
    const isAuthenticatedViaToken = hasValidUser && hasValidToken && !isError;

    const finalAuthState = isAuthenticatedViaCookie || isAuthenticatedViaToken;



    return finalAuthState;
  }, [user, isError]);
  
  // Check if user has specific role
  const hasRole = useCallback((role: UserRole | string) => {
    if (!user || !user.role) return false;
    
    // Normalize roles for comparison
    const userRole = user.role.toLowerCase();
    const checkRole = role.toLowerCase();
    
    // Handle role aliases if needed
    const roleMap: Record<string, string[]> = {
      'admin': ['admin', 'administrator'],
      'marketing_responsible': ['marketing', 'marketing_responsible', 'marketing-manager'],
      'user': ['user', 'member', 'client']
    };
    
    // Check direct match
    if (userRole === checkRole) return true;
    
    // Check aliases
    for (const [key, aliases] of Object.entries(roleMap)) {
      if (key === checkRole && aliases.includes(userRole)) return true;
      if (userRole === key && aliases.includes(checkRole)) return true;
    }
    
    return false;
  }, [user]);

  const value = {
    user: user || null,
    isLoading: isLoading || isLoadingUser,
    isAuthenticated: () => isAuthenticated(),
    hasRole,
    login,
    register,
    logout,
    refetchUser,
    checkAuthStatus, // Expose this for ProtectedRoute
  };
  
  // Debug: Log auth state changes
  useEffect(() => {
    console.log('Auth state updated:', {
      user: user ? { id: user._id, email: user.email, role: user.role } : null,
      isLoading: isLoading || isLoadingUser,
      isAuthenticated: isAuthenticated(),
    });
  }, [user, isLoading, isLoadingUser, isAuthenticated]);

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

// Do NOT add export default in this file. Use only named exports for Vite Fast Refresh compatibility.
// Only use the following named exports:
// export { AuthProvider, useAuth };
// Remove duplicate export statements below
