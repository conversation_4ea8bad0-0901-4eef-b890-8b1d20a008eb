import{ba as U,r as l,j as e,bb as G,bc as N,bd as H,X as V,be as w,bf as C,bg as k,o as B,aG as X,R as _}from"./react-vendor-Dq0qSR31.js";import{d as o,w as q,I as x,m as i,x as J,y as K}from"./index-CasGuY6o.js";import{G as M}from"./utils-vendor-DSNVchvY.js";import"./vendor-OXu-rwpf.js";import"./state-vendor-DU4y5LsH.js";const Q=U,W=G,E=l.forwardRef(({className:r,...s},a)=>e.jsx(C,{className:o("fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",r),...s,ref:a}));<PERSON>.displayName=C.displayName;const Y=M("fixed z-50 gap-4 bg-background p-6 shadow-lg transition ease-in-out data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:duration-300 data-[state=open]:duration-500",{variants:{side:{top:"inset-x-0 top-0 border-b data-[state=closed]:slide-out-to-top data-[state=open]:slide-in-from-top",bottom:"inset-x-0 bottom-0 border-t data-[state=closed]:slide-out-to-bottom data-[state=open]:slide-in-from-bottom",left:"inset-y-0 left-0 h-full w-3/4 border-r data-[state=closed]:slide-out-to-left data-[state=open]:slide-in-from-left sm:max-w-sm",right:"inset-y-0 right-0 h-full w-3/4  border-l data-[state=closed]:slide-out-to-right data-[state=open]:slide-in-from-right sm:max-w-sm"}},defaultVariants:{side:"right"}}),R=l.forwardRef(({side:r="right",className:s,children:a,...d},g)=>e.jsxs(W,{children:[e.jsx(E,{}),e.jsxs(N,{ref:g,className:o(Y({side:r}),s),...d,children:[a,e.jsxs(H,{className:"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-secondary",children:[e.jsx(V,{className:"h-4 w-4"}),e.jsx("span",{className:"sr-only",children:"Close"})]})]})]}));R.displayName=N.displayName;const D=({className:r,...s})=>e.jsx("div",{className:o("flex flex-col space-y-2 text-center sm:text-left",r),...s});D.displayName="SheetHeader";const P=({className:r,...s})=>e.jsx("div",{className:o("flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2",r),...s});P.displayName="SheetFooter";const I=l.forwardRef(({className:r,...s},a)=>e.jsx(w,{ref:a,className:o("text-lg font-semibold text-foreground",r),...s}));I.displayName=w.displayName;const Z=l.forwardRef(({className:r,...s},a)=>e.jsx(k,{ref:a,className:o("text-sm text-muted-foreground",r),...s}));Z.displayName=k.displayName;const $={title:"",description:"",image:"",subServices:[]},le=()=>{const r=B(),{id:s}=X(),[a,d]=l.useState($),[g,c]=l.useState(!1),[h,y]=l.useState(null),[n,u]=l.useState({title:"",description:"",image:""}),[f,b]=l.useState(!1),[S,p]=l.useState(null);_.useEffect(()=>{s&&(b(!0),q(s).then(t=>d(t.data)).catch(()=>p("Failed to load service")).finally(()=>b(!1)))},[s]);const v=t=>{d({...a,[t.target.name]:t.target.value})},A=()=>{y(null),u({title:"",description:"",image:""}),c(!0)},L=t=>{y(t),u(a.subServices?.[t]||{title:"",description:"",image:""}),c(!0)},j=t=>{u({...n,[t.target.name]:t.target.value})},O=()=>{if(!n.title.trim())return;let t=[...a.subServices||[]];h!==null?t[h]=n:t.push(n),d({...a,subServices:t}),c(!1)},z=t=>{d({...a,subServices:(a.subServices||[]).filter((m,T)=>T!==t)})},F=async()=>{b(!0),p(null);try{const t={title:a.title,description:a.description,image:a.image,subServices:a.subServices};s?await J(s,t):await K(t),r("/dashboard/services")}catch{p("Failed to save service")}finally{b(!1)}};return e.jsxs("div",{className:"min-h-screen bg-brand-grey-950 flex flex-col items-center py-10",children:[e.jsxs("div",{className:"w-full max-w-2xl bg-brand-grey-900 rounded-2xl shadow-2xl p-8 border border-brand-grey-800 flex flex-col gap-8",children:[e.jsx("h1",{className:"text-2xl font-bold text-brand-accent-100 mb-4",children:s?"Edit Service":"Add Service"}),e.jsxs("div",{className:"flex flex-col gap-6",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block mb-1 font-semibold text-brand-accent-100 text-lg",children:"Title"}),e.jsx(x,{name:"title",value:a.title,onChange:v,placeholder:"Enter service title",className:"bg-brand-grey-900 border-brand-grey-700 text-brand-white"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block mb-1 font-semibold text-brand-accent-100 text-lg",children:"Description"}),e.jsx("textarea",{name:"description",value:a.description,onChange:v,placeholder:"Enter service description",className:"bg-brand-grey-900 border-brand-grey-700 text-brand-white w-full rounded-md p-2",rows:4})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block mb-1 font-semibold text-brand-accent-100 text-lg",children:"Image URL"}),e.jsx(x,{name:"image",value:a.image,onChange:v,placeholder:"Paste image URL here (optional)",className:"bg-brand-grey-900 border-brand-grey-700 text-brand-white"}),a.image&&e.jsx("img",{src:a.image,alt:"Preview",className:"mt-2 max-h-40 rounded-xl border border-brand-grey-700 w-full object-cover shadow-lg"})]})]}),e.jsxs("div",{className:"flex flex-col gap-4 mt-8",children:[e.jsxs("div",{className:"flex items-center justify-between mb-2",children:[e.jsx("h2",{className:"text-xl font-bold text-brand-accent-100",children:"Sub-Services"}),e.jsx(i,{type:"button",onClick:A,className:"bg-brand-accent-100 text-brand-black font-semibold px-4 py-2 rounded-lg shadow hover:bg-brand-accent-200 transition",children:"Add Sub-Service"})]}),(a.subServices||[]).length===0&&e.jsx("div",{className:"text-brand-grey-400 text-sm",children:"No sub-services yet. Add your first one!"}),e.jsx("div",{className:"flex flex-col gap-3",children:(a.subServices||[]).map((t,m)=>e.jsxs("div",{className:"bg-brand-grey-800 border border-brand-grey-700 rounded-lg p-4 flex items-center gap-4",children:[t.image&&e.jsx("img",{src:t.image,alt:t.title,className:"w-12 h-12 object-cover rounded border border-brand-grey-700"}),e.jsxs("div",{className:"flex-1",children:[e.jsx("div",{className:"font-semibold text-brand-white",children:t.title}),e.jsx("div",{className:"text-brand-grey-300 text-xs",children:t.description})]}),e.jsx(i,{type:"button",size:"sm",variant:"secondary",onClick:()=>L(m),children:"Edit"}),e.jsx(i,{type:"button",size:"sm",variant:"destructive",onClick:()=>z(m),children:"Delete"})]},m))})]}),e.jsxs("div",{className:"flex flex-row gap-4 justify-end mt-8",children:[e.jsx(i,{type:"button",variant:"secondary",onClick:()=>r("/dashboard/services"),disabled:f,children:"Cancel"}),e.jsx(i,{type:"button",onClick:F,className:"bg-brand-accent-100 text-brand-black font-bold",disabled:f,children:f?"Saving...":"Save Service"})]}),S&&e.jsx("div",{className:"text-red-500 mt-2 text-center",children:S})]}),e.jsx(Q,{open:g,onOpenChange:c,children:e.jsxs(R,{side:"left",className:"bg-brand-grey-900 border-r border-brand-grey-800 max-w-md w-full",children:[e.jsx(D,{children:e.jsx(I,{children:h!==null?"Edit Sub-Service":"Add Sub-Service"})}),e.jsxs("div",{className:"flex-1 flex flex-col gap-6 p-6",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block mb-1 font-semibold text-brand-accent-100 text-lg",children:"Title"}),e.jsx(x,{name:"title",value:n.title,onChange:j,placeholder:"Enter sub-service title",className:"bg-brand-grey-900 border-brand-grey-700 text-brand-white"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block mb-1 font-semibold text-brand-accent-100 text-lg",children:"Description"}),e.jsx("textarea",{name:"description",value:n.description,onChange:j,placeholder:"Enter sub-service description",className:"bg-brand-grey-900 border-brand-grey-700 text-brand-white w-full rounded-md p-2",rows:3})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block mb-1 font-semibold text-brand-accent-100 text-lg",children:"Image URL"}),e.jsx(x,{name:"image",value:n.image,onChange:j,placeholder:"Paste image URL here (optional)",className:"bg-brand-grey-900 border-brand-grey-700 text-brand-white"}),n.image&&e.jsx("img",{src:n.image,alt:"Preview",className:"mt-2 max-h-32 rounded-xl border border-brand-grey-700 w-full object-cover shadow"})]})]}),e.jsxs(P,{children:[e.jsx(i,{type:"button",variant:"secondary",onClick:()=>c(!1),children:"Cancel"}),e.jsx(i,{type:"button",onClick:O,className:"bg-brand-accent-100 text-brand-black font-bold",children:"Save"})]})]})})]})};export{le as default};
