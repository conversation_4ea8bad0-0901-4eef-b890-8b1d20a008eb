import express from 'express';
import { v4 as uuidv4 } from 'uuid';
import { optionalAuth } from '../../shared/middleware/auth.js';
import {
  getCart,
  addToCart,
  removeFromCart,
  updateCartItem,
  clearCart,
  getCartSummary
} from './cartController.js';

const router = express.Router();

// Middleware to ensure session ID
const ensureSessionId = (req, res, next) => {
  if (!req.sessionID && !req.headers['session-id']) {
    req.headers['session-id'] = uuidv4();
  }
  next();
};

// Apply session middleware and optional auth to all routes
router.use(ensureSessionId);
router.use(optionalAuth); // This will set req.user if authenticated, but won't require auth

// Routes
// GET /api/cart - Get user's cart
router.get('/', getCart);

// GET /api/cart/summary - Get cart summary (item count, total)
router.get('/summary', getCartSummary);

// POST /api/cart/add - Add item to cart
router.post('/add', addToCart);

// PUT /api/cart/update/:serviceId/:subServiceId? - Update item quantity
router.put('/update/:serviceId/:subServiceId?', updateCartItem);

// DELETE /api/cart/remove/:serviceId/:subServiceId? - Remove item from cart
router.delete('/remove/:serviceId/:subServiceName?', removeFromCart);

// DELETE /api/cart/clear - Clear entire cart
router.delete('/clear', clearCart);

export default router;
