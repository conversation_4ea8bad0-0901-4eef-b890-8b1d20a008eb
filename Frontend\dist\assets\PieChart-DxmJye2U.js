var Rl=Math.pow;import{ab as oe,r as D,aa as Q,k as P}from"./index-hEW_vQ3f.js";import{w as Gn,n as Vn,t as tt,x as cb,l as Zv,y as sb,b as qr,q as lb,z as Jv,a as Rs,A as Qv,B as ey,C as fb,D as hb,E as ty,F as pb,G as db,h as vb,e as ha,o as yb,H as mb,r as gb,f as bb,v as xb,I as wb,J as Ob}from"./isEqual-CATH4cxC.js";var Ga,Dl;function Fr(){if(Dl)return Ga;Dl=1;var e=Gn(),t=Vn(),r="[object Symbol]";function n(i){return typeof i=="symbol"||t(i)&&e(i)==r}return Ga=n,Ga}var Va,Bl;function Ds(){if(Bl)return Va;Bl=1;var e=tt(),t=Fr(),r=/\.|\[(?:[^[\]]*|(["'])(?:(?!\1)[^\\]|\\.)*?\1)\]/,n=/^\w*$/;function i(a,o){if(e(a))return!1;var u=typeof a;return u=="number"||u=="symbol"||u=="boolean"||a==null||t(a)?!0:n.test(a)||!r.test(a)||o!=null&&a in Object(o)}return Va=i,Va}var Xa,Ll;function ry(){if(Ll)return Xa;Ll=1;var e=cb(),t="Expected a function";function r(n,i){if(typeof n!="function"||i!=null&&typeof i!="function")throw new TypeError(t);var a=function(){var o=arguments,u=i?i.apply(this,o):o[0],c=a.cache;if(c.has(u))return c.get(u);var s=n.apply(this,o);return a.cache=c.set(u,s)||c,s};return a.cache=new(r.Cache||e),a}return r.Cache=e,Xa=r,Xa}var Ya,ql;function Sb(){if(ql)return Ya;ql=1;var e=ry(),t=500;function r(n){var i=e(n,function(o){return a.size===t&&a.clear(),o}),a=i.cache;return i}return Ya=r,Ya}var Za,Fl;function Ab(){if(Fl)return Za;Fl=1;var e=Sb(),t=/[^.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|$))/g,r=/\\(\\)?/g,n=e(function(i){var a=[];return i.charCodeAt(0)===46&&a.push(""),i.replace(t,function(o,u,c,s){a.push(c?s.replace(r,"$1"):u||o)}),a});return Za=n,Za}var Ja,Wl;function Bs(){if(Wl)return Ja;Wl=1;function e(t,r){for(var n=-1,i=t==null?0:t.length,a=Array(i);++n<i;)a[n]=r(t[n],n,t);return a}return Ja=e,Ja}var Qa,zl;function Pb(){if(zl)return Qa;zl=1;var e=Zv(),t=Bs(),r=tt(),n=Fr(),i=e?e.prototype:void 0,a=i?i.toString:void 0;function o(u){if(typeof u=="string")return u;if(r(u))return t(u,o)+"";if(n(u))return a?a.call(u):"";var c=u+"";return c=="0"&&1/u==-1/0?"-0":c}return Qa=o,Qa}var eo,Ul;function ny(){if(Ul)return eo;Ul=1;var e=Pb();function t(r){return r==null?"":e(r)}return eo=t,eo}var to,Hl;function iy(){if(Hl)return to;Hl=1;var e=tt(),t=Ds(),r=Ab(),n=ny();function i(a,o){return e(a)?a:t(a,o)?[a]:r(n(a))}return to=i,to}var ro,Kl;function pa(){if(Kl)return ro;Kl=1;var e=Fr();function t(r){if(typeof r=="string"||e(r))return r;var n=r+"";return n=="0"&&1/r==-1/0?"-0":n}return ro=t,ro}var no,Gl;function Ls(){if(Gl)return no;Gl=1;var e=iy(),t=pa();function r(n,i){i=e(i,n);for(var a=0,o=i.length;n!=null&&a<o;)n=n[t(i[a++])];return a&&a==o?n:void 0}return no=r,no}var io,Vl;function ay(){if(Vl)return io;Vl=1;var e=Ls();function t(r,n,i){var a=r==null?void 0:e(r,n);return a===void 0?i:a}return io=t,io}var _b=ay();const We=oe(_b);var ao,Xl;function Eb(){if(Xl)return ao;Xl=1;function e(t){return t==null}return ao=e,ao}var Tb=Eb();const Z=oe(Tb);var oo,Yl;function jb(){if(Yl)return oo;Yl=1;var e=Gn(),t=tt(),r=Vn(),n="[object String]";function i(a){return typeof a=="string"||!t(a)&&r(a)&&e(a)==n}return oo=i,oo}var $b=jb();const Vt=oe($b);var Mb=sb();const X=oe(Mb);var Ib=qr();const Wr=oe(Ib);var uo={exports:{}},re={};/**
 * @license React
 * react-is.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Zl;function Cb(){if(Zl)return re;Zl=1;var e=Symbol.for("react.element"),t=Symbol.for("react.portal"),r=Symbol.for("react.fragment"),n=Symbol.for("react.strict_mode"),i=Symbol.for("react.profiler"),a=Symbol.for("react.provider"),o=Symbol.for("react.context"),u=Symbol.for("react.server_context"),c=Symbol.for("react.forward_ref"),s=Symbol.for("react.suspense"),f=Symbol.for("react.suspense_list"),l=Symbol.for("react.memo"),h=Symbol.for("react.lazy"),d=Symbol.for("react.offscreen"),y;y=Symbol.for("react.module.reference");function v(p){if(typeof p=="object"&&p!==null){var b=p.$$typeof;switch(b){case e:switch(p=p.type,p){case r:case i:case n:case s:case f:return p;default:switch(p=p&&p.$$typeof,p){case u:case o:case c:case h:case l:case a:return p;default:return b}}case t:return b}}}return re.ContextConsumer=o,re.ContextProvider=a,re.Element=e,re.ForwardRef=c,re.Fragment=r,re.Lazy=h,re.Memo=l,re.Portal=t,re.Profiler=i,re.StrictMode=n,re.Suspense=s,re.SuspenseList=f,re.isAsyncMode=function(){return!1},re.isConcurrentMode=function(){return!1},re.isContextConsumer=function(p){return v(p)===o},re.isContextProvider=function(p){return v(p)===a},re.isElement=function(p){return typeof p=="object"&&p!==null&&p.$$typeof===e},re.isForwardRef=function(p){return v(p)===c},re.isFragment=function(p){return v(p)===r},re.isLazy=function(p){return v(p)===h},re.isMemo=function(p){return v(p)===l},re.isPortal=function(p){return v(p)===t},re.isProfiler=function(p){return v(p)===i},re.isStrictMode=function(p){return v(p)===n},re.isSuspense=function(p){return v(p)===s},re.isSuspenseList=function(p){return v(p)===f},re.isValidElementType=function(p){return typeof p=="string"||typeof p=="function"||p===r||p===i||p===n||p===s||p===f||p===d||typeof p=="object"&&p!==null&&(p.$$typeof===h||p.$$typeof===l||p.$$typeof===a||p.$$typeof===o||p.$$typeof===c||p.$$typeof===y||p.getModuleId!==void 0)},re.typeOf=v,re}var Jl;function kb(){return Jl||(Jl=1,uo.exports=Cb()),uo.exports}var Nb=kb(),co,Ql;function oy(){if(Ql)return co;Ql=1;var e=Gn(),t=Vn(),r="[object Number]";function n(i){return typeof i=="number"||t(i)&&e(i)==r}return co=n,co}var so,ef;function Rb(){if(ef)return so;ef=1;var e=oy();function t(r){return e(r)&&r!=+r}return so=t,so}var Db=Rb();const Xn=oe(Db);var Bb=oy();const Lb=oe(Bb);var Ie=function(t){return t===0?0:t>0?1:-1},Wt=function(t){return Vt(t)&&t.indexOf("%")===t.length-1},B=function(t){return Lb(t)&&!Xn(t)},qb=function(t){return Z(t)},Se=function(t){return B(t)||Vt(t)},Fb=0,Yn=function(t){var r=++Fb;return"".concat(t||"").concat(r)},Ce=function(t,r){var n=arguments.length>2&&arguments[2]!==void 0?arguments[2]:0,i=arguments.length>3&&arguments[3]!==void 0?arguments[3]:!1;if(!B(t)&&!Vt(t))return n;var a;if(Wt(t)){var o=t.indexOf("%");a=r*parseFloat(t.slice(0,o))/100}else a=+t;return Xn(a)&&(a=n),i&&a>r&&(a=r),a},Pt=function(t){if(!t)return null;var r=Object.keys(t);return r&&r.length?t[r[0]]:null},Wb=function(t){if(!Array.isArray(t))return!1;for(var r=t.length,n={},i=0;i<r;i++)if(!n[t[i]])n[t[i]]=!0;else return!0;return!1},St=function(t,r){return B(t)&&B(r)?function(n){return t+n*(r-t)}:function(){return r}};function yi(e,t,r){return!e||!e.length?null:e.find(function(n){return n&&(typeof t=="function"?t(n):We(n,t))===r})}var zC=function(t){if(!t||!t.length)return null;for(var r=t.length,n=0,i=0,a=0,o=0,u=1/0,c=-1/0,s=0,f=0,l=0;l<r;l++)s=t[l].cx||0,f=t[l].cy||0,n+=s,i+=f,a+=s*f,o+=s*s,u=Math.min(u,s),c=Math.max(c,s);var h=r*o!==n*n?(r*a-n*i)/(r*o-n*n):0;return{xmin:u,xmax:c,a:h,b:(i-h*n)/r}},zb=function(t,r){return B(t)&&B(r)?t-r:Vt(t)&&Vt(r)?t.localeCompare(r):t instanceof Date&&r instanceof Date?t.getTime()-r.getTime():String(t).localeCompare(String(r))};function hr(e,t){for(var r in e)if({}.hasOwnProperty.call(e,r)&&(!{}.hasOwnProperty.call(t,r)||e[r]!==t[r]))return!1;for(var n in t)if({}.hasOwnProperty.call(t,n)&&!{}.hasOwnProperty.call(e,n))return!1;return!0}function yc(e){"@babel/helpers - typeof";return yc=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},yc(e)}var Ub=["viewBox","children"],Hb=["aria-activedescendant","aria-atomic","aria-autocomplete","aria-busy","aria-checked","aria-colcount","aria-colindex","aria-colspan","aria-controls","aria-current","aria-describedby","aria-details","aria-disabled","aria-errormessage","aria-expanded","aria-flowto","aria-haspopup","aria-hidden","aria-invalid","aria-keyshortcuts","aria-label","aria-labelledby","aria-level","aria-live","aria-modal","aria-multiline","aria-multiselectable","aria-orientation","aria-owns","aria-placeholder","aria-posinset","aria-pressed","aria-readonly","aria-relevant","aria-required","aria-roledescription","aria-rowcount","aria-rowindex","aria-rowspan","aria-selected","aria-setsize","aria-sort","aria-valuemax","aria-valuemin","aria-valuenow","aria-valuetext","className","color","height","id","lang","max","media","method","min","name","style","target","width","role","tabIndex","accentHeight","accumulate","additive","alignmentBaseline","allowReorder","alphabetic","amplitude","arabicForm","ascent","attributeName","attributeType","autoReverse","azimuth","baseFrequency","baselineShift","baseProfile","bbox","begin","bias","by","calcMode","capHeight","clip","clipPath","clipPathUnits","clipRule","colorInterpolation","colorInterpolationFilters","colorProfile","colorRendering","contentScriptType","contentStyleType","cursor","cx","cy","d","decelerate","descent","diffuseConstant","direction","display","divisor","dominantBaseline","dur","dx","dy","edgeMode","elevation","enableBackground","end","exponent","externalResourcesRequired","fill","fillOpacity","fillRule","filter","filterRes","filterUnits","floodColor","floodOpacity","focusable","fontFamily","fontSize","fontSizeAdjust","fontStretch","fontStyle","fontVariant","fontWeight","format","from","fx","fy","g1","g2","glyphName","glyphOrientationHorizontal","glyphOrientationVertical","glyphRef","gradientTransform","gradientUnits","hanging","horizAdvX","horizOriginX","href","ideographic","imageRendering","in2","in","intercept","k1","k2","k3","k4","k","kernelMatrix","kernelUnitLength","kerning","keyPoints","keySplines","keyTimes","lengthAdjust","letterSpacing","lightingColor","limitingConeAngle","local","markerEnd","markerHeight","markerMid","markerStart","markerUnits","markerWidth","mask","maskContentUnits","maskUnits","mathematical","mode","numOctaves","offset","opacity","operator","order","orient","orientation","origin","overflow","overlinePosition","overlineThickness","paintOrder","panose1","pathLength","patternContentUnits","patternTransform","patternUnits","pointerEvents","pointsAtX","pointsAtY","pointsAtZ","preserveAlpha","preserveAspectRatio","primitiveUnits","r","radius","refX","refY","renderingIntent","repeatCount","repeatDur","requiredExtensions","requiredFeatures","restart","result","rotate","rx","ry","seed","shapeRendering","slope","spacing","specularConstant","specularExponent","speed","spreadMethod","startOffset","stdDeviation","stemh","stemv","stitchTiles","stopColor","stopOpacity","strikethroughPosition","strikethroughThickness","string","stroke","strokeDasharray","strokeDashoffset","strokeLinecap","strokeLinejoin","strokeMiterlimit","strokeOpacity","strokeWidth","surfaceScale","systemLanguage","tableValues","targetX","targetY","textAnchor","textDecoration","textLength","textRendering","to","transform","u1","u2","underlinePosition","underlineThickness","unicode","unicodeBidi","unicodeRange","unitsPerEm","vAlphabetic","values","vectorEffect","version","vertAdvY","vertOriginX","vertOriginY","vHanging","vIdeographic","viewTarget","visibility","vMathematical","widths","wordSpacing","writingMode","x1","x2","x","xChannelSelector","xHeight","xlinkActuate","xlinkArcrole","xlinkHref","xlinkRole","xlinkShow","xlinkTitle","xlinkType","xmlBase","xmlLang","xmlns","xmlnsXlink","xmlSpace","y1","y2","y","yChannelSelector","z","zoomAndPan","ref","key","angle"],tf=["points","pathLength"],lo={svg:Ub,polygon:tf,polyline:tf},qs=["dangerouslySetInnerHTML","onCopy","onCopyCapture","onCut","onCutCapture","onPaste","onPasteCapture","onCompositionEnd","onCompositionEndCapture","onCompositionStart","onCompositionStartCapture","onCompositionUpdate","onCompositionUpdateCapture","onFocus","onFocusCapture","onBlur","onBlurCapture","onChange","onChangeCapture","onBeforeInput","onBeforeInputCapture","onInput","onInputCapture","onReset","onResetCapture","onSubmit","onSubmitCapture","onInvalid","onInvalidCapture","onLoad","onLoadCapture","onError","onErrorCapture","onKeyDown","onKeyDownCapture","onKeyPress","onKeyPressCapture","onKeyUp","onKeyUpCapture","onAbort","onAbortCapture","onCanPlay","onCanPlayCapture","onCanPlayThrough","onCanPlayThroughCapture","onDurationChange","onDurationChangeCapture","onEmptied","onEmptiedCapture","onEncrypted","onEncryptedCapture","onEnded","onEndedCapture","onLoadedData","onLoadedDataCapture","onLoadedMetadata","onLoadedMetadataCapture","onLoadStart","onLoadStartCapture","onPause","onPauseCapture","onPlay","onPlayCapture","onPlaying","onPlayingCapture","onProgress","onProgressCapture","onRateChange","onRateChangeCapture","onSeeked","onSeekedCapture","onSeeking","onSeekingCapture","onStalled","onStalledCapture","onSuspend","onSuspendCapture","onTimeUpdate","onTimeUpdateCapture","onVolumeChange","onVolumeChangeCapture","onWaiting","onWaitingCapture","onAuxClick","onAuxClickCapture","onClick","onClickCapture","onContextMenu","onContextMenuCapture","onDoubleClick","onDoubleClickCapture","onDrag","onDragCapture","onDragEnd","onDragEndCapture","onDragEnter","onDragEnterCapture","onDragExit","onDragExitCapture","onDragLeave","onDragLeaveCapture","onDragOver","onDragOverCapture","onDragStart","onDragStartCapture","onDrop","onDropCapture","onMouseDown","onMouseDownCapture","onMouseEnter","onMouseLeave","onMouseMove","onMouseMoveCapture","onMouseOut","onMouseOutCapture","onMouseOver","onMouseOverCapture","onMouseUp","onMouseUpCapture","onSelect","onSelectCapture","onTouchCancel","onTouchCancelCapture","onTouchEnd","onTouchEndCapture","onTouchMove","onTouchMoveCapture","onTouchStart","onTouchStartCapture","onPointerDown","onPointerDownCapture","onPointerMove","onPointerMoveCapture","onPointerUp","onPointerUpCapture","onPointerCancel","onPointerCancelCapture","onPointerEnter","onPointerEnterCapture","onPointerLeave","onPointerLeaveCapture","onPointerOver","onPointerOverCapture","onPointerOut","onPointerOutCapture","onGotPointerCapture","onGotPointerCaptureCapture","onLostPointerCapture","onLostPointerCaptureCapture","onScroll","onScrollCapture","onWheel","onWheelCapture","onAnimationStart","onAnimationStartCapture","onAnimationEnd","onAnimationEndCapture","onAnimationIteration","onAnimationIterationCapture","onTransitionEnd","onTransitionEndCapture"],mi=function(t,r){if(!t||typeof t=="function"||typeof t=="boolean")return null;var n=t;if(D.isValidElement(t)&&(n=t.props),!Wr(n))return null;var i={};return Object.keys(n).forEach(function(a){qs.includes(a)&&(i[a]=r||function(o){return n[a](n,o)})}),i},Kb=function(t,r,n){return function(i){return t(r,n,i),null}},Xt=function(t,r,n){if(!Wr(t)||yc(t)!=="object")return null;var i=null;return Object.keys(t).forEach(function(a){var o=t[a];qs.includes(a)&&typeof o=="function"&&(i||(i={}),i[a]=Kb(o,r,n))}),i},Gb=["children"],Vb=["children"];function rf(e,t){if(e==null)return{};var r=Xb(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function Xb(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}function mc(e){"@babel/helpers - typeof";return mc=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},mc(e)}var nf={click:"onClick",mousedown:"onMouseDown",mouseup:"onMouseUp",mouseover:"onMouseOver",mousemove:"onMouseMove",mouseout:"onMouseOut",mouseenter:"onMouseEnter",mouseleave:"onMouseLeave",touchcancel:"onTouchCancel",touchend:"onTouchEnd",touchmove:"onTouchMove",touchstart:"onTouchStart",contextmenu:"onContextMenu",dblclick:"onDoubleClick"},dt=function(t){return typeof t=="string"?t:t?t.displayName||t.name||"Component":""},af=null,fo=null,da=function e(t){if(t===af&&Array.isArray(fo))return fo;var r=[];return D.Children.forEach(t,function(n){Z(n)||(Nb.isFragment(n)?r=r.concat(e(n.props.children)):r.push(n))}),fo=r,af=t,r};function Ve(e,t){var r=[],n=[];return Array.isArray(t)?n=t.map(function(i){return dt(i)}):n=[dt(t)],da(e).forEach(function(i){var a=We(i,"type.displayName")||We(i,"type.name");n.indexOf(a)!==-1&&r.push(i)}),r}function qe(e,t){var r=Ve(e,t);return r&&r[0]}var of=function(t){if(!t||!t.props)return!1;var r=t.props,n=r.width,i=r.height;return!(!B(n)||n<=0||!B(i)||i<=0)},Yb=["a","altGlyph","altGlyphDef","altGlyphItem","animate","animateColor","animateMotion","animateTransform","circle","clipPath","color-profile","cursor","defs","desc","ellipse","feBlend","feColormatrix","feComponentTransfer","feComposite","feConvolveMatrix","feDiffuseLighting","feDisplacementMap","feDistantLight","feFlood","feFuncA","feFuncB","feFuncG","feFuncR","feGaussianBlur","feImage","feMerge","feMergeNode","feMorphology","feOffset","fePointLight","feSpecularLighting","feSpotLight","feTile","feTurbulence","filter","font","font-face","font-face-format","font-face-name","font-face-url","foreignObject","g","glyph","glyphRef","hkern","image","line","lineGradient","marker","mask","metadata","missing-glyph","mpath","path","pattern","polygon","polyline","radialGradient","rect","script","set","stop","style","svg","switch","symbol","text","textPath","title","tref","tspan","use","view","vkern"],uy=function(t){return t&&t.type&&Vt(t.type)&&Yb.indexOf(t.type)>=0},UC=function(t){return t&&mc(t)==="object"&&"clipDot"in t},Zb=function(t,r,n,i){var a,o=(a=lo==null?void 0:lo[i])!==null&&a!==void 0?a:[];return r.startsWith("data-")||!X(t)&&(i&&o.includes(r)||Hb.includes(r))||n&&qs.includes(r)},HC=function(t){var r=[];return da(t).forEach(function(n){uy(n)&&r.push(n)}),r},V=function(t,r,n){if(!t||typeof t=="function"||typeof t=="boolean")return null;var i=t;if(D.isValidElement(t)&&(i=t.props),!Wr(i))return null;var a={};return Object.keys(i).forEach(function(o){var u;Zb((u=i)===null||u===void 0?void 0:u[o],o,r,n)&&(a[o]=i[o])}),a},gc=function e(t,r){if(t===r)return!0;var n=D.Children.count(t);if(n!==D.Children.count(r))return!1;if(n===0)return!0;if(n===1)return uf(Array.isArray(t)?t[0]:t,Array.isArray(r)?r[0]:r);for(var i=0;i<n;i++){var a=t[i],o=r[i];if(Array.isArray(a)||Array.isArray(o)){if(!e(a,o))return!1}else if(!uf(a,o))return!1}return!0},uf=function(t,r){if(Z(t)&&Z(r))return!0;if(!Z(t)&&!Z(r)){var n=t.props||{},i=n.children,a=rf(n,Gb),o=r.props||{},u=o.children,c=rf(o,Vb);return i&&u?hr(a,c)&&gc(i,u):!i&&!u?hr(a,c):!1}return!1},cf=function(t,r){var n=[],i={};return da(t).forEach(function(a,o){if(uy(a))n.push(a);else if(a){var u=dt(a.type),c=r[u]||{},s=c.handler,f=c.once;if(s&&(!f||!i[u])){var l=s(a,u,o);n.push(l),i[u]=!0}}}),n},Jb=function(t){var r=t&&t.type;return r&&nf[r]?nf[r]:null},Qb=function(t,r){return da(r).indexOf(t)},e0=["children","width","height","viewBox","className","style","title","desc"];function bc(){return bc=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},bc.apply(this,arguments)}function t0(e,t){if(e==null)return{};var r=r0(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function r0(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}function xc(e){var t=e.children,r=e.width,n=e.height,i=e.viewBox,a=e.className,o=e.style,u=e.title,c=e.desc,s=t0(e,e0),f=i||{width:r,height:n,x:0,y:0},l=Q("recharts-surface",a);return P.createElement("svg",bc({},V(s,!0,"svg"),{className:l,width:r,height:n,style:o,viewBox:"".concat(f.x," ").concat(f.y," ").concat(f.width," ").concat(f.height)}),P.createElement("title",null,u),P.createElement("desc",null,c),t)}var n0=["children","className"];function wc(){return wc=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},wc.apply(this,arguments)}function i0(e,t){if(e==null)return{};var r=a0(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function a0(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}var ie=P.forwardRef(function(e,t){var r=e.children,n=e.className,i=i0(e,n0),a=Q("recharts-layer",n);return P.createElement("g",wc({className:a},V(i,!0),{ref:t}),r)}),et=function(t,r){for(var n=arguments.length,i=new Array(n>2?n-2:0),a=2;a<n;a++)i[a-2]=arguments[a]},ho,sf;function o0(){if(sf)return ho;sf=1;function e(t,r,n){var i=-1,a=t.length;r<0&&(r=-r>a?0:a+r),n=n>a?a:n,n<0&&(n+=a),a=r>n?0:n-r>>>0,r>>>=0;for(var o=Array(a);++i<a;)o[i]=t[i+r];return o}return ho=e,ho}var po,lf;function u0(){if(lf)return po;lf=1;var e=o0();function t(r,n,i){var a=r.length;return i=i===void 0?a:i,!n&&i>=a?r:e(r,n,i)}return po=t,po}var vo,ff;function cy(){if(ff)return vo;ff=1;var e="\\ud800-\\udfff",t="\\u0300-\\u036f",r="\\ufe20-\\ufe2f",n="\\u20d0-\\u20ff",i=t+r+n,a="\\ufe0e\\ufe0f",o="\\u200d",u=RegExp("["+o+e+i+a+"]");function c(s){return u.test(s)}return vo=c,vo}var yo,hf;function c0(){if(hf)return yo;hf=1;function e(t){return t.split("")}return yo=e,yo}var mo,pf;function s0(){if(pf)return mo;pf=1;var e="\\ud800-\\udfff",t="\\u0300-\\u036f",r="\\ufe20-\\ufe2f",n="\\u20d0-\\u20ff",i=t+r+n,a="\\ufe0e\\ufe0f",o="["+e+"]",u="["+i+"]",c="\\ud83c[\\udffb-\\udfff]",s="(?:"+u+"|"+c+")",f="[^"+e+"]",l="(?:\\ud83c[\\udde6-\\uddff]){2}",h="[\\ud800-\\udbff][\\udc00-\\udfff]",d="\\u200d",y=s+"?",v="["+a+"]?",p="(?:"+d+"(?:"+[f,l,h].join("|")+")"+v+y+")*",b=v+y+p,w="(?:"+[f+u+"?",u,l,h,o].join("|")+")",g=RegExp(c+"(?="+c+")|"+w+b,"g");function O(m){return m.match(g)||[]}return mo=O,mo}var go,df;function l0(){if(df)return go;df=1;var e=c0(),t=cy(),r=s0();function n(i){return t(i)?r(i):e(i)}return go=n,go}var bo,vf;function f0(){if(vf)return bo;vf=1;var e=u0(),t=cy(),r=l0(),n=ny();function i(a){return function(o){o=n(o);var u=t(o)?r(o):void 0,c=u?u[0]:o.charAt(0),s=u?e(u,1).join(""):o.slice(1);return c[a]()+s}}return bo=i,bo}var xo,yf;function h0(){if(yf)return xo;yf=1;var e=f0(),t=e("toUpperCase");return xo=t,xo}var p0=h0();const va=oe(p0);function se(e){return function(){return e}}const sy=Math.cos,gi=Math.sin,rt=Math.sqrt,bi=Math.PI,ya=2*bi,Oc=Math.PI,Sc=2*Oc,Lt=1e-6,d0=Sc-Lt;function ly(e){this._+=e[0];for(let t=1,r=e.length;t<r;++t)this._+=arguments[t]+e[t]}function v0(e){let t=Math.floor(e);if(!(t>=0))throw new Error(`invalid digits: ${e}`);if(t>15)return ly;const r=Rl(10,t);return function(n){this._+=n[0];for(let i=1,a=n.length;i<a;++i)this._+=Math.round(arguments[i]*r)/r+n[i]}}class y0{constructor(t){this._x0=this._y0=this._x1=this._y1=null,this._="",this._append=t==null?ly:v0(t)}moveTo(t,r){this._append`M${this._x0=this._x1=+t},${this._y0=this._y1=+r}`}closePath(){this._x1!==null&&(this._x1=this._x0,this._y1=this._y0,this._append`Z`)}lineTo(t,r){this._append`L${this._x1=+t},${this._y1=+r}`}quadraticCurveTo(t,r,n,i){this._append`Q${+t},${+r},${this._x1=+n},${this._y1=+i}`}bezierCurveTo(t,r,n,i,a,o){this._append`C${+t},${+r},${+n},${+i},${this._x1=+a},${this._y1=+o}`}arcTo(t,r,n,i,a){if(t=+t,r=+r,n=+n,i=+i,a=+a,a<0)throw new Error(`negative radius: ${a}`);let o=this._x1,u=this._y1,c=n-t,s=i-r,f=o-t,l=u-r,h=f*f+l*l;if(this._x1===null)this._append`M${this._x1=t},${this._y1=r}`;else if(h>Lt)if(!(Math.abs(l*c-s*f)>Lt)||!a)this._append`L${this._x1=t},${this._y1=r}`;else{let d=n-o,y=i-u,v=c*c+s*s,p=d*d+y*y,b=Math.sqrt(v),w=Math.sqrt(h),g=a*Math.tan((Oc-Math.acos((v+h-p)/(2*b*w)))/2),O=g/w,m=g/b;Math.abs(O-1)>Lt&&this._append`L${t+O*f},${r+O*l}`,this._append`A${a},${a},0,0,${+(l*d>f*y)},${this._x1=t+m*c},${this._y1=r+m*s}`}}arc(t,r,n,i,a,o){if(t=+t,r=+r,n=+n,o=!!o,n<0)throw new Error(`negative radius: ${n}`);let u=n*Math.cos(i),c=n*Math.sin(i),s=t+u,f=r+c,l=1^o,h=o?i-a:a-i;this._x1===null?this._append`M${s},${f}`:(Math.abs(this._x1-s)>Lt||Math.abs(this._y1-f)>Lt)&&this._append`L${s},${f}`,n&&(h<0&&(h=h%Sc+Sc),h>d0?this._append`A${n},${n},0,1,${l},${t-u},${r-c}A${n},${n},0,1,${l},${this._x1=s},${this._y1=f}`:h>Lt&&this._append`A${n},${n},0,${+(h>=Oc)},${l},${this._x1=t+n*Math.cos(a)},${this._y1=r+n*Math.sin(a)}`)}rect(t,r,n,i){this._append`M${this._x0=this._x1=+t},${this._y0=this._y1=+r}h${n=+n}v${+i}h${-n}Z`}toString(){return this._}}function Fs(e){let t=3;return e.digits=function(r){if(!arguments.length)return t;if(r==null)t=null;else{const n=Math.floor(r);if(!(n>=0))throw new RangeError(`invalid digits: ${r}`);t=n}return e},()=>new y0(t)}function Ws(e){return typeof e=="object"&&"length"in e?e:Array.from(e)}function fy(e){this._context=e}fy.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._point=0},lineEnd:function(){(this._line||this._line!==0&&this._point===1)&&this._context.closePath(),this._line=1-this._line},point:function(e,t){switch(e=+e,t=+t,this._point){case 0:this._point=1,this._line?this._context.lineTo(e,t):this._context.moveTo(e,t);break;case 1:this._point=2;default:this._context.lineTo(e,t);break}}};function ma(e){return new fy(e)}function hy(e){return e[0]}function py(e){return e[1]}function dy(e,t){var r=se(!0),n=null,i=ma,a=null,o=Fs(u);e=typeof e=="function"?e:e===void 0?hy:se(e),t=typeof t=="function"?t:t===void 0?py:se(t);function u(c){var s,f=(c=Ws(c)).length,l,h=!1,d;for(n==null&&(a=i(d=o())),s=0;s<=f;++s)!(s<f&&r(l=c[s],s,c))===h&&((h=!h)?a.lineStart():a.lineEnd()),h&&a.point(+e(l,s,c),+t(l,s,c));if(d)return a=null,d+""||null}return u.x=function(c){return arguments.length?(e=typeof c=="function"?c:se(+c),u):e},u.y=function(c){return arguments.length?(t=typeof c=="function"?c:se(+c),u):t},u.defined=function(c){return arguments.length?(r=typeof c=="function"?c:se(!!c),u):r},u.curve=function(c){return arguments.length?(i=c,n!=null&&(a=i(n)),u):i},u.context=function(c){return arguments.length?(c==null?n=a=null:a=i(n=c),u):n},u}function ri(e,t,r){var n=null,i=se(!0),a=null,o=ma,u=null,c=Fs(s);e=typeof e=="function"?e:e===void 0?hy:se(+e),t=typeof t=="function"?t:se(t===void 0?0:+t),r=typeof r=="function"?r:r===void 0?py:se(+r);function s(l){var h,d,y,v=(l=Ws(l)).length,p,b=!1,w,g=new Array(v),O=new Array(v);for(a==null&&(u=o(w=c())),h=0;h<=v;++h){if(!(h<v&&i(p=l[h],h,l))===b)if(b=!b)d=h,u.areaStart(),u.lineStart();else{for(u.lineEnd(),u.lineStart(),y=h-1;y>=d;--y)u.point(g[y],O[y]);u.lineEnd(),u.areaEnd()}b&&(g[h]=+e(p,h,l),O[h]=+t(p,h,l),u.point(n?+n(p,h,l):g[h],r?+r(p,h,l):O[h]))}if(w)return u=null,w+""||null}function f(){return dy().defined(i).curve(o).context(a)}return s.x=function(l){return arguments.length?(e=typeof l=="function"?l:se(+l),n=null,s):e},s.x0=function(l){return arguments.length?(e=typeof l=="function"?l:se(+l),s):e},s.x1=function(l){return arguments.length?(n=l==null?null:typeof l=="function"?l:se(+l),s):n},s.y=function(l){return arguments.length?(t=typeof l=="function"?l:se(+l),r=null,s):t},s.y0=function(l){return arguments.length?(t=typeof l=="function"?l:se(+l),s):t},s.y1=function(l){return arguments.length?(r=l==null?null:typeof l=="function"?l:se(+l),s):r},s.lineX0=s.lineY0=function(){return f().x(e).y(t)},s.lineY1=function(){return f().x(e).y(r)},s.lineX1=function(){return f().x(n).y(t)},s.defined=function(l){return arguments.length?(i=typeof l=="function"?l:se(!!l),s):i},s.curve=function(l){return arguments.length?(o=l,a!=null&&(u=o(a)),s):o},s.context=function(l){return arguments.length?(l==null?a=u=null:u=o(a=l),s):a},s}class vy{constructor(t,r){this._context=t,this._x=r}areaStart(){this._line=0}areaEnd(){this._line=NaN}lineStart(){this._point=0}lineEnd(){(this._line||this._line!==0&&this._point===1)&&this._context.closePath(),this._line=1-this._line}point(t,r){switch(t=+t,r=+r,this._point){case 0:{this._point=1,this._line?this._context.lineTo(t,r):this._context.moveTo(t,r);break}case 1:this._point=2;default:{this._x?this._context.bezierCurveTo(this._x0=(this._x0+t)/2,this._y0,this._x0,r,t,r):this._context.bezierCurveTo(this._x0,this._y0=(this._y0+r)/2,t,this._y0,t,r);break}}this._x0=t,this._y0=r}}function m0(e){return new vy(e,!0)}function g0(e){return new vy(e,!1)}const zs={draw(e,t){const r=rt(t/bi);e.moveTo(r,0),e.arc(0,0,r,0,ya)}},b0={draw(e,t){const r=rt(t/5)/2;e.moveTo(-3*r,-r),e.lineTo(-r,-r),e.lineTo(-r,-3*r),e.lineTo(r,-3*r),e.lineTo(r,-r),e.lineTo(3*r,-r),e.lineTo(3*r,r),e.lineTo(r,r),e.lineTo(r,3*r),e.lineTo(-r,3*r),e.lineTo(-r,r),e.lineTo(-3*r,r),e.closePath()}},yy=rt(1/3),x0=yy*2,w0={draw(e,t){const r=rt(t/x0),n=r*yy;e.moveTo(0,-r),e.lineTo(n,0),e.lineTo(0,r),e.lineTo(-n,0),e.closePath()}},O0={draw(e,t){const r=rt(t),n=-r/2;e.rect(n,n,r,r)}},S0=.8908130915292852,my=gi(bi/10)/gi(7*bi/10),A0=gi(ya/10)*my,P0=-sy(ya/10)*my,_0={draw(e,t){const r=rt(t*S0),n=A0*r,i=P0*r;e.moveTo(0,-r),e.lineTo(n,i);for(let a=1;a<5;++a){const o=ya*a/5,u=sy(o),c=gi(o);e.lineTo(c*r,-u*r),e.lineTo(u*n-c*i,c*n+u*i)}e.closePath()}},wo=rt(3),E0={draw(e,t){const r=-rt(t/(wo*3));e.moveTo(0,r*2),e.lineTo(-wo*r,-r),e.lineTo(wo*r,-r),e.closePath()}},ze=-.5,Ue=rt(3)/2,Ac=1/rt(12),T0=(Ac/2+1)*3,j0={draw(e,t){const r=rt(t/T0),n=r/2,i=r*Ac,a=n,o=r*Ac+r,u=-a,c=o;e.moveTo(n,i),e.lineTo(a,o),e.lineTo(u,c),e.lineTo(ze*n-Ue*i,Ue*n+ze*i),e.lineTo(ze*a-Ue*o,Ue*a+ze*o),e.lineTo(ze*u-Ue*c,Ue*u+ze*c),e.lineTo(ze*n+Ue*i,ze*i-Ue*n),e.lineTo(ze*a+Ue*o,ze*o-Ue*a),e.lineTo(ze*u+Ue*c,ze*c-Ue*u),e.closePath()}};function $0(e,t){let r=null,n=Fs(i);e=typeof e=="function"?e:se(e||zs),t=typeof t=="function"?t:se(t===void 0?64:+t);function i(){let a;if(r||(r=a=n()),e.apply(this,arguments).draw(r,+t.apply(this,arguments)),a)return r=null,a+""||null}return i.type=function(a){return arguments.length?(e=typeof a=="function"?a:se(a),i):e},i.size=function(a){return arguments.length?(t=typeof a=="function"?a:se(+a),i):t},i.context=function(a){return arguments.length?(r=a==null?null:a,i):r},i}function xi(){}function wi(e,t,r){e._context.bezierCurveTo((2*e._x0+e._x1)/3,(2*e._y0+e._y1)/3,(e._x0+2*e._x1)/3,(e._y0+2*e._y1)/3,(e._x0+4*e._x1+t)/6,(e._y0+4*e._y1+r)/6)}function gy(e){this._context=e}gy.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=NaN,this._point=0},lineEnd:function(){switch(this._point){case 3:wi(this,this._x1,this._y1);case 2:this._context.lineTo(this._x1,this._y1);break}(this._line||this._line!==0&&this._point===1)&&this._context.closePath(),this._line=1-this._line},point:function(e,t){switch(e=+e,t=+t,this._point){case 0:this._point=1,this._line?this._context.lineTo(e,t):this._context.moveTo(e,t);break;case 1:this._point=2;break;case 2:this._point=3,this._context.lineTo((5*this._x0+this._x1)/6,(5*this._y0+this._y1)/6);default:wi(this,e,t);break}this._x0=this._x1,this._x1=e,this._y0=this._y1,this._y1=t}};function M0(e){return new gy(e)}function by(e){this._context=e}by.prototype={areaStart:xi,areaEnd:xi,lineStart:function(){this._x0=this._x1=this._x2=this._x3=this._x4=this._y0=this._y1=this._y2=this._y3=this._y4=NaN,this._point=0},lineEnd:function(){switch(this._point){case 1:{this._context.moveTo(this._x2,this._y2),this._context.closePath();break}case 2:{this._context.moveTo((this._x2+2*this._x3)/3,(this._y2+2*this._y3)/3),this._context.lineTo((this._x3+2*this._x2)/3,(this._y3+2*this._y2)/3),this._context.closePath();break}case 3:{this.point(this._x2,this._y2),this.point(this._x3,this._y3),this.point(this._x4,this._y4);break}}},point:function(e,t){switch(e=+e,t=+t,this._point){case 0:this._point=1,this._x2=e,this._y2=t;break;case 1:this._point=2,this._x3=e,this._y3=t;break;case 2:this._point=3,this._x4=e,this._y4=t,this._context.moveTo((this._x0+4*this._x1+e)/6,(this._y0+4*this._y1+t)/6);break;default:wi(this,e,t);break}this._x0=this._x1,this._x1=e,this._y0=this._y1,this._y1=t}};function I0(e){return new by(e)}function xy(e){this._context=e}xy.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=NaN,this._point=0},lineEnd:function(){(this._line||this._line!==0&&this._point===3)&&this._context.closePath(),this._line=1-this._line},point:function(e,t){switch(e=+e,t=+t,this._point){case 0:this._point=1;break;case 1:this._point=2;break;case 2:this._point=3;var r=(this._x0+4*this._x1+e)/6,n=(this._y0+4*this._y1+t)/6;this._line?this._context.lineTo(r,n):this._context.moveTo(r,n);break;case 3:this._point=4;default:wi(this,e,t);break}this._x0=this._x1,this._x1=e,this._y0=this._y1,this._y1=t}};function C0(e){return new xy(e)}function wy(e){this._context=e}wy.prototype={areaStart:xi,areaEnd:xi,lineStart:function(){this._point=0},lineEnd:function(){this._point&&this._context.closePath()},point:function(e,t){e=+e,t=+t,this._point?this._context.lineTo(e,t):(this._point=1,this._context.moveTo(e,t))}};function k0(e){return new wy(e)}function mf(e){return e<0?-1:1}function gf(e,t,r){var n=e._x1-e._x0,i=t-e._x1,a=(e._y1-e._y0)/(n||i<0&&-0),o=(r-e._y1)/(i||n<0&&-0),u=(a*i+o*n)/(n+i);return(mf(a)+mf(o))*Math.min(Math.abs(a),Math.abs(o),.5*Math.abs(u))||0}function bf(e,t){var r=e._x1-e._x0;return r?(3*(e._y1-e._y0)/r-t)/2:t}function Oo(e,t,r){var n=e._x0,i=e._y0,a=e._x1,o=e._y1,u=(a-n)/3;e._context.bezierCurveTo(n+u,i+u*t,a-u,o-u*r,a,o)}function Oi(e){this._context=e}Oi.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=this._t0=NaN,this._point=0},lineEnd:function(){switch(this._point){case 2:this._context.lineTo(this._x1,this._y1);break;case 3:Oo(this,this._t0,bf(this,this._t0));break}(this._line||this._line!==0&&this._point===1)&&this._context.closePath(),this._line=1-this._line},point:function(e,t){var r=NaN;if(e=+e,t=+t,!(e===this._x1&&t===this._y1)){switch(this._point){case 0:this._point=1,this._line?this._context.lineTo(e,t):this._context.moveTo(e,t);break;case 1:this._point=2;break;case 2:this._point=3,Oo(this,bf(this,r=gf(this,e,t)),r);break;default:Oo(this,this._t0,r=gf(this,e,t));break}this._x0=this._x1,this._x1=e,this._y0=this._y1,this._y1=t,this._t0=r}}};function Oy(e){this._context=new Sy(e)}(Oy.prototype=Object.create(Oi.prototype)).point=function(e,t){Oi.prototype.point.call(this,t,e)};function Sy(e){this._context=e}Sy.prototype={moveTo:function(e,t){this._context.moveTo(t,e)},closePath:function(){this._context.closePath()},lineTo:function(e,t){this._context.lineTo(t,e)},bezierCurveTo:function(e,t,r,n,i,a){this._context.bezierCurveTo(t,e,n,r,a,i)}};function N0(e){return new Oi(e)}function R0(e){return new Oy(e)}function Ay(e){this._context=e}Ay.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x=[],this._y=[]},lineEnd:function(){var e=this._x,t=this._y,r=e.length;if(r)if(this._line?this._context.lineTo(e[0],t[0]):this._context.moveTo(e[0],t[0]),r===2)this._context.lineTo(e[1],t[1]);else for(var n=xf(e),i=xf(t),a=0,o=1;o<r;++a,++o)this._context.bezierCurveTo(n[0][a],i[0][a],n[1][a],i[1][a],e[o],t[o]);(this._line||this._line!==0&&r===1)&&this._context.closePath(),this._line=1-this._line,this._x=this._y=null},point:function(e,t){this._x.push(+e),this._y.push(+t)}};function xf(e){var t,r=e.length-1,n,i=new Array(r),a=new Array(r),o=new Array(r);for(i[0]=0,a[0]=2,o[0]=e[0]+2*e[1],t=1;t<r-1;++t)i[t]=1,a[t]=4,o[t]=4*e[t]+2*e[t+1];for(i[r-1]=2,a[r-1]=7,o[r-1]=8*e[r-1]+e[r],t=1;t<r;++t)n=i[t]/a[t-1],a[t]-=n,o[t]-=n*o[t-1];for(i[r-1]=o[r-1]/a[r-1],t=r-2;t>=0;--t)i[t]=(o[t]-i[t+1])/a[t];for(a[r-1]=(e[r]+i[r-1])/2,t=0;t<r-1;++t)a[t]=2*e[t+1]-i[t+1];return[i,a]}function D0(e){return new Ay(e)}function ga(e,t){this._context=e,this._t=t}ga.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x=this._y=NaN,this._point=0},lineEnd:function(){0<this._t&&this._t<1&&this._point===2&&this._context.lineTo(this._x,this._y),(this._line||this._line!==0&&this._point===1)&&this._context.closePath(),this._line>=0&&(this._t=1-this._t,this._line=1-this._line)},point:function(e,t){switch(e=+e,t=+t,this._point){case 0:this._point=1,this._line?this._context.lineTo(e,t):this._context.moveTo(e,t);break;case 1:this._point=2;default:{if(this._t<=0)this._context.lineTo(this._x,t),this._context.lineTo(e,t);else{var r=this._x*(1-this._t)+e*this._t;this._context.lineTo(r,this._y),this._context.lineTo(r,t)}break}}this._x=e,this._y=t}};function B0(e){return new ga(e,.5)}function L0(e){return new ga(e,0)}function q0(e){return new ga(e,1)}function yr(e,t){if((o=e.length)>1)for(var r=1,n,i,a=e[t[0]],o,u=a.length;r<o;++r)for(i=a,a=e[t[r]],n=0;n<u;++n)a[n][1]+=a[n][0]=isNaN(i[n][1])?i[n][0]:i[n][1]}function Pc(e){for(var t=e.length,r=new Array(t);--t>=0;)r[t]=t;return r}function F0(e,t){return e[t]}function W0(e){const t=[];return t.key=e,t}function z0(){var e=se([]),t=Pc,r=yr,n=F0;function i(a){var o=Array.from(e.apply(this,arguments),W0),u,c=o.length,s=-1,f;for(const l of a)for(u=0,++s;u<c;++u)(o[u][s]=[0,+n(l,o[u].key,s,a)]).data=l;for(u=0,f=Ws(t(o));u<c;++u)o[f[u]].index=u;return r(o,f),o}return i.keys=function(a){return arguments.length?(e=typeof a=="function"?a:se(Array.from(a)),i):e},i.value=function(a){return arguments.length?(n=typeof a=="function"?a:se(+a),i):n},i.order=function(a){return arguments.length?(t=a==null?Pc:typeof a=="function"?a:se(Array.from(a)),i):t},i.offset=function(a){return arguments.length?(r=a==null?yr:a,i):r},i}function U0(e,t){if((n=e.length)>0){for(var r,n,i=0,a=e[0].length,o;i<a;++i){for(o=r=0;r<n;++r)o+=e[r][i][1]||0;if(o)for(r=0;r<n;++r)e[r][i][1]/=o}yr(e,t)}}function H0(e,t){if((i=e.length)>0){for(var r=0,n=e[t[0]],i,a=n.length;r<a;++r){for(var o=0,u=0;o<i;++o)u+=e[o][r][1]||0;n[r][1]+=n[r][0]=-u/2}yr(e,t)}}function K0(e,t){if(!(!((o=e.length)>0)||!((a=(i=e[t[0]]).length)>0))){for(var r=0,n=1,i,a,o;n<a;++n){for(var u=0,c=0,s=0;u<o;++u){for(var f=e[t[u]],l=f[n][1]||0,h=f[n-1][1]||0,d=(l-h)/2,y=0;y<u;++y){var v=e[t[y]],p=v[n][1]||0,b=v[n-1][1]||0;d+=p-b}c+=l,s+=d*l}i[n-1][1]+=i[n-1][0]=r,c&&(r-=s/c)}i[n-1][1]+=i[n-1][0]=r,yr(e,t)}}function hn(e){"@babel/helpers - typeof";return hn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},hn(e)}var G0=["type","size","sizeType"];function _c(){return _c=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},_c.apply(this,arguments)}function wf(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Of(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?wf(Object(r),!0).forEach(function(n){V0(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):wf(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function V0(e,t,r){return t=X0(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function X0(e){var t=Y0(e,"string");return hn(t)=="symbol"?t:t+""}function Y0(e,t){if(hn(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(hn(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function Z0(e,t){if(e==null)return{};var r=J0(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function J0(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}var Py={symbolCircle:zs,symbolCross:b0,symbolDiamond:w0,symbolSquare:O0,symbolStar:_0,symbolTriangle:E0,symbolWye:j0},Q0=Math.PI/180,ex=function(t){var r="symbol".concat(va(t));return Py[r]||zs},tx=function(t,r,n){if(r==="area")return t;switch(n){case"cross":return 5*t*t/9;case"diamond":return .5*t*t/Math.sqrt(3);case"square":return t*t;case"star":{var i=18*Q0;return 1.25*t*t*(Math.tan(i)-Math.tan(i*2)*Math.pow(Math.tan(i),2))}case"triangle":return Math.sqrt(3)*t*t/4;case"wye":return(21-10*Math.sqrt(3))*t*t/8;default:return Math.PI*t*t/4}},rx=function(t,r){Py["symbol".concat(va(t))]=r},Us=function(t){var r=t.type,n=r===void 0?"circle":r,i=t.size,a=i===void 0?64:i,o=t.sizeType,u=o===void 0?"area":o,c=Z0(t,G0),s=Of(Of({},c),{},{type:n,size:a,sizeType:u}),f=function(){var p=ex(n),b=$0().type(p).size(tx(a,u,n));return b()},l=s.className,h=s.cx,d=s.cy,y=V(s,!0);return h===+h&&d===+d&&a===+a?P.createElement("path",_c({},y,{className:Q("recharts-symbols",l),transform:"translate(".concat(h,", ").concat(d,")"),d:f()})):null};Us.registerSymbol=rx;function mr(e){"@babel/helpers - typeof";return mr=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},mr(e)}function Ec(){return Ec=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Ec.apply(this,arguments)}function Sf(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function nx(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Sf(Object(r),!0).forEach(function(n){pn(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Sf(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function ix(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function ax(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,Ey(n.key),n)}}function ox(e,t,r){return t&&ax(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e}function ux(e,t,r){return t=Si(t),cx(e,_y()?Reflect.construct(t,r||[],Si(e).constructor):t.apply(e,r))}function cx(e,t){if(t&&(mr(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return sx(e)}function sx(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function _y(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(_y=function(){return!!e})()}function Si(e){return Si=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},Si(e)}function lx(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Tc(e,t)}function Tc(e,t){return Tc=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},Tc(e,t)}function pn(e,t,r){return t=Ey(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function Ey(e){var t=fx(e,"string");return mr(t)=="symbol"?t:t+""}function fx(e,t){if(mr(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(mr(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}var He=32,Hs=function(e){function t(){return ix(this,t),ux(this,t,arguments)}return lx(t,e),ox(t,[{key:"renderIcon",value:function(n){var i=this.props.inactiveColor,a=He/2,o=He/6,u=He/3,c=n.inactive?i:n.color;if(n.type==="plainline")return P.createElement("line",{strokeWidth:4,fill:"none",stroke:c,strokeDasharray:n.payload.strokeDasharray,x1:0,y1:a,x2:He,y2:a,className:"recharts-legend-icon"});if(n.type==="line")return P.createElement("path",{strokeWidth:4,fill:"none",stroke:c,d:"M0,".concat(a,"h").concat(u,`
            A`).concat(o,",").concat(o,",0,1,1,").concat(2*u,",").concat(a,`
            H`).concat(He,"M").concat(2*u,",").concat(a,`
            A`).concat(o,",").concat(o,",0,1,1,").concat(u,",").concat(a),className:"recharts-legend-icon"});if(n.type==="rect")return P.createElement("path",{stroke:"none",fill:c,d:"M0,".concat(He/8,"h").concat(He,"v").concat(He*3/4,"h").concat(-He,"z"),className:"recharts-legend-icon"});if(P.isValidElement(n.legendIcon)){var s=nx({},n);return delete s.legendIcon,P.cloneElement(n.legendIcon,s)}return P.createElement(Us,{fill:c,cx:a,cy:a,size:He,sizeType:"diameter",type:n.type})}},{key:"renderItems",value:function(){var n=this,i=this.props,a=i.payload,o=i.iconSize,u=i.layout,c=i.formatter,s=i.inactiveColor,f={x:0,y:0,width:He,height:He},l={display:u==="horizontal"?"inline-block":"block",marginRight:10},h={display:"inline-block",verticalAlign:"middle",marginRight:4};return a.map(function(d,y){var v=d.formatter||c,p=Q(pn(pn({"recharts-legend-item":!0},"legend-item-".concat(y),!0),"inactive",d.inactive));if(d.type==="none")return null;var b=X(d.value)?null:d.value;et(!X(d.value),`The name property is also required when using a function for the dataKey of a chart's cartesian components. Ex: <Bar name="Name of my Data"/>`);var w=d.inactive?s:d.color;return P.createElement("li",Ec({className:p,style:l,key:"legend-item-".concat(y)},Xt(n.props,d,y)),P.createElement(xc,{width:o,height:o,viewBox:f,style:h},n.renderIcon(d)),P.createElement("span",{className:"recharts-legend-item-text",style:{color:w}},v?v(b,d,y):b))})}},{key:"render",value:function(){var n=this.props,i=n.payload,a=n.layout,o=n.align;if(!i||!i.length)return null;var u={padding:0,margin:0,textAlign:a==="horizontal"?o:"left"};return P.createElement("ul",{className:"recharts-default-legend",style:u},this.renderItems())}}])}(D.PureComponent);pn(Hs,"displayName","Legend");pn(Hs,"defaultProps",{iconSize:14,layout:"horizontal",align:"center",verticalAlign:"middle",inactiveColor:"#ccc"});var So,Af;function hx(){if(Af)return So;Af=1;var e=lb(),t=Jv(),r=1,n=2;function i(a,o,u,c){var s=u.length,f=s,l=!c;if(a==null)return!f;for(a=Object(a);s--;){var h=u[s];if(l&&h[2]?h[1]!==a[h[0]]:!(h[0]in a))return!1}for(;++s<f;){h=u[s];var d=h[0],y=a[d],v=h[1];if(l&&h[2]){if(y===void 0&&!(d in a))return!1}else{var p=new e;if(c)var b=c(y,v,d,a,o,p);if(!(b===void 0?t(v,y,r|n,c,p):b))return!1}}return!0}return So=i,So}var Ao,Pf;function Ty(){if(Pf)return Ao;Pf=1;var e=qr();function t(r){return r===r&&!e(r)}return Ao=t,Ao}var Po,_f;function px(){if(_f)return Po;_f=1;var e=Ty(),t=Rs();function r(n){for(var i=t(n),a=i.length;a--;){var o=i[a],u=n[o];i[a]=[o,u,e(u)]}return i}return Po=r,Po}var _o,Ef;function jy(){if(Ef)return _o;Ef=1;function e(t,r){return function(n){return n==null?!1:n[t]===r&&(r!==void 0||t in Object(n))}}return _o=e,_o}var Eo,Tf;function dx(){if(Tf)return Eo;Tf=1;var e=hx(),t=px(),r=jy();function n(i){var a=t(i);return a.length==1&&a[0][2]?r(a[0][0],a[0][1]):function(o){return o===i||e(o,i,a)}}return Eo=n,Eo}var To,jf;function vx(){if(jf)return To;jf=1;function e(t,r){return t!=null&&r in Object(t)}return To=e,To}var jo,$f;function yx(){if($f)return jo;$f=1;var e=iy(),t=Qv(),r=tt(),n=ey(),i=fb(),a=pa();function o(u,c,s){c=e(c,u);for(var f=-1,l=c.length,h=!1;++f<l;){var d=a(c[f]);if(!(h=u!=null&&s(u,d)))break;u=u[d]}return h||++f!=l?h:(l=u==null?0:u.length,!!l&&i(l)&&n(d,l)&&(r(u)||t(u)))}return jo=o,jo}var $o,Mf;function mx(){if(Mf)return $o;Mf=1;var e=vx(),t=yx();function r(n,i){return n!=null&&t(n,i,e)}return $o=r,$o}var Mo,If;function gx(){if(If)return Mo;If=1;var e=Jv(),t=ay(),r=mx(),n=Ds(),i=Ty(),a=jy(),o=pa(),u=1,c=2;function s(f,l){return n(f)&&i(l)?a(o(f),l):function(h){var d=t(h,f);return d===void 0&&d===l?r(h,f):e(l,d,u|c)}}return Mo=s,Mo}var Io,Cf;function zr(){if(Cf)return Io;Cf=1;function e(t){return t}return Io=e,Io}var Co,kf;function bx(){if(kf)return Co;kf=1;function e(t){return function(r){return r==null?void 0:r[t]}}return Co=e,Co}var ko,Nf;function xx(){if(Nf)return ko;Nf=1;var e=Ls();function t(r){return function(n){return e(n,r)}}return ko=t,ko}var No,Rf;function wx(){if(Rf)return No;Rf=1;var e=bx(),t=xx(),r=Ds(),n=pa();function i(a){return r(a)?e(n(a)):t(a)}return No=i,No}var Ro,Df;function ut(){if(Df)return Ro;Df=1;var e=dx(),t=gx(),r=zr(),n=tt(),i=wx();function a(o){return typeof o=="function"?o:o==null?r:typeof o=="object"?n(o)?t(o[0],o[1]):e(o):i(o)}return Ro=a,Ro}var Do,Bf;function $y(){if(Bf)return Do;Bf=1;function e(t,r,n,i){for(var a=t.length,o=n+(i?1:-1);i?o--:++o<a;)if(r(t[o],o,t))return o;return-1}return Do=e,Do}var Bo,Lf;function Ox(){if(Lf)return Bo;Lf=1;function e(t){return t!==t}return Bo=e,Bo}var Lo,qf;function Sx(){if(qf)return Lo;qf=1;function e(t,r,n){for(var i=n-1,a=t.length;++i<a;)if(t[i]===r)return i;return-1}return Lo=e,Lo}var qo,Ff;function Ax(){if(Ff)return qo;Ff=1;var e=$y(),t=Ox(),r=Sx();function n(i,a,o){return a===a?r(i,a,o):e(i,t,o)}return qo=n,qo}var Fo,Wf;function Px(){if(Wf)return Fo;Wf=1;var e=Ax();function t(r,n){var i=r==null?0:r.length;return!!i&&e(r,n,0)>-1}return Fo=t,Fo}var Wo,zf;function _x(){if(zf)return Wo;zf=1;function e(t,r,n){for(var i=-1,a=t==null?0:t.length;++i<a;)if(n(r,t[i]))return!0;return!1}return Wo=e,Wo}var zo,Uf;function Ex(){if(Uf)return zo;Uf=1;function e(){}return zo=e,zo}var Uo,Hf;function Tx(){if(Hf)return Uo;Hf=1;var e=hb(),t=Ex(),r=ty(),n=1/0,i=e&&1/r(new e([,-0]))[1]==n?function(a){return new e(a)}:t;return Uo=i,Uo}var Ho,Kf;function jx(){if(Kf)return Ho;Kf=1;var e=pb(),t=Px(),r=_x(),n=db(),i=Tx(),a=ty(),o=200;function u(c,s,f){var l=-1,h=t,d=c.length,y=!0,v=[],p=v;if(f)y=!1,h=r;else if(d>=o){var b=s?null:i(c);if(b)return a(b);y=!1,h=n,p=new e}else p=s?[]:v;e:for(;++l<d;){var w=c[l],g=s?s(w):w;if(w=f||w!==0?w:0,y&&g===g){for(var O=p.length;O--;)if(p[O]===g)continue e;s&&p.push(g),v.push(w)}else h(p,g,f)||(p!==v&&p.push(g),v.push(w))}return v}return Ho=u,Ho}var Ko,Gf;function $x(){if(Gf)return Ko;Gf=1;var e=ut(),t=jx();function r(n,i){return n&&n.length?t(n,e(i,2)):[]}return Ko=r,Ko}var Mx=$x();const Vf=oe(Mx);function My(e,t,r){return t===!0?Vf(e,r):X(t)?Vf(e,t):e}function gr(e){"@babel/helpers - typeof";return gr=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},gr(e)}var Ix=["ref"];function Xf(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function ct(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Xf(Object(r),!0).forEach(function(n){ba(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Xf(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function Cx(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function Yf(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,Cy(n.key),n)}}function kx(e,t,r){return t&&Yf(e.prototype,t),r&&Yf(e,r),Object.defineProperty(e,"prototype",{writable:!1}),e}function Nx(e,t,r){return t=Ai(t),Rx(e,Iy()?Reflect.construct(t,r||[],Ai(e).constructor):t.apply(e,r))}function Rx(e,t){if(t&&(gr(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return Dx(e)}function Dx(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function Iy(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(Iy=function(){return!!e})()}function Ai(e){return Ai=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},Ai(e)}function Bx(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&jc(e,t)}function jc(e,t){return jc=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},jc(e,t)}function ba(e,t,r){return t=Cy(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function Cy(e){var t=Lx(e,"string");return gr(t)=="symbol"?t:t+""}function Lx(e,t){if(gr(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(gr(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}function qx(e,t){if(e==null)return{};var r=Fx(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function Fx(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}function Wx(e){return e.value}function zx(e,t){if(P.isValidElement(e))return P.cloneElement(e,t);if(typeof e=="function")return P.createElement(e,t);t.ref;var r=qx(t,Ix);return P.createElement(Hs,r)}var Zf=1,pr=function(e){function t(){var r;Cx(this,t);for(var n=arguments.length,i=new Array(n),a=0;a<n;a++)i[a]=arguments[a];return r=Nx(this,t,[].concat(i)),ba(r,"lastBoundingBox",{width:-1,height:-1}),r}return Bx(t,e),kx(t,[{key:"componentDidMount",value:function(){this.updateBBox()}},{key:"componentDidUpdate",value:function(){this.updateBBox()}},{key:"getBBox",value:function(){if(this.wrapperNode&&this.wrapperNode.getBoundingClientRect){var n=this.wrapperNode.getBoundingClientRect();return n.height=this.wrapperNode.offsetHeight,n.width=this.wrapperNode.offsetWidth,n}return null}},{key:"updateBBox",value:function(){var n=this.props.onBBoxUpdate,i=this.getBBox();i?(Math.abs(i.width-this.lastBoundingBox.width)>Zf||Math.abs(i.height-this.lastBoundingBox.height)>Zf)&&(this.lastBoundingBox.width=i.width,this.lastBoundingBox.height=i.height,n&&n(i)):(this.lastBoundingBox.width!==-1||this.lastBoundingBox.height!==-1)&&(this.lastBoundingBox.width=-1,this.lastBoundingBox.height=-1,n&&n(null))}},{key:"getBBoxSnapshot",value:function(){return this.lastBoundingBox.width>=0&&this.lastBoundingBox.height>=0?ct({},this.lastBoundingBox):{width:0,height:0}}},{key:"getDefaultPosition",value:function(n){var i=this.props,a=i.layout,o=i.align,u=i.verticalAlign,c=i.margin,s=i.chartWidth,f=i.chartHeight,l,h;if(!n||(n.left===void 0||n.left===null)&&(n.right===void 0||n.right===null))if(o==="center"&&a==="vertical"){var d=this.getBBoxSnapshot();l={left:((s||0)-d.width)/2}}else l=o==="right"?{right:c&&c.right||0}:{left:c&&c.left||0};if(!n||(n.top===void 0||n.top===null)&&(n.bottom===void 0||n.bottom===null))if(u==="middle"){var y=this.getBBoxSnapshot();h={top:((f||0)-y.height)/2}}else h=u==="bottom"?{bottom:c&&c.bottom||0}:{top:c&&c.top||0};return ct(ct({},l),h)}},{key:"render",value:function(){var n=this,i=this.props,a=i.content,o=i.width,u=i.height,c=i.wrapperStyle,s=i.payloadUniqBy,f=i.payload,l=ct(ct({position:"absolute",width:o||"auto",height:u||"auto"},this.getDefaultPosition(c)),c);return P.createElement("div",{className:"recharts-legend-wrapper",style:l,ref:function(d){n.wrapperNode=d}},zx(a,ct(ct({},this.props),{},{payload:My(f,s,Wx)})))}}],[{key:"getWithHeight",value:function(n,i){var a=ct(ct({},this.defaultProps),n.props),o=a.layout;return o==="vertical"&&B(n.props.height)?{height:n.props.height}:o==="horizontal"?{width:n.props.width||i}:null}}])}(D.PureComponent);ba(pr,"displayName","Legend");ba(pr,"defaultProps",{iconSize:14,layout:"horizontal",align:"center",verticalAlign:"bottom"});var Go,Jf;function Ux(){if(Jf)return Go;Jf=1;var e=Zv(),t=Qv(),r=tt(),n=e?e.isConcatSpreadable:void 0;function i(a){return r(a)||t(a)||!!(n&&a&&a[n])}return Go=i,Go}var Vo,Qf;function ky(){if(Qf)return Vo;Qf=1;var e=vb(),t=Ux();function r(n,i,a,o,u){var c=-1,s=n.length;for(a||(a=t),u||(u=[]);++c<s;){var f=n[c];i>0&&a(f)?i>1?r(f,i-1,a,o,u):e(u,f):o||(u[u.length]=f)}return u}return Vo=r,Vo}var Xo,eh;function Hx(){if(eh)return Xo;eh=1;function e(t){return function(r,n,i){for(var a=-1,o=Object(r),u=i(r),c=u.length;c--;){var s=u[t?c:++a];if(n(o[s],s,o)===!1)break}return r}}return Xo=e,Xo}var Yo,th;function Kx(){if(th)return Yo;th=1;var e=Hx(),t=e();return Yo=t,Yo}var Zo,rh;function Ny(){if(rh)return Zo;rh=1;var e=Kx(),t=Rs();function r(n,i){return n&&e(n,i,t)}return Zo=r,Zo}var Jo,nh;function Gx(){if(nh)return Jo;nh=1;var e=ha();function t(r,n){return function(i,a){if(i==null)return i;if(!e(i))return r(i,a);for(var o=i.length,u=n?o:-1,c=Object(i);(n?u--:++u<o)&&a(c[u],u,c)!==!1;);return i}}return Jo=t,Jo}var Qo,ih;function Ks(){if(ih)return Qo;ih=1;var e=Ny(),t=Gx(),r=t(e);return Qo=r,Qo}var eu,ah;function Ry(){if(ah)return eu;ah=1;var e=Ks(),t=ha();function r(n,i){var a=-1,o=t(n)?Array(n.length):[];return e(n,function(u,c,s){o[++a]=i(u,c,s)}),o}return eu=r,eu}var tu,oh;function Vx(){if(oh)return tu;oh=1;function e(t,r){var n=t.length;for(t.sort(r);n--;)t[n]=t[n].value;return t}return tu=e,tu}var ru,uh;function Xx(){if(uh)return ru;uh=1;var e=Fr();function t(r,n){if(r!==n){var i=r!==void 0,a=r===null,o=r===r,u=e(r),c=n!==void 0,s=n===null,f=n===n,l=e(n);if(!s&&!l&&!u&&r>n||u&&c&&f&&!s&&!l||a&&c&&f||!i&&f||!o)return 1;if(!a&&!u&&!l&&r<n||l&&i&&o&&!a&&!u||s&&i&&o||!c&&o||!f)return-1}return 0}return ru=t,ru}var nu,ch;function Yx(){if(ch)return nu;ch=1;var e=Xx();function t(r,n,i){for(var a=-1,o=r.criteria,u=n.criteria,c=o.length,s=i.length;++a<c;){var f=e(o[a],u[a]);if(f){if(a>=s)return f;var l=i[a];return f*(l=="desc"?-1:1)}}return r.index-n.index}return nu=t,nu}var iu,sh;function Zx(){if(sh)return iu;sh=1;var e=Bs(),t=Ls(),r=ut(),n=Ry(),i=Vx(),a=yb(),o=Yx(),u=zr(),c=tt();function s(f,l,h){l.length?l=e(l,function(v){return c(v)?function(p){return t(p,v.length===1?v[0]:v)}:v}):l=[u];var d=-1;l=e(l,a(r));var y=n(f,function(v,p,b){var w=e(l,function(g){return g(v)});return{criteria:w,index:++d,value:v}});return i(y,function(v,p){return o(v,p,h)})}return iu=s,iu}var au,lh;function Jx(){if(lh)return au;lh=1;function e(t,r,n){switch(n.length){case 0:return t.call(r);case 1:return t.call(r,n[0]);case 2:return t.call(r,n[0],n[1]);case 3:return t.call(r,n[0],n[1],n[2])}return t.apply(r,n)}return au=e,au}var ou,fh;function Qx(){if(fh)return ou;fh=1;var e=Jx(),t=Math.max;function r(n,i,a){return i=t(i===void 0?n.length-1:i,0),function(){for(var o=arguments,u=-1,c=t(o.length-i,0),s=Array(c);++u<c;)s[u]=o[i+u];u=-1;for(var f=Array(i+1);++u<i;)f[u]=o[u];return f[i]=a(s),e(n,this,f)}}return ou=r,ou}var uu,hh;function ew(){if(hh)return uu;hh=1;function e(t){return function(){return t}}return uu=e,uu}var cu,ph;function Dy(){if(ph)return cu;ph=1;var e=mb(),t=function(){try{var r=e(Object,"defineProperty");return r({},"",{}),r}catch(n){}}();return cu=t,cu}var su,dh;function tw(){if(dh)return su;dh=1;var e=ew(),t=Dy(),r=zr(),n=t?function(i,a){return t(i,"toString",{configurable:!0,enumerable:!1,value:e(a),writable:!0})}:r;return su=n,su}var lu,vh;function rw(){if(vh)return lu;vh=1;var e=800,t=16,r=Date.now;function n(i){var a=0,o=0;return function(){var u=r(),c=t-(u-o);if(o=u,c>0){if(++a>=e)return arguments[0]}else a=0;return i.apply(void 0,arguments)}}return lu=n,lu}var fu,yh;function nw(){if(yh)return fu;yh=1;var e=tw(),t=rw(),r=t(e);return fu=r,fu}var hu,mh;function iw(){if(mh)return hu;mh=1;var e=zr(),t=Qx(),r=nw();function n(i,a){return r(t(i,a,e),i+"")}return hu=n,hu}var pu,gh;function xa(){if(gh)return pu;gh=1;var e=gb(),t=ha(),r=ey(),n=qr();function i(a,o,u){if(!n(u))return!1;var c=typeof o;return(c=="number"?t(u)&&r(o,u.length):c=="string"&&o in u)?e(u[o],a):!1}return pu=i,pu}var du,bh;function aw(){if(bh)return du;bh=1;var e=ky(),t=Zx(),r=iw(),n=xa(),i=r(function(a,o){if(a==null)return[];var u=o.length;return u>1&&n(a,o[0],o[1])?o=[]:u>2&&n(o[0],o[1],o[2])&&(o=[o[0]]),t(a,e(o,1),[])});return du=i,du}var ow=aw();const Gs=oe(ow);function dn(e){"@babel/helpers - typeof";return dn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},dn(e)}function $c(){return $c=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},$c.apply(this,arguments)}function uw(e,t){return fw(e)||lw(e,t)||sw(e,t)||cw()}function cw(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function sw(e,t){if(e){if(typeof e=="string")return xh(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return xh(e,t)}}function xh(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function lw(e,t){var r=e==null?null:typeof Symbol!="undefined"&&e[Symbol.iterator]||e["@@iterator"];if(r!=null){var n,i,a,o,u=[],c=!0,s=!1;try{if(a=(r=r.call(e)).next,t!==0)for(;!(c=(n=a.call(r)).done)&&(u.push(n.value),u.length!==t);c=!0);}catch(f){s=!0,i=f}finally{try{if(!c&&r.return!=null&&(o=r.return(),Object(o)!==o))return}finally{if(s)throw i}}return u}}function fw(e){if(Array.isArray(e))return e}function wh(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function vu(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?wh(Object(r),!0).forEach(function(n){hw(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):wh(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function hw(e,t,r){return t=pw(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function pw(e){var t=dw(e,"string");return dn(t)=="symbol"?t:t+""}function dw(e,t){if(dn(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(dn(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function vw(e){return Array.isArray(e)&&Se(e[0])&&Se(e[1])?e.join(" ~ "):e}var yw=function(t){var r=t.separator,n=r===void 0?" : ":r,i=t.contentStyle,a=i===void 0?{}:i,o=t.itemStyle,u=o===void 0?{}:o,c=t.labelStyle,s=c===void 0?{}:c,f=t.payload,l=t.formatter,h=t.itemSorter,d=t.wrapperClassName,y=t.labelClassName,v=t.label,p=t.labelFormatter,b=t.accessibilityLayer,w=b===void 0?!1:b,g=function(){if(f&&f.length){var T={padding:0,margin:0},j=(h?Gs(f,h):f).map(function(C,I){if(C.type==="none")return null;var k=vu({display:"block",paddingTop:4,paddingBottom:4,color:C.color||"#000"},u),N=C.formatter||l||vw,L=C.value,q=C.name,U=L,K=q;if(N&&U!=null&&K!=null){var W=N(L,q,C,I,f);if(Array.isArray(W)){var G=uw(W,2);U=G[0],K=G[1]}else U=W}return P.createElement("li",{className:"recharts-tooltip-item",key:"tooltip-item-".concat(I),style:k},Se(K)?P.createElement("span",{className:"recharts-tooltip-item-name"},K):null,Se(K)?P.createElement("span",{className:"recharts-tooltip-item-separator"},n):null,P.createElement("span",{className:"recharts-tooltip-item-value"},U),P.createElement("span",{className:"recharts-tooltip-item-unit"},C.unit||""))});return P.createElement("ul",{className:"recharts-tooltip-item-list",style:T},j)}return null},O=vu({margin:0,padding:10,backgroundColor:"#fff",border:"1px solid #ccc",whiteSpace:"nowrap"},a),m=vu({margin:0},s),x=!Z(v),S=x?v:"",A=Q("recharts-default-tooltip",d),_=Q("recharts-tooltip-label",y);x&&p&&f!==void 0&&f!==null&&(S=p(v,f));var $=w?{role:"status","aria-live":"assertive"}:{};return P.createElement("div",$c({className:A,style:O},$),P.createElement("p",{className:_,style:m},P.isValidElement(S)?S:"".concat(S)),g())};function vn(e){"@babel/helpers - typeof";return vn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},vn(e)}function ni(e,t,r){return t=mw(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function mw(e){var t=gw(e,"string");return vn(t)=="symbol"?t:t+""}function gw(e,t){if(vn(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(vn(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var Xr="recharts-tooltip-wrapper",bw={visibility:"hidden"};function xw(e){var t=e.coordinate,r=e.translateX,n=e.translateY;return Q(Xr,ni(ni(ni(ni({},"".concat(Xr,"-right"),B(r)&&t&&B(t.x)&&r>=t.x),"".concat(Xr,"-left"),B(r)&&t&&B(t.x)&&r<t.x),"".concat(Xr,"-bottom"),B(n)&&t&&B(t.y)&&n>=t.y),"".concat(Xr,"-top"),B(n)&&t&&B(t.y)&&n<t.y))}function Oh(e){var t=e.allowEscapeViewBox,r=e.coordinate,n=e.key,i=e.offsetTopLeft,a=e.position,o=e.reverseDirection,u=e.tooltipDimension,c=e.viewBox,s=e.viewBoxDimension;if(a&&B(a[n]))return a[n];var f=r[n]-u-i,l=r[n]+i;if(t[n])return o[n]?f:l;if(o[n]){var h=f,d=c[n];return h<d?Math.max(l,c[n]):Math.max(f,c[n])}var y=l+u,v=c[n]+s;return y>v?Math.max(f,c[n]):Math.max(l,c[n])}function ww(e){var t=e.translateX,r=e.translateY,n=e.useTranslate3d;return{transform:n?"translate3d(".concat(t,"px, ").concat(r,"px, 0)"):"translate(".concat(t,"px, ").concat(r,"px)")}}function Ow(e){var t=e.allowEscapeViewBox,r=e.coordinate,n=e.offsetTopLeft,i=e.position,a=e.reverseDirection,o=e.tooltipBox,u=e.useTranslate3d,c=e.viewBox,s,f,l;return o.height>0&&o.width>0&&r?(f=Oh({allowEscapeViewBox:t,coordinate:r,key:"x",offsetTopLeft:n,position:i,reverseDirection:a,tooltipDimension:o.width,viewBox:c,viewBoxDimension:c.width}),l=Oh({allowEscapeViewBox:t,coordinate:r,key:"y",offsetTopLeft:n,position:i,reverseDirection:a,tooltipDimension:o.height,viewBox:c,viewBoxDimension:c.height}),s=ww({translateX:f,translateY:l,useTranslate3d:u})):s=bw,{cssProperties:s,cssClasses:xw({translateX:f,translateY:l,coordinate:r})}}function br(e){"@babel/helpers - typeof";return br=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},br(e)}function Sh(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Ah(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Sh(Object(r),!0).forEach(function(n){Ic(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Sh(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function Sw(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function Aw(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,Ly(n.key),n)}}function Pw(e,t,r){return t&&Aw(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e}function _w(e,t,r){return t=Pi(t),Ew(e,By()?Reflect.construct(t,r||[],Pi(e).constructor):t.apply(e,r))}function Ew(e,t){if(t&&(br(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return Tw(e)}function Tw(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function By(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(By=function(){return!!e})()}function Pi(e){return Pi=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},Pi(e)}function jw(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Mc(e,t)}function Mc(e,t){return Mc=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},Mc(e,t)}function Ic(e,t,r){return t=Ly(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function Ly(e){var t=$w(e,"string");return br(t)=="symbol"?t:t+""}function $w(e,t){if(br(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(br(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}var Ph=1,Mw=function(e){function t(){var r;Sw(this,t);for(var n=arguments.length,i=new Array(n),a=0;a<n;a++)i[a]=arguments[a];return r=_w(this,t,[].concat(i)),Ic(r,"state",{dismissed:!1,dismissedAtCoordinate:{x:0,y:0},lastBoundingBox:{width:-1,height:-1}}),Ic(r,"handleKeyDown",function(o){if(o.key==="Escape"){var u,c,s,f;r.setState({dismissed:!0,dismissedAtCoordinate:{x:(u=(c=r.props.coordinate)===null||c===void 0?void 0:c.x)!==null&&u!==void 0?u:0,y:(s=(f=r.props.coordinate)===null||f===void 0?void 0:f.y)!==null&&s!==void 0?s:0}})}}),r}return jw(t,e),Pw(t,[{key:"updateBBox",value:function(){if(this.wrapperNode&&this.wrapperNode.getBoundingClientRect){var n=this.wrapperNode.getBoundingClientRect();(Math.abs(n.width-this.state.lastBoundingBox.width)>Ph||Math.abs(n.height-this.state.lastBoundingBox.height)>Ph)&&this.setState({lastBoundingBox:{width:n.width,height:n.height}})}else(this.state.lastBoundingBox.width!==-1||this.state.lastBoundingBox.height!==-1)&&this.setState({lastBoundingBox:{width:-1,height:-1}})}},{key:"componentDidMount",value:function(){document.addEventListener("keydown",this.handleKeyDown),this.updateBBox()}},{key:"componentWillUnmount",value:function(){document.removeEventListener("keydown",this.handleKeyDown)}},{key:"componentDidUpdate",value:function(){var n,i;this.props.active&&this.updateBBox(),this.state.dismissed&&(((n=this.props.coordinate)===null||n===void 0?void 0:n.x)!==this.state.dismissedAtCoordinate.x||((i=this.props.coordinate)===null||i===void 0?void 0:i.y)!==this.state.dismissedAtCoordinate.y)&&(this.state.dismissed=!1)}},{key:"render",value:function(){var n=this,i=this.props,a=i.active,o=i.allowEscapeViewBox,u=i.animationDuration,c=i.animationEasing,s=i.children,f=i.coordinate,l=i.hasPayload,h=i.isAnimationActive,d=i.offset,y=i.position,v=i.reverseDirection,p=i.useTranslate3d,b=i.viewBox,w=i.wrapperStyle,g=Ow({allowEscapeViewBox:o,coordinate:f,offsetTopLeft:d,position:y,reverseDirection:v,tooltipBox:this.state.lastBoundingBox,useTranslate3d:p,viewBox:b}),O=g.cssClasses,m=g.cssProperties,x=Ah(Ah({transition:h&&a?"transform ".concat(u,"ms ").concat(c):void 0},m),{},{pointerEvents:"none",visibility:!this.state.dismissed&&a&&l?"visible":"hidden",position:"absolute",top:0,left:0},w);return P.createElement("div",{tabIndex:-1,className:O,style:x,ref:function(A){n.wrapperNode=A}},s)}}])}(D.PureComponent),Iw=function(){return!(typeof window!="undefined"&&window.document&&window.document.createElement&&window.setTimeout)},vt={isSsr:Iw(),get:function(t){return vt[t]},set:function(t,r){if(typeof t=="string")vt[t]=r;else{var n=Object.keys(t);n&&n.length&&n.forEach(function(i){vt[i]=t[i]})}}};function xr(e){"@babel/helpers - typeof";return xr=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},xr(e)}function _h(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Eh(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?_h(Object(r),!0).forEach(function(n){Vs(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):_h(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function Cw(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function kw(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,Fy(n.key),n)}}function Nw(e,t,r){return t&&kw(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e}function Rw(e,t,r){return t=_i(t),Dw(e,qy()?Reflect.construct(t,r||[],_i(e).constructor):t.apply(e,r))}function Dw(e,t){if(t&&(xr(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return Bw(e)}function Bw(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function qy(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(qy=function(){return!!e})()}function _i(e){return _i=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},_i(e)}function Lw(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Cc(e,t)}function Cc(e,t){return Cc=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},Cc(e,t)}function Vs(e,t,r){return t=Fy(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function Fy(e){var t=qw(e,"string");return xr(t)=="symbol"?t:t+""}function qw(e,t){if(xr(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(xr(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}function Fw(e){return e.dataKey}function Ww(e,t){return P.isValidElement(e)?P.cloneElement(e,t):typeof e=="function"?P.createElement(e,t):P.createElement(yw,t)}var st=function(e){function t(){return Cw(this,t),Rw(this,t,arguments)}return Lw(t,e),Nw(t,[{key:"render",value:function(){var n=this,i=this.props,a=i.active,o=i.allowEscapeViewBox,u=i.animationDuration,c=i.animationEasing,s=i.content,f=i.coordinate,l=i.filterNull,h=i.isAnimationActive,d=i.offset,y=i.payload,v=i.payloadUniqBy,p=i.position,b=i.reverseDirection,w=i.useTranslate3d,g=i.viewBox,O=i.wrapperStyle,m=y!=null?y:[];l&&m.length&&(m=My(y.filter(function(S){return S.value!=null&&(S.hide!==!0||n.props.includeHidden)}),v,Fw));var x=m.length>0;return P.createElement(Mw,{allowEscapeViewBox:o,animationDuration:u,animationEasing:c,isAnimationActive:h,active:a,coordinate:f,hasPayload:x,offset:d,position:p,reverseDirection:b,useTranslate3d:w,viewBox:g,wrapperStyle:O},Ww(s,Eh(Eh({},this.props),{},{payload:m})))}}])}(D.PureComponent);Vs(st,"displayName","Tooltip");Vs(st,"defaultProps",{accessibilityLayer:!1,allowEscapeViewBox:{x:!1,y:!1},animationDuration:400,animationEasing:"ease",contentStyle:{},coordinate:{x:0,y:0},cursor:!0,cursorStyle:{},filterNull:!0,isAnimationActive:!vt.isSsr,itemStyle:{},labelStyle:{},offset:10,reverseDirection:{x:!1,y:!1},separator:" : ",trigger:"hover",useTranslate3d:!1,viewBox:{x:0,y:0,height:0,width:0},wrapperStyle:{}});var yu,Th;function zw(){if(Th)return yu;Th=1;var e=bb(),t=function(){return e.Date.now()};return yu=t,yu}var mu,jh;function Uw(){if(jh)return mu;jh=1;var e=/\s/;function t(r){for(var n=r.length;n--&&e.test(r.charAt(n)););return n}return mu=t,mu}var gu,$h;function Hw(){if($h)return gu;$h=1;var e=Uw(),t=/^\s+/;function r(n){return n&&n.slice(0,e(n)+1).replace(t,"")}return gu=r,gu}var bu,Mh;function Wy(){if(Mh)return bu;Mh=1;var e=Hw(),t=qr(),r=Fr(),n=NaN,i=/^[-+]0x[0-9a-f]+$/i,a=/^0b[01]+$/i,o=/^0o[0-7]+$/i,u=parseInt;function c(s){if(typeof s=="number")return s;if(r(s))return n;if(t(s)){var f=typeof s.valueOf=="function"?s.valueOf():s;s=t(f)?f+"":f}if(typeof s!="string")return s===0?s:+s;s=e(s);var l=a.test(s);return l||o.test(s)?u(s.slice(2),l?2:8):i.test(s)?n:+s}return bu=c,bu}var xu,Ih;function Kw(){if(Ih)return xu;Ih=1;var e=qr(),t=zw(),r=Wy(),n="Expected a function",i=Math.max,a=Math.min;function o(u,c,s){var f,l,h,d,y,v,p=0,b=!1,w=!1,g=!0;if(typeof u!="function")throw new TypeError(n);c=r(c)||0,e(s)&&(b=!!s.leading,w="maxWait"in s,h=w?i(r(s.maxWait)||0,c):h,g="trailing"in s?!!s.trailing:g);function O(j){var C=f,I=l;return f=l=void 0,p=j,d=u.apply(I,C),d}function m(j){return p=j,y=setTimeout(A,c),b?O(j):d}function x(j){var C=j-v,I=j-p,k=c-C;return w?a(k,h-I):k}function S(j){var C=j-v,I=j-p;return v===void 0||C>=c||C<0||w&&I>=h}function A(){var j=t();if(S(j))return _(j);y=setTimeout(A,x(j))}function _(j){return y=void 0,g&&f?O(j):(f=l=void 0,d)}function $(){y!==void 0&&clearTimeout(y),p=0,f=v=l=y=void 0}function E(){return y===void 0?d:_(t())}function T(){var j=t(),C=S(j);if(f=arguments,l=this,v=j,C){if(y===void 0)return m(v);if(w)return clearTimeout(y),y=setTimeout(A,c),O(v)}return y===void 0&&(y=setTimeout(A,c)),d}return T.cancel=$,T.flush=E,T}return xu=o,xu}var wu,Ch;function Gw(){if(Ch)return wu;Ch=1;var e=Kw(),t=qr(),r="Expected a function";function n(i,a,o){var u=!0,c=!0;if(typeof i!="function")throw new TypeError(r);return t(o)&&(u="leading"in o?!!o.leading:u,c="trailing"in o?!!o.trailing:c),e(i,a,{leading:u,maxWait:a,trailing:c})}return wu=n,wu}var Vw=Gw();const zy=oe(Vw);function yn(e){"@babel/helpers - typeof";return yn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},yn(e)}function kh(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function ii(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?kh(Object(r),!0).forEach(function(n){Xw(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):kh(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function Xw(e,t,r){return t=Yw(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function Yw(e){var t=Zw(e,"string");return yn(t)=="symbol"?t:t+""}function Zw(e,t){if(yn(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(yn(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function Jw(e,t){return rO(e)||tO(e,t)||eO(e,t)||Qw()}function Qw(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function eO(e,t){if(e){if(typeof e=="string")return Nh(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Nh(e,t)}}function Nh(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function tO(e,t){var r=e==null?null:typeof Symbol!="undefined"&&e[Symbol.iterator]||e["@@iterator"];if(r!=null){var n,i,a,o,u=[],c=!0,s=!1;try{if(a=(r=r.call(e)).next,t!==0)for(;!(c=(n=a.call(r)).done)&&(u.push(n.value),u.length!==t);c=!0);}catch(f){s=!0,i=f}finally{try{if(!c&&r.return!=null&&(o=r.return(),Object(o)!==o))return}finally{if(s)throw i}}return u}}function rO(e){if(Array.isArray(e))return e}var KC=D.forwardRef(function(e,t){var r=e.aspect,n=e.initialDimension,i=n===void 0?{width:-1,height:-1}:n,a=e.width,o=a===void 0?"100%":a,u=e.height,c=u===void 0?"100%":u,s=e.minWidth,f=s===void 0?0:s,l=e.minHeight,h=e.maxHeight,d=e.children,y=e.debounce,v=y===void 0?0:y,p=e.id,b=e.className,w=e.onResize,g=e.style,O=g===void 0?{}:g,m=D.useRef(null),x=D.useRef();x.current=w,D.useImperativeHandle(t,function(){return Object.defineProperty(m.current,"current",{get:function(){return console.warn("The usage of ref.current.current is deprecated and will no longer be supported."),m.current},configurable:!0})});var S=D.useState({containerWidth:i.width,containerHeight:i.height}),A=Jw(S,2),_=A[0],$=A[1],E=D.useCallback(function(j,C){$(function(I){var k=Math.round(j),N=Math.round(C);return I.containerWidth===k&&I.containerHeight===N?I:{containerWidth:k,containerHeight:N}})},[]);D.useEffect(function(){var j=function(q){var U,K=q[0].contentRect,W=K.width,G=K.height;E(W,G),(U=x.current)===null||U===void 0||U.call(x,W,G)};v>0&&(j=zy(j,v,{trailing:!0,leading:!1}));var C=new ResizeObserver(j),I=m.current.getBoundingClientRect(),k=I.width,N=I.height;return E(k,N),C.observe(m.current),function(){C.disconnect()}},[E,v]);var T=D.useMemo(function(){var j=_.containerWidth,C=_.containerHeight;if(j<0||C<0)return null;et(Wt(o)||Wt(c),`The width(%s) and height(%s) are both fixed numbers,
       maybe you don't need to use a ResponsiveContainer.`,o,c),et(!r||r>0,"The aspect(%s) must be greater than zero.",r);var I=Wt(o)?j:o,k=Wt(c)?C:c;r&&r>0&&(I?k=I/r:k&&(I=k*r),h&&k>h&&(k=h)),et(I>0||k>0,`The width(%s) and height(%s) of chart should be greater than 0,
       please check the style of container, or the props width(%s) and height(%s),
       or add a minWidth(%s) or minHeight(%s) or use aspect(%s) to control the
       height and width.`,I,k,o,c,f,l,r);var N=!Array.isArray(d)&&dt(d.type).endsWith("Chart");return P.Children.map(d,function(L){return P.isValidElement(L)?D.cloneElement(L,ii({width:I,height:k},N?{style:ii({height:"100%",width:"100%",maxHeight:k,maxWidth:I},L.props.style)}:{})):L})},[r,d,c,h,l,f,_,o]);return P.createElement("div",{id:p?"".concat(p):void 0,className:Q("recharts-responsive-container",b),style:ii(ii({},O),{},{width:o,height:c,minWidth:f,minHeight:l,maxHeight:h}),ref:m},T)}),Xs=function(t){return null};Xs.displayName="Cell";function mn(e){"@babel/helpers - typeof";return mn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},mn(e)}function Rh(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function kc(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Rh(Object(r),!0).forEach(function(n){nO(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Rh(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function nO(e,t,r){return t=iO(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function iO(e){var t=aO(e,"string");return mn(t)=="symbol"?t:t+""}function aO(e,t){if(mn(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(mn(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var ir={widthCache:{},cacheCount:0},oO=2e3,uO={position:"absolute",top:"-20000px",left:0,padding:0,margin:0,border:"none",whiteSpace:"pre"},Dh="recharts_measurement_span";function cO(e){var t=kc({},e);return Object.keys(t).forEach(function(r){t[r]||delete t[r]}),t}var on=function(t){var r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};if(t==null||vt.isSsr)return{width:0,height:0};var n=cO(r),i=JSON.stringify({text:t,copyStyle:n});if(ir.widthCache[i])return ir.widthCache[i];try{var a=document.getElementById(Dh);a||(a=document.createElement("span"),a.setAttribute("id",Dh),a.setAttribute("aria-hidden","true"),document.body.appendChild(a));var o=kc(kc({},uO),n);Object.assign(a.style,o),a.textContent="".concat(t);var u=a.getBoundingClientRect(),c={width:u.width,height:u.height};return ir.widthCache[i]=c,++ir.cacheCount>oO&&(ir.cacheCount=0,ir.widthCache={}),c}catch(s){return{width:0,height:0}}},sO=function(t){return{top:t.top+window.scrollY-document.documentElement.clientTop,left:t.left+window.scrollX-document.documentElement.clientLeft}};function gn(e){"@babel/helpers - typeof";return gn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},gn(e)}function Ei(e,t){return pO(e)||hO(e,t)||fO(e,t)||lO()}function lO(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function fO(e,t){if(e){if(typeof e=="string")return Bh(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Bh(e,t)}}function Bh(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function hO(e,t){var r=e==null?null:typeof Symbol!="undefined"&&e[Symbol.iterator]||e["@@iterator"];if(r!=null){var n,i,a,o,u=[],c=!0,s=!1;try{if(a=(r=r.call(e)).next,t===0){if(Object(r)!==r)return;c=!1}else for(;!(c=(n=a.call(r)).done)&&(u.push(n.value),u.length!==t);c=!0);}catch(f){s=!0,i=f}finally{try{if(!c&&r.return!=null&&(o=r.return(),Object(o)!==o))return}finally{if(s)throw i}}return u}}function pO(e){if(Array.isArray(e))return e}function dO(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function Lh(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,yO(n.key),n)}}function vO(e,t,r){return t&&Lh(e.prototype,t),r&&Lh(e,r),Object.defineProperty(e,"prototype",{writable:!1}),e}function yO(e){var t=mO(e,"string");return gn(t)=="symbol"?t:t+""}function mO(e,t){if(gn(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(gn(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}var qh=/(-?\d+(?:\.\d+)?[a-zA-Z%]*)([*/])(-?\d+(?:\.\d+)?[a-zA-Z%]*)/,Fh=/(-?\d+(?:\.\d+)?[a-zA-Z%]*)([+-])(-?\d+(?:\.\d+)?[a-zA-Z%]*)/,gO=/^px|cm|vh|vw|em|rem|%|mm|in|pt|pc|ex|ch|vmin|vmax|Q$/,bO=/(-?\d+(?:\.\d+)?)([a-zA-Z%]+)?/,Uy={cm:96/2.54,mm:96/25.4,pt:96/72,pc:96/6,in:96,Q:96/(2.54*40),px:1},xO=Object.keys(Uy),ur="NaN";function wO(e,t){return e*Uy[t]}var ai=function(){function e(t,r){dO(this,e),this.num=t,this.unit=r,this.num=t,this.unit=r,Number.isNaN(t)&&(this.unit=""),r!==""&&!gO.test(r)&&(this.num=NaN,this.unit=""),xO.includes(r)&&(this.num=wO(t,r),this.unit="px")}return vO(e,[{key:"add",value:function(r){return this.unit!==r.unit?new e(NaN,""):new e(this.num+r.num,this.unit)}},{key:"subtract",value:function(r){return this.unit!==r.unit?new e(NaN,""):new e(this.num-r.num,this.unit)}},{key:"multiply",value:function(r){return this.unit!==""&&r.unit!==""&&this.unit!==r.unit?new e(NaN,""):new e(this.num*r.num,this.unit||r.unit)}},{key:"divide",value:function(r){return this.unit!==""&&r.unit!==""&&this.unit!==r.unit?new e(NaN,""):new e(this.num/r.num,this.unit||r.unit)}},{key:"toString",value:function(){return"".concat(this.num).concat(this.unit)}},{key:"isNaN",value:function(){return Number.isNaN(this.num)}}],[{key:"parse",value:function(r){var n,i=(n=bO.exec(r))!==null&&n!==void 0?n:[],a=Ei(i,3),o=a[1],u=a[2];return new e(parseFloat(o),u!=null?u:"")}}])}();function Hy(e){if(e.includes(ur))return ur;for(var t=e;t.includes("*")||t.includes("/");){var r,n=(r=qh.exec(t))!==null&&r!==void 0?r:[],i=Ei(n,4),a=i[1],o=i[2],u=i[3],c=ai.parse(a!=null?a:""),s=ai.parse(u!=null?u:""),f=o==="*"?c.multiply(s):c.divide(s);if(f.isNaN())return ur;t=t.replace(qh,f.toString())}for(;t.includes("+")||/.-\d+(?:\.\d+)?/.test(t);){var l,h=(l=Fh.exec(t))!==null&&l!==void 0?l:[],d=Ei(h,4),y=d[1],v=d[2],p=d[3],b=ai.parse(y!=null?y:""),w=ai.parse(p!=null?p:""),g=v==="+"?b.add(w):b.subtract(w);if(g.isNaN())return ur;t=t.replace(Fh,g.toString())}return t}var Wh=/\(([^()]*)\)/;function OO(e){for(var t=e;t.includes("(");){var r=Wh.exec(t),n=Ei(r,2),i=n[1];t=t.replace(Wh,Hy(i))}return t}function SO(e){var t=e.replace(/\s+/g,"");return t=OO(t),t=Hy(t),t}function AO(e){try{return SO(e)}catch(t){return ur}}function Ou(e){var t=AO(e.slice(5,-1));return t===ur?"":t}var PO=["x","y","lineHeight","capHeight","scaleToFit","textAnchor","verticalAnchor","fill"],_O=["dx","dy","angle","className","breakAll"];function Nc(){return Nc=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Nc.apply(this,arguments)}function zh(e,t){if(e==null)return{};var r=EO(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function EO(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}function Uh(e,t){return MO(e)||$O(e,t)||jO(e,t)||TO()}function TO(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function jO(e,t){if(e){if(typeof e=="string")return Hh(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Hh(e,t)}}function Hh(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function $O(e,t){var r=e==null?null:typeof Symbol!="undefined"&&e[Symbol.iterator]||e["@@iterator"];if(r!=null){var n,i,a,o,u=[],c=!0,s=!1;try{if(a=(r=r.call(e)).next,t===0){if(Object(r)!==r)return;c=!1}else for(;!(c=(n=a.call(r)).done)&&(u.push(n.value),u.length!==t);c=!0);}catch(f){s=!0,i=f}finally{try{if(!c&&r.return!=null&&(o=r.return(),Object(o)!==o))return}finally{if(s)throw i}}return u}}function MO(e){if(Array.isArray(e))return e}var Ky=/[ \f\n\r\t\v\u2028\u2029]+/,Gy=function(t){var r=t.children,n=t.breakAll,i=t.style;try{var a=[];Z(r)||(n?a=r.toString().split(""):a=r.toString().split(Ky));var o=a.map(function(c){return{word:c,width:on(c,i).width}}),u=n?0:on(" ",i).width;return{wordsWithComputedWidth:o,spaceWidth:u}}catch(c){return null}},IO=function(t,r,n,i,a){var o=t.maxLines,u=t.children,c=t.style,s=t.breakAll,f=B(o),l=u,h=function(){var I=arguments.length>0&&arguments[0]!==void 0?arguments[0]:[];return I.reduce(function(k,N){var L=N.word,q=N.width,U=k[k.length-1];if(U&&(i==null||a||U.width+q+n<Number(i)))U.words.push(L),U.width+=q+n;else{var K={words:[L],width:q};k.push(K)}return k},[])},d=h(r),y=function(I){return I.reduce(function(k,N){return k.width>N.width?k:N})};if(!f)return d;for(var v="…",p=function(I){var k=l.slice(0,I),N=Gy({breakAll:s,style:c,children:k+v}).wordsWithComputedWidth,L=h(N),q=L.length>o||y(L).width>Number(i);return[q,L]},b=0,w=l.length-1,g=0,O;b<=w&&g<=l.length-1;){var m=Math.floor((b+w)/2),x=m-1,S=p(x),A=Uh(S,2),_=A[0],$=A[1],E=p(m),T=Uh(E,1),j=T[0];if(!_&&!j&&(b=m+1),_&&j&&(w=m-1),!_&&j){O=$;break}g++}return O||d},Kh=function(t){var r=Z(t)?[]:t.toString().split(Ky);return[{words:r}]},CO=function(t){var r=t.width,n=t.scaleToFit,i=t.children,a=t.style,o=t.breakAll,u=t.maxLines;if((r||n)&&!vt.isSsr){var c,s,f=Gy({breakAll:o,children:i,style:a});if(f){var l=f.wordsWithComputedWidth,h=f.spaceWidth;c=l,s=h}else return Kh(i);return IO({breakAll:o,children:i,maxLines:u,style:a},c,s,r,n)}return Kh(i)},Gh="#808080",Yt=function(t){var r=t.x,n=r===void 0?0:r,i=t.y,a=i===void 0?0:i,o=t.lineHeight,u=o===void 0?"1em":o,c=t.capHeight,s=c===void 0?"0.71em":c,f=t.scaleToFit,l=f===void 0?!1:f,h=t.textAnchor,d=h===void 0?"start":h,y=t.verticalAnchor,v=y===void 0?"end":y,p=t.fill,b=p===void 0?Gh:p,w=zh(t,PO),g=D.useMemo(function(){return CO({breakAll:w.breakAll,children:w.children,maxLines:w.maxLines,scaleToFit:l,style:w.style,width:w.width})},[w.breakAll,w.children,w.maxLines,l,w.style,w.width]),O=w.dx,m=w.dy,x=w.angle,S=w.className,A=w.breakAll,_=zh(w,_O);if(!Se(n)||!Se(a))return null;var $=n+(B(O)?O:0),E=a+(B(m)?m:0),T;switch(v){case"start":T=Ou("calc(".concat(s,")"));break;case"middle":T=Ou("calc(".concat((g.length-1)/2," * -").concat(u," + (").concat(s," / 2))"));break;default:T=Ou("calc(".concat(g.length-1," * -").concat(u,")"));break}var j=[];if(l){var C=g[0].width,I=w.width;j.push("scale(".concat((B(I)?I/C:1)/C,")"))}return x&&j.push("rotate(".concat(x,", ").concat($,", ").concat(E,")")),j.length&&(_.transform=j.join(" ")),P.createElement("text",Nc({},V(_,!0),{x:$,y:E,className:Q("recharts-text",S),textAnchor:d,fill:b.includes("url")?Gh:b}),g.map(function(k,N){var L=k.words.join(A?"":" ");return P.createElement("tspan",{x:$,dy:N===0?T:u,key:"".concat(L,"-").concat(N)},L)}))};function Et(e,t){return e==null||t==null?NaN:e<t?-1:e>t?1:e>=t?0:NaN}function kO(e,t){return e==null||t==null?NaN:t<e?-1:t>e?1:t>=e?0:NaN}function Ys(e){let t,r,n;e.length!==2?(t=Et,r=(u,c)=>Et(e(u),c),n=(u,c)=>e(u)-c):(t=e===Et||e===kO?e:NO,r=e,n=e);function i(u,c,s=0,f=u.length){if(s<f){if(t(c,c)!==0)return f;do{const l=s+f>>>1;r(u[l],c)<0?s=l+1:f=l}while(s<f)}return s}function a(u,c,s=0,f=u.length){if(s<f){if(t(c,c)!==0)return f;do{const l=s+f>>>1;r(u[l],c)<=0?s=l+1:f=l}while(s<f)}return s}function o(u,c,s=0,f=u.length){const l=i(u,c,s,f-1);return l>s&&n(u[l-1],c)>-n(u[l],c)?l-1:l}return{left:i,center:o,right:a}}function NO(){return 0}function Vy(e){return e===null?NaN:+e}function*RO(e,t){for(let r of e)r!=null&&(r=+r)>=r&&(yield r)}const DO=Ys(Et),Zn=DO.right;Ys(Vy).center;class Vh extends Map{constructor(t,r=qO){if(super(),Object.defineProperties(this,{_intern:{value:new Map},_key:{value:r}}),t!=null)for(const[n,i]of t)this.set(n,i)}get(t){return super.get(Xh(this,t))}has(t){return super.has(Xh(this,t))}set(t,r){return super.set(BO(this,t),r)}delete(t){return super.delete(LO(this,t))}}function Xh({_intern:e,_key:t},r){const n=t(r);return e.has(n)?e.get(n):r}function BO({_intern:e,_key:t},r){const n=t(r);return e.has(n)?e.get(n):(e.set(n,r),r)}function LO({_intern:e,_key:t},r){const n=t(r);return e.has(n)&&(r=e.get(n),e.delete(n)),r}function qO(e){return e!==null&&typeof e=="object"?e.valueOf():e}function FO(e=Et){if(e===Et)return Xy;if(typeof e!="function")throw new TypeError("compare is not a function");return(t,r)=>{const n=e(t,r);return n||n===0?n:(e(r,r)===0)-(e(t,t)===0)}}function Xy(e,t){return(e==null||!(e>=e))-(t==null||!(t>=t))||(e<t?-1:e>t?1:0)}const WO=Math.sqrt(50),zO=Math.sqrt(10),UO=Math.sqrt(2);function Ti(e,t,r){const n=(t-e)/Math.max(0,r),i=Math.floor(Math.log10(n)),a=n/Math.pow(10,i),o=a>=WO?10:a>=zO?5:a>=UO?2:1;let u,c,s;return i<0?(s=Math.pow(10,-i)/o,u=Math.round(e*s),c=Math.round(t*s),u/s<e&&++u,c/s>t&&--c,s=-s):(s=Math.pow(10,i)*o,u=Math.round(e/s),c=Math.round(t/s),u*s<e&&++u,c*s>t&&--c),c<u&&.5<=r&&r<2?Ti(e,t,r*2):[u,c,s]}function Rc(e,t,r){if(t=+t,e=+e,r=+r,!(r>0))return[];if(e===t)return[e];const n=t<e,[i,a,o]=n?Ti(t,e,r):Ti(e,t,r);if(!(a>=i))return[];const u=a-i+1,c=new Array(u);if(n)if(o<0)for(let s=0;s<u;++s)c[s]=(a-s)/-o;else for(let s=0;s<u;++s)c[s]=(a-s)*o;else if(o<0)for(let s=0;s<u;++s)c[s]=(i+s)/-o;else for(let s=0;s<u;++s)c[s]=(i+s)*o;return c}function Dc(e,t,r){return t=+t,e=+e,r=+r,Ti(e,t,r)[2]}function Bc(e,t,r){t=+t,e=+e,r=+r;const n=t<e,i=n?Dc(t,e,r):Dc(e,t,r);return(n?-1:1)*(i<0?1/-i:i)}function Yh(e,t){let r;for(const n of e)n!=null&&(r<n||r===void 0&&n>=n)&&(r=n);return r}function Zh(e,t){let r;for(const n of e)n!=null&&(r>n||r===void 0&&n>=n)&&(r=n);return r}function Yy(e,t,r=0,n=1/0,i){if(t=Math.floor(t),r=Math.floor(Math.max(0,r)),n=Math.floor(Math.min(e.length-1,n)),!(r<=t&&t<=n))return e;for(i=i===void 0?Xy:FO(i);n>r;){if(n-r>600){const c=n-r+1,s=t-r+1,f=Math.log(c),l=.5*Math.exp(2*f/3),h=.5*Math.sqrt(f*l*(c-l)/c)*(s-c/2<0?-1:1),d=Math.max(r,Math.floor(t-s*l/c+h)),y=Math.min(n,Math.floor(t+(c-s)*l/c+h));Yy(e,t,d,y,i)}const a=e[t];let o=r,u=n;for(Yr(e,r,t),i(e[n],a)>0&&Yr(e,r,n);o<u;){for(Yr(e,o,u),++o,--u;i(e[o],a)<0;)++o;for(;i(e[u],a)>0;)--u}i(e[r],a)===0?Yr(e,r,u):(++u,Yr(e,u,n)),u<=t&&(r=u+1),t<=u&&(n=u-1)}return e}function Yr(e,t,r){const n=e[t];e[t]=e[r],e[r]=n}function HO(e,t,r){if(e=Float64Array.from(RO(e)),!(!(n=e.length)||isNaN(t=+t))){if(t<=0||n<2)return Zh(e);if(t>=1)return Yh(e);var n,i=(n-1)*t,a=Math.floor(i),o=Yh(Yy(e,a).subarray(0,a+1)),u=Zh(e.subarray(a+1));return o+(u-o)*(i-a)}}function KO(e,t,r=Vy){if(!(!(n=e.length)||isNaN(t=+t))){if(t<=0||n<2)return+r(e[0],0,e);if(t>=1)return+r(e[n-1],n-1,e);var n,i=(n-1)*t,a=Math.floor(i),o=+r(e[a],a,e),u=+r(e[a+1],a+1,e);return o+(u-o)*(i-a)}}function GO(e,t,r){e=+e,t=+t,r=(i=arguments.length)<2?(t=e,e=0,1):i<3?1:+r;for(var n=-1,i=Math.max(0,Math.ceil((t-e)/r))|0,a=new Array(i);++n<i;)a[n]=e+n*r;return a}function Ye(e,t){switch(arguments.length){case 0:break;case 1:this.range(e);break;default:this.range(t).domain(e);break}return this}function wt(e,t){switch(arguments.length){case 0:break;case 1:{typeof e=="function"?this.interpolator(e):this.range(e);break}default:{this.domain(e),typeof t=="function"?this.interpolator(t):this.range(t);break}}return this}const Lc=Symbol("implicit");function Zs(){var e=new Vh,t=[],r=[],n=Lc;function i(a){let o=e.get(a);if(o===void 0){if(n!==Lc)return n;e.set(a,o=t.push(a)-1)}return r[o%r.length]}return i.domain=function(a){if(!arguments.length)return t.slice();t=[],e=new Vh;for(const o of a)e.has(o)||e.set(o,t.push(o)-1);return i},i.range=function(a){return arguments.length?(r=Array.from(a),i):r.slice()},i.unknown=function(a){return arguments.length?(n=a,i):n},i.copy=function(){return Zs(t,r).unknown(n)},Ye.apply(i,arguments),i}function bn(){var e=Zs().unknown(void 0),t=e.domain,r=e.range,n=0,i=1,a,o,u=!1,c=0,s=0,f=.5;delete e.unknown;function l(){var h=t().length,d=i<n,y=d?i:n,v=d?n:i;a=(v-y)/Math.max(1,h-c+s*2),u&&(a=Math.floor(a)),y+=(v-y-a*(h-c))*f,o=a*(1-c),u&&(y=Math.round(y),o=Math.round(o));var p=GO(h).map(function(b){return y+a*b});return r(d?p.reverse():p)}return e.domain=function(h){return arguments.length?(t(h),l()):t()},e.range=function(h){return arguments.length?([n,i]=h,n=+n,i=+i,l()):[n,i]},e.rangeRound=function(h){return[n,i]=h,n=+n,i=+i,u=!0,l()},e.bandwidth=function(){return o},e.step=function(){return a},e.round=function(h){return arguments.length?(u=!!h,l()):u},e.padding=function(h){return arguments.length?(c=Math.min(1,s=+h),l()):c},e.paddingInner=function(h){return arguments.length?(c=Math.min(1,h),l()):c},e.paddingOuter=function(h){return arguments.length?(s=+h,l()):s},e.align=function(h){return arguments.length?(f=Math.max(0,Math.min(1,h)),l()):f},e.copy=function(){return bn(t(),[n,i]).round(u).paddingInner(c).paddingOuter(s).align(f)},Ye.apply(l(),arguments)}function Zy(e){var t=e.copy;return e.padding=e.paddingOuter,delete e.paddingInner,delete e.paddingOuter,e.copy=function(){return Zy(t())},e}function un(){return Zy(bn.apply(null,arguments).paddingInner(1))}function Js(e,t,r){e.prototype=t.prototype=r,r.constructor=e}function Jy(e,t){var r=Object.create(e.prototype);for(var n in t)r[n]=t[n];return r}function Jn(){}var xn=.7,ji=1/xn,dr="\\s*([+-]?\\d+)\\s*",wn="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)\\s*",it="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)%\\s*",VO=/^#([0-9a-f]{3,8})$/,XO=new RegExp(`^rgb\\(${dr},${dr},${dr}\\)$`),YO=new RegExp(`^rgb\\(${it},${it},${it}\\)$`),ZO=new RegExp(`^rgba\\(${dr},${dr},${dr},${wn}\\)$`),JO=new RegExp(`^rgba\\(${it},${it},${it},${wn}\\)$`),QO=new RegExp(`^hsl\\(${wn},${it},${it}\\)$`),e1=new RegExp(`^hsla\\(${wn},${it},${it},${wn}\\)$`),Jh={aliceblue:15792383,antiquewhite:16444375,aqua:65535,aquamarine:8388564,azure:15794175,beige:16119260,bisque:16770244,black:0,blanchedalmond:16772045,blue:255,blueviolet:9055202,brown:10824234,burlywood:14596231,cadetblue:6266528,chartreuse:8388352,chocolate:13789470,coral:16744272,cornflowerblue:6591981,cornsilk:16775388,crimson:14423100,cyan:65535,darkblue:139,darkcyan:35723,darkgoldenrod:12092939,darkgray:11119017,darkgreen:25600,darkgrey:11119017,darkkhaki:12433259,darkmagenta:9109643,darkolivegreen:5597999,darkorange:16747520,darkorchid:10040012,darkred:9109504,darksalmon:15308410,darkseagreen:9419919,darkslateblue:4734347,darkslategray:3100495,darkslategrey:3100495,darkturquoise:52945,darkviolet:9699539,deeppink:16716947,deepskyblue:49151,dimgray:6908265,dimgrey:6908265,dodgerblue:2003199,firebrick:11674146,floralwhite:16775920,forestgreen:2263842,fuchsia:16711935,gainsboro:14474460,ghostwhite:16316671,gold:16766720,goldenrod:14329120,gray:8421504,green:32768,greenyellow:11403055,grey:8421504,honeydew:15794160,hotpink:16738740,indianred:13458524,indigo:4915330,ivory:16777200,khaki:15787660,lavender:15132410,lavenderblush:16773365,lawngreen:8190976,lemonchiffon:16775885,lightblue:11393254,lightcoral:15761536,lightcyan:14745599,lightgoldenrodyellow:16448210,lightgray:13882323,lightgreen:9498256,lightgrey:13882323,lightpink:16758465,lightsalmon:16752762,lightseagreen:2142890,lightskyblue:8900346,lightslategray:7833753,lightslategrey:7833753,lightsteelblue:11584734,lightyellow:16777184,lime:65280,limegreen:3329330,linen:16445670,magenta:16711935,maroon:8388608,mediumaquamarine:6737322,mediumblue:205,mediumorchid:12211667,mediumpurple:9662683,mediumseagreen:3978097,mediumslateblue:8087790,mediumspringgreen:64154,mediumturquoise:4772300,mediumvioletred:13047173,midnightblue:1644912,mintcream:16121850,mistyrose:16770273,moccasin:16770229,navajowhite:16768685,navy:128,oldlace:16643558,olive:8421376,olivedrab:7048739,orange:16753920,orangered:16729344,orchid:14315734,palegoldenrod:15657130,palegreen:10025880,paleturquoise:11529966,palevioletred:14381203,papayawhip:16773077,peachpuff:16767673,peru:13468991,pink:16761035,plum:14524637,powderblue:11591910,purple:8388736,rebeccapurple:6697881,red:16711680,rosybrown:12357519,royalblue:4286945,saddlebrown:9127187,salmon:16416882,sandybrown:16032864,seagreen:3050327,seashell:16774638,sienna:10506797,silver:12632256,skyblue:8900331,slateblue:6970061,slategray:7372944,slategrey:7372944,snow:16775930,springgreen:65407,steelblue:4620980,tan:13808780,teal:32896,thistle:14204888,tomato:16737095,turquoise:4251856,violet:15631086,wheat:16113331,white:16777215,whitesmoke:16119285,yellow:16776960,yellowgreen:10145074};Js(Jn,On,{copy(e){return Object.assign(new this.constructor,this,e)},displayable(){return this.rgb().displayable()},hex:Qh,formatHex:Qh,formatHex8:t1,formatHsl:r1,formatRgb:ep,toString:ep});function Qh(){return this.rgb().formatHex()}function t1(){return this.rgb().formatHex8()}function r1(){return Qy(this).formatHsl()}function ep(){return this.rgb().formatRgb()}function On(e){var t,r;return e=(e+"").trim().toLowerCase(),(t=VO.exec(e))?(r=t[1].length,t=parseInt(t[1],16),r===6?tp(t):r===3?new De(t>>8&15|t>>4&240,t>>4&15|t&240,(t&15)<<4|t&15,1):r===8?oi(t>>24&255,t>>16&255,t>>8&255,(t&255)/255):r===4?oi(t>>12&15|t>>8&240,t>>8&15|t>>4&240,t>>4&15|t&240,((t&15)<<4|t&15)/255):null):(t=XO.exec(e))?new De(t[1],t[2],t[3],1):(t=YO.exec(e))?new De(t[1]*255/100,t[2]*255/100,t[3]*255/100,1):(t=ZO.exec(e))?oi(t[1],t[2],t[3],t[4]):(t=JO.exec(e))?oi(t[1]*255/100,t[2]*255/100,t[3]*255/100,t[4]):(t=QO.exec(e))?ip(t[1],t[2]/100,t[3]/100,1):(t=e1.exec(e))?ip(t[1],t[2]/100,t[3]/100,t[4]):Jh.hasOwnProperty(e)?tp(Jh[e]):e==="transparent"?new De(NaN,NaN,NaN,0):null}function tp(e){return new De(e>>16&255,e>>8&255,e&255,1)}function oi(e,t,r,n){return n<=0&&(e=t=r=NaN),new De(e,t,r,n)}function n1(e){return e instanceof Jn||(e=On(e)),e?(e=e.rgb(),new De(e.r,e.g,e.b,e.opacity)):new De}function qc(e,t,r,n){return arguments.length===1?n1(e):new De(e,t,r,n==null?1:n)}function De(e,t,r,n){this.r=+e,this.g=+t,this.b=+r,this.opacity=+n}Js(De,qc,Jy(Jn,{brighter(e){return e=e==null?ji:Math.pow(ji,e),new De(this.r*e,this.g*e,this.b*e,this.opacity)},darker(e){return e=e==null?xn:Math.pow(xn,e),new De(this.r*e,this.g*e,this.b*e,this.opacity)},rgb(){return this},clamp(){return new De(Kt(this.r),Kt(this.g),Kt(this.b),$i(this.opacity))},displayable(){return-.5<=this.r&&this.r<255.5&&-.5<=this.g&&this.g<255.5&&-.5<=this.b&&this.b<255.5&&0<=this.opacity&&this.opacity<=1},hex:rp,formatHex:rp,formatHex8:i1,formatRgb:np,toString:np}));function rp(){return`#${zt(this.r)}${zt(this.g)}${zt(this.b)}`}function i1(){return`#${zt(this.r)}${zt(this.g)}${zt(this.b)}${zt((isNaN(this.opacity)?1:this.opacity)*255)}`}function np(){const e=$i(this.opacity);return`${e===1?"rgb(":"rgba("}${Kt(this.r)}, ${Kt(this.g)}, ${Kt(this.b)}${e===1?")":`, ${e})`}`}function $i(e){return isNaN(e)?1:Math.max(0,Math.min(1,e))}function Kt(e){return Math.max(0,Math.min(255,Math.round(e)||0))}function zt(e){return e=Kt(e),(e<16?"0":"")+e.toString(16)}function ip(e,t,r,n){return n<=0?e=t=r=NaN:r<=0||r>=1?e=t=NaN:t<=0&&(e=NaN),new Qe(e,t,r,n)}function Qy(e){if(e instanceof Qe)return new Qe(e.h,e.s,e.l,e.opacity);if(e instanceof Jn||(e=On(e)),!e)return new Qe;if(e instanceof Qe)return e;e=e.rgb();var t=e.r/255,r=e.g/255,n=e.b/255,i=Math.min(t,r,n),a=Math.max(t,r,n),o=NaN,u=a-i,c=(a+i)/2;return u?(t===a?o=(r-n)/u+(r<n)*6:r===a?o=(n-t)/u+2:o=(t-r)/u+4,u/=c<.5?a+i:2-a-i,o*=60):u=c>0&&c<1?0:o,new Qe(o,u,c,e.opacity)}function a1(e,t,r,n){return arguments.length===1?Qy(e):new Qe(e,t,r,n==null?1:n)}function Qe(e,t,r,n){this.h=+e,this.s=+t,this.l=+r,this.opacity=+n}Js(Qe,a1,Jy(Jn,{brighter(e){return e=e==null?ji:Math.pow(ji,e),new Qe(this.h,this.s,this.l*e,this.opacity)},darker(e){return e=e==null?xn:Math.pow(xn,e),new Qe(this.h,this.s,this.l*e,this.opacity)},rgb(){var e=this.h%360+(this.h<0)*360,t=isNaN(e)||isNaN(this.s)?0:this.s,r=this.l,n=r+(r<.5?r:1-r)*t,i=2*r-n;return new De(Su(e>=240?e-240:e+120,i,n),Su(e,i,n),Su(e<120?e+240:e-120,i,n),this.opacity)},clamp(){return new Qe(ap(this.h),ui(this.s),ui(this.l),$i(this.opacity))},displayable(){return(0<=this.s&&this.s<=1||isNaN(this.s))&&0<=this.l&&this.l<=1&&0<=this.opacity&&this.opacity<=1},formatHsl(){const e=$i(this.opacity);return`${e===1?"hsl(":"hsla("}${ap(this.h)}, ${ui(this.s)*100}%, ${ui(this.l)*100}%${e===1?")":`, ${e})`}`}}));function ap(e){return e=(e||0)%360,e<0?e+360:e}function ui(e){return Math.max(0,Math.min(1,e||0))}function Su(e,t,r){return(e<60?t+(r-t)*e/60:e<180?r:e<240?t+(r-t)*(240-e)/60:t)*255}const Qs=e=>()=>e;function o1(e,t){return function(r){return e+r*t}}function u1(e,t,r){return e=Math.pow(e,r),t=Math.pow(t,r)-e,r=1/r,function(n){return Math.pow(e+n*t,r)}}function c1(e){return(e=+e)==1?em:function(t,r){return r-t?u1(t,r,e):Qs(isNaN(t)?r:t)}}function em(e,t){var r=t-e;return r?o1(e,r):Qs(isNaN(e)?t:e)}const op=function e(t){var r=c1(t);function n(i,a){var o=r((i=qc(i)).r,(a=qc(a)).r),u=r(i.g,a.g),c=r(i.b,a.b),s=em(i.opacity,a.opacity);return function(f){return i.r=o(f),i.g=u(f),i.b=c(f),i.opacity=s(f),i+""}}return n.gamma=e,n}(1);function s1(e,t){t||(t=[]);var r=e?Math.min(t.length,e.length):0,n=t.slice(),i;return function(a){for(i=0;i<r;++i)n[i]=e[i]*(1-a)+t[i]*a;return n}}function l1(e){return ArrayBuffer.isView(e)&&!(e instanceof DataView)}function f1(e,t){var r=t?t.length:0,n=e?Math.min(r,e.length):0,i=new Array(n),a=new Array(r),o;for(o=0;o<n;++o)i[o]=Ur(e[o],t[o]);for(;o<r;++o)a[o]=t[o];return function(u){for(o=0;o<n;++o)a[o]=i[o](u);return a}}function h1(e,t){var r=new Date;return e=+e,t=+t,function(n){return r.setTime(e*(1-n)+t*n),r}}function Mi(e,t){return e=+e,t=+t,function(r){return e*(1-r)+t*r}}function p1(e,t){var r={},n={},i;(e===null||typeof e!="object")&&(e={}),(t===null||typeof t!="object")&&(t={});for(i in t)i in e?r[i]=Ur(e[i],t[i]):n[i]=t[i];return function(a){for(i in r)n[i]=r[i](a);return n}}var Fc=/[-+]?(?:\d+\.?\d*|\.?\d+)(?:[eE][-+]?\d+)?/g,Au=new RegExp(Fc.source,"g");function d1(e){return function(){return e}}function v1(e){return function(t){return e(t)+""}}function y1(e,t){var r=Fc.lastIndex=Au.lastIndex=0,n,i,a,o=-1,u=[],c=[];for(e=e+"",t=t+"";(n=Fc.exec(e))&&(i=Au.exec(t));)(a=i.index)>r&&(a=t.slice(r,a),u[o]?u[o]+=a:u[++o]=a),(n=n[0])===(i=i[0])?u[o]?u[o]+=i:u[++o]=i:(u[++o]=null,c.push({i:o,x:Mi(n,i)})),r=Au.lastIndex;return r<t.length&&(a=t.slice(r),u[o]?u[o]+=a:u[++o]=a),u.length<2?c[0]?v1(c[0].x):d1(t):(t=c.length,function(s){for(var f=0,l;f<t;++f)u[(l=c[f]).i]=l.x(s);return u.join("")})}function Ur(e,t){var r=typeof t,n;return t==null||r==="boolean"?Qs(t):(r==="number"?Mi:r==="string"?(n=On(t))?(t=n,op):y1:t instanceof On?op:t instanceof Date?h1:l1(t)?s1:Array.isArray(t)?f1:typeof t.valueOf!="function"&&typeof t.toString!="function"||isNaN(t)?p1:Mi)(e,t)}function el(e,t){return e=+e,t=+t,function(r){return Math.round(e*(1-r)+t*r)}}function m1(e,t){t===void 0&&(t=e,e=Ur);for(var r=0,n=t.length-1,i=t[0],a=new Array(n<0?0:n);r<n;)a[r]=e(i,i=t[++r]);return function(o){var u=Math.max(0,Math.min(n-1,Math.floor(o*=n)));return a[u](o-u)}}function g1(e){return function(){return e}}function Ii(e){return+e}var up=[0,1];function ke(e){return e}function Wc(e,t){return(t-=e=+e)?function(r){return(r-e)/t}:g1(isNaN(t)?NaN:.5)}function b1(e,t){var r;return e>t&&(r=e,e=t,t=r),function(n){return Math.max(e,Math.min(t,n))}}function x1(e,t,r){var n=e[0],i=e[1],a=t[0],o=t[1];return i<n?(n=Wc(i,n),a=r(o,a)):(n=Wc(n,i),a=r(a,o)),function(u){return a(n(u))}}function w1(e,t,r){var n=Math.min(e.length,t.length)-1,i=new Array(n),a=new Array(n),o=-1;for(e[n]<e[0]&&(e=e.slice().reverse(),t=t.slice().reverse());++o<n;)i[o]=Wc(e[o],e[o+1]),a[o]=r(t[o],t[o+1]);return function(u){var c=Zn(e,u,1,n)-1;return a[c](i[c](u))}}function Qn(e,t){return t.domain(e.domain()).range(e.range()).interpolate(e.interpolate()).clamp(e.clamp()).unknown(e.unknown())}function wa(){var e=up,t=up,r=Ur,n,i,a,o=ke,u,c,s;function f(){var h=Math.min(e.length,t.length);return o!==ke&&(o=b1(e[0],e[h-1])),u=h>2?w1:x1,c=s=null,l}function l(h){return h==null||isNaN(h=+h)?a:(c||(c=u(e.map(n),t,r)))(n(o(h)))}return l.invert=function(h){return o(i((s||(s=u(t,e.map(n),Mi)))(h)))},l.domain=function(h){return arguments.length?(e=Array.from(h,Ii),f()):e.slice()},l.range=function(h){return arguments.length?(t=Array.from(h),f()):t.slice()},l.rangeRound=function(h){return t=Array.from(h),r=el,f()},l.clamp=function(h){return arguments.length?(o=h?!0:ke,f()):o!==ke},l.interpolate=function(h){return arguments.length?(r=h,f()):r},l.unknown=function(h){return arguments.length?(a=h,l):a},function(h,d){return n=h,i=d,f()}}function tl(){return wa()(ke,ke)}function O1(e){return Math.abs(e=Math.round(e))>=1e21?e.toLocaleString("en").replace(/,/g,""):e.toString(10)}function Ci(e,t){if((r=(e=t?e.toExponential(t-1):e.toExponential()).indexOf("e"))<0)return null;var r,n=e.slice(0,r);return[n.length>1?n[0]+n.slice(2):n,+e.slice(r+1)]}function wr(e){return e=Ci(Math.abs(e)),e?e[1]:NaN}function S1(e,t){return function(r,n){for(var i=r.length,a=[],o=0,u=e[0],c=0;i>0&&u>0&&(c+u+1>n&&(u=Math.max(1,n-c)),a.push(r.substring(i-=u,i+u)),!((c+=u+1)>n));)u=e[o=(o+1)%e.length];return a.reverse().join(t)}}function A1(e){return function(t){return t.replace(/[0-9]/g,function(r){return e[+r]})}}var P1=/^(?:(.)?([<>=^]))?([+\-( ])?([$#])?(0)?(\d+)?(,)?(\.\d+)?(~)?([a-z%])?$/i;function Sn(e){if(!(t=P1.exec(e)))throw new Error("invalid format: "+e);var t;return new rl({fill:t[1],align:t[2],sign:t[3],symbol:t[4],zero:t[5],width:t[6],comma:t[7],precision:t[8]&&t[8].slice(1),trim:t[9],type:t[10]})}Sn.prototype=rl.prototype;function rl(e){this.fill=e.fill===void 0?" ":e.fill+"",this.align=e.align===void 0?">":e.align+"",this.sign=e.sign===void 0?"-":e.sign+"",this.symbol=e.symbol===void 0?"":e.symbol+"",this.zero=!!e.zero,this.width=e.width===void 0?void 0:+e.width,this.comma=!!e.comma,this.precision=e.precision===void 0?void 0:+e.precision,this.trim=!!e.trim,this.type=e.type===void 0?"":e.type+""}rl.prototype.toString=function(){return this.fill+this.align+this.sign+this.symbol+(this.zero?"0":"")+(this.width===void 0?"":Math.max(1,this.width|0))+(this.comma?",":"")+(this.precision===void 0?"":"."+Math.max(0,this.precision|0))+(this.trim?"~":"")+this.type};function _1(e){e:for(var t=e.length,r=1,n=-1,i;r<t;++r)switch(e[r]){case".":n=i=r;break;case"0":n===0&&(n=r),i=r;break;default:if(!+e[r])break e;n>0&&(n=0);break}return n>0?e.slice(0,n)+e.slice(i+1):e}var tm;function E1(e,t){var r=Ci(e,t);if(!r)return e+"";var n=r[0],i=r[1],a=i-(tm=Math.max(-8,Math.min(8,Math.floor(i/3)))*3)+1,o=n.length;return a===o?n:a>o?n+new Array(a-o+1).join("0"):a>0?n.slice(0,a)+"."+n.slice(a):"0."+new Array(1-a).join("0")+Ci(e,Math.max(0,t+a-1))[0]}function cp(e,t){var r=Ci(e,t);if(!r)return e+"";var n=r[0],i=r[1];return i<0?"0."+new Array(-i).join("0")+n:n.length>i+1?n.slice(0,i+1)+"."+n.slice(i+1):n+new Array(i-n.length+2).join("0")}const sp={"%":(e,t)=>(e*100).toFixed(t),b:e=>Math.round(e).toString(2),c:e=>e+"",d:O1,e:(e,t)=>e.toExponential(t),f:(e,t)=>e.toFixed(t),g:(e,t)=>e.toPrecision(t),o:e=>Math.round(e).toString(8),p:(e,t)=>cp(e*100,t),r:cp,s:E1,X:e=>Math.round(e).toString(16).toUpperCase(),x:e=>Math.round(e).toString(16)};function lp(e){return e}var fp=Array.prototype.map,hp=["y","z","a","f","p","n","µ","m","","k","M","G","T","P","E","Z","Y"];function T1(e){var t=e.grouping===void 0||e.thousands===void 0?lp:S1(fp.call(e.grouping,Number),e.thousands+""),r=e.currency===void 0?"":e.currency[0]+"",n=e.currency===void 0?"":e.currency[1]+"",i=e.decimal===void 0?".":e.decimal+"",a=e.numerals===void 0?lp:A1(fp.call(e.numerals,String)),o=e.percent===void 0?"%":e.percent+"",u=e.minus===void 0?"−":e.minus+"",c=e.nan===void 0?"NaN":e.nan+"";function s(l){l=Sn(l);var h=l.fill,d=l.align,y=l.sign,v=l.symbol,p=l.zero,b=l.width,w=l.comma,g=l.precision,O=l.trim,m=l.type;m==="n"?(w=!0,m="g"):sp[m]||(g===void 0&&(g=12),O=!0,m="g"),(p||h==="0"&&d==="=")&&(p=!0,h="0",d="=");var x=v==="$"?r:v==="#"&&/[boxX]/.test(m)?"0"+m.toLowerCase():"",S=v==="$"?n:/[%p]/.test(m)?o:"",A=sp[m],_=/[defgprs%]/.test(m);g=g===void 0?6:/[gprs]/.test(m)?Math.max(1,Math.min(21,g)):Math.max(0,Math.min(20,g));function $(E){var T=x,j=S,C,I,k;if(m==="c")j=A(E)+j,E="";else{E=+E;var N=E<0||1/E<0;if(E=isNaN(E)?c:A(Math.abs(E),g),O&&(E=_1(E)),N&&+E==0&&y!=="+"&&(N=!1),T=(N?y==="("?y:u:y==="-"||y==="("?"":y)+T,j=(m==="s"?hp[8+tm/3]:"")+j+(N&&y==="("?")":""),_){for(C=-1,I=E.length;++C<I;)if(k=E.charCodeAt(C),48>k||k>57){j=(k===46?i+E.slice(C+1):E.slice(C))+j,E=E.slice(0,C);break}}}w&&!p&&(E=t(E,1/0));var L=T.length+E.length+j.length,q=L<b?new Array(b-L+1).join(h):"";switch(w&&p&&(E=t(q+E,q.length?b-j.length:1/0),q=""),d){case"<":E=T+E+j+q;break;case"=":E=T+q+E+j;break;case"^":E=q.slice(0,L=q.length>>1)+T+E+j+q.slice(L);break;default:E=q+T+E+j;break}return a(E)}return $.toString=function(){return l+""},$}function f(l,h){var d=s((l=Sn(l),l.type="f",l)),y=Math.max(-8,Math.min(8,Math.floor(wr(h)/3)))*3,v=Math.pow(10,-y),p=hp[8+y/3];return function(b){return d(v*b)+p}}return{format:s,formatPrefix:f}}var ci,nl,rm;j1({thousands:",",grouping:[3],currency:["$",""]});function j1(e){return ci=T1(e),nl=ci.format,rm=ci.formatPrefix,ci}function $1(e){return Math.max(0,-wr(Math.abs(e)))}function M1(e,t){return Math.max(0,Math.max(-8,Math.min(8,Math.floor(wr(t)/3)))*3-wr(Math.abs(e)))}function I1(e,t){return e=Math.abs(e),t=Math.abs(t)-e,Math.max(0,wr(t)-wr(e))+1}function nm(e,t,r,n){var i=Bc(e,t,r),a;switch(n=Sn(n==null?",f":n),n.type){case"s":{var o=Math.max(Math.abs(e),Math.abs(t));return n.precision==null&&!isNaN(a=M1(i,o))&&(n.precision=a),rm(n,o)}case"":case"e":case"g":case"p":case"r":{n.precision==null&&!isNaN(a=I1(i,Math.max(Math.abs(e),Math.abs(t))))&&(n.precision=a-(n.type==="e"));break}case"f":case"%":{n.precision==null&&!isNaN(a=$1(i))&&(n.precision=a-(n.type==="%")*2);break}}return nl(n)}function jt(e){var t=e.domain;return e.ticks=function(r){var n=t();return Rc(n[0],n[n.length-1],r==null?10:r)},e.tickFormat=function(r,n){var i=t();return nm(i[0],i[i.length-1],r==null?10:r,n)},e.nice=function(r){r==null&&(r=10);var n=t(),i=0,a=n.length-1,o=n[i],u=n[a],c,s,f=10;for(u<o&&(s=o,o=u,u=s,s=i,i=a,a=s);f-- >0;){if(s=Dc(o,u,r),s===c)return n[i]=o,n[a]=u,t(n);if(s>0)o=Math.floor(o/s)*s,u=Math.ceil(u/s)*s;else if(s<0)o=Math.ceil(o*s)/s,u=Math.floor(u*s)/s;else break;c=s}return e},e}function ki(){var e=tl();return e.copy=function(){return Qn(e,ki())},Ye.apply(e,arguments),jt(e)}function im(e){var t;function r(n){return n==null||isNaN(n=+n)?t:n}return r.invert=r,r.domain=r.range=function(n){return arguments.length?(e=Array.from(n,Ii),r):e.slice()},r.unknown=function(n){return arguments.length?(t=n,r):t},r.copy=function(){return im(e).unknown(t)},e=arguments.length?Array.from(e,Ii):[0,1],jt(r)}function am(e,t){e=e.slice();var r=0,n=e.length-1,i=e[r],a=e[n],o;return a<i&&(o=r,r=n,n=o,o=i,i=a,a=o),e[r]=t.floor(i),e[n]=t.ceil(a),e}function pp(e){return Math.log(e)}function dp(e){return Math.exp(e)}function C1(e){return-Math.log(-e)}function k1(e){return-Math.exp(-e)}function N1(e){return isFinite(e)?+("1e"+e):e<0?0:e}function R1(e){return e===10?N1:e===Math.E?Math.exp:t=>Math.pow(e,t)}function D1(e){return e===Math.E?Math.log:e===10&&Math.log10||e===2&&Math.log2||(e=Math.log(e),t=>Math.log(t)/e)}function vp(e){return(t,r)=>-e(-t,r)}function il(e){const t=e(pp,dp),r=t.domain;let n=10,i,a;function o(){return i=D1(n),a=R1(n),r()[0]<0?(i=vp(i),a=vp(a),e(C1,k1)):e(pp,dp),t}return t.base=function(u){return arguments.length?(n=+u,o()):n},t.domain=function(u){return arguments.length?(r(u),o()):r()},t.ticks=u=>{const c=r();let s=c[0],f=c[c.length-1];const l=f<s;l&&([s,f]=[f,s]);let h=i(s),d=i(f),y,v;const p=u==null?10:+u;let b=[];if(!(n%1)&&d-h<p){if(h=Math.floor(h),d=Math.ceil(d),s>0){for(;h<=d;++h)for(y=1;y<n;++y)if(v=h<0?y/a(-h):y*a(h),!(v<s)){if(v>f)break;b.push(v)}}else for(;h<=d;++h)for(y=n-1;y>=1;--y)if(v=h>0?y/a(-h):y*a(h),!(v<s)){if(v>f)break;b.push(v)}b.length*2<p&&(b=Rc(s,f,p))}else b=Rc(h,d,Math.min(d-h,p)).map(a);return l?b.reverse():b},t.tickFormat=(u,c)=>{if(u==null&&(u=10),c==null&&(c=n===10?"s":","),typeof c!="function"&&(!(n%1)&&(c=Sn(c)).precision==null&&(c.trim=!0),c=nl(c)),u===1/0)return c;const s=Math.max(1,n*u/t.ticks().length);return f=>{let l=f/a(Math.round(i(f)));return l*n<n-.5&&(l*=n),l<=s?c(f):""}},t.nice=()=>r(am(r(),{floor:u=>a(Math.floor(i(u))),ceil:u=>a(Math.ceil(i(u)))})),t}function om(){const e=il(wa()).domain([1,10]);return e.copy=()=>Qn(e,om()).base(e.base()),Ye.apply(e,arguments),e}function yp(e){return function(t){return Math.sign(t)*Math.log1p(Math.abs(t/e))}}function mp(e){return function(t){return Math.sign(t)*Math.expm1(Math.abs(t))*e}}function al(e){var t=1,r=e(yp(t),mp(t));return r.constant=function(n){return arguments.length?e(yp(t=+n),mp(t)):t},jt(r)}function um(){var e=al(wa());return e.copy=function(){return Qn(e,um()).constant(e.constant())},Ye.apply(e,arguments)}function gp(e){return function(t){return t<0?-Math.pow(-t,e):Math.pow(t,e)}}function B1(e){return e<0?-Math.sqrt(-e):Math.sqrt(e)}function L1(e){return e<0?-e*e:e*e}function ol(e){var t=e(ke,ke),r=1;function n(){return r===1?e(ke,ke):r===.5?e(B1,L1):e(gp(r),gp(1/r))}return t.exponent=function(i){return arguments.length?(r=+i,n()):r},jt(t)}function ul(){var e=ol(wa());return e.copy=function(){return Qn(e,ul()).exponent(e.exponent())},Ye.apply(e,arguments),e}function q1(){return ul.apply(null,arguments).exponent(.5)}function bp(e){return Math.sign(e)*e*e}function F1(e){return Math.sign(e)*Math.sqrt(Math.abs(e))}function cm(){var e=tl(),t=[0,1],r=!1,n;function i(a){var o=F1(e(a));return isNaN(o)?n:r?Math.round(o):o}return i.invert=function(a){return e.invert(bp(a))},i.domain=function(a){return arguments.length?(e.domain(a),i):e.domain()},i.range=function(a){return arguments.length?(e.range((t=Array.from(a,Ii)).map(bp)),i):t.slice()},i.rangeRound=function(a){return i.range(a).round(!0)},i.round=function(a){return arguments.length?(r=!!a,i):r},i.clamp=function(a){return arguments.length?(e.clamp(a),i):e.clamp()},i.unknown=function(a){return arguments.length?(n=a,i):n},i.copy=function(){return cm(e.domain(),t).round(r).clamp(e.clamp()).unknown(n)},Ye.apply(i,arguments),jt(i)}function sm(){var e=[],t=[],r=[],n;function i(){var o=0,u=Math.max(1,t.length);for(r=new Array(u-1);++o<u;)r[o-1]=KO(e,o/u);return a}function a(o){return o==null||isNaN(o=+o)?n:t[Zn(r,o)]}return a.invertExtent=function(o){var u=t.indexOf(o);return u<0?[NaN,NaN]:[u>0?r[u-1]:e[0],u<r.length?r[u]:e[e.length-1]]},a.domain=function(o){if(!arguments.length)return e.slice();e=[];for(let u of o)u!=null&&!isNaN(u=+u)&&e.push(u);return e.sort(Et),i()},a.range=function(o){return arguments.length?(t=Array.from(o),i()):t.slice()},a.unknown=function(o){return arguments.length?(n=o,a):n},a.quantiles=function(){return r.slice()},a.copy=function(){return sm().domain(e).range(t).unknown(n)},Ye.apply(a,arguments)}function lm(){var e=0,t=1,r=1,n=[.5],i=[0,1],a;function o(c){return c!=null&&c<=c?i[Zn(n,c,0,r)]:a}function u(){var c=-1;for(n=new Array(r);++c<r;)n[c]=((c+1)*t-(c-r)*e)/(r+1);return o}return o.domain=function(c){return arguments.length?([e,t]=c,e=+e,t=+t,u()):[e,t]},o.range=function(c){return arguments.length?(r=(i=Array.from(c)).length-1,u()):i.slice()},o.invertExtent=function(c){var s=i.indexOf(c);return s<0?[NaN,NaN]:s<1?[e,n[0]]:s>=r?[n[r-1],t]:[n[s-1],n[s]]},o.unknown=function(c){return arguments.length&&(a=c),o},o.thresholds=function(){return n.slice()},o.copy=function(){return lm().domain([e,t]).range(i).unknown(a)},Ye.apply(jt(o),arguments)}function fm(){var e=[.5],t=[0,1],r,n=1;function i(a){return a!=null&&a<=a?t[Zn(e,a,0,n)]:r}return i.domain=function(a){return arguments.length?(e=Array.from(a),n=Math.min(e.length,t.length-1),i):e.slice()},i.range=function(a){return arguments.length?(t=Array.from(a),n=Math.min(e.length,t.length-1),i):t.slice()},i.invertExtent=function(a){var o=t.indexOf(a);return[e[o-1],e[o]]},i.unknown=function(a){return arguments.length?(r=a,i):r},i.copy=function(){return fm().domain(e).range(t).unknown(r)},Ye.apply(i,arguments)}const Pu=new Date,_u=new Date;function Ae(e,t,r,n){function i(a){return e(a=arguments.length===0?new Date:new Date(+a)),a}return i.floor=a=>(e(a=new Date(+a)),a),i.ceil=a=>(e(a=new Date(a-1)),t(a,1),e(a),a),i.round=a=>{const o=i(a),u=i.ceil(a);return a-o<u-a?o:u},i.offset=(a,o)=>(t(a=new Date(+a),o==null?1:Math.floor(o)),a),i.range=(a,o,u)=>{const c=[];if(a=i.ceil(a),u=u==null?1:Math.floor(u),!(a<o)||!(u>0))return c;let s;do c.push(s=new Date(+a)),t(a,u),e(a);while(s<a&&a<o);return c},i.filter=a=>Ae(o=>{if(o>=o)for(;e(o),!a(o);)o.setTime(o-1)},(o,u)=>{if(o>=o)if(u<0)for(;++u<=0;)for(;t(o,-1),!a(o););else for(;--u>=0;)for(;t(o,1),!a(o););}),r&&(i.count=(a,o)=>(Pu.setTime(+a),_u.setTime(+o),e(Pu),e(_u),Math.floor(r(Pu,_u))),i.every=a=>(a=Math.floor(a),!isFinite(a)||!(a>0)?null:a>1?i.filter(n?o=>n(o)%a===0:o=>i.count(0,o)%a===0):i)),i}const Ni=Ae(()=>{},(e,t)=>{e.setTime(+e+t)},(e,t)=>t-e);Ni.every=e=>(e=Math.floor(e),!isFinite(e)||!(e>0)?null:e>1?Ae(t=>{t.setTime(Math.floor(t/e)*e)},(t,r)=>{t.setTime(+t+r*e)},(t,r)=>(r-t)/e):Ni);Ni.range;const ft=1e3,Ge=ft*60,ht=Ge*60,mt=ht*24,cl=mt*7,xp=mt*30,Eu=mt*365,Ut=Ae(e=>{e.setTime(e-e.getMilliseconds())},(e,t)=>{e.setTime(+e+t*ft)},(e,t)=>(t-e)/ft,e=>e.getUTCSeconds());Ut.range;const sl=Ae(e=>{e.setTime(e-e.getMilliseconds()-e.getSeconds()*ft)},(e,t)=>{e.setTime(+e+t*Ge)},(e,t)=>(t-e)/Ge,e=>e.getMinutes());sl.range;const ll=Ae(e=>{e.setUTCSeconds(0,0)},(e,t)=>{e.setTime(+e+t*Ge)},(e,t)=>(t-e)/Ge,e=>e.getUTCMinutes());ll.range;const fl=Ae(e=>{e.setTime(e-e.getMilliseconds()-e.getSeconds()*ft-e.getMinutes()*Ge)},(e,t)=>{e.setTime(+e+t*ht)},(e,t)=>(t-e)/ht,e=>e.getHours());fl.range;const hl=Ae(e=>{e.setUTCMinutes(0,0,0)},(e,t)=>{e.setTime(+e+t*ht)},(e,t)=>(t-e)/ht,e=>e.getUTCHours());hl.range;const ei=Ae(e=>e.setHours(0,0,0,0),(e,t)=>e.setDate(e.getDate()+t),(e,t)=>(t-e-(t.getTimezoneOffset()-e.getTimezoneOffset())*Ge)/mt,e=>e.getDate()-1);ei.range;const Oa=Ae(e=>{e.setUTCHours(0,0,0,0)},(e,t)=>{e.setUTCDate(e.getUTCDate()+t)},(e,t)=>(t-e)/mt,e=>e.getUTCDate()-1);Oa.range;const hm=Ae(e=>{e.setUTCHours(0,0,0,0)},(e,t)=>{e.setUTCDate(e.getUTCDate()+t)},(e,t)=>(t-e)/mt,e=>Math.floor(e/mt));hm.range;function er(e){return Ae(t=>{t.setDate(t.getDate()-(t.getDay()+7-e)%7),t.setHours(0,0,0,0)},(t,r)=>{t.setDate(t.getDate()+r*7)},(t,r)=>(r-t-(r.getTimezoneOffset()-t.getTimezoneOffset())*Ge)/cl)}const Sa=er(0),Ri=er(1),W1=er(2),z1=er(3),Or=er(4),U1=er(5),H1=er(6);Sa.range;Ri.range;W1.range;z1.range;Or.range;U1.range;H1.range;function tr(e){return Ae(t=>{t.setUTCDate(t.getUTCDate()-(t.getUTCDay()+7-e)%7),t.setUTCHours(0,0,0,0)},(t,r)=>{t.setUTCDate(t.getUTCDate()+r*7)},(t,r)=>(r-t)/cl)}const Aa=tr(0),Di=tr(1),K1=tr(2),G1=tr(3),Sr=tr(4),V1=tr(5),X1=tr(6);Aa.range;Di.range;K1.range;G1.range;Sr.range;V1.range;X1.range;const pl=Ae(e=>{e.setDate(1),e.setHours(0,0,0,0)},(e,t)=>{e.setMonth(e.getMonth()+t)},(e,t)=>t.getMonth()-e.getMonth()+(t.getFullYear()-e.getFullYear())*12,e=>e.getMonth());pl.range;const dl=Ae(e=>{e.setUTCDate(1),e.setUTCHours(0,0,0,0)},(e,t)=>{e.setUTCMonth(e.getUTCMonth()+t)},(e,t)=>t.getUTCMonth()-e.getUTCMonth()+(t.getUTCFullYear()-e.getUTCFullYear())*12,e=>e.getUTCMonth());dl.range;const gt=Ae(e=>{e.setMonth(0,1),e.setHours(0,0,0,0)},(e,t)=>{e.setFullYear(e.getFullYear()+t)},(e,t)=>t.getFullYear()-e.getFullYear(),e=>e.getFullYear());gt.every=e=>!isFinite(e=Math.floor(e))||!(e>0)?null:Ae(t=>{t.setFullYear(Math.floor(t.getFullYear()/e)*e),t.setMonth(0,1),t.setHours(0,0,0,0)},(t,r)=>{t.setFullYear(t.getFullYear()+r*e)});gt.range;const bt=Ae(e=>{e.setUTCMonth(0,1),e.setUTCHours(0,0,0,0)},(e,t)=>{e.setUTCFullYear(e.getUTCFullYear()+t)},(e,t)=>t.getUTCFullYear()-e.getUTCFullYear(),e=>e.getUTCFullYear());bt.every=e=>!isFinite(e=Math.floor(e))||!(e>0)?null:Ae(t=>{t.setUTCFullYear(Math.floor(t.getUTCFullYear()/e)*e),t.setUTCMonth(0,1),t.setUTCHours(0,0,0,0)},(t,r)=>{t.setUTCFullYear(t.getUTCFullYear()+r*e)});bt.range;function pm(e,t,r,n,i,a){const o=[[Ut,1,ft],[Ut,5,5*ft],[Ut,15,15*ft],[Ut,30,30*ft],[a,1,Ge],[a,5,5*Ge],[a,15,15*Ge],[a,30,30*Ge],[i,1,ht],[i,3,3*ht],[i,6,6*ht],[i,12,12*ht],[n,1,mt],[n,2,2*mt],[r,1,cl],[t,1,xp],[t,3,3*xp],[e,1,Eu]];function u(s,f,l){const h=f<s;h&&([s,f]=[f,s]);const d=l&&typeof l.range=="function"?l:c(s,f,l),y=d?d.range(s,+f+1):[];return h?y.reverse():y}function c(s,f,l){const h=Math.abs(f-s)/l,d=Ys(([,,p])=>p).right(o,h);if(d===o.length)return e.every(Bc(s/Eu,f/Eu,l));if(d===0)return Ni.every(Math.max(Bc(s,f,l),1));const[y,v]=o[h/o[d-1][2]<o[d][2]/h?d-1:d];return y.every(v)}return[u,c]}const[Y1,Z1]=pm(bt,dl,Aa,hm,hl,ll),[J1,Q1]=pm(gt,pl,Sa,ei,fl,sl);function Tu(e){if(0<=e.y&&e.y<100){var t=new Date(-1,e.m,e.d,e.H,e.M,e.S,e.L);return t.setFullYear(e.y),t}return new Date(e.y,e.m,e.d,e.H,e.M,e.S,e.L)}function ju(e){if(0<=e.y&&e.y<100){var t=new Date(Date.UTC(-1,e.m,e.d,e.H,e.M,e.S,e.L));return t.setUTCFullYear(e.y),t}return new Date(Date.UTC(e.y,e.m,e.d,e.H,e.M,e.S,e.L))}function Zr(e,t,r){return{y:e,m:t,d:r,H:0,M:0,S:0,L:0}}function eS(e){var t=e.dateTime,r=e.date,n=e.time,i=e.periods,a=e.days,o=e.shortDays,u=e.months,c=e.shortMonths,s=Jr(i),f=Qr(i),l=Jr(a),h=Qr(a),d=Jr(o),y=Qr(o),v=Jr(u),p=Qr(u),b=Jr(c),w=Qr(c),g={a:N,A:L,b:q,B:U,c:null,d:_p,e:_p,f:SS,g:CS,G:NS,H:xS,I:wS,j:OS,L:dm,m:AS,M:PS,p:K,q:W,Q:jp,s:$p,S:_S,u:ES,U:TS,V:jS,w:$S,W:MS,x:null,X:null,y:IS,Y:kS,Z:RS,"%":Tp},O={a:G,A:fe,b:ye,B:Be,c:null,d:Ep,e:Ep,f:qS,g:YS,G:JS,H:DS,I:BS,j:LS,L:ym,m:FS,M:WS,p:Ct,q:Ne,Q:jp,s:$p,S:zS,u:US,U:HS,V:KS,w:GS,W:VS,x:null,X:null,y:XS,Y:ZS,Z:QS,"%":Tp},m={a:$,A:E,b:T,B:j,c:C,d:Ap,e:Ap,f:yS,g:Sp,G:Op,H:Pp,I:Pp,j:hS,L:vS,m:fS,M:pS,p:_,q:lS,Q:gS,s:bS,S:dS,u:aS,U:oS,V:uS,w:iS,W:cS,x:I,X:k,y:Sp,Y:Op,Z:sS,"%":mS};g.x=x(r,g),g.X=x(n,g),g.c=x(t,g),O.x=x(r,O),O.X=x(n,O),O.c=x(t,O);function x(F,Y){return function(J){var R=[],de=-1,ee=0,be=F.length,xe,Re,Ot;for(J instanceof Date||(J=new Date(+J));++de<be;)F.charCodeAt(de)===37&&(R.push(F.slice(ee,de)),(Re=wp[xe=F.charAt(++de)])!=null?xe=F.charAt(++de):Re=xe==="e"?" ":"0",(Ot=Y[xe])&&(xe=Ot(J,Re)),R.push(xe),ee=de+1);return R.push(F.slice(ee,de)),R.join("")}}function S(F,Y){return function(J){var R=Zr(1900,void 0,1),de=A(R,F,J+="",0),ee,be;if(de!=J.length)return null;if("Q"in R)return new Date(R.Q);if("s"in R)return new Date(R.s*1e3+("L"in R?R.L:0));if(Y&&!("Z"in R)&&(R.Z=0),"p"in R&&(R.H=R.H%12+R.p*12),R.m===void 0&&(R.m="q"in R?R.q:0),"V"in R){if(R.V<1||R.V>53)return null;"w"in R||(R.w=1),"Z"in R?(ee=ju(Zr(R.y,0,1)),be=ee.getUTCDay(),ee=be>4||be===0?Di.ceil(ee):Di(ee),ee=Oa.offset(ee,(R.V-1)*7),R.y=ee.getUTCFullYear(),R.m=ee.getUTCMonth(),R.d=ee.getUTCDate()+(R.w+6)%7):(ee=Tu(Zr(R.y,0,1)),be=ee.getDay(),ee=be>4||be===0?Ri.ceil(ee):Ri(ee),ee=ei.offset(ee,(R.V-1)*7),R.y=ee.getFullYear(),R.m=ee.getMonth(),R.d=ee.getDate()+(R.w+6)%7)}else("W"in R||"U"in R)&&("w"in R||(R.w="u"in R?R.u%7:"W"in R?1:0),be="Z"in R?ju(Zr(R.y,0,1)).getUTCDay():Tu(Zr(R.y,0,1)).getDay(),R.m=0,R.d="W"in R?(R.w+6)%7+R.W*7-(be+5)%7:R.w+R.U*7-(be+6)%7);return"Z"in R?(R.H+=R.Z/100|0,R.M+=R.Z%100,ju(R)):Tu(R)}}function A(F,Y,J,R){for(var de=0,ee=Y.length,be=J.length,xe,Re;de<ee;){if(R>=be)return-1;if(xe=Y.charCodeAt(de++),xe===37){if(xe=Y.charAt(de++),Re=m[xe in wp?Y.charAt(de++):xe],!Re||(R=Re(F,J,R))<0)return-1}else if(xe!=J.charCodeAt(R++))return-1}return R}function _(F,Y,J){var R=s.exec(Y.slice(J));return R?(F.p=f.get(R[0].toLowerCase()),J+R[0].length):-1}function $(F,Y,J){var R=d.exec(Y.slice(J));return R?(F.w=y.get(R[0].toLowerCase()),J+R[0].length):-1}function E(F,Y,J){var R=l.exec(Y.slice(J));return R?(F.w=h.get(R[0].toLowerCase()),J+R[0].length):-1}function T(F,Y,J){var R=b.exec(Y.slice(J));return R?(F.m=w.get(R[0].toLowerCase()),J+R[0].length):-1}function j(F,Y,J){var R=v.exec(Y.slice(J));return R?(F.m=p.get(R[0].toLowerCase()),J+R[0].length):-1}function C(F,Y,J){return A(F,t,Y,J)}function I(F,Y,J){return A(F,r,Y,J)}function k(F,Y,J){return A(F,n,Y,J)}function N(F){return o[F.getDay()]}function L(F){return a[F.getDay()]}function q(F){return c[F.getMonth()]}function U(F){return u[F.getMonth()]}function K(F){return i[+(F.getHours()>=12)]}function W(F){return 1+~~(F.getMonth()/3)}function G(F){return o[F.getUTCDay()]}function fe(F){return a[F.getUTCDay()]}function ye(F){return c[F.getUTCMonth()]}function Be(F){return u[F.getUTCMonth()]}function Ct(F){return i[+(F.getUTCHours()>=12)]}function Ne(F){return 1+~~(F.getUTCMonth()/3)}return{format:function(F){var Y=x(F+="",g);return Y.toString=function(){return F},Y},parse:function(F){var Y=S(F+="",!1);return Y.toString=function(){return F},Y},utcFormat:function(F){var Y=x(F+="",O);return Y.toString=function(){return F},Y},utcParse:function(F){var Y=S(F+="",!0);return Y.toString=function(){return F},Y}}}var wp={"-":"",_:" ",0:"0"},Ee=/^\s*\d+/,tS=/^%/,rS=/[\\^$*+?|[\]().{}]/g;function te(e,t,r){var n=e<0?"-":"",i=(n?-e:e)+"",a=i.length;return n+(a<r?new Array(r-a+1).join(t)+i:i)}function nS(e){return e.replace(rS,"\\$&")}function Jr(e){return new RegExp("^(?:"+e.map(nS).join("|")+")","i")}function Qr(e){return new Map(e.map((t,r)=>[t.toLowerCase(),r]))}function iS(e,t,r){var n=Ee.exec(t.slice(r,r+1));return n?(e.w=+n[0],r+n[0].length):-1}function aS(e,t,r){var n=Ee.exec(t.slice(r,r+1));return n?(e.u=+n[0],r+n[0].length):-1}function oS(e,t,r){var n=Ee.exec(t.slice(r,r+2));return n?(e.U=+n[0],r+n[0].length):-1}function uS(e,t,r){var n=Ee.exec(t.slice(r,r+2));return n?(e.V=+n[0],r+n[0].length):-1}function cS(e,t,r){var n=Ee.exec(t.slice(r,r+2));return n?(e.W=+n[0],r+n[0].length):-1}function Op(e,t,r){var n=Ee.exec(t.slice(r,r+4));return n?(e.y=+n[0],r+n[0].length):-1}function Sp(e,t,r){var n=Ee.exec(t.slice(r,r+2));return n?(e.y=+n[0]+(+n[0]>68?1900:2e3),r+n[0].length):-1}function sS(e,t,r){var n=/^(Z)|([+-]\d\d)(?::?(\d\d))?/.exec(t.slice(r,r+6));return n?(e.Z=n[1]?0:-(n[2]+(n[3]||"00")),r+n[0].length):-1}function lS(e,t,r){var n=Ee.exec(t.slice(r,r+1));return n?(e.q=n[0]*3-3,r+n[0].length):-1}function fS(e,t,r){var n=Ee.exec(t.slice(r,r+2));return n?(e.m=n[0]-1,r+n[0].length):-1}function Ap(e,t,r){var n=Ee.exec(t.slice(r,r+2));return n?(e.d=+n[0],r+n[0].length):-1}function hS(e,t,r){var n=Ee.exec(t.slice(r,r+3));return n?(e.m=0,e.d=+n[0],r+n[0].length):-1}function Pp(e,t,r){var n=Ee.exec(t.slice(r,r+2));return n?(e.H=+n[0],r+n[0].length):-1}function pS(e,t,r){var n=Ee.exec(t.slice(r,r+2));return n?(e.M=+n[0],r+n[0].length):-1}function dS(e,t,r){var n=Ee.exec(t.slice(r,r+2));return n?(e.S=+n[0],r+n[0].length):-1}function vS(e,t,r){var n=Ee.exec(t.slice(r,r+3));return n?(e.L=+n[0],r+n[0].length):-1}function yS(e,t,r){var n=Ee.exec(t.slice(r,r+6));return n?(e.L=Math.floor(n[0]/1e3),r+n[0].length):-1}function mS(e,t,r){var n=tS.exec(t.slice(r,r+1));return n?r+n[0].length:-1}function gS(e,t,r){var n=Ee.exec(t.slice(r));return n?(e.Q=+n[0],r+n[0].length):-1}function bS(e,t,r){var n=Ee.exec(t.slice(r));return n?(e.s=+n[0],r+n[0].length):-1}function _p(e,t){return te(e.getDate(),t,2)}function xS(e,t){return te(e.getHours(),t,2)}function wS(e,t){return te(e.getHours()%12||12,t,2)}function OS(e,t){return te(1+ei.count(gt(e),e),t,3)}function dm(e,t){return te(e.getMilliseconds(),t,3)}function SS(e,t){return dm(e,t)+"000"}function AS(e,t){return te(e.getMonth()+1,t,2)}function PS(e,t){return te(e.getMinutes(),t,2)}function _S(e,t){return te(e.getSeconds(),t,2)}function ES(e){var t=e.getDay();return t===0?7:t}function TS(e,t){return te(Sa.count(gt(e)-1,e),t,2)}function vm(e){var t=e.getDay();return t>=4||t===0?Or(e):Or.ceil(e)}function jS(e,t){return e=vm(e),te(Or.count(gt(e),e)+(gt(e).getDay()===4),t,2)}function $S(e){return e.getDay()}function MS(e,t){return te(Ri.count(gt(e)-1,e),t,2)}function IS(e,t){return te(e.getFullYear()%100,t,2)}function CS(e,t){return e=vm(e),te(e.getFullYear()%100,t,2)}function kS(e,t){return te(e.getFullYear()%1e4,t,4)}function NS(e,t){var r=e.getDay();return e=r>=4||r===0?Or(e):Or.ceil(e),te(e.getFullYear()%1e4,t,4)}function RS(e){var t=e.getTimezoneOffset();return(t>0?"-":(t*=-1,"+"))+te(t/60|0,"0",2)+te(t%60,"0",2)}function Ep(e,t){return te(e.getUTCDate(),t,2)}function DS(e,t){return te(e.getUTCHours(),t,2)}function BS(e,t){return te(e.getUTCHours()%12||12,t,2)}function LS(e,t){return te(1+Oa.count(bt(e),e),t,3)}function ym(e,t){return te(e.getUTCMilliseconds(),t,3)}function qS(e,t){return ym(e,t)+"000"}function FS(e,t){return te(e.getUTCMonth()+1,t,2)}function WS(e,t){return te(e.getUTCMinutes(),t,2)}function zS(e,t){return te(e.getUTCSeconds(),t,2)}function US(e){var t=e.getUTCDay();return t===0?7:t}function HS(e,t){return te(Aa.count(bt(e)-1,e),t,2)}function mm(e){var t=e.getUTCDay();return t>=4||t===0?Sr(e):Sr.ceil(e)}function KS(e,t){return e=mm(e),te(Sr.count(bt(e),e)+(bt(e).getUTCDay()===4),t,2)}function GS(e){return e.getUTCDay()}function VS(e,t){return te(Di.count(bt(e)-1,e),t,2)}function XS(e,t){return te(e.getUTCFullYear()%100,t,2)}function YS(e,t){return e=mm(e),te(e.getUTCFullYear()%100,t,2)}function ZS(e,t){return te(e.getUTCFullYear()%1e4,t,4)}function JS(e,t){var r=e.getUTCDay();return e=r>=4||r===0?Sr(e):Sr.ceil(e),te(e.getUTCFullYear()%1e4,t,4)}function QS(){return"+0000"}function Tp(){return"%"}function jp(e){return+e}function $p(e){return Math.floor(+e/1e3)}var ar,gm,bm;eA({dateTime:"%x, %X",date:"%-m/%-d/%Y",time:"%-I:%M:%S %p",periods:["AM","PM"],days:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],shortDays:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],months:["January","February","March","April","May","June","July","August","September","October","November","December"],shortMonths:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"]});function eA(e){return ar=eS(e),gm=ar.format,ar.parse,bm=ar.utcFormat,ar.utcParse,ar}function tA(e){return new Date(e)}function rA(e){return e instanceof Date?+e:+new Date(+e)}function vl(e,t,r,n,i,a,o,u,c,s){var f=tl(),l=f.invert,h=f.domain,d=s(".%L"),y=s(":%S"),v=s("%I:%M"),p=s("%I %p"),b=s("%a %d"),w=s("%b %d"),g=s("%B"),O=s("%Y");function m(x){return(c(x)<x?d:u(x)<x?y:o(x)<x?v:a(x)<x?p:n(x)<x?i(x)<x?b:w:r(x)<x?g:O)(x)}return f.invert=function(x){return new Date(l(x))},f.domain=function(x){return arguments.length?h(Array.from(x,rA)):h().map(tA)},f.ticks=function(x){var S=h();return e(S[0],S[S.length-1],x==null?10:x)},f.tickFormat=function(x,S){return S==null?m:s(S)},f.nice=function(x){var S=h();return(!x||typeof x.range!="function")&&(x=t(S[0],S[S.length-1],x==null?10:x)),x?h(am(S,x)):f},f.copy=function(){return Qn(f,vl(e,t,r,n,i,a,o,u,c,s))},f}function nA(){return Ye.apply(vl(J1,Q1,gt,pl,Sa,ei,fl,sl,Ut,gm).domain([new Date(2e3,0,1),new Date(2e3,0,2)]),arguments)}function iA(){return Ye.apply(vl(Y1,Z1,bt,dl,Aa,Oa,hl,ll,Ut,bm).domain([Date.UTC(2e3,0,1),Date.UTC(2e3,0,2)]),arguments)}function Pa(){var e=0,t=1,r,n,i,a,o=ke,u=!1,c;function s(l){return l==null||isNaN(l=+l)?c:o(i===0?.5:(l=(a(l)-r)*i,u?Math.max(0,Math.min(1,l)):l))}s.domain=function(l){return arguments.length?([e,t]=l,r=a(e=+e),n=a(t=+t),i=r===n?0:1/(n-r),s):[e,t]},s.clamp=function(l){return arguments.length?(u=!!l,s):u},s.interpolator=function(l){return arguments.length?(o=l,s):o};function f(l){return function(h){var d,y;return arguments.length?([d,y]=h,o=l(d,y),s):[o(0),o(1)]}}return s.range=f(Ur),s.rangeRound=f(el),s.unknown=function(l){return arguments.length?(c=l,s):c},function(l){return a=l,r=l(e),n=l(t),i=r===n?0:1/(n-r),s}}function $t(e,t){return t.domain(e.domain()).interpolator(e.interpolator()).clamp(e.clamp()).unknown(e.unknown())}function xm(){var e=jt(Pa()(ke));return e.copy=function(){return $t(e,xm())},wt.apply(e,arguments)}function wm(){var e=il(Pa()).domain([1,10]);return e.copy=function(){return $t(e,wm()).base(e.base())},wt.apply(e,arguments)}function Om(){var e=al(Pa());return e.copy=function(){return $t(e,Om()).constant(e.constant())},wt.apply(e,arguments)}function yl(){var e=ol(Pa());return e.copy=function(){return $t(e,yl()).exponent(e.exponent())},wt.apply(e,arguments)}function aA(){return yl.apply(null,arguments).exponent(.5)}function Sm(){var e=[],t=ke;function r(n){if(n!=null&&!isNaN(n=+n))return t((Zn(e,n,1)-1)/(e.length-1))}return r.domain=function(n){if(!arguments.length)return e.slice();e=[];for(let i of n)i!=null&&!isNaN(i=+i)&&e.push(i);return e.sort(Et),r},r.interpolator=function(n){return arguments.length?(t=n,r):t},r.range=function(){return e.map((n,i)=>t(i/(e.length-1)))},r.quantiles=function(n){return Array.from({length:n+1},(i,a)=>HO(e,a/n))},r.copy=function(){return Sm(t).domain(e)},wt.apply(r,arguments)}function _a(){var e=0,t=.5,r=1,n=1,i,a,o,u,c,s=ke,f,l=!1,h;function d(v){return isNaN(v=+v)?h:(v=.5+((v=+f(v))-a)*(n*v<n*a?u:c),s(l?Math.max(0,Math.min(1,v)):v))}d.domain=function(v){return arguments.length?([e,t,r]=v,i=f(e=+e),a=f(t=+t),o=f(r=+r),u=i===a?0:.5/(a-i),c=a===o?0:.5/(o-a),n=a<i?-1:1,d):[e,t,r]},d.clamp=function(v){return arguments.length?(l=!!v,d):l},d.interpolator=function(v){return arguments.length?(s=v,d):s};function y(v){return function(p){var b,w,g;return arguments.length?([b,w,g]=p,s=m1(v,[b,w,g]),d):[s(0),s(.5),s(1)]}}return d.range=y(Ur),d.rangeRound=y(el),d.unknown=function(v){return arguments.length?(h=v,d):h},function(v){return f=v,i=v(e),a=v(t),o=v(r),u=i===a?0:.5/(a-i),c=a===o?0:.5/(o-a),n=a<i?-1:1,d}}function Am(){var e=jt(_a()(ke));return e.copy=function(){return $t(e,Am())},wt.apply(e,arguments)}function Pm(){var e=il(_a()).domain([.1,1,10]);return e.copy=function(){return $t(e,Pm()).base(e.base())},wt.apply(e,arguments)}function _m(){var e=al(_a());return e.copy=function(){return $t(e,_m()).constant(e.constant())},wt.apply(e,arguments)}function ml(){var e=ol(_a());return e.copy=function(){return $t(e,ml()).exponent(e.exponent())},wt.apply(e,arguments)}function oA(){return ml.apply(null,arguments).exponent(.5)}const Mp=Object.freeze(Object.defineProperty({__proto__:null,scaleBand:bn,scaleDiverging:Am,scaleDivergingLog:Pm,scaleDivergingPow:ml,scaleDivergingSqrt:oA,scaleDivergingSymlog:_m,scaleIdentity:im,scaleImplicit:Lc,scaleLinear:ki,scaleLog:om,scaleOrdinal:Zs,scalePoint:un,scalePow:ul,scaleQuantile:sm,scaleQuantize:lm,scaleRadial:cm,scaleSequential:xm,scaleSequentialLog:wm,scaleSequentialPow:yl,scaleSequentialQuantile:Sm,scaleSequentialSqrt:aA,scaleSequentialSymlog:Om,scaleSqrt:q1,scaleSymlog:um,scaleThreshold:fm,scaleTime:nA,scaleUtc:iA,tickFormat:nm},Symbol.toStringTag,{value:"Module"}));var $u,Ip;function Ea(){if(Ip)return $u;Ip=1;var e=Fr();function t(r,n,i){for(var a=-1,o=r.length;++a<o;){var u=r[a],c=n(u);if(c!=null&&(s===void 0?c===c&&!e(c):i(c,s)))var s=c,f=u}return f}return $u=t,$u}var Mu,Cp;function Em(){if(Cp)return Mu;Cp=1;function e(t,r){return t>r}return Mu=e,Mu}var Iu,kp;function uA(){if(kp)return Iu;kp=1;var e=Ea(),t=Em(),r=zr();function n(i){return i&&i.length?e(i,r,t):void 0}return Iu=n,Iu}var cA=uA();const Ta=oe(cA);var Cu,Np;function Tm(){if(Np)return Cu;Np=1;function e(t,r){return t<r}return Cu=e,Cu}var ku,Rp;function sA(){if(Rp)return ku;Rp=1;var e=Ea(),t=Tm(),r=zr();function n(i){return i&&i.length?e(i,r,t):void 0}return ku=n,ku}var lA=sA();const ja=oe(lA);var Nu,Dp;function fA(){if(Dp)return Nu;Dp=1;var e=Bs(),t=ut(),r=Ry(),n=tt();function i(a,o){var u=n(a)?e:r;return u(a,t(o,3))}return Nu=i,Nu}var Ru,Bp;function hA(){if(Bp)return Ru;Bp=1;var e=ky(),t=fA();function r(n,i){return e(t(n,i),1)}return Ru=r,Ru}var pA=hA();const dA=oe(pA);var vA=xb();const $a=oe(vA);var Hr=1e9,yA={precision:20,rounding:4,toExpNeg:-7,toExpPos:21,LN10:"2.302585092994045684017991454684364207601101488628772976033327900967572609677352480235997205089598298341967784042286"},bl,pe=!0,Xe="[DecimalError] ",Gt=Xe+"Invalid argument: ",gl=Xe+"Exponent out of range: ",Kr=Math.floor,qt=Math.pow,mA=/^(\d+(\.\d*)?|\.\d+)(e[+-]?\d+)?$/i,Fe,Pe=1e7,he=7,jm=9007199254740991,Bi=Kr(jm/he),z={};z.absoluteValue=z.abs=function(){var e=new this.constructor(this);return e.s&&(e.s=1),e};z.comparedTo=z.cmp=function(e){var t,r,n,i,a=this;if(e=new a.constructor(e),a.s!==e.s)return a.s||-e.s;if(a.e!==e.e)return a.e>e.e^a.s<0?1:-1;for(n=a.d.length,i=e.d.length,t=0,r=n<i?n:i;t<r;++t)if(a.d[t]!==e.d[t])return a.d[t]>e.d[t]^a.s<0?1:-1;return n===i?0:n>i^a.s<0?1:-1};z.decimalPlaces=z.dp=function(){var e=this,t=e.d.length-1,r=(t-e.e)*he;if(t=e.d[t],t)for(;t%10==0;t/=10)r--;return r<0?0:r};z.dividedBy=z.div=function(e){return yt(this,new this.constructor(e))};z.dividedToIntegerBy=z.idiv=function(e){var t=this,r=t.constructor;return ue(yt(t,new r(e),0,1),r.precision)};z.equals=z.eq=function(e){return!this.cmp(e)};z.exponent=function(){return ge(this)};z.greaterThan=z.gt=function(e){return this.cmp(e)>0};z.greaterThanOrEqualTo=z.gte=function(e){return this.cmp(e)>=0};z.isInteger=z.isint=function(){return this.e>this.d.length-2};z.isNegative=z.isneg=function(){return this.s<0};z.isPositive=z.ispos=function(){return this.s>0};z.isZero=function(){return this.s===0};z.lessThan=z.lt=function(e){return this.cmp(e)<0};z.lessThanOrEqualTo=z.lte=function(e){return this.cmp(e)<1};z.logarithm=z.log=function(e){var t,r=this,n=r.constructor,i=n.precision,a=i+5;if(e===void 0)e=new n(10);else if(e=new n(e),e.s<1||e.eq(Fe))throw Error(Xe+"NaN");if(r.s<1)throw Error(Xe+(r.s?"NaN":"-Infinity"));return r.eq(Fe)?new n(0):(pe=!1,t=yt(An(r,a),An(e,a),a),pe=!0,ue(t,i))};z.minus=z.sub=function(e){var t=this;return e=new t.constructor(e),t.s==e.s?Im(t,e):$m(t,(e.s=-e.s,e))};z.modulo=z.mod=function(e){var t,r=this,n=r.constructor,i=n.precision;if(e=new n(e),!e.s)throw Error(Xe+"NaN");return r.s?(pe=!1,t=yt(r,e,0,1).times(e),pe=!0,r.minus(t)):ue(new n(r),i)};z.naturalExponential=z.exp=function(){return Mm(this)};z.naturalLogarithm=z.ln=function(){return An(this)};z.negated=z.neg=function(){var e=new this.constructor(this);return e.s=-e.s||0,e};z.plus=z.add=function(e){var t=this;return e=new t.constructor(e),t.s==e.s?$m(t,e):Im(t,(e.s=-e.s,e))};z.precision=z.sd=function(e){var t,r,n,i=this;if(e!==void 0&&e!==!!e&&e!==1&&e!==0)throw Error(Gt+e);if(t=ge(i)+1,n=i.d.length-1,r=n*he+1,n=i.d[n],n){for(;n%10==0;n/=10)r--;for(n=i.d[0];n>=10;n/=10)r++}return e&&t>r?t:r};z.squareRoot=z.sqrt=function(){var e,t,r,n,i,a,o,u=this,c=u.constructor;if(u.s<1){if(!u.s)return new c(0);throw Error(Xe+"NaN")}for(e=ge(u),pe=!1,i=Math.sqrt(+u),i==0||i==1/0?(t=nt(u.d),(t.length+e)%2==0&&(t+="0"),i=Math.sqrt(t),e=Kr((e+1)/2)-(e<0||e%2),i==1/0?t="5e"+e:(t=i.toExponential(),t=t.slice(0,t.indexOf("e")+1)+e),n=new c(t)):n=new c(i.toString()),r=c.precision,i=o=r+3;;)if(a=n,n=a.plus(yt(u,a,o+2)).times(.5),nt(a.d).slice(0,o)===(t=nt(n.d)).slice(0,o)){if(t=t.slice(o-3,o+1),i==o&&t=="4999"){if(ue(a,r+1,0),a.times(a).eq(u)){n=a;break}}else if(t!="9999")break;o+=4}return pe=!0,ue(n,r)};z.times=z.mul=function(e){var t,r,n,i,a,o,u,c,s,f=this,l=f.constructor,h=f.d,d=(e=new l(e)).d;if(!f.s||!e.s)return new l(0);for(e.s*=f.s,r=f.e+e.e,c=h.length,s=d.length,c<s&&(a=h,h=d,d=a,o=c,c=s,s=o),a=[],o=c+s,n=o;n--;)a.push(0);for(n=s;--n>=0;){for(t=0,i=c+n;i>n;)u=a[i]+d[n]*h[i-n-1]+t,a[i--]=u%Pe|0,t=u/Pe|0;a[i]=(a[i]+t)%Pe|0}for(;!a[--o];)a.pop();return t?++r:a.shift(),e.d=a,e.e=r,pe?ue(e,l.precision):e};z.toDecimalPlaces=z.todp=function(e,t){var r=this,n=r.constructor;return r=new n(r),e===void 0?r:(ot(e,0,Hr),t===void 0?t=n.rounding:ot(t,0,8),ue(r,e+ge(r)+1,t))};z.toExponential=function(e,t){var r,n=this,i=n.constructor;return e===void 0?r=Zt(n,!0):(ot(e,0,Hr),t===void 0?t=i.rounding:ot(t,0,8),n=ue(new i(n),e+1,t),r=Zt(n,!0,e+1)),r};z.toFixed=function(e,t){var r,n,i=this,a=i.constructor;return e===void 0?Zt(i):(ot(e,0,Hr),t===void 0?t=a.rounding:ot(t,0,8),n=ue(new a(i),e+ge(i)+1,t),r=Zt(n.abs(),!1,e+ge(n)+1),i.isneg()&&!i.isZero()?"-"+r:r)};z.toInteger=z.toint=function(){var e=this,t=e.constructor;return ue(new t(e),ge(e)+1,t.rounding)};z.toNumber=function(){return+this};z.toPower=z.pow=function(e){var t,r,n,i,a,o,u=this,c=u.constructor,s=12,f=+(e=new c(e));if(!e.s)return new c(Fe);if(u=new c(u),!u.s){if(e.s<1)throw Error(Xe+"Infinity");return u}if(u.eq(Fe))return u;if(n=c.precision,e.eq(Fe))return ue(u,n);if(t=e.e,r=e.d.length-1,o=t>=r,a=u.s,o){if((r=f<0?-f:f)<=jm){for(i=new c(Fe),t=Math.ceil(n/he+4),pe=!1;r%2&&(i=i.times(u),qp(i.d,t)),r=Kr(r/2),r!==0;)u=u.times(u),qp(u.d,t);return pe=!0,e.s<0?new c(Fe).div(i):ue(i,n)}}else if(a<0)throw Error(Xe+"NaN");return a=a<0&&e.d[Math.max(t,r)]&1?-1:1,u.s=1,pe=!1,i=e.times(An(u,n+s)),pe=!0,i=Mm(i),i.s=a,i};z.toPrecision=function(e,t){var r,n,i=this,a=i.constructor;return e===void 0?(r=ge(i),n=Zt(i,r<=a.toExpNeg||r>=a.toExpPos)):(ot(e,1,Hr),t===void 0?t=a.rounding:ot(t,0,8),i=ue(new a(i),e,t),r=ge(i),n=Zt(i,e<=r||r<=a.toExpNeg,e)),n};z.toSignificantDigits=z.tosd=function(e,t){var r=this,n=r.constructor;return e===void 0?(e=n.precision,t=n.rounding):(ot(e,1,Hr),t===void 0?t=n.rounding:ot(t,0,8)),ue(new n(r),e,t)};z.toString=z.valueOf=z.val=z.toJSON=z[Symbol.for("nodejs.util.inspect.custom")]=function(){var e=this,t=ge(e),r=e.constructor;return Zt(e,t<=r.toExpNeg||t>=r.toExpPos)};function $m(e,t){var r,n,i,a,o,u,c,s,f=e.constructor,l=f.precision;if(!e.s||!t.s)return t.s||(t=new f(e)),pe?ue(t,l):t;if(c=e.d,s=t.d,o=e.e,i=t.e,c=c.slice(),a=o-i,a){for(a<0?(n=c,a=-a,u=s.length):(n=s,i=o,u=c.length),o=Math.ceil(l/he),u=o>u?o+1:u+1,a>u&&(a=u,n.length=1),n.reverse();a--;)n.push(0);n.reverse()}for(u=c.length,a=s.length,u-a<0&&(a=u,n=s,s=c,c=n),r=0;a;)r=(c[--a]=c[a]+s[a]+r)/Pe|0,c[a]%=Pe;for(r&&(c.unshift(r),++i),u=c.length;c[--u]==0;)c.pop();return t.d=c,t.e=i,pe?ue(t,l):t}function ot(e,t,r){if(e!==~~e||e<t||e>r)throw Error(Gt+e)}function nt(e){var t,r,n,i=e.length-1,a="",o=e[0];if(i>0){for(a+=o,t=1;t<i;t++)n=e[t]+"",r=he-n.length,r&&(a+=At(r)),a+=n;o=e[t],n=o+"",r=he-n.length,r&&(a+=At(r))}else if(o===0)return"0";for(;o%10===0;)o/=10;return a+o}var yt=function(){function e(n,i){var a,o=0,u=n.length;for(n=n.slice();u--;)a=n[u]*i+o,n[u]=a%Pe|0,o=a/Pe|0;return o&&n.unshift(o),n}function t(n,i,a,o){var u,c;if(a!=o)c=a>o?1:-1;else for(u=c=0;u<a;u++)if(n[u]!=i[u]){c=n[u]>i[u]?1:-1;break}return c}function r(n,i,a){for(var o=0;a--;)n[a]-=o,o=n[a]<i[a]?1:0,n[a]=o*Pe+n[a]-i[a];for(;!n[0]&&n.length>1;)n.shift()}return function(n,i,a,o){var u,c,s,f,l,h,d,y,v,p,b,w,g,O,m,x,S,A,_=n.constructor,$=n.s==i.s?1:-1,E=n.d,T=i.d;if(!n.s)return new _(n);if(!i.s)throw Error(Xe+"Division by zero");for(c=n.e-i.e,S=T.length,m=E.length,d=new _($),y=d.d=[],s=0;T[s]==(E[s]||0);)++s;if(T[s]>(E[s]||0)&&--c,a==null?w=a=_.precision:o?w=a+(ge(n)-ge(i))+1:w=a,w<0)return new _(0);if(w=w/he+2|0,s=0,S==1)for(f=0,T=T[0],w++;(s<m||f)&&w--;s++)g=f*Pe+(E[s]||0),y[s]=g/T|0,f=g%T|0;else{for(f=Pe/(T[0]+1)|0,f>1&&(T=e(T,f),E=e(E,f),S=T.length,m=E.length),O=S,v=E.slice(0,S),p=v.length;p<S;)v[p++]=0;A=T.slice(),A.unshift(0),x=T[0],T[1]>=Pe/2&&++x;do f=0,u=t(T,v,S,p),u<0?(b=v[0],S!=p&&(b=b*Pe+(v[1]||0)),f=b/x|0,f>1?(f>=Pe&&(f=Pe-1),l=e(T,f),h=l.length,p=v.length,u=t(l,v,h,p),u==1&&(f--,r(l,S<h?A:T,h))):(f==0&&(u=f=1),l=T.slice()),h=l.length,h<p&&l.unshift(0),r(v,l,p),u==-1&&(p=v.length,u=t(T,v,S,p),u<1&&(f++,r(v,S<p?A:T,p))),p=v.length):u===0&&(f++,v=[0]),y[s++]=f,u&&v[0]?v[p++]=E[O]||0:(v=[E[O]],p=1);while((O++<m||v[0]!==void 0)&&w--)}return y[0]||y.shift(),d.e=c,ue(d,o?a+ge(d)+1:a)}}();function Mm(e,t){var r,n,i,a,o,u,c=0,s=0,f=e.constructor,l=f.precision;if(ge(e)>16)throw Error(gl+ge(e));if(!e.s)return new f(Fe);for(pe=!1,u=l,o=new f(.03125);e.abs().gte(.1);)e=e.times(o),s+=5;for(n=Math.log(qt(2,s))/Math.LN10*2+5|0,u+=n,r=i=a=new f(Fe),f.precision=u;;){if(i=ue(i.times(e),u),r=r.times(++c),o=a.plus(yt(i,r,u)),nt(o.d).slice(0,u)===nt(a.d).slice(0,u)){for(;s--;)a=ue(a.times(a),u);return f.precision=l,t==null?(pe=!0,ue(a,l)):a}a=o}}function ge(e){for(var t=e.e*he,r=e.d[0];r>=10;r/=10)t++;return t}function Du(e,t,r){if(t>e.LN10.sd())throw pe=!0,r&&(e.precision=r),Error(Xe+"LN10 precision limit exceeded");return ue(new e(e.LN10),t)}function At(e){for(var t="";e--;)t+="0";return t}function An(e,t){var r,n,i,a,o,u,c,s,f,l=1,h=10,d=e,y=d.d,v=d.constructor,p=v.precision;if(d.s<1)throw Error(Xe+(d.s?"NaN":"-Infinity"));if(d.eq(Fe))return new v(0);if(t==null?(pe=!1,s=p):s=t,d.eq(10))return t==null&&(pe=!0),Du(v,s);if(s+=h,v.precision=s,r=nt(y),n=r.charAt(0),a=ge(d),Math.abs(a)<15e14){for(;n<7&&n!=1||n==1&&r.charAt(1)>3;)d=d.times(e),r=nt(d.d),n=r.charAt(0),l++;a=ge(d),n>1?(d=new v("0."+r),a++):d=new v(n+"."+r.slice(1))}else return c=Du(v,s+2,p).times(a+""),d=An(new v(n+"."+r.slice(1)),s-h).plus(c),v.precision=p,t==null?(pe=!0,ue(d,p)):d;for(u=o=d=yt(d.minus(Fe),d.plus(Fe),s),f=ue(d.times(d),s),i=3;;){if(o=ue(o.times(f),s),c=u.plus(yt(o,new v(i),s)),nt(c.d).slice(0,s)===nt(u.d).slice(0,s))return u=u.times(2),a!==0&&(u=u.plus(Du(v,s+2,p).times(a+""))),u=yt(u,new v(l),s),v.precision=p,t==null?(pe=!0,ue(u,p)):u;u=c,i+=2}}function Lp(e,t){var r,n,i;for((r=t.indexOf("."))>-1&&(t=t.replace(".","")),(n=t.search(/e/i))>0?(r<0&&(r=n),r+=+t.slice(n+1),t=t.substring(0,n)):r<0&&(r=t.length),n=0;t.charCodeAt(n)===48;)++n;for(i=t.length;t.charCodeAt(i-1)===48;)--i;if(t=t.slice(n,i),t){if(i-=n,r=r-n-1,e.e=Kr(r/he),e.d=[],n=(r+1)%he,r<0&&(n+=he),n<i){for(n&&e.d.push(+t.slice(0,n)),i-=he;n<i;)e.d.push(+t.slice(n,n+=he));t=t.slice(n),n=he-t.length}else n-=i;for(;n--;)t+="0";if(e.d.push(+t),pe&&(e.e>Bi||e.e<-Bi))throw Error(gl+r)}else e.s=0,e.e=0,e.d=[0];return e}function ue(e,t,r){var n,i,a,o,u,c,s,f,l=e.d;for(o=1,a=l[0];a>=10;a/=10)o++;if(n=t-o,n<0)n+=he,i=t,s=l[f=0];else{if(f=Math.ceil((n+1)/he),a=l.length,f>=a)return e;for(s=a=l[f],o=1;a>=10;a/=10)o++;n%=he,i=n-he+o}if(r!==void 0&&(a=qt(10,o-i-1),u=s/a%10|0,c=t<0||l[f+1]!==void 0||s%a,c=r<4?(u||c)&&(r==0||r==(e.s<0?3:2)):u>5||u==5&&(r==4||c||r==6&&(n>0?i>0?s/qt(10,o-i):0:l[f-1])%10&1||r==(e.s<0?8:7))),t<1||!l[0])return c?(a=ge(e),l.length=1,t=t-a-1,l[0]=qt(10,(he-t%he)%he),e.e=Kr(-t/he)||0):(l.length=1,l[0]=e.e=e.s=0),e;if(n==0?(l.length=f,a=1,f--):(l.length=f+1,a=qt(10,he-n),l[f]=i>0?(s/qt(10,o-i)%qt(10,i)|0)*a:0),c)for(;;)if(f==0){(l[0]+=a)==Pe&&(l[0]=1,++e.e);break}else{if(l[f]+=a,l[f]!=Pe)break;l[f--]=0,a=1}for(n=l.length;l[--n]===0;)l.pop();if(pe&&(e.e>Bi||e.e<-Bi))throw Error(gl+ge(e));return e}function Im(e,t){var r,n,i,a,o,u,c,s,f,l,h=e.constructor,d=h.precision;if(!e.s||!t.s)return t.s?t.s=-t.s:t=new h(e),pe?ue(t,d):t;if(c=e.d,l=t.d,n=t.e,s=e.e,c=c.slice(),o=s-n,o){for(f=o<0,f?(r=c,o=-o,u=l.length):(r=l,n=s,u=c.length),i=Math.max(Math.ceil(d/he),u)+2,o>i&&(o=i,r.length=1),r.reverse(),i=o;i--;)r.push(0);r.reverse()}else{for(i=c.length,u=l.length,f=i<u,f&&(u=i),i=0;i<u;i++)if(c[i]!=l[i]){f=c[i]<l[i];break}o=0}for(f&&(r=c,c=l,l=r,t.s=-t.s),u=c.length,i=l.length-u;i>0;--i)c[u++]=0;for(i=l.length;i>o;){if(c[--i]<l[i]){for(a=i;a&&c[--a]===0;)c[a]=Pe-1;--c[a],c[i]+=Pe}c[i]-=l[i]}for(;c[--u]===0;)c.pop();for(;c[0]===0;c.shift())--n;return c[0]?(t.d=c,t.e=n,pe?ue(t,d):t):new h(0)}function Zt(e,t,r){var n,i=ge(e),a=nt(e.d),o=a.length;return t?(r&&(n=r-o)>0?a=a.charAt(0)+"."+a.slice(1)+At(n):o>1&&(a=a.charAt(0)+"."+a.slice(1)),a=a+(i<0?"e":"e+")+i):i<0?(a="0."+At(-i-1)+a,r&&(n=r-o)>0&&(a+=At(n))):i>=o?(a+=At(i+1-o),r&&(n=r-i-1)>0&&(a=a+"."+At(n))):((n=i+1)<o&&(a=a.slice(0,n)+"."+a.slice(n)),r&&(n=r-o)>0&&(i+1===o&&(a+="."),a+=At(n))),e.s<0?"-"+a:a}function qp(e,t){if(e.length>t)return e.length=t,!0}function Cm(e){var t,r,n;function i(a){var o=this;if(!(o instanceof i))return new i(a);if(o.constructor=i,a instanceof i){o.s=a.s,o.e=a.e,o.d=(a=a.d)?a.slice():a;return}if(typeof a=="number"){if(a*0!==0)throw Error(Gt+a);if(a>0)o.s=1;else if(a<0)a=-a,o.s=-1;else{o.s=0,o.e=0,o.d=[0];return}if(a===~~a&&a<1e7){o.e=0,o.d=[a];return}return Lp(o,a.toString())}else if(typeof a!="string")throw Error(Gt+a);if(a.charCodeAt(0)===45?(a=a.slice(1),o.s=-1):o.s=1,mA.test(a))Lp(o,a);else throw Error(Gt+a)}if(i.prototype=z,i.ROUND_UP=0,i.ROUND_DOWN=1,i.ROUND_CEIL=2,i.ROUND_FLOOR=3,i.ROUND_HALF_UP=4,i.ROUND_HALF_DOWN=5,i.ROUND_HALF_EVEN=6,i.ROUND_HALF_CEIL=7,i.ROUND_HALF_FLOOR=8,i.clone=Cm,i.config=i.set=gA,e===void 0&&(e={}),e)for(n=["precision","rounding","toExpNeg","toExpPos","LN10"],t=0;t<n.length;)e.hasOwnProperty(r=n[t++])||(e[r]=this[r]);return i.config(e),i}function gA(e){if(!e||typeof e!="object")throw Error(Xe+"Object expected");var t,r,n,i=["precision",1,Hr,"rounding",0,8,"toExpNeg",-1/0,0,"toExpPos",0,1/0];for(t=0;t<i.length;t+=3)if((n=e[r=i[t]])!==void 0)if(Kr(n)===n&&n>=i[t+1]&&n<=i[t+2])this[r]=n;else throw Error(Gt+r+": "+n);if((n=e[r="LN10"])!==void 0)if(n==Math.LN10)this[r]=new this(n);else throw Error(Gt+r+": "+n);return this}var bl=Cm(yA);Fe=new bl(1);const ae=bl;function bA(e){return SA(e)||OA(e)||wA(e)||xA()}function xA(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function wA(e,t){if(e){if(typeof e=="string")return zc(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return zc(e,t)}}function OA(e){if(typeof Symbol!="undefined"&&Symbol.iterator in Object(e))return Array.from(e)}function SA(e){if(Array.isArray(e))return zc(e)}function zc(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}var AA=function(t){return t},km={},Nm=function(t){return t===km},Fp=function(t){return function r(){return arguments.length===0||arguments.length===1&&Nm(arguments.length<=0?void 0:arguments[0])?r:t.apply(void 0,arguments)}},PA=function e(t,r){return t===1?r:Fp(function(){for(var n=arguments.length,i=new Array(n),a=0;a<n;a++)i[a]=arguments[a];var o=i.filter(function(u){return u!==km}).length;return o>=t?r.apply(void 0,i):e(t-o,Fp(function(){for(var u=arguments.length,c=new Array(u),s=0;s<u;s++)c[s]=arguments[s];var f=i.map(function(l){return Nm(l)?c.shift():l});return r.apply(void 0,bA(f).concat(c))}))})},Ma=function(t){return PA(t.length,t)},Uc=function(t,r){for(var n=[],i=t;i<r;++i)n[i-t]=i;return n},_A=Ma(function(e,t){return Array.isArray(t)?t.map(e):Object.keys(t).map(function(r){return t[r]}).map(e)}),EA=function(){for(var t=arguments.length,r=new Array(t),n=0;n<t;n++)r[n]=arguments[n];if(!r.length)return AA;var i=r.reverse(),a=i[0],o=i.slice(1);return function(){return o.reduce(function(u,c){return c(u)},a.apply(void 0,arguments))}},Hc=function(t){return Array.isArray(t)?t.reverse():t.split("").reverse.join("")},Rm=function(t){var r=null,n=null;return function(){for(var i=arguments.length,a=new Array(i),o=0;o<i;o++)a[o]=arguments[o];return r&&a.every(function(u,c){return u===r[c]})||(r=a,n=t.apply(void 0,a)),n}};function TA(e){var t;return e===0?t=1:t=Math.floor(new ae(e).abs().log(10).toNumber())+1,t}function jA(e,t,r){for(var n=new ae(e),i=0,a=[];n.lt(t)&&i<1e5;)a.push(n.toNumber()),n=n.add(r),i++;return a}var $A=Ma(function(e,t,r){var n=+e,i=+t;return n+r*(i-n)}),MA=Ma(function(e,t,r){var n=t-+e;return n=n||1/0,(r-e)/n}),IA=Ma(function(e,t,r){var n=t-+e;return n=n||1/0,Math.max(0,Math.min(1,(r-e)/n))});const Ia={rangeStep:jA,getDigitCount:TA,interpolateNumber:$A,uninterpolateNumber:MA,uninterpolateTruncation:IA};function Kc(e){return NA(e)||kA(e)||Dm(e)||CA()}function CA(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function kA(e){if(typeof Symbol!="undefined"&&Symbol.iterator in Object(e))return Array.from(e)}function NA(e){if(Array.isArray(e))return Gc(e)}function Pn(e,t){return BA(e)||DA(e,t)||Dm(e,t)||RA()}function RA(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function Dm(e,t){if(e){if(typeof e=="string")return Gc(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Gc(e,t)}}function Gc(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function DA(e,t){if(!(typeof Symbol=="undefined"||!(Symbol.iterator in Object(e)))){var r=[],n=!0,i=!1,a=void 0;try{for(var o=e[Symbol.iterator](),u;!(n=(u=o.next()).done)&&(r.push(u.value),!(t&&r.length===t));n=!0);}catch(c){i=!0,a=c}finally{try{!n&&o.return!=null&&o.return()}finally{if(i)throw a}}return r}}function BA(e){if(Array.isArray(e))return e}function Bm(e){var t=Pn(e,2),r=t[0],n=t[1],i=r,a=n;return r>n&&(i=n,a=r),[i,a]}function Lm(e,t,r){if(e.lte(0))return new ae(0);var n=Ia.getDigitCount(e.toNumber()),i=new ae(10).pow(n),a=e.div(i),o=n!==1?.05:.1,u=new ae(Math.ceil(a.div(o).toNumber())).add(r).mul(o),c=u.mul(i);return t?c:new ae(Math.ceil(c))}function LA(e,t,r){var n=1,i=new ae(e);if(!i.isint()&&r){var a=Math.abs(e);a<1?(n=new ae(10).pow(Ia.getDigitCount(e)-1),i=new ae(Math.floor(i.div(n).toNumber())).mul(n)):a>1&&(i=new ae(Math.floor(e)))}else e===0?i=new ae(Math.floor((t-1)/2)):r||(i=new ae(Math.floor(e)));var o=Math.floor((t-1)/2),u=EA(_A(function(c){return i.add(new ae(c-o).mul(n)).toNumber()}),Uc);return u(0,t)}function qm(e,t,r,n){var i=arguments.length>4&&arguments[4]!==void 0?arguments[4]:0;if(!Number.isFinite((t-e)/(r-1)))return{step:new ae(0),tickMin:new ae(0),tickMax:new ae(0)};var a=Lm(new ae(t).sub(e).div(r-1),n,i),o;e<=0&&t>=0?o=new ae(0):(o=new ae(e).add(t).div(2),o=o.sub(new ae(o).mod(a)));var u=Math.ceil(o.sub(e).div(a).toNumber()),c=Math.ceil(new ae(t).sub(o).div(a).toNumber()),s=u+c+1;return s>r?qm(e,t,r,n,i+1):(s<r&&(c=t>0?c+(r-s):c,u=t>0?u:u+(r-s)),{step:a,tickMin:o.sub(new ae(u).mul(a)),tickMax:o.add(new ae(c).mul(a))})}function qA(e){var t=Pn(e,2),r=t[0],n=t[1],i=arguments.length>1&&arguments[1]!==void 0?arguments[1]:6,a=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!0,o=Math.max(i,2),u=Bm([r,n]),c=Pn(u,2),s=c[0],f=c[1];if(s===-1/0||f===1/0){var l=f===1/0?[s].concat(Kc(Uc(0,i-1).map(function(){return 1/0}))):[].concat(Kc(Uc(0,i-1).map(function(){return-1/0})),[f]);return r>n?Hc(l):l}if(s===f)return LA(s,i,a);var h=qm(s,f,o,a),d=h.step,y=h.tickMin,v=h.tickMax,p=Ia.rangeStep(y,v.add(new ae(.1).mul(d)),d);return r>n?Hc(p):p}function FA(e,t){var r=Pn(e,2),n=r[0],i=r[1],a=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!0,o=Bm([n,i]),u=Pn(o,2),c=u[0],s=u[1];if(c===-1/0||s===1/0)return[n,i];if(c===s)return[c];var f=Math.max(t,2),l=Lm(new ae(s).sub(c).div(f-1),a,0),h=[].concat(Kc(Ia.rangeStep(new ae(c),new ae(s).sub(new ae(.99).mul(l)),l)),[s]);return n>i?Hc(h):h}var WA=Rm(qA),zA=Rm(FA),UA="Invariant failed";function Jt(e,t){throw new Error(UA)}var HA=["offset","layout","width","dataKey","data","dataPointFormatter","xAxis","yAxis"];function Ar(e){"@babel/helpers - typeof";return Ar=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Ar(e)}function Li(){return Li=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Li.apply(this,arguments)}function KA(e,t){return YA(e)||XA(e,t)||VA(e,t)||GA()}function GA(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function VA(e,t){if(e){if(typeof e=="string")return Wp(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Wp(e,t)}}function Wp(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function XA(e,t){var r=e==null?null:typeof Symbol!="undefined"&&e[Symbol.iterator]||e["@@iterator"];if(r!=null){var n,i,a,o,u=[],c=!0,s=!1;try{if(a=(r=r.call(e)).next,t!==0)for(;!(c=(n=a.call(r)).done)&&(u.push(n.value),u.length!==t);c=!0);}catch(f){s=!0,i=f}finally{try{if(!c&&r.return!=null&&(o=r.return(),Object(o)!==o))return}finally{if(s)throw i}}return u}}function YA(e){if(Array.isArray(e))return e}function ZA(e,t){if(e==null)return{};var r=JA(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function JA(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}function QA(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function eP(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,zm(n.key),n)}}function tP(e,t,r){return t&&eP(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e}function rP(e,t,r){return t=qi(t),nP(e,Fm()?Reflect.construct(t,r||[],qi(e).constructor):t.apply(e,r))}function nP(e,t){if(t&&(Ar(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return iP(e)}function iP(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function Fm(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(Fm=function(){return!!e})()}function qi(e){return qi=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},qi(e)}function aP(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Vc(e,t)}function Vc(e,t){return Vc=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},Vc(e,t)}function Wm(e,t,r){return t=zm(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function zm(e){var t=oP(e,"string");return Ar(t)=="symbol"?t:t+""}function oP(e,t){if(Ar(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Ar(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}var Ca=function(e){function t(){return QA(this,t),rP(this,t,arguments)}return aP(t,e),tP(t,[{key:"render",value:function(){var n=this.props,i=n.offset,a=n.layout,o=n.width,u=n.dataKey,c=n.data,s=n.dataPointFormatter,f=n.xAxis,l=n.yAxis,h=ZA(n,HA),d=V(h,!1);this.props.direction==="x"&&f.type!=="number"&&Jt();var y=c.map(function(v){var p=s(v,u),b=p.x,w=p.y,g=p.value,O=p.errorVal;if(!O)return null;var m=[],x,S;if(Array.isArray(O)){var A=KA(O,2);x=A[0],S=A[1]}else x=S=O;if(a==="vertical"){var _=f.scale,$=w+i,E=$+o,T=$-o,j=_(g-x),C=_(g+S);m.push({x1:C,y1:E,x2:C,y2:T}),m.push({x1:j,y1:$,x2:C,y2:$}),m.push({x1:j,y1:E,x2:j,y2:T})}else if(a==="horizontal"){var I=l.scale,k=b+i,N=k-o,L=k+o,q=I(g-x),U=I(g+S);m.push({x1:N,y1:U,x2:L,y2:U}),m.push({x1:k,y1:q,x2:k,y2:U}),m.push({x1:N,y1:q,x2:L,y2:q})}return P.createElement(ie,Li({className:"recharts-errorBar",key:"bar-".concat(m.map(function(K){return"".concat(K.x1,"-").concat(K.x2,"-").concat(K.y1,"-").concat(K.y2)}))},d),m.map(function(K){return P.createElement("line",Li({},K,{key:"line-".concat(K.x1,"-").concat(K.x2,"-").concat(K.y1,"-").concat(K.y2)}))}))});return P.createElement(ie,{className:"recharts-errorBars"},y)}}])}(P.Component);Wm(Ca,"defaultProps",{stroke:"black",strokeWidth:1.5,width:5,offset:0,layout:"horizontal"});Wm(Ca,"displayName","ErrorBar");function _n(e){"@babel/helpers - typeof";return _n=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},_n(e)}function zp(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Rt(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?zp(Object(r),!0).forEach(function(n){uP(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):zp(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function uP(e,t,r){return t=cP(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function cP(e){var t=sP(e,"string");return _n(t)=="symbol"?t:t+""}function sP(e,t){if(_n(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(_n(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var Um=function(t){var r=t.children,n=t.formattedGraphicalItems,i=t.legendWidth,a=t.legendContent,o=qe(r,pr);if(!o)return null;var u=pr.defaultProps,c=u!==void 0?Rt(Rt({},u),o.props):{},s;return o.props&&o.props.payload?s=o.props&&o.props.payload:a==="children"?s=(n||[]).reduce(function(f,l){var h=l.item,d=l.props,y=d.sectors||d.data||[];return f.concat(y.map(function(v){return{type:o.props.iconType||h.props.legendType,value:v.name,color:v.fill,payload:v}}))},[]):s=(n||[]).map(function(f){var l=f.item,h=l.type.defaultProps,d=h!==void 0?Rt(Rt({},h),l.props):{},y=d.dataKey,v=d.name,p=d.legendType,b=d.hide;return{inactive:b,dataKey:y,type:c.iconType||p||"square",color:xl(l),value:v||y,payload:d}}),Rt(Rt(Rt({},c),pr.getWithHeight(o,i)),{},{payload:s,item:o})};function En(e){"@babel/helpers - typeof";return En=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},En(e)}function Up(e){return pP(e)||hP(e)||fP(e)||lP()}function lP(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function fP(e,t){if(e){if(typeof e=="string")return Xc(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Xc(e,t)}}function hP(e){if(typeof Symbol!="undefined"&&e[Symbol.iterator]!=null||e["@@iterator"]!=null)return Array.from(e)}function pP(e){if(Array.isArray(e))return Xc(e)}function Xc(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function Hp(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function ve(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Hp(Object(r),!0).forEach(function(n){vr(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Hp(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function vr(e,t,r){return t=dP(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function dP(e){var t=vP(e,"string");return En(t)=="symbol"?t:t+""}function vP(e,t){if(En(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(En(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function je(e,t,r){return Z(e)||Z(t)?r:Se(t)?We(e,t,r):X(t)?t(e):r}function cn(e,t,r,n){var i=dA(e,function(u){return je(u,t)});if(r==="number"){var a=i.filter(function(u){return B(u)||parseFloat(u)});return a.length?[ja(a),Ta(a)]:[1/0,-1/0]}var o=n?i.filter(function(u){return!Z(u)}):i;return o.map(function(u){return Se(u)||u instanceof Date?u:""})}var yP=function(t){var r,n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:[],i=arguments.length>2?arguments[2]:void 0,a=arguments.length>3?arguments[3]:void 0,o=-1,u=(r=n==null?void 0:n.length)!==null&&r!==void 0?r:0;if(u<=1)return 0;if(a&&a.axisType==="angleAxis"&&Math.abs(Math.abs(a.range[1]-a.range[0])-360)<=1e-6)for(var c=a.range,s=0;s<u;s++){var f=s>0?i[s-1].coordinate:i[u-1].coordinate,l=i[s].coordinate,h=s>=u-1?i[0].coordinate:i[s+1].coordinate,d=void 0;if(Ie(l-f)!==Ie(h-l)){var y=[];if(Ie(h-l)===Ie(c[1]-c[0])){d=h;var v=l+c[1]-c[0];y[0]=Math.min(v,(v+f)/2),y[1]=Math.max(v,(v+f)/2)}else{d=f;var p=h+c[1]-c[0];y[0]=Math.min(l,(p+l)/2),y[1]=Math.max(l,(p+l)/2)}var b=[Math.min(l,(d+l)/2),Math.max(l,(d+l)/2)];if(t>b[0]&&t<=b[1]||t>=y[0]&&t<=y[1]){o=i[s].index;break}}else{var w=Math.min(f,h),g=Math.max(f,h);if(t>(w+l)/2&&t<=(g+l)/2){o=i[s].index;break}}}else for(var O=0;O<u;O++)if(O===0&&t<=(n[O].coordinate+n[O+1].coordinate)/2||O>0&&O<u-1&&t>(n[O].coordinate+n[O-1].coordinate)/2&&t<=(n[O].coordinate+n[O+1].coordinate)/2||O===u-1&&t>(n[O].coordinate+n[O-1].coordinate)/2){o=n[O].index;break}return o},xl=function(t){var r,n=t,i=n.type.displayName,a=(r=t.type)!==null&&r!==void 0&&r.defaultProps?ve(ve({},t.type.defaultProps),t.props):t.props,o=a.stroke,u=a.fill,c;switch(i){case"Line":c=o;break;case"Area":case"Radar":c=o&&o!=="none"?o:u;break;default:c=u;break}return c},mP=function(t){var r=t.barSize,n=t.totalSize,i=t.stackGroups,a=i===void 0?{}:i;if(!a)return{};for(var o={},u=Object.keys(a),c=0,s=u.length;c<s;c++)for(var f=a[u[c]].stackGroups,l=Object.keys(f),h=0,d=l.length;h<d;h++){var y=f[l[h]],v=y.items,p=y.cateAxisId,b=v.filter(function(S){return dt(S.type).indexOf("Bar")>=0});if(b&&b.length){var w=b[0].type.defaultProps,g=w!==void 0?ve(ve({},w),b[0].props):b[0].props,O=g.barSize,m=g[p];o[m]||(o[m]=[]);var x=Z(O)?r:O;o[m].push({item:b[0],stackList:b.slice(1),barSize:Z(x)?void 0:Ce(x,n,0)})}}return o},gP=function(t){var r=t.barGap,n=t.barCategoryGap,i=t.bandSize,a=t.sizeList,o=a===void 0?[]:a,u=t.maxBarSize,c=o.length;if(c<1)return null;var s=Ce(r,i,0,!0),f,l=[];if(o[0].barSize===+o[0].barSize){var h=!1,d=i/c,y=o.reduce(function(O,m){return O+m.barSize||0},0);y+=(c-1)*s,y>=i&&(y-=(c-1)*s,s=0),y>=i&&d>0&&(h=!0,d*=.9,y=c*d);var v=(i-y)/2>>0,p={offset:v-s,size:0};f=o.reduce(function(O,m){var x={item:m.item,position:{offset:p.offset+p.size+s,size:h?d:m.barSize}},S=[].concat(Up(O),[x]);return p=S[S.length-1].position,m.stackList&&m.stackList.length&&m.stackList.forEach(function(A){S.push({item:A,position:p})}),S},l)}else{var b=Ce(n,i,0,!0);i-2*b-(c-1)*s<=0&&(s=0);var w=(i-2*b-(c-1)*s)/c;w>1&&(w>>=0);var g=u===+u?Math.min(w,u):w;f=o.reduce(function(O,m,x){var S=[].concat(Up(O),[{item:m.item,position:{offset:b+(w+s)*x+(w-g)/2,size:g}}]);return m.stackList&&m.stackList.length&&m.stackList.forEach(function(A){S.push({item:A,position:S[S.length-1].position})}),S},l)}return f},bP=function(t,r,n,i){var a=n.children,o=n.width,u=n.margin,c=o-(u.left||0)-(u.right||0),s=Um({children:a,legendWidth:c});if(s){var f=i||{},l=f.width,h=f.height,d=s.align,y=s.verticalAlign,v=s.layout;if((v==="vertical"||v==="horizontal"&&y==="middle")&&d!=="center"&&B(t[d]))return ve(ve({},t),{},vr({},d,t[d]+(l||0)));if((v==="horizontal"||v==="vertical"&&d==="center")&&y!=="middle"&&B(t[y]))return ve(ve({},t),{},vr({},y,t[y]+(h||0)))}return t},xP=function(t,r,n){return Z(r)?!0:t==="horizontal"?r==="yAxis":t==="vertical"||n==="x"?r==="xAxis":n==="y"?r==="yAxis":!0},Hm=function(t,r,n,i,a){var o=r.props.children,u=Ve(o,Ca).filter(function(s){return xP(i,a,s.props.direction)});if(u&&u.length){var c=u.map(function(s){return s.props.dataKey});return t.reduce(function(s,f){var l=je(f,n);if(Z(l))return s;var h=Array.isArray(l)?[ja(l),Ta(l)]:[l,l],d=c.reduce(function(y,v){var p=je(f,v,0),b=h[0]-Math.abs(Array.isArray(p)?p[0]:p),w=h[1]+Math.abs(Array.isArray(p)?p[1]:p);return[Math.min(b,y[0]),Math.max(w,y[1])]},[1/0,-1/0]);return[Math.min(d[0],s[0]),Math.max(d[1],s[1])]},[1/0,-1/0])}return null},wP=function(t,r,n,i,a){var o=r.map(function(u){return Hm(t,u,n,a,i)}).filter(function(u){return!Z(u)});return o&&o.length?o.reduce(function(u,c){return[Math.min(u[0],c[0]),Math.max(u[1],c[1])]},[1/0,-1/0]):null},Km=function(t,r,n,i,a){var o=r.map(function(c){var s=c.props.dataKey;return n==="number"&&s&&Hm(t,c,s,i)||cn(t,s,n,a)});if(n==="number")return o.reduce(function(c,s){return[Math.min(c[0],s[0]),Math.max(c[1],s[1])]},[1/0,-1/0]);var u={};return o.reduce(function(c,s){for(var f=0,l=s.length;f<l;f++)u[s[f]]||(u[s[f]]=!0,c.push(s[f]));return c},[])},Gm=function(t,r){return t==="horizontal"&&r==="xAxis"||t==="vertical"&&r==="yAxis"||t==="centric"&&r==="angleAxis"||t==="radial"&&r==="radiusAxis"},Vm=function(t,r,n,i){if(i)return t.map(function(c){return c.coordinate});var a,o,u=t.map(function(c){return c.coordinate===r&&(a=!0),c.coordinate===n&&(o=!0),c.coordinate});return a||u.push(r),o||u.push(n),u},pt=function(t,r,n){if(!t)return null;var i=t.scale,a=t.duplicateDomain,o=t.type,u=t.range,c=t.realScaleType==="scaleBand"?i.bandwidth()/2:2,s=(r||n)&&o==="category"&&i.bandwidth?i.bandwidth()/c:0;if(s=t.axisType==="angleAxis"&&(u==null?void 0:u.length)>=2?Ie(u[0]-u[1])*2*s:s,r&&(t.ticks||t.niceTicks)){var f=(t.ticks||t.niceTicks).map(function(l){var h=a?a.indexOf(l):l;return{coordinate:i(h)+s,value:l,offset:s}});return f.filter(function(l){return!Xn(l.coordinate)})}return t.isCategorical&&t.categoricalDomain?t.categoricalDomain.map(function(l,h){return{coordinate:i(l)+s,value:l,index:h,offset:s}}):i.ticks&&!n?i.ticks(t.tickCount).map(function(l){return{coordinate:i(l)+s,value:l,offset:s}}):i.domain().map(function(l,h){return{coordinate:i(l)+s,value:a?a[l]:l,index:h,offset:s}})},Bu=new WeakMap,si=function(t,r){if(typeof r!="function")return t;Bu.has(t)||Bu.set(t,new WeakMap);var n=Bu.get(t);if(n.has(r))return n.get(r);var i=function(){t.apply(void 0,arguments),r.apply(void 0,arguments)};return n.set(r,i),i},Xm=function(t,r,n){var i=t.scale,a=t.type,o=t.layout,u=t.axisType;if(i==="auto")return o==="radial"&&u==="radiusAxis"?{scale:bn(),realScaleType:"band"}:o==="radial"&&u==="angleAxis"?{scale:ki(),realScaleType:"linear"}:a==="category"&&r&&(r.indexOf("LineChart")>=0||r.indexOf("AreaChart")>=0||r.indexOf("ComposedChart")>=0&&!n)?{scale:un(),realScaleType:"point"}:a==="category"?{scale:bn(),realScaleType:"band"}:{scale:ki(),realScaleType:"linear"};if(Vt(i)){var c="scale".concat(va(i));return{scale:(Mp[c]||un)(),realScaleType:Mp[c]?c:"point"}}return X(i)?{scale:i}:{scale:un(),realScaleType:"point"}},Kp=1e-4,Ym=function(t){var r=t.domain();if(!(!r||r.length<=2)){var n=r.length,i=t.range(),a=Math.min(i[0],i[1])-Kp,o=Math.max(i[0],i[1])+Kp,u=t(r[0]),c=t(r[n-1]);(u<a||u>o||c<a||c>o)&&t.domain([r[0],r[n-1]])}},OP=function(t,r){if(!t)return null;for(var n=0,i=t.length;n<i;n++)if(t[n].item===r)return t[n].position;return null},SP=function(t,r){if(!r||r.length!==2||!B(r[0])||!B(r[1]))return t;var n=Math.min(r[0],r[1]),i=Math.max(r[0],r[1]),a=[t[0],t[1]];return(!B(t[0])||t[0]<n)&&(a[0]=n),(!B(t[1])||t[1]>i)&&(a[1]=i),a[0]>i&&(a[0]=i),a[1]<n&&(a[1]=n),a},AP=function(t){var r=t.length;if(!(r<=0))for(var n=0,i=t[0].length;n<i;++n)for(var a=0,o=0,u=0;u<r;++u){var c=Xn(t[u][n][1])?t[u][n][0]:t[u][n][1];c>=0?(t[u][n][0]=a,t[u][n][1]=a+c,a=t[u][n][1]):(t[u][n][0]=o,t[u][n][1]=o+c,o=t[u][n][1])}},PP=function(t){var r=t.length;if(!(r<=0))for(var n=0,i=t[0].length;n<i;++n)for(var a=0,o=0;o<r;++o){var u=Xn(t[o][n][1])?t[o][n][0]:t[o][n][1];u>=0?(t[o][n][0]=a,t[o][n][1]=a+u,a=t[o][n][1]):(t[o][n][0]=0,t[o][n][1]=0)}},_P={sign:AP,expand:U0,none:yr,silhouette:H0,wiggle:K0,positive:PP},EP=function(t,r,n){var i=r.map(function(u){return u.props.dataKey}),a=_P[n],o=z0().keys(i).value(function(u,c){return+je(u,c,0)}).order(Pc).offset(a);return o(t)},TP=function(t,r,n,i,a,o){if(!t)return null;var u=o?r.reverse():r,c={},s=u.reduce(function(l,h){var d,y=(d=h.type)!==null&&d!==void 0&&d.defaultProps?ve(ve({},h.type.defaultProps),h.props):h.props,v=y.stackId,p=y.hide;if(p)return l;var b=y[n],w=l[b]||{hasStack:!1,stackGroups:{}};if(Se(v)){var g=w.stackGroups[v]||{numericAxisId:n,cateAxisId:i,items:[]};g.items.push(h),w.hasStack=!0,w.stackGroups[v]=g}else w.stackGroups[Yn("_stackId_")]={numericAxisId:n,cateAxisId:i,items:[h]};return ve(ve({},l),{},vr({},b,w))},c),f={};return Object.keys(s).reduce(function(l,h){var d=s[h];if(d.hasStack){var y={};d.stackGroups=Object.keys(d.stackGroups).reduce(function(v,p){var b=d.stackGroups[p];return ve(ve({},v),{},vr({},p,{numericAxisId:n,cateAxisId:i,items:b.items,stackedData:EP(t,b.items,a)}))},y)}return ve(ve({},l),{},vr({},h,d))},f)},Zm=function(t,r){var n=r.realScaleType,i=r.type,a=r.tickCount,o=r.originalDomain,u=r.allowDecimals,c=n||r.scale;if(c!=="auto"&&c!=="linear")return null;if(a&&i==="number"&&o&&(o[0]==="auto"||o[1]==="auto")){var s=t.domain();if(!s.length)return null;var f=WA(s,a,u);return t.domain([ja(f),Ta(f)]),{niceTicks:f}}if(a&&i==="number"){var l=t.domain(),h=zA(l,a,u);return{niceTicks:h}}return null};function GC(e){var t=e.axis,r=e.ticks,n=e.bandSize,i=e.entry,a=e.index,o=e.dataKey;if(t.type==="category"){if(!t.allowDuplicatedCategory&&t.dataKey&&!Z(i[t.dataKey])){var u=yi(r,"value",i[t.dataKey]);if(u)return u.coordinate+n/2}return r[a]?r[a].coordinate+n/2:null}var c=je(i,Z(o)?t.dataKey:o);return Z(c)?null:t.scale(c)}var Gp=function(t){var r=t.axis,n=t.ticks,i=t.offset,a=t.bandSize,o=t.entry,u=t.index;if(r.type==="category")return n[u]?n[u].coordinate+i:null;var c=je(o,r.dataKey,r.domain[u]);return Z(c)?null:r.scale(c)-a/2+i},jP=function(t){var r=t.numericAxis,n=r.scale.domain();if(r.type==="number"){var i=Math.min(n[0],n[1]),a=Math.max(n[0],n[1]);return i<=0&&a>=0?0:a<0?a:i}return n[0]},$P=function(t,r){var n,i=(n=t.type)!==null&&n!==void 0&&n.defaultProps?ve(ve({},t.type.defaultProps),t.props):t.props,a=i.stackId;if(Se(a)){var o=r[a];if(o){var u=o.items.indexOf(t);return u>=0?o.stackedData[u]:null}}return null},MP=function(t){return t.reduce(function(r,n){return[ja(n.concat([r[0]]).filter(B)),Ta(n.concat([r[1]]).filter(B))]},[1/0,-1/0])},Jm=function(t,r,n){return Object.keys(t).reduce(function(i,a){var o=t[a],u=o.stackedData,c=u.reduce(function(s,f){var l=MP(f.slice(r,n+1));return[Math.min(s[0],l[0]),Math.max(s[1],l[1])]},[1/0,-1/0]);return[Math.min(c[0],i[0]),Math.max(c[1],i[1])]},[1/0,-1/0]).map(function(i){return i===1/0||i===-1/0?0:i})},Vp=/^dataMin[\s]*-[\s]*([0-9]+([.]{1}[0-9]+){0,1})$/,Xp=/^dataMax[\s]*\+[\s]*([0-9]+([.]{1}[0-9]+){0,1})$/,Yc=function(t,r,n){if(X(t))return t(r,n);if(!Array.isArray(t))return r;var i=[];if(B(t[0]))i[0]=n?t[0]:Math.min(t[0],r[0]);else if(Vp.test(t[0])){var a=+Vp.exec(t[0])[1];i[0]=r[0]-a}else X(t[0])?i[0]=t[0](r[0]):i[0]=r[0];if(B(t[1]))i[1]=n?t[1]:Math.max(t[1],r[1]);else if(Xp.test(t[1])){var o=+Xp.exec(t[1])[1];i[1]=r[1]+o}else X(t[1])?i[1]=t[1](r[1]):i[1]=r[1];return i},Fi=function(t,r,n){if(t&&t.scale&&t.scale.bandwidth){var i=t.scale.bandwidth();if(!n||i>0)return i}if(t&&r&&r.length>=2){for(var a=Gs(r,function(l){return l.coordinate}),o=1/0,u=1,c=a.length;u<c;u++){var s=a[u],f=a[u-1];o=Math.min((s.coordinate||0)-(f.coordinate||0),o)}return o===1/0?0:o}return n?void 0:0},Yp=function(t,r,n){return!t||!t.length||$a(t,We(n,"type.defaultProps.domain"))?r:t},Qm=function(t,r){var n=t.type.defaultProps?ve(ve({},t.type.defaultProps),t.props):t.props,i=n.dataKey,a=n.name,o=n.unit,u=n.formatter,c=n.tooltipType,s=n.chartType,f=n.hide;return ve(ve({},V(t,!1)),{},{dataKey:i,unit:o,formatter:u,name:a||i,color:xl(t),value:je(r,i),type:c,payload:r,chartType:s,hide:f})};function Tn(e){"@babel/helpers - typeof";return Tn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Tn(e)}function Zp(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function lt(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Zp(Object(r),!0).forEach(function(n){eg(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Zp(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function eg(e,t,r){return t=IP(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function IP(e){var t=CP(e,"string");return Tn(t)=="symbol"?t:t+""}function CP(e,t){if(Tn(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Tn(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function kP(e,t){return BP(e)||DP(e,t)||RP(e,t)||NP()}function NP(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function RP(e,t){if(e){if(typeof e=="string")return Jp(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Jp(e,t)}}function Jp(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function DP(e,t){var r=e==null?null:typeof Symbol!="undefined"&&e[Symbol.iterator]||e["@@iterator"];if(r!=null){var n,i,a,o,u=[],c=!0,s=!1;try{if(a=(r=r.call(e)).next,t!==0)for(;!(c=(n=a.call(r)).done)&&(u.push(n.value),u.length!==t);c=!0);}catch(f){s=!0,i=f}finally{try{if(!c&&r.return!=null&&(o=r.return(),Object(o)!==o))return}finally{if(s)throw i}}return u}}function BP(e){if(Array.isArray(e))return e}var Wi=Math.PI/180,LP=function(t){return t*180/Math.PI},le=function(t,r,n,i){return{x:t+Math.cos(-Wi*i)*n,y:r+Math.sin(-Wi*i)*n}},tg=function(t,r){var n=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{top:0,right:0,bottom:0,left:0};return Math.min(Math.abs(t-(n.left||0)-(n.right||0)),Math.abs(r-(n.top||0)-(n.bottom||0)))/2},qP=function(t,r,n,i,a){var o=t.width,u=t.height,c=t.startAngle,s=t.endAngle,f=Ce(t.cx,o,o/2),l=Ce(t.cy,u,u/2),h=tg(o,u,n),d=Ce(t.innerRadius,h,0),y=Ce(t.outerRadius,h,h*.8),v=Object.keys(r);return v.reduce(function(p,b){var w=r[b],g=w.domain,O=w.reversed,m;if(Z(w.range))i==="angleAxis"?m=[c,s]:i==="radiusAxis"&&(m=[d,y]),O&&(m=[m[1],m[0]]);else{m=w.range;var x=m,S=kP(x,2);c=S[0],s=S[1]}var A=Xm(w,a),_=A.realScaleType,$=A.scale;$.domain(g).range(m),Ym($);var E=Zm($,lt(lt({},w),{},{realScaleType:_})),T=lt(lt(lt({},w),E),{},{range:m,radius:y,realScaleType:_,scale:$,cx:f,cy:l,innerRadius:d,outerRadius:y,startAngle:c,endAngle:s});return lt(lt({},p),{},eg({},b,T))},{})},FP=function(t,r){var n=t.x,i=t.y,a=r.x,o=r.y;return Math.sqrt(Math.pow(n-a,2)+Math.pow(i-o,2))},WP=function(t,r){var n=t.x,i=t.y,a=r.cx,o=r.cy,u=FP({x:n,y:i},{x:a,y:o});if(u<=0)return{radius:u};var c=(n-a)/u,s=Math.acos(c);return i>o&&(s=2*Math.PI-s),{radius:u,angle:LP(s),angleInRadian:s}},zP=function(t){var r=t.startAngle,n=t.endAngle,i=Math.floor(r/360),a=Math.floor(n/360),o=Math.min(i,a);return{startAngle:r-o*360,endAngle:n-o*360}},UP=function(t,r){var n=r.startAngle,i=r.endAngle,a=Math.floor(n/360),o=Math.floor(i/360),u=Math.min(a,o);return t+u*360},Qp=function(t,r){var n=t.x,i=t.y,a=WP({x:n,y:i},r),o=a.radius,u=a.angle,c=r.innerRadius,s=r.outerRadius;if(o<c||o>s)return!1;if(o===0)return!0;var f=zP(r),l=f.startAngle,h=f.endAngle,d=u,y;if(l<=h){for(;d>h;)d-=360;for(;d<l;)d+=360;y=d>=l&&d<=h}else{for(;d>l;)d-=360;for(;d<h;)d+=360;y=d>=h&&d<=l}return y?lt(lt({},r),{},{radius:o,angle:UP(d,r)}):null},rg=function(t){return!D.isValidElement(t)&&!X(t)&&typeof t!="boolean"?t.className:""};function jn(e){"@babel/helpers - typeof";return jn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},jn(e)}var HP=["offset"];function KP(e){return YP(e)||XP(e)||VP(e)||GP()}function GP(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function VP(e,t){if(e){if(typeof e=="string")return Zc(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Zc(e,t)}}function XP(e){if(typeof Symbol!="undefined"&&e[Symbol.iterator]!=null||e["@@iterator"]!=null)return Array.from(e)}function YP(e){if(Array.isArray(e))return Zc(e)}function Zc(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function ZP(e,t){if(e==null)return{};var r=JP(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function JP(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}function ed(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Oe(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?ed(Object(r),!0).forEach(function(n){QP(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):ed(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function QP(e,t,r){return t=e_(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function e_(e){var t=t_(e,"string");return jn(t)=="symbol"?t:t+""}function t_(e,t){if(jn(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(jn(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function $n(){return $n=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},$n.apply(this,arguments)}var r_=function(t){var r=t.value,n=t.formatter,i=Z(t.children)?r:t.children;return X(n)?n(i):i},n_=function(t,r){var n=Ie(r-t),i=Math.min(Math.abs(r-t),360);return n*i},i_=function(t,r,n){var i=t.position,a=t.viewBox,o=t.offset,u=t.className,c=a,s=c.cx,f=c.cy,l=c.innerRadius,h=c.outerRadius,d=c.startAngle,y=c.endAngle,v=c.clockWise,p=(l+h)/2,b=n_(d,y),w=b>=0?1:-1,g,O;i==="insideStart"?(g=d+w*o,O=v):i==="insideEnd"?(g=y-w*o,O=!v):i==="end"&&(g=y+w*o,O=v),O=b<=0?O:!O;var m=le(s,f,p,g),x=le(s,f,p,g+(O?1:-1)*359),S="M".concat(m.x,",").concat(m.y,`
    A`).concat(p,",").concat(p,",0,1,").concat(O?0:1,`,
    `).concat(x.x,",").concat(x.y),A=Z(t.id)?Yn("recharts-radial-line-"):t.id;return P.createElement("text",$n({},n,{dominantBaseline:"central",className:Q("recharts-radial-bar-label",u)}),P.createElement("defs",null,P.createElement("path",{id:A,d:S})),P.createElement("textPath",{xlinkHref:"#".concat(A)},r))},a_=function(t){var r=t.viewBox,n=t.offset,i=t.position,a=r,o=a.cx,u=a.cy,c=a.innerRadius,s=a.outerRadius,f=a.startAngle,l=a.endAngle,h=(f+l)/2;if(i==="outside"){var d=le(o,u,s+n,h),y=d.x,v=d.y;return{x:y,y:v,textAnchor:y>=o?"start":"end",verticalAnchor:"middle"}}if(i==="center")return{x:o,y:u,textAnchor:"middle",verticalAnchor:"middle"};if(i==="centerTop")return{x:o,y:u,textAnchor:"middle",verticalAnchor:"start"};if(i==="centerBottom")return{x:o,y:u,textAnchor:"middle",verticalAnchor:"end"};var p=(c+s)/2,b=le(o,u,p,h),w=b.x,g=b.y;return{x:w,y:g,textAnchor:"middle",verticalAnchor:"middle"}},o_=function(t){var r=t.viewBox,n=t.parentViewBox,i=t.offset,a=t.position,o=r,u=o.x,c=o.y,s=o.width,f=o.height,l=f>=0?1:-1,h=l*i,d=l>0?"end":"start",y=l>0?"start":"end",v=s>=0?1:-1,p=v*i,b=v>0?"end":"start",w=v>0?"start":"end";if(a==="top"){var g={x:u+s/2,y:c-l*i,textAnchor:"middle",verticalAnchor:d};return Oe(Oe({},g),n?{height:Math.max(c-n.y,0),width:s}:{})}if(a==="bottom"){var O={x:u+s/2,y:c+f+h,textAnchor:"middle",verticalAnchor:y};return Oe(Oe({},O),n?{height:Math.max(n.y+n.height-(c+f),0),width:s}:{})}if(a==="left"){var m={x:u-p,y:c+f/2,textAnchor:b,verticalAnchor:"middle"};return Oe(Oe({},m),n?{width:Math.max(m.x-n.x,0),height:f}:{})}if(a==="right"){var x={x:u+s+p,y:c+f/2,textAnchor:w,verticalAnchor:"middle"};return Oe(Oe({},x),n?{width:Math.max(n.x+n.width-x.x,0),height:f}:{})}var S=n?{width:s,height:f}:{};return a==="insideLeft"?Oe({x:u+p,y:c+f/2,textAnchor:w,verticalAnchor:"middle"},S):a==="insideRight"?Oe({x:u+s-p,y:c+f/2,textAnchor:b,verticalAnchor:"middle"},S):a==="insideTop"?Oe({x:u+s/2,y:c+h,textAnchor:"middle",verticalAnchor:y},S):a==="insideBottom"?Oe({x:u+s/2,y:c+f-h,textAnchor:"middle",verticalAnchor:d},S):a==="insideTopLeft"?Oe({x:u+p,y:c+h,textAnchor:w,verticalAnchor:y},S):a==="insideTopRight"?Oe({x:u+s-p,y:c+h,textAnchor:b,verticalAnchor:y},S):a==="insideBottomLeft"?Oe({x:u+p,y:c+f-h,textAnchor:w,verticalAnchor:d},S):a==="insideBottomRight"?Oe({x:u+s-p,y:c+f-h,textAnchor:b,verticalAnchor:d},S):Wr(a)&&(B(a.x)||Wt(a.x))&&(B(a.y)||Wt(a.y))?Oe({x:u+Ce(a.x,s),y:c+Ce(a.y,f),textAnchor:"end",verticalAnchor:"end"},S):Oe({x:u+s/2,y:c+f/2,textAnchor:"middle",verticalAnchor:"middle"},S)},u_=function(t){return"cx"in t&&B(t.cx)};function _e(e){var t=e.offset,r=t===void 0?5:t,n=ZP(e,HP),i=Oe({offset:r},n),a=i.viewBox,o=i.position,u=i.value,c=i.children,s=i.content,f=i.className,l=f===void 0?"":f,h=i.textBreakAll;if(!a||Z(u)&&Z(c)&&!D.isValidElement(s)&&!X(s))return null;if(D.isValidElement(s))return D.cloneElement(s,i);var d;if(X(s)){if(d=D.createElement(s,i),D.isValidElement(d))return d}else d=r_(i);var y=u_(a),v=V(i,!0);if(y&&(o==="insideStart"||o==="insideEnd"||o==="end"))return i_(i,d,v);var p=y?a_(i):o_(i);return P.createElement(Yt,$n({className:Q("recharts-label",l)},v,p,{breakAll:h}),d)}_e.displayName="Label";var ng=function(t){var r=t.cx,n=t.cy,i=t.angle,a=t.startAngle,o=t.endAngle,u=t.r,c=t.radius,s=t.innerRadius,f=t.outerRadius,l=t.x,h=t.y,d=t.top,y=t.left,v=t.width,p=t.height,b=t.clockWise,w=t.labelViewBox;if(w)return w;if(B(v)&&B(p)){if(B(l)&&B(h))return{x:l,y:h,width:v,height:p};if(B(d)&&B(y))return{x:d,y,width:v,height:p}}return B(l)&&B(h)?{x:l,y:h,width:0,height:0}:B(r)&&B(n)?{cx:r,cy:n,startAngle:a||i||0,endAngle:o||i||0,innerRadius:s||0,outerRadius:f||c||u||0,clockWise:b}:t.viewBox?t.viewBox:{}},c_=function(t,r){return t?t===!0?P.createElement(_e,{key:"label-implicit",viewBox:r}):Se(t)?P.createElement(_e,{key:"label-implicit",viewBox:r,value:t}):D.isValidElement(t)?t.type===_e?D.cloneElement(t,{key:"label-implicit",viewBox:r}):P.createElement(_e,{key:"label-implicit",content:t,viewBox:r}):X(t)?P.createElement(_e,{key:"label-implicit",content:t,viewBox:r}):Wr(t)?P.createElement(_e,$n({viewBox:r},t,{key:"label-implicit"})):null:null},s_=function(t,r){var n=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!0;if(!t||!t.children&&n&&!t.label)return null;var i=t.children,a=ng(t),o=Ve(i,_e).map(function(c,s){return D.cloneElement(c,{viewBox:r||a,key:"label-".concat(s)})});if(!n)return o;var u=c_(t.label,r||a);return[u].concat(KP(o))};_e.parseViewBox=ng;_e.renderCallByParent=s_;var Lu,td;function l_(){if(td)return Lu;td=1;function e(t){var r=t==null?0:t.length;return r?t[r-1]:void 0}return Lu=e,Lu}var f_=l_();const h_=oe(f_);function Mn(e){"@babel/helpers - typeof";return Mn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Mn(e)}var p_=["valueAccessor"],d_=["data","dataKey","clockWise","id","textBreakAll"];function v_(e){return b_(e)||g_(e)||m_(e)||y_()}function y_(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function m_(e,t){if(e){if(typeof e=="string")return Jc(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Jc(e,t)}}function g_(e){if(typeof Symbol!="undefined"&&e[Symbol.iterator]!=null||e["@@iterator"]!=null)return Array.from(e)}function b_(e){if(Array.isArray(e))return Jc(e)}function Jc(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function zi(){return zi=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},zi.apply(this,arguments)}function rd(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function nd(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?rd(Object(r),!0).forEach(function(n){x_(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):rd(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function x_(e,t,r){return t=w_(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function w_(e){var t=O_(e,"string");return Mn(t)=="symbol"?t:t+""}function O_(e,t){if(Mn(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Mn(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function id(e,t){if(e==null)return{};var r=S_(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function S_(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}var A_=function(t){return Array.isArray(t.value)?h_(t.value):t.value};function Tt(e){var t=e.valueAccessor,r=t===void 0?A_:t,n=id(e,p_),i=n.data,a=n.dataKey,o=n.clockWise,u=n.id,c=n.textBreakAll,s=id(n,d_);return!i||!i.length?null:P.createElement(ie,{className:"recharts-label-list"},i.map(function(f,l){var h=Z(a)?r(f,l):je(f&&f.payload,a),d=Z(u)?{}:{id:"".concat(u,"-").concat(l)};return P.createElement(_e,zi({},V(f,!0),s,d,{parentViewBox:f.parentViewBox,value:h,textBreakAll:c,viewBox:_e.parseViewBox(Z(o)?f:nd(nd({},f),{},{clockWise:o})),key:"label-".concat(l),index:l}))}))}Tt.displayName="LabelList";function P_(e,t){return e?e===!0?P.createElement(Tt,{key:"labelList-implicit",data:t}):P.isValidElement(e)||X(e)?P.createElement(Tt,{key:"labelList-implicit",data:t,content:e}):Wr(e)?P.createElement(Tt,zi({data:t},e,{key:"labelList-implicit"})):null:null}function __(e,t){var r=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!0;if(!e||!e.children&&r&&!e.label)return null;var n=e.children,i=Ve(n,Tt).map(function(o,u){return D.cloneElement(o,{data:t,key:"labelList-".concat(u)})});if(!r)return i;var a=P_(e.label,t);return[a].concat(v_(i))}Tt.renderCallByParent=__;function In(e){"@babel/helpers - typeof";return In=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},In(e)}function Qc(){return Qc=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Qc.apply(this,arguments)}function ad(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function od(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?ad(Object(r),!0).forEach(function(n){E_(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):ad(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function E_(e,t,r){return t=T_(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function T_(e){var t=j_(e,"string");return In(t)=="symbol"?t:t+""}function j_(e,t){if(In(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(In(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var $_=function(t,r){var n=Ie(r-t),i=Math.min(Math.abs(r-t),359.999);return n*i},li=function(t){var r=t.cx,n=t.cy,i=t.radius,a=t.angle,o=t.sign,u=t.isExternal,c=t.cornerRadius,s=t.cornerIsExternal,f=c*(u?1:-1)+i,l=Math.asin(c/f)/Wi,h=s?a:a+o*l,d=le(r,n,f,h),y=le(r,n,i,h),v=s?a-o*l:a,p=le(r,n,f*Math.cos(l*Wi),v);return{center:d,circleTangency:y,lineTangency:p,theta:l}},ig=function(t){var r=t.cx,n=t.cy,i=t.innerRadius,a=t.outerRadius,o=t.startAngle,u=t.endAngle,c=$_(o,u),s=o+c,f=le(r,n,a,o),l=le(r,n,a,s),h="M ".concat(f.x,",").concat(f.y,`
    A `).concat(a,",").concat(a,`,0,
    `).concat(+(Math.abs(c)>180),",").concat(+(o>s),`,
    `).concat(l.x,",").concat(l.y,`
  `);if(i>0){var d=le(r,n,i,o),y=le(r,n,i,s);h+="L ".concat(y.x,",").concat(y.y,`
            A `).concat(i,",").concat(i,`,0,
            `).concat(+(Math.abs(c)>180),",").concat(+(o<=s),`,
            `).concat(d.x,",").concat(d.y," Z")}else h+="L ".concat(r,",").concat(n," Z");return h},M_=function(t){var r=t.cx,n=t.cy,i=t.innerRadius,a=t.outerRadius,o=t.cornerRadius,u=t.forceCornerRadius,c=t.cornerIsExternal,s=t.startAngle,f=t.endAngle,l=Ie(f-s),h=li({cx:r,cy:n,radius:a,angle:s,sign:l,cornerRadius:o,cornerIsExternal:c}),d=h.circleTangency,y=h.lineTangency,v=h.theta,p=li({cx:r,cy:n,radius:a,angle:f,sign:-l,cornerRadius:o,cornerIsExternal:c}),b=p.circleTangency,w=p.lineTangency,g=p.theta,O=c?Math.abs(s-f):Math.abs(s-f)-v-g;if(O<0)return u?"M ".concat(y.x,",").concat(y.y,`
        a`).concat(o,",").concat(o,",0,0,1,").concat(o*2,`,0
        a`).concat(o,",").concat(o,",0,0,1,").concat(-o*2,`,0
      `):ig({cx:r,cy:n,innerRadius:i,outerRadius:a,startAngle:s,endAngle:f});var m="M ".concat(y.x,",").concat(y.y,`
    A`).concat(o,",").concat(o,",0,0,").concat(+(l<0),",").concat(d.x,",").concat(d.y,`
    A`).concat(a,",").concat(a,",0,").concat(+(O>180),",").concat(+(l<0),",").concat(b.x,",").concat(b.y,`
    A`).concat(o,",").concat(o,",0,0,").concat(+(l<0),",").concat(w.x,",").concat(w.y,`
  `);if(i>0){var x=li({cx:r,cy:n,radius:i,angle:s,sign:l,isExternal:!0,cornerRadius:o,cornerIsExternal:c}),S=x.circleTangency,A=x.lineTangency,_=x.theta,$=li({cx:r,cy:n,radius:i,angle:f,sign:-l,isExternal:!0,cornerRadius:o,cornerIsExternal:c}),E=$.circleTangency,T=$.lineTangency,j=$.theta,C=c?Math.abs(s-f):Math.abs(s-f)-_-j;if(C<0&&o===0)return"".concat(m,"L").concat(r,",").concat(n,"Z");m+="L".concat(T.x,",").concat(T.y,`
      A`).concat(o,",").concat(o,",0,0,").concat(+(l<0),",").concat(E.x,",").concat(E.y,`
      A`).concat(i,",").concat(i,",0,").concat(+(C>180),",").concat(+(l>0),",").concat(S.x,",").concat(S.y,`
      A`).concat(o,",").concat(o,",0,0,").concat(+(l<0),",").concat(A.x,",").concat(A.y,"Z")}else m+="L".concat(r,",").concat(n,"Z");return m},I_={cx:0,cy:0,innerRadius:0,outerRadius:0,startAngle:0,endAngle:0,cornerRadius:0,forceCornerRadius:!1,cornerIsExternal:!1},ag=function(t){var r=od(od({},I_),t),n=r.cx,i=r.cy,a=r.innerRadius,o=r.outerRadius,u=r.cornerRadius,c=r.forceCornerRadius,s=r.cornerIsExternal,f=r.startAngle,l=r.endAngle,h=r.className;if(o<a||f===l)return null;var d=Q("recharts-sector",h),y=o-a,v=Ce(u,y,0,!0),p;return v>0&&Math.abs(f-l)<360?p=M_({cx:n,cy:i,innerRadius:a,outerRadius:o,cornerRadius:Math.min(v,y/2),forceCornerRadius:c,cornerIsExternal:s,startAngle:f,endAngle:l}):p=ig({cx:n,cy:i,innerRadius:a,outerRadius:o,startAngle:f,endAngle:l}),P.createElement("path",Qc({},V(r,!0),{className:d,d:p,role:"img"}))};function Cn(e){"@babel/helpers - typeof";return Cn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Cn(e)}function es(){return es=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},es.apply(this,arguments)}function ud(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function cd(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?ud(Object(r),!0).forEach(function(n){C_(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):ud(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function C_(e,t,r){return t=k_(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function k_(e){var t=N_(e,"string");return Cn(t)=="symbol"?t:t+""}function N_(e,t){if(Cn(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Cn(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var sd={curveBasisClosed:I0,curveBasisOpen:C0,curveBasis:M0,curveBumpX:m0,curveBumpY:g0,curveLinearClosed:k0,curveLinear:ma,curveMonotoneX:N0,curveMonotoneY:R0,curveNatural:D0,curveStep:B0,curveStepAfter:q0,curveStepBefore:L0},fi=function(t){return t.x===+t.x&&t.y===+t.y},en=function(t){return t.x},tn=function(t){return t.y},R_=function(t,r){if(X(t))return t;var n="curve".concat(va(t));return(n==="curveMonotone"||n==="curveBump")&&r?sd["".concat(n).concat(r==="vertical"?"Y":"X")]:sd[n]||ma},D_=function(t){var r=t.type,n=r===void 0?"linear":r,i=t.points,a=i===void 0?[]:i,o=t.baseLine,u=t.layout,c=t.connectNulls,s=c===void 0?!1:c,f=R_(n,u),l=s?a.filter(function(v){return fi(v)}):a,h;if(Array.isArray(o)){var d=s?o.filter(function(v){return fi(v)}):o,y=l.map(function(v,p){return cd(cd({},v),{},{base:d[p]})});return u==="vertical"?h=ri().y(tn).x1(en).x0(function(v){return v.base.x}):h=ri().x(en).y1(tn).y0(function(v){return v.base.y}),h.defined(fi).curve(f),h(y)}return u==="vertical"&&B(o)?h=ri().y(tn).x1(en).x0(o):B(o)?h=ri().x(en).y1(tn).y0(o):h=dy().x(en).y(tn),h.defined(fi).curve(f),h(l)},ts=function(t){var r=t.className,n=t.points,i=t.path,a=t.pathRef;if((!n||!n.length)&&!i)return null;var o=n&&n.length?D_(t):i;return D.createElement("path",es({},V(t,!1),mi(t),{className:Q("recharts-curve",r),d:o,ref:a}))},qu={exports:{}},Fu,ld;function B_(){if(ld)return Fu;ld=1;var e="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED";return Fu=e,Fu}var Wu,fd;function L_(){if(fd)return Wu;fd=1;var e=B_();function t(){}function r(){}return r.resetWarningCache=t,Wu=function(){function n(o,u,c,s,f,l){if(l!==e){var h=new Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types");throw h.name="Invariant Violation",h}}n.isRequired=n;function i(){return n}var a={array:n,bigint:n,bool:n,func:n,number:n,object:n,string:n,symbol:n,any:n,arrayOf:i,element:n,elementType:n,instanceOf:i,node:n,objectOf:i,oneOf:i,oneOfType:i,shape:i,exact:i,checkPropTypes:r,resetWarningCache:t};return a.PropTypes=a,a},Wu}var hd;function q_(){return hd||(hd=1,qu.exports=L_()()),qu.exports}var F_=q_();const ne=oe(F_);var W_=Object.getOwnPropertyNames,z_=Object.getOwnPropertySymbols,U_=Object.prototype.hasOwnProperty;function pd(e,t){return function(n,i,a){return e(n,i,a)&&t(n,i,a)}}function hi(e){return function(r,n,i){if(!r||!n||typeof r!="object"||typeof n!="object")return e(r,n,i);var a=i.cache,o=a.get(r),u=a.get(n);if(o&&u)return o===n&&u===r;a.set(r,n),a.set(n,r);var c=e(r,n,i);return a.delete(r),a.delete(n),c}}function dd(e){return W_(e).concat(z_(e))}var H_=Object.hasOwn||function(e,t){return U_.call(e,t)};function rr(e,t){return e===t||!e&&!t&&e!==e&&t!==t}var K_="__v",G_="__o",V_="_owner",vd=Object.getOwnPropertyDescriptor,yd=Object.keys;function X_(e,t,r){var n=e.length;if(t.length!==n)return!1;for(;n-- >0;)if(!r.equals(e[n],t[n],n,n,e,t,r))return!1;return!0}function Y_(e,t){return rr(e.getTime(),t.getTime())}function Z_(e,t){return e.name===t.name&&e.message===t.message&&e.cause===t.cause&&e.stack===t.stack}function J_(e,t){return e===t}function md(e,t,r){var n=e.size;if(n!==t.size)return!1;if(!n)return!0;for(var i=new Array(n),a=e.entries(),o,u,c=0;(o=a.next())&&!o.done;){for(var s=t.entries(),f=!1,l=0;(u=s.next())&&!u.done;){if(i[l]){l++;continue}var h=o.value,d=u.value;if(r.equals(h[0],d[0],c,l,e,t,r)&&r.equals(h[1],d[1],h[0],d[0],e,t,r)){f=i[l]=!0;break}l++}if(!f)return!1;c++}return!0}var Q_=rr;function eE(e,t,r){var n=yd(e),i=n.length;if(yd(t).length!==i)return!1;for(;i-- >0;)if(!og(e,t,r,n[i]))return!1;return!0}function rn(e,t,r){var n=dd(e),i=n.length;if(dd(t).length!==i)return!1;for(var a,o,u;i-- >0;)if(a=n[i],!og(e,t,r,a)||(o=vd(e,a),u=vd(t,a),(o||u)&&(!o||!u||o.configurable!==u.configurable||o.enumerable!==u.enumerable||o.writable!==u.writable)))return!1;return!0}function tE(e,t){return rr(e.valueOf(),t.valueOf())}function rE(e,t){return e.source===t.source&&e.flags===t.flags}function gd(e,t,r){var n=e.size;if(n!==t.size)return!1;if(!n)return!0;for(var i=new Array(n),a=e.values(),o,u;(o=a.next())&&!o.done;){for(var c=t.values(),s=!1,f=0;(u=c.next())&&!u.done;){if(!i[f]&&r.equals(o.value,u.value,o.value,u.value,e,t,r)){s=i[f]=!0;break}f++}if(!s)return!1}return!0}function nE(e,t){var r=e.length;if(t.length!==r)return!1;for(;r-- >0;)if(e[r]!==t[r])return!1;return!0}function iE(e,t){return e.hostname===t.hostname&&e.pathname===t.pathname&&e.protocol===t.protocol&&e.port===t.port&&e.hash===t.hash&&e.username===t.username&&e.password===t.password}function og(e,t,r,n){return(n===V_||n===G_||n===K_)&&(e.$$typeof||t.$$typeof)?!0:H_(t,n)&&r.equals(e[n],t[n],n,n,e,t,r)}var aE="[object Arguments]",oE="[object Boolean]",uE="[object Date]",cE="[object Error]",sE="[object Map]",lE="[object Number]",fE="[object Object]",hE="[object RegExp]",pE="[object Set]",dE="[object String]",vE="[object URL]",yE=Array.isArray,bd=typeof ArrayBuffer=="function"&&ArrayBuffer.isView?ArrayBuffer.isView:null,xd=Object.assign,mE=Object.prototype.toString.call.bind(Object.prototype.toString);function gE(e){var t=e.areArraysEqual,r=e.areDatesEqual,n=e.areErrorsEqual,i=e.areFunctionsEqual,a=e.areMapsEqual,o=e.areNumbersEqual,u=e.areObjectsEqual,c=e.arePrimitiveWrappersEqual,s=e.areRegExpsEqual,f=e.areSetsEqual,l=e.areTypedArraysEqual,h=e.areUrlsEqual;return function(y,v,p){if(y===v)return!0;if(y==null||v==null)return!1;var b=typeof y;if(b!==typeof v)return!1;if(b!=="object")return b==="number"?o(y,v,p):b==="function"?i(y,v,p):!1;var w=y.constructor;if(w!==v.constructor)return!1;if(w===Object)return u(y,v,p);if(yE(y))return t(y,v,p);if(bd!=null&&bd(y))return l(y,v,p);if(w===Date)return r(y,v,p);if(w===RegExp)return s(y,v,p);if(w===Map)return a(y,v,p);if(w===Set)return f(y,v,p);var g=mE(y);return g===uE?r(y,v,p):g===hE?s(y,v,p):g===sE?a(y,v,p):g===pE?f(y,v,p):g===fE?typeof y.then!="function"&&typeof v.then!="function"&&u(y,v,p):g===vE?h(y,v,p):g===cE?n(y,v,p):g===aE?u(y,v,p):g===oE||g===lE||g===dE?c(y,v,p):!1}}function bE(e){var t=e.circular,r=e.createCustomConfig,n=e.strict,i={areArraysEqual:n?rn:X_,areDatesEqual:Y_,areErrorsEqual:Z_,areFunctionsEqual:J_,areMapsEqual:n?pd(md,rn):md,areNumbersEqual:Q_,areObjectsEqual:n?rn:eE,arePrimitiveWrappersEqual:tE,areRegExpsEqual:rE,areSetsEqual:n?pd(gd,rn):gd,areTypedArraysEqual:n?rn:nE,areUrlsEqual:iE};if(r&&(i=xd({},i,r(i))),t){var a=hi(i.areArraysEqual),o=hi(i.areMapsEqual),u=hi(i.areObjectsEqual),c=hi(i.areSetsEqual);i=xd({},i,{areArraysEqual:a,areMapsEqual:o,areObjectsEqual:u,areSetsEqual:c})}return i}function xE(e){return function(t,r,n,i,a,o,u){return e(t,r,u)}}function wE(e){var t=e.circular,r=e.comparator,n=e.createState,i=e.equals,a=e.strict;if(n)return function(c,s){var f=n(),l=f.cache,h=l===void 0?t?new WeakMap:void 0:l,d=f.meta;return r(c,s,{cache:h,equals:i,meta:d,strict:a})};if(t)return function(c,s){return r(c,s,{cache:new WeakMap,equals:i,meta:void 0,strict:a})};var o={cache:void 0,equals:i,meta:void 0,strict:a};return function(c,s){return r(c,s,o)}}var OE=Mt();Mt({strict:!0});Mt({circular:!0});Mt({circular:!0,strict:!0});Mt({createInternalComparator:function(){return rr}});Mt({strict:!0,createInternalComparator:function(){return rr}});Mt({circular:!0,createInternalComparator:function(){return rr}});Mt({circular:!0,createInternalComparator:function(){return rr},strict:!0});function Mt(e){e===void 0&&(e={});var t=e.circular,r=t===void 0?!1:t,n=e.createInternalComparator,i=e.createState,a=e.strict,o=a===void 0?!1:a,u=bE(e),c=gE(u),s=n?n(c):xE(c);return wE({circular:r,comparator:c,createState:i,equals:s,strict:o})}function SE(e){typeof requestAnimationFrame!="undefined"&&requestAnimationFrame(e)}function wd(e){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0,r=-1,n=function i(a){r<0&&(r=a),a-r>t?(e(a),r=-1):SE(i)};requestAnimationFrame(n)}function rs(e){"@babel/helpers - typeof";return rs=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},rs(e)}function AE(e){return TE(e)||EE(e)||_E(e)||PE()}function PE(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function _E(e,t){if(e){if(typeof e=="string")return Od(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Od(e,t)}}function Od(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function EE(e){if(typeof Symbol!="undefined"&&e[Symbol.iterator]!=null||e["@@iterator"]!=null)return Array.from(e)}function TE(e){if(Array.isArray(e))return e}function jE(){var e={},t=function(){return null},r=!1,n=function i(a){if(!r){if(Array.isArray(a)){if(!a.length)return;var o=a,u=AE(o),c=u[0],s=u.slice(1);if(typeof c=="number"){wd(i.bind(null,s),c);return}i(c),wd(i.bind(null,s));return}rs(a)==="object"&&(e=a,t(e)),typeof a=="function"&&a()}};return{stop:function(){r=!0},start:function(a){r=!1,n(a)},subscribe:function(a){return t=a,function(){t=function(){return null}}}}}function kn(e){"@babel/helpers - typeof";return kn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},kn(e)}function Sd(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Ad(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Sd(Object(r),!0).forEach(function(n){ug(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Sd(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function ug(e,t,r){return t=$E(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function $E(e){var t=ME(e,"string");return kn(t)==="symbol"?t:String(t)}function ME(e,t){if(kn(e)!=="object"||e===null)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(kn(n)!=="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var IE=function(t,r){return[Object.keys(t),Object.keys(r)].reduce(function(n,i){return n.filter(function(a){return i.includes(a)})})},CE=function(t){return t},kE=function(t){return t.replace(/([A-Z])/g,function(r){return"-".concat(r.toLowerCase())})},sn=function(t,r){return Object.keys(r).reduce(function(n,i){return Ad(Ad({},n),{},ug({},i,t(i,r[i])))},{})},Pd=function(t,r,n){return t.map(function(i){return"".concat(kE(i)," ").concat(r,"ms ").concat(n)}).join(",")};function NE(e,t){return BE(e)||DE(e,t)||cg(e,t)||RE()}function RE(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function DE(e,t){var r=e==null?null:typeof Symbol!="undefined"&&e[Symbol.iterator]||e["@@iterator"];if(r!=null){var n,i,a,o,u=[],c=!0,s=!1;try{if(a=(r=r.call(e)).next,t!==0)for(;!(c=(n=a.call(r)).done)&&(u.push(n.value),u.length!==t);c=!0);}catch(f){s=!0,i=f}finally{try{if(!c&&r.return!=null&&(o=r.return(),Object(o)!==o))return}finally{if(s)throw i}}return u}}function BE(e){if(Array.isArray(e))return e}function LE(e){return WE(e)||FE(e)||cg(e)||qE()}function qE(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function cg(e,t){if(e){if(typeof e=="string")return ns(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return ns(e,t)}}function FE(e){if(typeof Symbol!="undefined"&&e[Symbol.iterator]!=null||e["@@iterator"]!=null)return Array.from(e)}function WE(e){if(Array.isArray(e))return ns(e)}function ns(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}var Ui=1e-4,sg=function(t,r){return[0,3*t,3*r-6*t,3*t-3*r+1]},lg=function(t,r){return t.map(function(n,i){return n*Math.pow(r,i)}).reduce(function(n,i){return n+i})},_d=function(t,r){return function(n){var i=sg(t,r);return lg(i,n)}},zE=function(t,r){return function(n){var i=sg(t,r),a=[].concat(LE(i.map(function(o,u){return o*u}).slice(1)),[0]);return lg(a,n)}},Ed=function(){for(var t=arguments.length,r=new Array(t),n=0;n<t;n++)r[n]=arguments[n];var i=r[0],a=r[1],o=r[2],u=r[3];if(r.length===1)switch(r[0]){case"linear":i=0,a=0,o=1,u=1;break;case"ease":i=.25,a=.1,o=.25,u=1;break;case"ease-in":i=.42,a=0,o=1,u=1;break;case"ease-out":i=.42,a=0,o=.58,u=1;break;case"ease-in-out":i=0,a=0,o=.58,u=1;break;default:{var c=r[0].split("(");if(c[0]==="cubic-bezier"&&c[1].split(")")[0].split(",").length===4){var s=c[1].split(")")[0].split(",").map(function(p){return parseFloat(p)}),f=NE(s,4);i=f[0],a=f[1],o=f[2],u=f[3]}}}var l=_d(i,o),h=_d(a,u),d=zE(i,o),y=function(b){return b>1?1:b<0?0:b},v=function(b){for(var w=b>1?1:b,g=w,O=0;O<8;++O){var m=l(g)-w,x=d(g);if(Math.abs(m-w)<Ui||x<Ui)return h(g);g=y(g-m/x)}return h(g)};return v.isStepper=!1,v},UE=function(){var t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},r=t.stiff,n=r===void 0?100:r,i=t.damping,a=i===void 0?8:i,o=t.dt,u=o===void 0?17:o,c=function(f,l,h){var d=-(f-l)*n,y=h*a,v=h+(d-y)*u/1e3,p=h*u/1e3+f;return Math.abs(p-l)<Ui&&Math.abs(v)<Ui?[l,0]:[p,v]};return c.isStepper=!0,c.dt=u,c},HE=function(){for(var t=arguments.length,r=new Array(t),n=0;n<t;n++)r[n]=arguments[n];var i=r[0];if(typeof i=="string")switch(i){case"ease":case"ease-in-out":case"ease-out":case"ease-in":case"linear":return Ed(i);case"spring":return UE();default:if(i.split("(")[0]==="cubic-bezier")return Ed(i)}return typeof i=="function"?i:null};function Nn(e){"@babel/helpers - typeof";return Nn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Nn(e)}function Td(e){return VE(e)||GE(e)||fg(e)||KE()}function KE(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function GE(e){if(typeof Symbol!="undefined"&&e[Symbol.iterator]!=null||e["@@iterator"]!=null)return Array.from(e)}function VE(e){if(Array.isArray(e))return as(e)}function jd(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Te(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?jd(Object(r),!0).forEach(function(n){is(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):jd(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function is(e,t,r){return t=XE(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function XE(e){var t=YE(e,"string");return Nn(t)==="symbol"?t:String(t)}function YE(e,t){if(Nn(e)!=="object"||e===null)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Nn(n)!=="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function ZE(e,t){return eT(e)||QE(e,t)||fg(e,t)||JE()}function JE(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function fg(e,t){if(e){if(typeof e=="string")return as(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return as(e,t)}}function as(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function QE(e,t){var r=e==null?null:typeof Symbol!="undefined"&&e[Symbol.iterator]||e["@@iterator"];if(r!=null){var n,i,a,o,u=[],c=!0,s=!1;try{if(a=(r=r.call(e)).next,t!==0)for(;!(c=(n=a.call(r)).done)&&(u.push(n.value),u.length!==t);c=!0);}catch(f){s=!0,i=f}finally{try{if(!c&&r.return!=null&&(o=r.return(),Object(o)!==o))return}finally{if(s)throw i}}return u}}function eT(e){if(Array.isArray(e))return e}var Hi=function(t,r,n){return t+(r-t)*n},os=function(t){var r=t.from,n=t.to;return r!==n},tT=function e(t,r,n){var i=sn(function(a,o){if(os(o)){var u=t(o.from,o.to,o.velocity),c=ZE(u,2),s=c[0],f=c[1];return Te(Te({},o),{},{from:s,velocity:f})}return o},r);return n<1?sn(function(a,o){return os(o)?Te(Te({},o),{},{velocity:Hi(o.velocity,i[a].velocity,n),from:Hi(o.from,i[a].from,n)}):o},r):e(t,i,n-1)};const rT=function(e,t,r,n,i){var a=IE(e,t),o=a.reduce(function(p,b){return Te(Te({},p),{},is({},b,[e[b],t[b]]))},{}),u=a.reduce(function(p,b){return Te(Te({},p),{},is({},b,{from:e[b],velocity:0,to:t[b]}))},{}),c=-1,s,f,l=function(){return null},h=function(){return sn(function(b,w){return w.from},u)},d=function(){return!Object.values(u).filter(os).length},y=function(b){s||(s=b);var w=b-s,g=w/r.dt;u=tT(r,u,g),i(Te(Te(Te({},e),t),h())),s=b,d()||(c=requestAnimationFrame(l))},v=function(b){f||(f=b);var w=(b-f)/n,g=sn(function(m,x){return Hi.apply(void 0,Td(x).concat([r(w)]))},o);if(i(Te(Te(Te({},e),t),g)),w<1)c=requestAnimationFrame(l);else{var O=sn(function(m,x){return Hi.apply(void 0,Td(x).concat([r(1)]))},o);i(Te(Te(Te({},e),t),O))}};return l=r.isStepper?y:v,function(){return requestAnimationFrame(l),function(){cancelAnimationFrame(c)}}};function Pr(e){"@babel/helpers - typeof";return Pr=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Pr(e)}var nT=["children","begin","duration","attributeName","easing","isActive","steps","from","to","canBegin","onAnimationEnd","shouldReAnimate","onAnimationReStart"];function iT(e,t){if(e==null)return{};var r=aT(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function aT(e,t){if(e==null)return{};var r={},n=Object.keys(e),i,a;for(a=0;a<n.length;a++)i=n[a],!(t.indexOf(i)>=0)&&(r[i]=e[i]);return r}function zu(e){return sT(e)||cT(e)||uT(e)||oT()}function oT(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function uT(e,t){if(e){if(typeof e=="string")return us(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return us(e,t)}}function cT(e){if(typeof Symbol!="undefined"&&e[Symbol.iterator]!=null||e["@@iterator"]!=null)return Array.from(e)}function sT(e){if(Array.isArray(e))return us(e)}function us(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function $d(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Ze(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?$d(Object(r),!0).forEach(function(n){an(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):$d(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function an(e,t,r){return t=hg(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function lT(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function fT(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,hg(n.key),n)}}function hT(e,t,r){return t&&fT(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e}function hg(e){var t=pT(e,"string");return Pr(t)==="symbol"?t:String(t)}function pT(e,t){if(Pr(e)!=="object"||e===null)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Pr(n)!=="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function dT(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&cs(e,t)}function cs(e,t){return cs=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},cs(e,t)}function vT(e){var t=yT();return function(){var n=Ki(e),i;if(t){var a=Ki(this).constructor;i=Reflect.construct(n,arguments,a)}else i=n.apply(this,arguments);return ss(this,i)}}function ss(e,t){if(t&&(Pr(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return ls(e)}function ls(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function yT(){if(typeof Reflect=="undefined"||!Reflect.construct||Reflect.construct.sham)return!1;if(typeof Proxy=="function")return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch(e){return!1}}function Ki(e){return Ki=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},Ki(e)}var xt=function(e){dT(r,e);var t=vT(r);function r(n,i){var a;lT(this,r),a=t.call(this,n,i);var o=a.props,u=o.isActive,c=o.attributeName,s=o.from,f=o.to,l=o.steps,h=o.children,d=o.duration;if(a.handleStyleChange=a.handleStyleChange.bind(ls(a)),a.changeStyle=a.changeStyle.bind(ls(a)),!u||d<=0)return a.state={style:{}},typeof h=="function"&&(a.state={style:f}),ss(a);if(l&&l.length)a.state={style:l[0].style};else if(s){if(typeof h=="function")return a.state={style:s},ss(a);a.state={style:c?an({},c,s):s}}else a.state={style:{}};return a}return hT(r,[{key:"componentDidMount",value:function(){var i=this.props,a=i.isActive,o=i.canBegin;this.mounted=!0,!(!a||!o)&&this.runAnimation(this.props)}},{key:"componentDidUpdate",value:function(i){var a=this.props,o=a.isActive,u=a.canBegin,c=a.attributeName,s=a.shouldReAnimate,f=a.to,l=a.from,h=this.state.style;if(u){if(!o){var d={style:c?an({},c,f):f};this.state&&h&&(c&&h[c]!==f||!c&&h!==f)&&this.setState(d);return}if(!(OE(i.to,f)&&i.canBegin&&i.isActive)){var y=!i.canBegin||!i.isActive;this.manager&&this.manager.stop(),this.stopJSAnimation&&this.stopJSAnimation();var v=y||s?l:i.to;if(this.state&&h){var p={style:c?an({},c,v):v};(c&&h[c]!==v||!c&&h!==v)&&this.setState(p)}this.runAnimation(Ze(Ze({},this.props),{},{from:v,begin:0}))}}}},{key:"componentWillUnmount",value:function(){this.mounted=!1;var i=this.props.onAnimationEnd;this.unSubscribe&&this.unSubscribe(),this.manager&&(this.manager.stop(),this.manager=null),this.stopJSAnimation&&this.stopJSAnimation(),i&&i()}},{key:"handleStyleChange",value:function(i){this.changeStyle(i)}},{key:"changeStyle",value:function(i){this.mounted&&this.setState({style:i})}},{key:"runJSAnimation",value:function(i){var a=this,o=i.from,u=i.to,c=i.duration,s=i.easing,f=i.begin,l=i.onAnimationEnd,h=i.onAnimationStart,d=rT(o,u,HE(s),c,this.changeStyle),y=function(){a.stopJSAnimation=d()};this.manager.start([h,f,y,c,l])}},{key:"runStepAnimation",value:function(i){var a=this,o=i.steps,u=i.begin,c=i.onAnimationStart,s=o[0],f=s.style,l=s.duration,h=l===void 0?0:l,d=function(v,p,b){if(b===0)return v;var w=p.duration,g=p.easing,O=g===void 0?"ease":g,m=p.style,x=p.properties,S=p.onAnimationEnd,A=b>0?o[b-1]:p,_=x||Object.keys(m);if(typeof O=="function"||O==="spring")return[].concat(zu(v),[a.runJSAnimation.bind(a,{from:A.style,to:m,duration:w,easing:O}),w]);var $=Pd(_,w,O),E=Ze(Ze(Ze({},A.style),m),{},{transition:$});return[].concat(zu(v),[E,w,S]).filter(CE)};return this.manager.start([c].concat(zu(o.reduce(d,[f,Math.max(h,u)])),[i.onAnimationEnd]))}},{key:"runAnimation",value:function(i){this.manager||(this.manager=jE());var a=i.begin,o=i.duration,u=i.attributeName,c=i.to,s=i.easing,f=i.onAnimationStart,l=i.onAnimationEnd,h=i.steps,d=i.children,y=this.manager;if(this.unSubscribe=y.subscribe(this.handleStyleChange),typeof s=="function"||typeof d=="function"||s==="spring"){this.runJSAnimation(i);return}if(h.length>1){this.runStepAnimation(i);return}var v=u?an({},u,c):c,p=Pd(Object.keys(v),o,s);y.start([f,a,Ze(Ze({},v),{},{transition:p}),o,l])}},{key:"render",value:function(){var i=this.props,a=i.children;i.begin;var o=i.duration;i.attributeName,i.easing;var u=i.isActive;i.steps,i.from,i.to,i.canBegin,i.onAnimationEnd,i.shouldReAnimate,i.onAnimationReStart;var c=iT(i,nT),s=D.Children.count(a),f=this.state.style;if(typeof a=="function")return a(f);if(!u||s===0||o<=0)return a;var l=function(d){var y=d.props,v=y.style,p=v===void 0?{}:v,b=y.className,w=D.cloneElement(d,Ze(Ze({},c),{},{style:Ze(Ze({},p),f),className:b}));return w};return s===1?l(D.Children.only(a)):P.createElement("div",null,D.Children.map(a,function(h){return l(h)}))}}]),r}(D.PureComponent);xt.displayName="Animate";xt.defaultProps={begin:0,duration:1e3,from:"",to:"",attributeName:"",easing:"ease",isActive:!0,canBegin:!0,steps:[],onAnimationEnd:function(){},onAnimationStart:function(){}};xt.propTypes={from:ne.oneOfType([ne.object,ne.string]),to:ne.oneOfType([ne.object,ne.string]),attributeName:ne.string,duration:ne.number,begin:ne.number,easing:ne.oneOfType([ne.string,ne.func]),steps:ne.arrayOf(ne.shape({duration:ne.number.isRequired,style:ne.object.isRequired,easing:ne.oneOfType([ne.oneOf(["ease","ease-in","ease-out","ease-in-out","linear"]),ne.func]),properties:ne.arrayOf("string"),onAnimationEnd:ne.func})),children:ne.oneOfType([ne.node,ne.func]),isActive:ne.bool,canBegin:ne.bool,onAnimationEnd:ne.func,shouldReAnimate:ne.bool,onAnimationStart:ne.func,onAnimationReStart:ne.func};function Rn(e){"@babel/helpers - typeof";return Rn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Rn(e)}function Gi(){return Gi=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Gi.apply(this,arguments)}function mT(e,t){return wT(e)||xT(e,t)||bT(e,t)||gT()}function gT(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function bT(e,t){if(e){if(typeof e=="string")return Md(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Md(e,t)}}function Md(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function xT(e,t){var r=e==null?null:typeof Symbol!="undefined"&&e[Symbol.iterator]||e["@@iterator"];if(r!=null){var n,i,a,o,u=[],c=!0,s=!1;try{if(a=(r=r.call(e)).next,t!==0)for(;!(c=(n=a.call(r)).done)&&(u.push(n.value),u.length!==t);c=!0);}catch(f){s=!0,i=f}finally{try{if(!c&&r.return!=null&&(o=r.return(),Object(o)!==o))return}finally{if(s)throw i}}return u}}function wT(e){if(Array.isArray(e))return e}function Id(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Cd(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Id(Object(r),!0).forEach(function(n){OT(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Id(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function OT(e,t,r){return t=ST(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function ST(e){var t=AT(e,"string");return Rn(t)=="symbol"?t:t+""}function AT(e,t){if(Rn(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Rn(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var kd=function(t,r,n,i,a){var o=Math.min(Math.abs(n)/2,Math.abs(i)/2),u=i>=0?1:-1,c=n>=0?1:-1,s=i>=0&&n>=0||i<0&&n<0?1:0,f;if(o>0&&a instanceof Array){for(var l=[0,0,0,0],h=0,d=4;h<d;h++)l[h]=a[h]>o?o:a[h];f="M".concat(t,",").concat(r+u*l[0]),l[0]>0&&(f+="A ".concat(l[0],",").concat(l[0],",0,0,").concat(s,",").concat(t+c*l[0],",").concat(r)),f+="L ".concat(t+n-c*l[1],",").concat(r),l[1]>0&&(f+="A ".concat(l[1],",").concat(l[1],",0,0,").concat(s,`,
        `).concat(t+n,",").concat(r+u*l[1])),f+="L ".concat(t+n,",").concat(r+i-u*l[2]),l[2]>0&&(f+="A ".concat(l[2],",").concat(l[2],",0,0,").concat(s,`,
        `).concat(t+n-c*l[2],",").concat(r+i)),f+="L ".concat(t+c*l[3],",").concat(r+i),l[3]>0&&(f+="A ".concat(l[3],",").concat(l[3],",0,0,").concat(s,`,
        `).concat(t,",").concat(r+i-u*l[3])),f+="Z"}else if(o>0&&a===+a&&a>0){var y=Math.min(o,a);f="M ".concat(t,",").concat(r+u*y,`
            A `).concat(y,",").concat(y,",0,0,").concat(s,",").concat(t+c*y,",").concat(r,`
            L `).concat(t+n-c*y,",").concat(r,`
            A `).concat(y,",").concat(y,",0,0,").concat(s,",").concat(t+n,",").concat(r+u*y,`
            L `).concat(t+n,",").concat(r+i-u*y,`
            A `).concat(y,",").concat(y,",0,0,").concat(s,",").concat(t+n-c*y,",").concat(r+i,`
            L `).concat(t+c*y,",").concat(r+i,`
            A `).concat(y,",").concat(y,",0,0,").concat(s,",").concat(t,",").concat(r+i-u*y," Z")}else f="M ".concat(t,",").concat(r," h ").concat(n," v ").concat(i," h ").concat(-n," Z");return f},PT=function(t,r){if(!t||!r)return!1;var n=t.x,i=t.y,a=r.x,o=r.y,u=r.width,c=r.height;if(Math.abs(u)>0&&Math.abs(c)>0){var s=Math.min(a,a+u),f=Math.max(a,a+u),l=Math.min(o,o+c),h=Math.max(o,o+c);return n>=s&&n<=f&&i>=l&&i<=h}return!1},_T={x:0,y:0,width:0,height:0,radius:0,isAnimationActive:!1,isUpdateAnimationActive:!1,animationBegin:0,animationDuration:1500,animationEasing:"ease"},wl=function(t){var r=Cd(Cd({},_T),t),n=D.useRef(),i=D.useState(-1),a=mT(i,2),o=a[0],u=a[1];D.useEffect(function(){if(n.current&&n.current.getTotalLength)try{var O=n.current.getTotalLength();O&&u(O)}catch(m){}},[]);var c=r.x,s=r.y,f=r.width,l=r.height,h=r.radius,d=r.className,y=r.animationEasing,v=r.animationDuration,p=r.animationBegin,b=r.isAnimationActive,w=r.isUpdateAnimationActive;if(c!==+c||s!==+s||f!==+f||l!==+l||f===0||l===0)return null;var g=Q("recharts-rectangle",d);return w?P.createElement(xt,{canBegin:o>0,from:{width:f,height:l,x:c,y:s},to:{width:f,height:l,x:c,y:s},duration:v,animationEasing:y,isActive:w},function(O){var m=O.width,x=O.height,S=O.x,A=O.y;return P.createElement(xt,{canBegin:o>0,from:"0px ".concat(o===-1?1:o,"px"),to:"".concat(o,"px 0px"),attributeName:"strokeDasharray",begin:p,duration:v,isActive:b,easing:y},P.createElement("path",Gi({},V(r,!0),{className:g,d:kd(S,A,m,x,h),ref:n})))}):P.createElement("path",Gi({},V(r,!0),{className:g,d:kd(c,s,f,l,h)}))},ET=["points","className","baseLinePoints","connectNulls"];function cr(){return cr=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},cr.apply(this,arguments)}function TT(e,t){if(e==null)return{};var r=jT(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function jT(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}function Nd(e){return CT(e)||IT(e)||MT(e)||$T()}function $T(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function MT(e,t){if(e){if(typeof e=="string")return fs(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return fs(e,t)}}function IT(e){if(typeof Symbol!="undefined"&&e[Symbol.iterator]!=null||e["@@iterator"]!=null)return Array.from(e)}function CT(e){if(Array.isArray(e))return fs(e)}function fs(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}var Rd=function(t){return t&&t.x===+t.x&&t.y===+t.y},kT=function(){var t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:[],r=[[]];return t.forEach(function(n){Rd(n)?r[r.length-1].push(n):r[r.length-1].length>0&&r.push([])}),Rd(t[0])&&r[r.length-1].push(t[0]),r[r.length-1].length<=0&&(r=r.slice(0,-1)),r},ln=function(t,r){var n=kT(t);r&&(n=[n.reduce(function(a,o){return[].concat(Nd(a),Nd(o))},[])]);var i=n.map(function(a){return a.reduce(function(o,u,c){return"".concat(o).concat(c===0?"M":"L").concat(u.x,",").concat(u.y)},"")}).join("");return n.length===1?"".concat(i,"Z"):i},NT=function(t,r,n){var i=ln(t,n);return"".concat(i.slice(-1)==="Z"?i.slice(0,-1):i,"L").concat(ln(r.reverse(),n).slice(1))},RT=function(t){var r=t.points,n=t.className,i=t.baseLinePoints,a=t.connectNulls,o=TT(t,ET);if(!r||!r.length)return null;var u=Q("recharts-polygon",n);if(i&&i.length){var c=o.stroke&&o.stroke!=="none",s=NT(r,i,a);return P.createElement("g",{className:u},P.createElement("path",cr({},V(o,!0),{fill:s.slice(-1)==="Z"?o.fill:"none",stroke:"none",d:s})),c?P.createElement("path",cr({},V(o,!0),{fill:"none",d:ln(r,a)})):null,c?P.createElement("path",cr({},V(o,!0),{fill:"none",d:ln(i,a)})):null)}var f=ln(r,a);return P.createElement("path",cr({},V(o,!0),{fill:f.slice(-1)==="Z"?o.fill:"none",className:u,d:f}))};function hs(){return hs=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},hs.apply(this,arguments)}var Ol=function(t){var r=t.cx,n=t.cy,i=t.r,a=t.className,o=Q("recharts-dot",a);return r===+r&&n===+n&&i===+i?D.createElement("circle",hs({},V(t,!1),mi(t),{className:o,cx:r,cy:n,r:i})):null};function Dn(e){"@babel/helpers - typeof";return Dn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Dn(e)}var DT=["x","y","top","left","width","height","className"];function ps(){return ps=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},ps.apply(this,arguments)}function Dd(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function BT(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Dd(Object(r),!0).forEach(function(n){LT(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Dd(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function LT(e,t,r){return t=qT(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function qT(e){var t=FT(e,"string");return Dn(t)=="symbol"?t:t+""}function FT(e,t){if(Dn(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Dn(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function WT(e,t){if(e==null)return{};var r=zT(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function zT(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}var UT=function(t,r,n,i,a,o){return"M".concat(t,",").concat(a,"v").concat(i,"M").concat(o,",").concat(r,"h").concat(n)},HT=function(t){var r=t.x,n=r===void 0?0:r,i=t.y,a=i===void 0?0:i,o=t.top,u=o===void 0?0:o,c=t.left,s=c===void 0?0:c,f=t.width,l=f===void 0?0:f,h=t.height,d=h===void 0?0:h,y=t.className,v=WT(t,DT),p=BT({x:n,y:a,top:u,left:s,width:l,height:d},v);return!B(n)||!B(a)||!B(l)||!B(d)||!B(u)||!B(s)?null:P.createElement("path",ps({},V(p,!0),{className:Q("recharts-cross",y),d:UT(n,a,l,d,u,s)}))},Uu,Bd;function KT(){if(Bd)return Uu;Bd=1;var e=Ea(),t=Em(),r=ut();function n(i,a){return i&&i.length?e(i,r(a,2),t):void 0}return Uu=n,Uu}var GT=KT();const VT=oe(GT);var Hu,Ld;function XT(){if(Ld)return Hu;Ld=1;var e=Ea(),t=ut(),r=Tm();function n(i,a){return i&&i.length?e(i,t(a,2),r):void 0}return Hu=n,Hu}var YT=XT();const ZT=oe(YT);var JT=["cx","cy","angle","ticks","axisLine"],QT=["ticks","tick","angle","tickFormatter","stroke"];function _r(e){"@babel/helpers - typeof";return _r=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},_r(e)}function fn(){return fn=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},fn.apply(this,arguments)}function qd(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Dt(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?qd(Object(r),!0).forEach(function(n){ka(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):qd(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function Fd(e,t){if(e==null)return{};var r=ej(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function ej(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}function tj(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function Wd(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,dg(n.key),n)}}function rj(e,t,r){return t&&Wd(e.prototype,t),r&&Wd(e,r),Object.defineProperty(e,"prototype",{writable:!1}),e}function nj(e,t,r){return t=Vi(t),ij(e,pg()?Reflect.construct(t,r||[],Vi(e).constructor):t.apply(e,r))}function ij(e,t){if(t&&(_r(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return aj(e)}function aj(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function pg(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(pg=function(){return!!e})()}function Vi(e){return Vi=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},Vi(e)}function oj(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&ds(e,t)}function ds(e,t){return ds=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},ds(e,t)}function ka(e,t,r){return t=dg(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function dg(e){var t=uj(e,"string");return _r(t)=="symbol"?t:t+""}function uj(e,t){if(_r(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(_r(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}var Na=function(e){function t(){return tj(this,t),nj(this,t,arguments)}return oj(t,e),rj(t,[{key:"getTickValueCoord",value:function(n){var i=n.coordinate,a=this.props,o=a.angle,u=a.cx,c=a.cy;return le(u,c,i,o)}},{key:"getTickTextAnchor",value:function(){var n=this.props.orientation,i;switch(n){case"left":i="end";break;case"right":i="start";break;default:i="middle";break}return i}},{key:"getViewBox",value:function(){var n=this.props,i=n.cx,a=n.cy,o=n.angle,u=n.ticks,c=VT(u,function(f){return f.coordinate||0}),s=ZT(u,function(f){return f.coordinate||0});return{cx:i,cy:a,startAngle:o,endAngle:o,innerRadius:s.coordinate||0,outerRadius:c.coordinate||0}}},{key:"renderAxisLine",value:function(){var n=this.props,i=n.cx,a=n.cy,o=n.angle,u=n.ticks,c=n.axisLine,s=Fd(n,JT),f=u.reduce(function(y,v){return[Math.min(y[0],v.coordinate),Math.max(y[1],v.coordinate)]},[1/0,-1/0]),l=le(i,a,f[0],o),h=le(i,a,f[1],o),d=Dt(Dt(Dt({},V(s,!1)),{},{fill:"none"},V(c,!1)),{},{x1:l.x,y1:l.y,x2:h.x,y2:h.y});return P.createElement("line",fn({className:"recharts-polar-radius-axis-line"},d))}},{key:"renderTicks",value:function(){var n=this,i=this.props,a=i.ticks,o=i.tick,u=i.angle,c=i.tickFormatter,s=i.stroke,f=Fd(i,QT),l=this.getTickTextAnchor(),h=V(f,!1),d=V(o,!1),y=a.map(function(v,p){var b=n.getTickValueCoord(v),w=Dt(Dt(Dt(Dt({textAnchor:l,transform:"rotate(".concat(90-u,", ").concat(b.x,", ").concat(b.y,")")},h),{},{stroke:"none",fill:s},d),{},{index:p},b),{},{payload:v});return P.createElement(ie,fn({className:Q("recharts-polar-radius-axis-tick",rg(o)),key:"tick-".concat(v.coordinate)},Xt(n.props,v,p)),t.renderTickItem(o,w,c?c(v.value,p):v.value))});return P.createElement(ie,{className:"recharts-polar-radius-axis-ticks"},y)}},{key:"render",value:function(){var n=this.props,i=n.ticks,a=n.axisLine,o=n.tick;return!i||!i.length?null:P.createElement(ie,{className:Q("recharts-polar-radius-axis",this.props.className)},a&&this.renderAxisLine(),o&&this.renderTicks(),_e.renderCallByParent(this.props,this.getViewBox()))}}],[{key:"renderTickItem",value:function(n,i,a){var o;return P.isValidElement(n)?o=P.cloneElement(n,i):X(n)?o=n(i):o=P.createElement(Yt,fn({},i,{className:"recharts-polar-radius-axis-tick-value"}),a),o}}])}(D.PureComponent);ka(Na,"displayName","PolarRadiusAxis");ka(Na,"axisType","radiusAxis");ka(Na,"defaultProps",{type:"number",radiusAxisId:0,cx:0,cy:0,angle:0,orientation:"right",stroke:"#ccc",axisLine:!0,tick:!0,tickCount:5,allowDataOverflow:!1,scale:"auto",allowDuplicatedCategory:!0});function Er(e){"@babel/helpers - typeof";return Er=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Er(e)}function Ft(){return Ft=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Ft.apply(this,arguments)}function zd(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Bt(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?zd(Object(r),!0).forEach(function(n){Ra(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):zd(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function cj(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function Ud(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,yg(n.key),n)}}function sj(e,t,r){return t&&Ud(e.prototype,t),r&&Ud(e,r),Object.defineProperty(e,"prototype",{writable:!1}),e}function lj(e,t,r){return t=Xi(t),fj(e,vg()?Reflect.construct(t,r||[],Xi(e).constructor):t.apply(e,r))}function fj(e,t){if(t&&(Er(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return hj(e)}function hj(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function vg(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(vg=function(){return!!e})()}function Xi(e){return Xi=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},Xi(e)}function pj(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&vs(e,t)}function vs(e,t){return vs=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},vs(e,t)}function Ra(e,t,r){return t=yg(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function yg(e){var t=dj(e,"string");return Er(t)=="symbol"?t:t+""}function dj(e,t){if(Er(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Er(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}var vj=Math.PI/180,Hd=1e-5,Da=function(e){function t(){return cj(this,t),lj(this,t,arguments)}return pj(t,e),sj(t,[{key:"getTickLineCoord",value:function(n){var i=this.props,a=i.cx,o=i.cy,u=i.radius,c=i.orientation,s=i.tickSize,f=s||8,l=le(a,o,u,n.coordinate),h=le(a,o,u+(c==="inner"?-1:1)*f,n.coordinate);return{x1:l.x,y1:l.y,x2:h.x,y2:h.y}}},{key:"getTickTextAnchor",value:function(n){var i=this.props.orientation,a=Math.cos(-n.coordinate*vj),o;return a>Hd?o=i==="outer"?"start":"end":a<-Hd?o=i==="outer"?"end":"start":o="middle",o}},{key:"renderAxisLine",value:function(){var n=this.props,i=n.cx,a=n.cy,o=n.radius,u=n.axisLine,c=n.axisLineType,s=Bt(Bt({},V(this.props,!1)),{},{fill:"none"},V(u,!1));if(c==="circle")return P.createElement(Ol,Ft({className:"recharts-polar-angle-axis-line"},s,{cx:i,cy:a,r:o}));var f=this.props.ticks,l=f.map(function(h){return le(i,a,o,h.coordinate)});return P.createElement(RT,Ft({className:"recharts-polar-angle-axis-line"},s,{points:l}))}},{key:"renderTicks",value:function(){var n=this,i=this.props,a=i.ticks,o=i.tick,u=i.tickLine,c=i.tickFormatter,s=i.stroke,f=V(this.props,!1),l=V(o,!1),h=Bt(Bt({},f),{},{fill:"none"},V(u,!1)),d=a.map(function(y,v){var p=n.getTickLineCoord(y),b=n.getTickTextAnchor(y),w=Bt(Bt(Bt({textAnchor:b},f),{},{stroke:"none",fill:s},l),{},{index:v,payload:y,x:p.x2,y:p.y2});return P.createElement(ie,Ft({className:Q("recharts-polar-angle-axis-tick",rg(o)),key:"tick-".concat(y.coordinate)},Xt(n.props,y,v)),u&&P.createElement("line",Ft({className:"recharts-polar-angle-axis-tick-line"},h,p)),o&&t.renderTickItem(o,w,c?c(y.value,v):y.value))});return P.createElement(ie,{className:"recharts-polar-angle-axis-ticks"},d)}},{key:"render",value:function(){var n=this.props,i=n.ticks,a=n.radius,o=n.axisLine;return a<=0||!i||!i.length?null:P.createElement(ie,{className:Q("recharts-polar-angle-axis",this.props.className)},o&&this.renderAxisLine(),this.renderTicks())}}],[{key:"renderTickItem",value:function(n,i,a){var o;return P.isValidElement(n)?o=P.cloneElement(n,i):X(n)?o=n(i):o=P.createElement(Yt,Ft({},i,{className:"recharts-polar-angle-axis-tick-value"}),a),o}}])}(D.PureComponent);Ra(Da,"displayName","PolarAngleAxis");Ra(Da,"axisType","angleAxis");Ra(Da,"defaultProps",{type:"category",angleAxisId:0,scale:"auto",cx:0,cy:0,orientation:"outer",axisLine:!0,tickLine:!0,tickSize:8,tick:!0,hide:!1,allowDuplicatedCategory:!0});var Ku,Kd;function yj(){if(Kd)return Ku;Kd=1;var e=wb(),t=e(Object.getPrototypeOf,Object);return Ku=t,Ku}var Gu,Gd;function mj(){if(Gd)return Gu;Gd=1;var e=Gn(),t=yj(),r=Vn(),n="[object Object]",i=Function.prototype,a=Object.prototype,o=i.toString,u=a.hasOwnProperty,c=o.call(Object);function s(f){if(!r(f)||e(f)!=n)return!1;var l=t(f);if(l===null)return!0;var h=u.call(l,"constructor")&&l.constructor;return typeof h=="function"&&h instanceof h&&o.call(h)==c}return Gu=s,Gu}var gj=mj();const bj=oe(gj);var Vu,Vd;function xj(){if(Vd)return Vu;Vd=1;var e=Gn(),t=Vn(),r="[object Boolean]";function n(i){return i===!0||i===!1||t(i)&&e(i)==r}return Vu=n,Vu}var wj=xj();const Oj=oe(wj);function Bn(e){"@babel/helpers - typeof";return Bn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Bn(e)}function Yi(){return Yi=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Yi.apply(this,arguments)}function Sj(e,t){return Ej(e)||_j(e,t)||Pj(e,t)||Aj()}function Aj(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function Pj(e,t){if(e){if(typeof e=="string")return Xd(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Xd(e,t)}}function Xd(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function _j(e,t){var r=e==null?null:typeof Symbol!="undefined"&&e[Symbol.iterator]||e["@@iterator"];if(r!=null){var n,i,a,o,u=[],c=!0,s=!1;try{if(a=(r=r.call(e)).next,t!==0)for(;!(c=(n=a.call(r)).done)&&(u.push(n.value),u.length!==t);c=!0);}catch(f){s=!0,i=f}finally{try{if(!c&&r.return!=null&&(o=r.return(),Object(o)!==o))return}finally{if(s)throw i}}return u}}function Ej(e){if(Array.isArray(e))return e}function Yd(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Zd(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Yd(Object(r),!0).forEach(function(n){Tj(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Yd(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function Tj(e,t,r){return t=jj(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function jj(e){var t=$j(e,"string");return Bn(t)=="symbol"?t:t+""}function $j(e,t){if(Bn(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Bn(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var Jd=function(t,r,n,i,a){var o=n-i,u;return u="M ".concat(t,",").concat(r),u+="L ".concat(t+n,",").concat(r),u+="L ".concat(t+n-o/2,",").concat(r+a),u+="L ".concat(t+n-o/2-i,",").concat(r+a),u+="L ".concat(t,",").concat(r," Z"),u},Mj={x:0,y:0,upperWidth:0,lowerWidth:0,height:0,isUpdateAnimationActive:!1,animationBegin:0,animationDuration:1500,animationEasing:"ease"},Ij=function(t){var r=Zd(Zd({},Mj),t),n=D.useRef(),i=D.useState(-1),a=Sj(i,2),o=a[0],u=a[1];D.useEffect(function(){if(n.current&&n.current.getTotalLength)try{var g=n.current.getTotalLength();g&&u(g)}catch(O){}},[]);var c=r.x,s=r.y,f=r.upperWidth,l=r.lowerWidth,h=r.height,d=r.className,y=r.animationEasing,v=r.animationDuration,p=r.animationBegin,b=r.isUpdateAnimationActive;if(c!==+c||s!==+s||f!==+f||l!==+l||h!==+h||f===0&&l===0||h===0)return null;var w=Q("recharts-trapezoid",d);return b?P.createElement(xt,{canBegin:o>0,from:{upperWidth:0,lowerWidth:0,height:h,x:c,y:s},to:{upperWidth:f,lowerWidth:l,height:h,x:c,y:s},duration:v,animationEasing:y,isActive:b},function(g){var O=g.upperWidth,m=g.lowerWidth,x=g.height,S=g.x,A=g.y;return P.createElement(xt,{canBegin:o>0,from:"0px ".concat(o===-1?1:o,"px"),to:"".concat(o,"px 0px"),attributeName:"strokeDasharray",begin:p,duration:v,easing:y},P.createElement("path",Yi({},V(r,!0),{className:w,d:Jd(S,A,O,m,x),ref:n})))}):P.createElement("g",null,P.createElement("path",Yi({},V(r,!0),{className:w,d:Jd(c,s,f,l,h)})))},Cj=["option","shapeType","propTransformer","activeClassName","isActive"];function Ln(e){"@babel/helpers - typeof";return Ln=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Ln(e)}function kj(e,t){if(e==null)return{};var r=Nj(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function Nj(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}function Qd(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Zi(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Qd(Object(r),!0).forEach(function(n){Rj(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Qd(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function Rj(e,t,r){return t=Dj(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function Dj(e){var t=Bj(e,"string");return Ln(t)=="symbol"?t:t+""}function Bj(e,t){if(Ln(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Ln(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function Lj(e,t){return Zi(Zi({},t),e)}function qj(e,t){return e==="symbols"}function ev(e){var t=e.shapeType,r=e.elementProps;switch(t){case"rectangle":return P.createElement(wl,r);case"trapezoid":return P.createElement(Ij,r);case"sector":return P.createElement(ag,r);case"symbols":if(qj(t))return P.createElement(Us,r);break;default:return null}}function Fj(e){return D.isValidElement(e)?e.props:e}function mg(e){var t=e.option,r=e.shapeType,n=e.propTransformer,i=n===void 0?Lj:n,a=e.activeClassName,o=a===void 0?"recharts-active-shape":a,u=e.isActive,c=kj(e,Cj),s;if(D.isValidElement(t))s=D.cloneElement(t,Zi(Zi({},c),Fj(t)));else if(X(t))s=t(c);else if(bj(t)&&!Oj(t)){var f=i(t,c);s=P.createElement(ev,{shapeType:r,elementProps:f})}else{var l=c;s=P.createElement(ev,{shapeType:r,elementProps:l})}return u?P.createElement(ie,{className:o},s):s}function Ba(e,t){return t!=null&&"trapezoids"in e.props}function La(e,t){return t!=null&&"sectors"in e.props}function qn(e,t){return t!=null&&"points"in e.props}function Wj(e,t){var r,n,i=e.x===(t==null||(r=t.labelViewBox)===null||r===void 0?void 0:r.x)||e.x===t.x,a=e.y===(t==null||(n=t.labelViewBox)===null||n===void 0?void 0:n.y)||e.y===t.y;return i&&a}function zj(e,t){var r=e.endAngle===t.endAngle,n=e.startAngle===t.startAngle;return r&&n}function Uj(e,t){var r=e.x===t.x,n=e.y===t.y,i=e.z===t.z;return r&&n&&i}function Hj(e,t){var r;return Ba(e,t)?r=Wj:La(e,t)?r=zj:qn(e,t)&&(r=Uj),r}function Kj(e,t){var r;return Ba(e,t)?r="trapezoids":La(e,t)?r="sectors":qn(e,t)&&(r="points"),r}function Gj(e,t){if(Ba(e,t)){var r;return(r=t.tooltipPayload)===null||r===void 0||(r=r[0])===null||r===void 0||(r=r.payload)===null||r===void 0?void 0:r.payload}if(La(e,t)){var n;return(n=t.tooltipPayload)===null||n===void 0||(n=n[0])===null||n===void 0||(n=n.payload)===null||n===void 0?void 0:n.payload}return qn(e,t)?t.payload:{}}function Vj(e){var t=e.activeTooltipItem,r=e.graphicalItem,n=e.itemData,i=Kj(r,t),a=Gj(r,t),o=n.filter(function(c,s){var f=$a(a,c),l=r.props[i].filter(function(y){var v=Hj(r,t);return v(y,t)}),h=r.props[i].indexOf(l[l.length-1]),d=s===h;return f&&d}),u=n.indexOf(o[o.length-1]);return u}var vi;function Tr(e){"@babel/helpers - typeof";return Tr=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Tr(e)}function sr(){return sr=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},sr.apply(this,arguments)}function tv(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function ce(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?tv(Object(r),!0).forEach(function(n){Ke(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):tv(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function Xj(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function rv(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,bg(n.key),n)}}function Yj(e,t,r){return t&&rv(e.prototype,t),r&&rv(e,r),Object.defineProperty(e,"prototype",{writable:!1}),e}function Zj(e,t,r){return t=Ji(t),Jj(e,gg()?Reflect.construct(t,r||[],Ji(e).constructor):t.apply(e,r))}function Jj(e,t){if(t&&(Tr(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return Qj(e)}function Qj(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function gg(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(gg=function(){return!!e})()}function Ji(e){return Ji=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},Ji(e)}function e$(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&ys(e,t)}function ys(e,t){return ys=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},ys(e,t)}function Ke(e,t,r){return t=bg(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function bg(e){var t=t$(e,"string");return Tr(t)=="symbol"?t:t+""}function t$(e,t){if(Tr(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Tr(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}var It=function(e){function t(r){var n;return Xj(this,t),n=Zj(this,t,[r]),Ke(n,"pieRef",null),Ke(n,"sectorRefs",[]),Ke(n,"id",Yn("recharts-pie-")),Ke(n,"handleAnimationEnd",function(){var i=n.props.onAnimationEnd;n.setState({isAnimationFinished:!0}),X(i)&&i()}),Ke(n,"handleAnimationStart",function(){var i=n.props.onAnimationStart;n.setState({isAnimationFinished:!1}),X(i)&&i()}),n.state={isAnimationFinished:!r.isAnimationActive,prevIsAnimationActive:r.isAnimationActive,prevAnimationId:r.animationId,sectorToFocus:0},n}return e$(t,e),Yj(t,[{key:"isActiveIndex",value:function(n){var i=this.props.activeIndex;return Array.isArray(i)?i.indexOf(n)!==-1:n===i}},{key:"hasActiveIndex",value:function(){var n=this.props.activeIndex;return Array.isArray(n)?n.length!==0:n||n===0}},{key:"renderLabels",value:function(n){var i=this.props.isAnimationActive;if(i&&!this.state.isAnimationFinished)return null;var a=this.props,o=a.label,u=a.labelLine,c=a.dataKey,s=a.valueKey,f=V(this.props,!1),l=V(o,!1),h=V(u,!1),d=o&&o.offsetRadius||20,y=n.map(function(v,p){var b=(v.startAngle+v.endAngle)/2,w=le(v.cx,v.cy,v.outerRadius+d,b),g=ce(ce(ce(ce({},f),v),{},{stroke:"none"},l),{},{index:p,textAnchor:t.getTextAnchor(w.x,v.cx)},w),O=ce(ce(ce(ce({},f),v),{},{fill:"none",stroke:v.fill},h),{},{index:p,points:[le(v.cx,v.cy,v.outerRadius,b),w]}),m=c;return Z(c)&&Z(s)?m="value":Z(c)&&(m=s),P.createElement(ie,{key:"label-".concat(v.startAngle,"-").concat(v.endAngle,"-").concat(v.midAngle,"-").concat(p)},u&&t.renderLabelLineItem(u,O,"line"),t.renderLabelItem(o,g,je(v,m)))});return P.createElement(ie,{className:"recharts-pie-labels"},y)}},{key:"renderSectorsStatically",value:function(n){var i=this,a=this.props,o=a.activeShape,u=a.blendStroke,c=a.inactiveShape;return n.map(function(s,f){if((s==null?void 0:s.startAngle)===0&&(s==null?void 0:s.endAngle)===0&&n.length!==1)return null;var l=i.isActiveIndex(f),h=c&&i.hasActiveIndex()?c:null,d=l?o:h,y=ce(ce({},s),{},{stroke:u?s.fill:s.stroke,tabIndex:-1});return P.createElement(ie,sr({ref:function(p){p&&!i.sectorRefs.includes(p)&&i.sectorRefs.push(p)},tabIndex:-1,className:"recharts-pie-sector"},Xt(i.props,s,f),{key:"sector-".concat(s==null?void 0:s.startAngle,"-").concat(s==null?void 0:s.endAngle,"-").concat(s.midAngle,"-").concat(f)}),P.createElement(mg,sr({option:d,isActive:l,shapeType:"sector"},y)))})}},{key:"renderSectorsWithAnimation",value:function(){var n=this,i=this.props,a=i.sectors,o=i.isAnimationActive,u=i.animationBegin,c=i.animationDuration,s=i.animationEasing,f=i.animationId,l=this.state,h=l.prevSectors,d=l.prevIsAnimationActive;return P.createElement(xt,{begin:u,duration:c,isActive:o,easing:s,from:{t:0},to:{t:1},key:"pie-".concat(f,"-").concat(d),onAnimationStart:this.handleAnimationStart,onAnimationEnd:this.handleAnimationEnd},function(y){var v=y.t,p=[],b=a&&a[0],w=b.startAngle;return a.forEach(function(g,O){var m=h&&h[O],x=O>0?We(g,"paddingAngle",0):0;if(m){var S=St(m.endAngle-m.startAngle,g.endAngle-g.startAngle),A=ce(ce({},g),{},{startAngle:w+x,endAngle:w+S(v)+x});p.push(A),w=A.endAngle}else{var _=g.endAngle,$=g.startAngle,E=St(0,_-$),T=E(v),j=ce(ce({},g),{},{startAngle:w+x,endAngle:w+T+x});p.push(j),w=j.endAngle}}),P.createElement(ie,null,n.renderSectorsStatically(p))})}},{key:"attachKeyboardHandlers",value:function(n){var i=this;n.onkeydown=function(a){if(!a.altKey)switch(a.key){case"ArrowLeft":{var o=++i.state.sectorToFocus%i.sectorRefs.length;i.sectorRefs[o].focus(),i.setState({sectorToFocus:o});break}case"ArrowRight":{var u=--i.state.sectorToFocus<0?i.sectorRefs.length-1:i.state.sectorToFocus%i.sectorRefs.length;i.sectorRefs[u].focus(),i.setState({sectorToFocus:u});break}case"Escape":{i.sectorRefs[i.state.sectorToFocus].blur(),i.setState({sectorToFocus:0});break}}}}},{key:"renderSectors",value:function(){var n=this.props,i=n.sectors,a=n.isAnimationActive,o=this.state.prevSectors;return a&&i&&i.length&&(!o||!$a(o,i))?this.renderSectorsWithAnimation():this.renderSectorsStatically(i)}},{key:"componentDidMount",value:function(){this.pieRef&&this.attachKeyboardHandlers(this.pieRef)}},{key:"render",value:function(){var n=this,i=this.props,a=i.hide,o=i.sectors,u=i.className,c=i.label,s=i.cx,f=i.cy,l=i.innerRadius,h=i.outerRadius,d=i.isAnimationActive,y=this.state.isAnimationFinished;if(a||!o||!o.length||!B(s)||!B(f)||!B(l)||!B(h))return null;var v=Q("recharts-pie",u);return P.createElement(ie,{tabIndex:this.props.rootTabIndex,className:v,ref:function(b){n.pieRef=b}},this.renderSectors(),c&&this.renderLabels(o),_e.renderCallByParent(this.props,null,!1),(!d||y)&&Tt.renderCallByParent(this.props,o,!1))}}],[{key:"getDerivedStateFromProps",value:function(n,i){return i.prevIsAnimationActive!==n.isAnimationActive?{prevIsAnimationActive:n.isAnimationActive,prevAnimationId:n.animationId,curSectors:n.sectors,prevSectors:[],isAnimationFinished:!0}:n.isAnimationActive&&n.animationId!==i.prevAnimationId?{prevAnimationId:n.animationId,curSectors:n.sectors,prevSectors:i.curSectors,isAnimationFinished:!0}:n.sectors!==i.curSectors?{curSectors:n.sectors,isAnimationFinished:!0}:null}},{key:"getTextAnchor",value:function(n,i){return n>i?"start":n<i?"end":"middle"}},{key:"renderLabelLineItem",value:function(n,i,a){if(P.isValidElement(n))return P.cloneElement(n,i);if(X(n))return n(i);var o=Q("recharts-pie-label-line",typeof n!="boolean"?n.className:"");return P.createElement(ts,sr({},i,{key:a,type:"linear",className:o}))}},{key:"renderLabelItem",value:function(n,i,a){if(P.isValidElement(n))return P.cloneElement(n,i);var o=a;if(X(n)&&(o=n(i),P.isValidElement(o)))return o;var u=Q("recharts-pie-label-text",typeof n!="boolean"&&!X(n)?n.className:"");return P.createElement(Yt,sr({},i,{alignmentBaseline:"middle",className:u}),o)}}])}(D.PureComponent);vi=It;Ke(It,"displayName","Pie");Ke(It,"defaultProps",{stroke:"#fff",fill:"#808080",legendType:"rect",cx:"50%",cy:"50%",startAngle:0,endAngle:360,innerRadius:0,outerRadius:"80%",paddingAngle:0,labelLine:!0,hide:!1,minAngle:0,isAnimationActive:!vt.isSsr,animationBegin:400,animationDuration:1500,animationEasing:"ease",nameKey:"name",blendStroke:!1,rootTabIndex:0});Ke(It,"parseDeltaAngle",function(e,t){var r=Ie(t-e),n=Math.min(Math.abs(t-e),360);return r*n});Ke(It,"getRealPieData",function(e){var t=e.data,r=e.children,n=V(e,!1),i=Ve(r,Xs);return t&&t.length?t.map(function(a,o){return ce(ce(ce({payload:a},n),a),i&&i[o]&&i[o].props)}):i&&i.length?i.map(function(a){return ce(ce({},n),a.props)}):[]});Ke(It,"parseCoordinateOfPie",function(e,t){var r=t.top,n=t.left,i=t.width,a=t.height,o=tg(i,a),u=n+Ce(e.cx,i,i/2),c=r+Ce(e.cy,a,a/2),s=Ce(e.innerRadius,o,0),f=Ce(e.outerRadius,o,o*.8),l=e.maxRadius||Math.sqrt(i*i+a*a)/2;return{cx:u,cy:c,innerRadius:s,outerRadius:f,maxRadius:l}});Ke(It,"getComposedData",function(e){var t=e.item,r=e.offset,n=t.type.defaultProps!==void 0?ce(ce({},t.type.defaultProps),t.props):t.props,i=vi.getRealPieData(n);if(!i||!i.length)return null;var a=n.cornerRadius,o=n.startAngle,u=n.endAngle,c=n.paddingAngle,s=n.dataKey,f=n.nameKey,l=n.valueKey,h=n.tooltipType,d=Math.abs(n.minAngle),y=vi.parseCoordinateOfPie(n,r),v=vi.parseDeltaAngle(o,u),p=Math.abs(v),b=s;Z(s)&&Z(l)?(et(!1,`Use "dataKey" to specify the value of pie,
      the props "valueKey" will be deprecated in 1.1.0`),b="value"):Z(s)&&(et(!1,`Use "dataKey" to specify the value of pie,
      the props "valueKey" will be deprecated in 1.1.0`),b=l);var w=i.filter(function(A){return je(A,b,0)!==0}).length,g=(p>=360?w:w-1)*c,O=p-w*d-g,m=i.reduce(function(A,_){var $=je(_,b,0);return A+(B($)?$:0)},0),x;if(m>0){var S;x=i.map(function(A,_){var $=je(A,b,0),E=je(A,f,_),T=(B($)?$:0)/m,j;_?j=S.endAngle+Ie(v)*c*($!==0?1:0):j=o;var C=j+Ie(v)*(($!==0?d:0)+T*O),I=(j+C)/2,k=(y.innerRadius+y.outerRadius)/2,N=[{name:E,value:$,payload:A,dataKey:b,type:h}],L=le(y.cx,y.cy,k,I);return S=ce(ce(ce({percent:T,cornerRadius:a,name:E,tooltipPayload:N,midAngle:I,middleRadius:k,tooltipPosition:L},A),y),{},{value:je(A,b),startAngle:j,endAngle:C,payload:A,paddingAngle:Ie(v)*c}),S})}return ce(ce({},y),{},{sectors:x,data:i})});var Xu,nv;function r$(){if(nv)return Xu;nv=1;var e=Math.ceil,t=Math.max;function r(n,i,a,o){for(var u=-1,c=t(e((i-n)/(a||1)),0),s=Array(c);c--;)s[o?c:++u]=n,n+=a;return s}return Xu=r,Xu}var Yu,iv;function xg(){if(iv)return Yu;iv=1;var e=Wy(),t=1/0,r=17976931348623157e292;function n(i){if(!i)return i===0?i:0;if(i=e(i),i===t||i===-t){var a=i<0?-1:1;return a*r}return i===i?i:0}return Yu=n,Yu}var Zu,av;function n$(){if(av)return Zu;av=1;var e=r$(),t=xa(),r=xg();function n(i){return function(a,o,u){return u&&typeof u!="number"&&t(a,o,u)&&(o=u=void 0),a=r(a),o===void 0?(o=a,a=0):o=r(o),u=u===void 0?a<o?1:-1:r(u),e(a,o,u,i)}}return Zu=n,Zu}var Ju,ov;function i$(){if(ov)return Ju;ov=1;var e=n$(),t=e();return Ju=t,Ju}var a$=i$();const Qi=oe(a$);function Fn(e){"@babel/helpers - typeof";return Fn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Fn(e)}function uv(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function cv(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?uv(Object(r),!0).forEach(function(n){wg(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):uv(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function wg(e,t,r){return t=o$(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function o$(e){var t=u$(e,"string");return Fn(t)=="symbol"?t:t+""}function u$(e,t){if(Fn(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Fn(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var c$=["Webkit","Moz","O","ms"],s$=function(t,r){var n=t.replace(/(\w)/,function(a){return a.toUpperCase()}),i=c$.reduce(function(a,o){return cv(cv({},a),{},wg({},o+n,r))},{});return i[t]=r,i};function jr(e){"@babel/helpers - typeof";return jr=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},jr(e)}function ea(){return ea=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},ea.apply(this,arguments)}function sv(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Qu(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?sv(Object(r),!0).forEach(function(n){Le(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):sv(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function l$(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function lv(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,Sg(n.key),n)}}function f$(e,t,r){return t&&lv(e.prototype,t),r&&lv(e,r),Object.defineProperty(e,"prototype",{writable:!1}),e}function h$(e,t,r){return t=ta(t),p$(e,Og()?Reflect.construct(t,r||[],ta(e).constructor):t.apply(e,r))}function p$(e,t){if(t&&(jr(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return d$(e)}function d$(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function Og(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(Og=function(){return!!e})()}function ta(e){return ta=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},ta(e)}function v$(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&ms(e,t)}function ms(e,t){return ms=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},ms(e,t)}function Le(e,t,r){return t=Sg(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function Sg(e){var t=y$(e,"string");return jr(t)=="symbol"?t:t+""}function y$(e,t){if(jr(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(jr(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}var m$=function(t){var r=t.data,n=t.startIndex,i=t.endIndex,a=t.x,o=t.width,u=t.travellerWidth;if(!r||!r.length)return{};var c=r.length,s=un().domain(Qi(0,c)).range([a,a+o-u]),f=s.domain().map(function(l){return s(l)});return{isTextActive:!1,isSlideMoving:!1,isTravellerMoving:!1,isTravellerFocused:!1,startX:s(n),endX:s(i),scale:s,scaleValues:f}},fv=function(t){return t.changedTouches&&!!t.changedTouches.length},$r=function(e){function t(r){var n;return l$(this,t),n=h$(this,t,[r]),Le(n,"handleDrag",function(i){n.leaveTimer&&(clearTimeout(n.leaveTimer),n.leaveTimer=null),n.state.isTravellerMoving?n.handleTravellerMove(i):n.state.isSlideMoving&&n.handleSlideDrag(i)}),Le(n,"handleTouchMove",function(i){i.changedTouches!=null&&i.changedTouches.length>0&&n.handleDrag(i.changedTouches[0])}),Le(n,"handleDragEnd",function(){n.setState({isTravellerMoving:!1,isSlideMoving:!1},function(){var i=n.props,a=i.endIndex,o=i.onDragEnd,u=i.startIndex;o==null||o({endIndex:a,startIndex:u})}),n.detachDragEndListener()}),Le(n,"handleLeaveWrapper",function(){(n.state.isTravellerMoving||n.state.isSlideMoving)&&(n.leaveTimer=window.setTimeout(n.handleDragEnd,n.props.leaveTimeOut))}),Le(n,"handleEnterSlideOrTraveller",function(){n.setState({isTextActive:!0})}),Le(n,"handleLeaveSlideOrTraveller",function(){n.setState({isTextActive:!1})}),Le(n,"handleSlideDragStart",function(i){var a=fv(i)?i.changedTouches[0]:i;n.setState({isTravellerMoving:!1,isSlideMoving:!0,slideMoveStartX:a.pageX}),n.attachDragEndListener()}),n.travellerDragStartHandlers={startX:n.handleTravellerDragStart.bind(n,"startX"),endX:n.handleTravellerDragStart.bind(n,"endX")},n.state={},n}return v$(t,e),f$(t,[{key:"componentWillUnmount",value:function(){this.leaveTimer&&(clearTimeout(this.leaveTimer),this.leaveTimer=null),this.detachDragEndListener()}},{key:"getIndex",value:function(n){var i=n.startX,a=n.endX,o=this.state.scaleValues,u=this.props,c=u.gap,s=u.data,f=s.length-1,l=Math.min(i,a),h=Math.max(i,a),d=t.getIndexInRange(o,l),y=t.getIndexInRange(o,h);return{startIndex:d-d%c,endIndex:y===f?f:y-y%c}}},{key:"getTextOfTick",value:function(n){var i=this.props,a=i.data,o=i.tickFormatter,u=i.dataKey,c=je(a[n],u,n);return X(o)?o(c,n):c}},{key:"attachDragEndListener",value:function(){window.addEventListener("mouseup",this.handleDragEnd,!0),window.addEventListener("touchend",this.handleDragEnd,!0),window.addEventListener("mousemove",this.handleDrag,!0)}},{key:"detachDragEndListener",value:function(){window.removeEventListener("mouseup",this.handleDragEnd,!0),window.removeEventListener("touchend",this.handleDragEnd,!0),window.removeEventListener("mousemove",this.handleDrag,!0)}},{key:"handleSlideDrag",value:function(n){var i=this.state,a=i.slideMoveStartX,o=i.startX,u=i.endX,c=this.props,s=c.x,f=c.width,l=c.travellerWidth,h=c.startIndex,d=c.endIndex,y=c.onChange,v=n.pageX-a;v>0?v=Math.min(v,s+f-l-u,s+f-l-o):v<0&&(v=Math.max(v,s-o,s-u));var p=this.getIndex({startX:o+v,endX:u+v});(p.startIndex!==h||p.endIndex!==d)&&y&&y(p),this.setState({startX:o+v,endX:u+v,slideMoveStartX:n.pageX})}},{key:"handleTravellerDragStart",value:function(n,i){var a=fv(i)?i.changedTouches[0]:i;this.setState({isSlideMoving:!1,isTravellerMoving:!0,movingTravellerId:n,brushMoveStartX:a.pageX}),this.attachDragEndListener()}},{key:"handleTravellerMove",value:function(n){var i=this.state,a=i.brushMoveStartX,o=i.movingTravellerId,u=i.endX,c=i.startX,s=this.state[o],f=this.props,l=f.x,h=f.width,d=f.travellerWidth,y=f.onChange,v=f.gap,p=f.data,b={startX:this.state.startX,endX:this.state.endX},w=n.pageX-a;w>0?w=Math.min(w,l+h-d-s):w<0&&(w=Math.max(w,l-s)),b[o]=s+w;var g=this.getIndex(b),O=g.startIndex,m=g.endIndex,x=function(){var A=p.length-1;return o==="startX"&&(u>c?O%v===0:m%v===0)||u<c&&m===A||o==="endX"&&(u>c?m%v===0:O%v===0)||u>c&&m===A};this.setState(Le(Le({},o,s+w),"brushMoveStartX",n.pageX),function(){y&&x()&&y(g)})}},{key:"handleTravellerMoveKeyboard",value:function(n,i){var a=this,o=this.state,u=o.scaleValues,c=o.startX,s=o.endX,f=this.state[i],l=u.indexOf(f);if(l!==-1){var h=l+n;if(!(h===-1||h>=u.length)){var d=u[h];i==="startX"&&d>=s||i==="endX"&&d<=c||this.setState(Le({},i,d),function(){a.props.onChange(a.getIndex({startX:a.state.startX,endX:a.state.endX}))})}}}},{key:"renderBackground",value:function(){var n=this.props,i=n.x,a=n.y,o=n.width,u=n.height,c=n.fill,s=n.stroke;return P.createElement("rect",{stroke:s,fill:c,x:i,y:a,width:o,height:u})}},{key:"renderPanorama",value:function(){var n=this.props,i=n.x,a=n.y,o=n.width,u=n.height,c=n.data,s=n.children,f=n.padding,l=D.Children.only(s);return l?P.cloneElement(l,{x:i,y:a,width:o,height:u,margin:f,compact:!0,data:c}):null}},{key:"renderTravellerLayer",value:function(n,i){var a,o,u=this,c=this.props,s=c.y,f=c.travellerWidth,l=c.height,h=c.traveller,d=c.ariaLabel,y=c.data,v=c.startIndex,p=c.endIndex,b=Math.max(n,this.props.x),w=Qu(Qu({},V(this.props,!1)),{},{x:b,y:s,width:f,height:l}),g=d||"Min value: ".concat((a=y[v])===null||a===void 0?void 0:a.name,", Max value: ").concat((o=y[p])===null||o===void 0?void 0:o.name);return P.createElement(ie,{tabIndex:0,role:"slider","aria-label":g,"aria-valuenow":n,className:"recharts-brush-traveller",onMouseEnter:this.handleEnterSlideOrTraveller,onMouseLeave:this.handleLeaveSlideOrTraveller,onMouseDown:this.travellerDragStartHandlers[i],onTouchStart:this.travellerDragStartHandlers[i],onKeyDown:function(m){["ArrowLeft","ArrowRight"].includes(m.key)&&(m.preventDefault(),m.stopPropagation(),u.handleTravellerMoveKeyboard(m.key==="ArrowRight"?1:-1,i))},onFocus:function(){u.setState({isTravellerFocused:!0})},onBlur:function(){u.setState({isTravellerFocused:!1})},style:{cursor:"col-resize"}},t.renderTraveller(h,w))}},{key:"renderSlide",value:function(n,i){var a=this.props,o=a.y,u=a.height,c=a.stroke,s=a.travellerWidth,f=Math.min(n,i)+s,l=Math.max(Math.abs(i-n)-s,0);return P.createElement("rect",{className:"recharts-brush-slide",onMouseEnter:this.handleEnterSlideOrTraveller,onMouseLeave:this.handleLeaveSlideOrTraveller,onMouseDown:this.handleSlideDragStart,onTouchStart:this.handleSlideDragStart,style:{cursor:"move"},stroke:"none",fill:c,fillOpacity:.2,x:f,y:o,width:l,height:u})}},{key:"renderText",value:function(){var n=this.props,i=n.startIndex,a=n.endIndex,o=n.y,u=n.height,c=n.travellerWidth,s=n.stroke,f=this.state,l=f.startX,h=f.endX,d=5,y={pointerEvents:"none",fill:s};return P.createElement(ie,{className:"recharts-brush-texts"},P.createElement(Yt,ea({textAnchor:"end",verticalAnchor:"middle",x:Math.min(l,h)-d,y:o+u/2},y),this.getTextOfTick(i)),P.createElement(Yt,ea({textAnchor:"start",verticalAnchor:"middle",x:Math.max(l,h)+c+d,y:o+u/2},y),this.getTextOfTick(a)))}},{key:"render",value:function(){var n=this.props,i=n.data,a=n.className,o=n.children,u=n.x,c=n.y,s=n.width,f=n.height,l=n.alwaysShowText,h=this.state,d=h.startX,y=h.endX,v=h.isTextActive,p=h.isSlideMoving,b=h.isTravellerMoving,w=h.isTravellerFocused;if(!i||!i.length||!B(u)||!B(c)||!B(s)||!B(f)||s<=0||f<=0)return null;var g=Q("recharts-brush",a),O=P.Children.count(o)===1,m=s$("userSelect","none");return P.createElement(ie,{className:g,onMouseLeave:this.handleLeaveWrapper,onTouchMove:this.handleTouchMove,style:m},this.renderBackground(),O&&this.renderPanorama(),this.renderSlide(d,y),this.renderTravellerLayer(d,"startX"),this.renderTravellerLayer(y,"endX"),(v||p||b||w||l)&&this.renderText())}}],[{key:"renderDefaultTraveller",value:function(n){var i=n.x,a=n.y,o=n.width,u=n.height,c=n.stroke,s=Math.floor(a+u/2)-1;return P.createElement(P.Fragment,null,P.createElement("rect",{x:i,y:a,width:o,height:u,fill:c,stroke:"none"}),P.createElement("line",{x1:i+1,y1:s,x2:i+o-1,y2:s,fill:"none",stroke:"#fff"}),P.createElement("line",{x1:i+1,y1:s+2,x2:i+o-1,y2:s+2,fill:"none",stroke:"#fff"}))}},{key:"renderTraveller",value:function(n,i){var a;return P.isValidElement(n)?a=P.cloneElement(n,i):X(n)?a=n(i):a=t.renderDefaultTraveller(i),a}},{key:"getDerivedStateFromProps",value:function(n,i){var a=n.data,o=n.width,u=n.x,c=n.travellerWidth,s=n.updateId,f=n.startIndex,l=n.endIndex;if(a!==i.prevData||s!==i.prevUpdateId)return Qu({prevData:a,prevTravellerWidth:c,prevUpdateId:s,prevX:u,prevWidth:o},a&&a.length?m$({data:a,width:o,x:u,travellerWidth:c,startIndex:f,endIndex:l}):{scale:null,scaleValues:null});if(i.scale&&(o!==i.prevWidth||u!==i.prevX||c!==i.prevTravellerWidth)){i.scale.range([u,u+o-c]);var h=i.scale.domain().map(function(d){return i.scale(d)});return{prevData:a,prevTravellerWidth:c,prevUpdateId:s,prevX:u,prevWidth:o,startX:i.scale(n.startIndex),endX:i.scale(n.endIndex),scaleValues:h}}return null}},{key:"getIndexInRange",value:function(n,i){for(var a=n.length,o=0,u=a-1;u-o>1;){var c=Math.floor((o+u)/2);n[c]>i?u=c:o=c}return i>=n[u]?u:o}}])}(D.PureComponent);Le($r,"displayName","Brush");Le($r,"defaultProps",{height:40,travellerWidth:5,gap:1,fill:"#fff",stroke:"#666",padding:{top:1,right:1,bottom:1,left:1},leaveTimeOut:1e3,alwaysShowText:!1});var ec,hv;function g$(){if(hv)return ec;hv=1;var e=Ks();function t(r,n){var i;return e(r,function(a,o,u){return i=n(a,o,u),!i}),!!i}return ec=t,ec}var tc,pv;function b$(){if(pv)return tc;pv=1;var e=Ob(),t=ut(),r=g$(),n=tt(),i=xa();function a(o,u,c){var s=n(o)?e:r;return c&&i(o,u,c)&&(u=void 0),s(o,t(u,3))}return tc=a,tc}var x$=b$();const w$=oe(x$);var at=function(t,r){var n=t.alwaysShow,i=t.ifOverflow;return n&&(i="extendDomain"),i===r},rc,dv;function O$(){if(dv)return rc;dv=1;var e=Dy();function t(r,n,i){n=="__proto__"&&e?e(r,n,{configurable:!0,enumerable:!0,value:i,writable:!0}):r[n]=i}return rc=t,rc}var nc,vv;function S$(){if(vv)return nc;vv=1;var e=O$(),t=Ny(),r=ut();function n(i,a){var o={};return a=r(a,3),t(i,function(u,c,s){e(o,c,a(u,c,s))}),o}return nc=n,nc}var A$=S$();const P$=oe(A$);var ic,yv;function _$(){if(yv)return ic;yv=1;function e(t,r){for(var n=-1,i=t==null?0:t.length;++n<i;)if(!r(t[n],n,t))return!1;return!0}return ic=e,ic}var ac,mv;function E$(){if(mv)return ac;mv=1;var e=Ks();function t(r,n){var i=!0;return e(r,function(a,o,u){return i=!!n(a,o,u),i}),i}return ac=t,ac}var oc,gv;function T$(){if(gv)return oc;gv=1;var e=_$(),t=E$(),r=ut(),n=tt(),i=xa();function a(o,u,c){var s=n(o)?e:t;return c&&i(o,u,c)&&(u=void 0),s(o,r(u,3))}return oc=a,oc}var j$=T$();const Ag=oe(j$);var $$=["x","y"];function Wn(e){"@babel/helpers - typeof";return Wn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Wn(e)}function gs(){return gs=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},gs.apply(this,arguments)}function bv(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function nn(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?bv(Object(r),!0).forEach(function(n){M$(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):bv(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function M$(e,t,r){return t=I$(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function I$(e){var t=C$(e,"string");return Wn(t)=="symbol"?t:t+""}function C$(e,t){if(Wn(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Wn(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function k$(e,t){if(e==null)return{};var r=N$(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function N$(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}function R$(e,t){var r=e.x,n=e.y,i=k$(e,$$),a="".concat(r),o=parseInt(a,10),u="".concat(n),c=parseInt(u,10),s="".concat(t.height||i.height),f=parseInt(s,10),l="".concat(t.width||i.width),h=parseInt(l,10);return nn(nn(nn(nn(nn({},t),i),o?{x:o}:{}),c?{y:c}:{}),{},{height:f,width:h,name:t.name,radius:t.radius})}function xv(e){return P.createElement(mg,gs({shapeType:"rectangle",propTransformer:R$,activeClassName:"recharts-active-bar"},e))}var D$=function(t){var r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0;return function(n,i){if(typeof t=="number")return t;var a=B(n)||qb(n);return a?t(n,i):(a||Jt(),r)}},B$=["value","background"],Pg;function Mr(e){"@babel/helpers - typeof";return Mr=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Mr(e)}function L$(e,t){if(e==null)return{};var r=q$(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function q$(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}function ra(){return ra=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},ra.apply(this,arguments)}function wv(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function me(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?wv(Object(r),!0).forEach(function(n){_t(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):wv(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function F$(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function Ov(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,Eg(n.key),n)}}function W$(e,t,r){return t&&Ov(e.prototype,t),r&&Ov(e,r),Object.defineProperty(e,"prototype",{writable:!1}),e}function z$(e,t,r){return t=na(t),U$(e,_g()?Reflect.construct(t,r||[],na(e).constructor):t.apply(e,r))}function U$(e,t){if(t&&(Mr(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return H$(e)}function H$(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function _g(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(_g=function(){return!!e})()}function na(e){return na=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},na(e)}function K$(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&bs(e,t)}function bs(e,t){return bs=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},bs(e,t)}function _t(e,t,r){return t=Eg(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function Eg(e){var t=G$(e,"string");return Mr(t)=="symbol"?t:t+""}function G$(e,t){if(Mr(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Mr(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}var Gr=function(e){function t(){var r;F$(this,t);for(var n=arguments.length,i=new Array(n),a=0;a<n;a++)i[a]=arguments[a];return r=z$(this,t,[].concat(i)),_t(r,"state",{isAnimationFinished:!1}),_t(r,"id",Yn("recharts-bar-")),_t(r,"handleAnimationEnd",function(){var o=r.props.onAnimationEnd;r.setState({isAnimationFinished:!0}),o&&o()}),_t(r,"handleAnimationStart",function(){var o=r.props.onAnimationStart;r.setState({isAnimationFinished:!1}),o&&o()}),r}return K$(t,e),W$(t,[{key:"renderRectanglesStatically",value:function(n){var i=this,a=this.props,o=a.shape,u=a.dataKey,c=a.activeIndex,s=a.activeBar,f=V(this.props,!1);return n&&n.map(function(l,h){var d=h===c,y=d?s:o,v=me(me(me({},f),l),{},{isActive:d,option:y,index:h,dataKey:u,onAnimationStart:i.handleAnimationStart,onAnimationEnd:i.handleAnimationEnd});return P.createElement(ie,ra({className:"recharts-bar-rectangle"},Xt(i.props,l,h),{key:"rectangle-".concat(l==null?void 0:l.x,"-").concat(l==null?void 0:l.y,"-").concat(l==null?void 0:l.value,"-").concat(h)}),P.createElement(xv,v))})}},{key:"renderRectanglesWithAnimation",value:function(){var n=this,i=this.props,a=i.data,o=i.layout,u=i.isAnimationActive,c=i.animationBegin,s=i.animationDuration,f=i.animationEasing,l=i.animationId,h=this.state.prevData;return P.createElement(xt,{begin:c,duration:s,isActive:u,easing:f,from:{t:0},to:{t:1},key:"bar-".concat(l),onAnimationEnd:this.handleAnimationEnd,onAnimationStart:this.handleAnimationStart},function(d){var y=d.t,v=a.map(function(p,b){var w=h&&h[b];if(w){var g=St(w.x,p.x),O=St(w.y,p.y),m=St(w.width,p.width),x=St(w.height,p.height);return me(me({},p),{},{x:g(y),y:O(y),width:m(y),height:x(y)})}if(o==="horizontal"){var S=St(0,p.height),A=S(y);return me(me({},p),{},{y:p.y+p.height-A,height:A})}var _=St(0,p.width),$=_(y);return me(me({},p),{},{width:$})});return P.createElement(ie,null,n.renderRectanglesStatically(v))})}},{key:"renderRectangles",value:function(){var n=this.props,i=n.data,a=n.isAnimationActive,o=this.state.prevData;return a&&i&&i.length&&(!o||!$a(o,i))?this.renderRectanglesWithAnimation():this.renderRectanglesStatically(i)}},{key:"renderBackground",value:function(){var n=this,i=this.props,a=i.data,o=i.dataKey,u=i.activeIndex,c=V(this.props.background,!1);return a.map(function(s,f){s.value;var l=s.background,h=L$(s,B$);if(!l)return null;var d=me(me(me(me(me({},h),{},{fill:"#eee"},l),c),Xt(n.props,s,f)),{},{onAnimationStart:n.handleAnimationStart,onAnimationEnd:n.handleAnimationEnd,dataKey:o,index:f,className:"recharts-bar-background-rectangle"});return P.createElement(xv,ra({key:"background-bar-".concat(f),option:n.props.background,isActive:f===u},d))})}},{key:"renderErrorBar",value:function(n,i){if(this.props.isAnimationActive&&!this.state.isAnimationFinished)return null;var a=this.props,o=a.data,u=a.xAxis,c=a.yAxis,s=a.layout,f=a.children,l=Ve(f,Ca);if(!l)return null;var h=s==="vertical"?o[0].height/2:o[0].width/2,d=function(p,b){var w=Array.isArray(p.value)?p.value[1]:p.value;return{x:p.x,y:p.y,value:w,errorVal:je(p,b)}},y={clipPath:n?"url(#clipPath-".concat(i,")"):null};return P.createElement(ie,y,l.map(function(v){return P.cloneElement(v,{key:"error-bar-".concat(i,"-").concat(v.props.dataKey),data:o,xAxis:u,yAxis:c,layout:s,offset:h,dataPointFormatter:d})}))}},{key:"render",value:function(){var n=this.props,i=n.hide,a=n.data,o=n.className,u=n.xAxis,c=n.yAxis,s=n.left,f=n.top,l=n.width,h=n.height,d=n.isAnimationActive,y=n.background,v=n.id;if(i||!a||!a.length)return null;var p=this.state.isAnimationFinished,b=Q("recharts-bar",o),w=u&&u.allowDataOverflow,g=c&&c.allowDataOverflow,O=w||g,m=Z(v)?this.id:v;return P.createElement(ie,{className:b},w||g?P.createElement("defs",null,P.createElement("clipPath",{id:"clipPath-".concat(m)},P.createElement("rect",{x:w?s:s-l/2,y:g?f:f-h/2,width:w?l:l*2,height:g?h:h*2}))):null,P.createElement(ie,{className:"recharts-bar-rectangles",clipPath:O?"url(#clipPath-".concat(m,")"):null},y?this.renderBackground():null,this.renderRectangles()),this.renderErrorBar(O,m),(!d||p)&&Tt.renderCallByParent(this.props,a))}}],[{key:"getDerivedStateFromProps",value:function(n,i){return n.animationId!==i.prevAnimationId?{prevAnimationId:n.animationId,curData:n.data,prevData:i.curData}:n.data!==i.curData?{curData:n.data}:null}}])}(D.PureComponent);Pg=Gr;_t(Gr,"displayName","Bar");_t(Gr,"defaultProps",{xAxisId:0,yAxisId:0,legendType:"rect",minPointSize:0,hide:!1,data:[],layout:"vertical",activeBar:!1,isAnimationActive:!vt.isSsr,animationBegin:0,animationDuration:400,animationEasing:"ease"});_t(Gr,"getComposedData",function(e){var t=e.props,r=e.item,n=e.barPosition,i=e.bandSize,a=e.xAxis,o=e.yAxis,u=e.xAxisTicks,c=e.yAxisTicks,s=e.stackedData,f=e.dataStartIndex,l=e.displayedData,h=e.offset,d=OP(n,r);if(!d)return null;var y=t.layout,v=r.type.defaultProps,p=v!==void 0?me(me({},v),r.props):r.props,b=p.dataKey,w=p.children,g=p.minPointSize,O=y==="horizontal"?o:a,m=s?O.scale.domain():null,x=jP({numericAxis:O}),S=Ve(w,Xs),A=l.map(function(_,$){var E,T,j,C,I,k;s?E=SP(s[f+$],m):(E=je(_,b),Array.isArray(E)||(E=[x,E]));var N=D$(g,Pg.defaultProps.minPointSize)(E[1],$);if(y==="horizontal"){var L,q=[o.scale(E[0]),o.scale(E[1])],U=q[0],K=q[1];T=Gp({axis:a,ticks:u,bandSize:i,offset:d.offset,entry:_,index:$}),j=(L=K!=null?K:U)!==null&&L!==void 0?L:void 0,C=d.size;var W=U-K;if(I=Number.isNaN(W)?0:W,k={x:T,y:o.y,width:C,height:o.height},Math.abs(N)>0&&Math.abs(I)<Math.abs(N)){var G=Ie(I||N)*(Math.abs(N)-Math.abs(I));j-=G,I+=G}}else{var fe=[a.scale(E[0]),a.scale(E[1])],ye=fe[0],Be=fe[1];if(T=ye,j=Gp({axis:o,ticks:c,bandSize:i,offset:d.offset,entry:_,index:$}),C=Be-ye,I=d.size,k={x:a.x,y:j,width:a.width,height:I},Math.abs(N)>0&&Math.abs(C)<Math.abs(N)){var Ct=Ie(C||N)*(Math.abs(N)-Math.abs(C));C+=Ct}}return me(me(me({},_),{},{x:T,y:j,width:C,height:I,value:s?E:E[1],payload:_,background:k},S&&S[$]&&S[$].props),{},{tooltipPayload:[Qm(r,_)],tooltipPosition:{x:T+C/2,y:j+I/2}})});return me({data:A,layout:y},h)});function zn(e){"@babel/helpers - typeof";return zn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},zn(e)}function V$(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function Sv(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,Tg(n.key),n)}}function X$(e,t,r){return t&&Sv(e.prototype,t),r&&Sv(e,r),Object.defineProperty(e,"prototype",{writable:!1}),e}function Av(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Je(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Av(Object(r),!0).forEach(function(n){qa(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Av(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function qa(e,t,r){return t=Tg(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function Tg(e){var t=Y$(e,"string");return zn(t)=="symbol"?t:t+""}function Y$(e,t){if(zn(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(zn(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var Z$=function(t,r,n,i,a){var o=t.width,u=t.height,c=t.layout,s=t.children,f=Object.keys(r),l={left:n.left,leftMirror:n.left,right:o-n.right,rightMirror:o-n.right,top:n.top,topMirror:n.top,bottom:u-n.bottom,bottomMirror:u-n.bottom},h=!!qe(s,Gr);return f.reduce(function(d,y){var v=r[y],p=v.orientation,b=v.domain,w=v.padding,g=w===void 0?{}:w,O=v.mirror,m=v.reversed,x="".concat(p).concat(O?"Mirror":""),S,A,_,$,E;if(v.type==="number"&&(v.padding==="gap"||v.padding==="no-gap")){var T=b[1]-b[0],j=1/0,C=v.categoricalDomain.sort(zb);if(C.forEach(function(fe,ye){ye>0&&(j=Math.min((fe||0)-(C[ye-1]||0),j))}),Number.isFinite(j)){var I=j/T,k=v.layout==="vertical"?n.height:n.width;if(v.padding==="gap"&&(S=I*k/2),v.padding==="no-gap"){var N=Ce(t.barCategoryGap,I*k),L=I*k/2;S=L-N-(L-N)/k*N}}}i==="xAxis"?A=[n.left+(g.left||0)+(S||0),n.left+n.width-(g.right||0)-(S||0)]:i==="yAxis"?A=c==="horizontal"?[n.top+n.height-(g.bottom||0),n.top+(g.top||0)]:[n.top+(g.top||0)+(S||0),n.top+n.height-(g.bottom||0)-(S||0)]:A=v.range,m&&(A=[A[1],A[0]]);var q=Xm(v,a,h),U=q.scale,K=q.realScaleType;U.domain(b).range(A),Ym(U);var W=Zm(U,Je(Je({},v),{},{realScaleType:K}));i==="xAxis"?(E=p==="top"&&!O||p==="bottom"&&O,_=n.left,$=l[x]-E*v.height):i==="yAxis"&&(E=p==="left"&&!O||p==="right"&&O,_=l[x]-E*v.width,$=n.top);var G=Je(Je(Je({},v),W),{},{realScaleType:K,x:_,y:$,scale:U,width:i==="xAxis"?n.width:v.width,height:i==="yAxis"?n.height:v.height});return G.bandSize=Fi(G,W),!v.hide&&i==="xAxis"?l[x]+=(E?-1:1)*G.height:v.hide||(l[x]+=(E?-1:1)*G.width),Je(Je({},d),{},qa({},y,G))},{})},jg=function(t,r){var n=t.x,i=t.y,a=r.x,o=r.y;return{x:Math.min(n,a),y:Math.min(i,o),width:Math.abs(a-n),height:Math.abs(o-i)}},J$=function(t){var r=t.x1,n=t.y1,i=t.x2,a=t.y2;return jg({x:r,y:n},{x:i,y:a})},$g=function(){function e(t){V$(this,e),this.scale=t}return X$(e,[{key:"domain",get:function(){return this.scale.domain}},{key:"range",get:function(){return this.scale.range}},{key:"rangeMin",get:function(){return this.range()[0]}},{key:"rangeMax",get:function(){return this.range()[1]}},{key:"bandwidth",get:function(){return this.scale.bandwidth}},{key:"apply",value:function(r){var n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},i=n.bandAware,a=n.position;if(r!==void 0){if(a)switch(a){case"start":return this.scale(r);case"middle":{var o=this.bandwidth?this.bandwidth()/2:0;return this.scale(r)+o}case"end":{var u=this.bandwidth?this.bandwidth():0;return this.scale(r)+u}default:return this.scale(r)}if(i){var c=this.bandwidth?this.bandwidth()/2:0;return this.scale(r)+c}return this.scale(r)}}},{key:"isInRange",value:function(r){var n=this.range(),i=n[0],a=n[n.length-1];return i<=a?r>=i&&r<=a:r>=a&&r<=i}}],[{key:"create",value:function(r){return new e(r)}}])}();qa($g,"EPS",1e-4);var Sl=function(t){var r=Object.keys(t).reduce(function(n,i){return Je(Je({},n),{},qa({},i,$g.create(t[i])))},{});return Je(Je({},r),{},{apply:function(i){var a=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},o=a.bandAware,u=a.position;return P$(i,function(c,s){return r[s].apply(c,{bandAware:o,position:u})})},isInRange:function(i){return Ag(i,function(a,o){return r[o].isInRange(a)})}})};function Q$(e){return(e%180+180)%180}var eM=function(t){var r=t.width,n=t.height,i=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0,a=Q$(i),o=a*Math.PI/180,u=Math.atan(n/r),c=o>u&&o<Math.PI-u?n/Math.sin(o):r/Math.cos(o);return Math.abs(c)},uc,Pv;function tM(){if(Pv)return uc;Pv=1;var e=ut(),t=ha(),r=Rs();function n(i){return function(a,o,u){var c=Object(a);if(!t(a)){var s=e(o,3);a=r(a),o=function(l){return s(c[l],l,c)}}var f=i(a,o,u);return f>-1?c[s?a[f]:f]:void 0}}return uc=n,uc}var cc,_v;function rM(){if(_v)return cc;_v=1;var e=xg();function t(r){var n=e(r),i=n%1;return n===n?i?n-i:n:0}return cc=t,cc}var sc,Ev;function nM(){if(Ev)return sc;Ev=1;var e=$y(),t=ut(),r=rM(),n=Math.max;function i(a,o,u){var c=a==null?0:a.length;if(!c)return-1;var s=u==null?0:r(u);return s<0&&(s=n(c+s,0)),e(a,t(o,3),s)}return sc=i,sc}var lc,Tv;function iM(){if(Tv)return lc;Tv=1;var e=tM(),t=nM(),r=e(t);return lc=r,lc}var aM=iM();const oM=oe(aM);var uM=ry();const cM=oe(uM);var sM=cM(function(e){return{x:e.left,y:e.top,width:e.width,height:e.height}},function(e){return["l",e.left,"t",e.top,"w",e.width,"h",e.height].join("")}),Al=D.createContext(void 0),Pl=D.createContext(void 0),Mg=D.createContext(void 0),Ig=D.createContext({}),Cg=D.createContext(void 0),kg=D.createContext(0),Ng=D.createContext(0),jv=function(t){var r=t.state,n=r.xAxisMap,i=r.yAxisMap,a=r.offset,o=t.clipPathId,u=t.children,c=t.width,s=t.height,f=sM(a);return P.createElement(Al.Provider,{value:n},P.createElement(Pl.Provider,{value:i},P.createElement(Ig.Provider,{value:a},P.createElement(Mg.Provider,{value:f},P.createElement(Cg.Provider,{value:o},P.createElement(kg.Provider,{value:s},P.createElement(Ng.Provider,{value:c},u)))))))},lM=function(){return D.useContext(Cg)},Rg=function(t){var r=D.useContext(Al);r==null&&Jt();var n=r[t];return n==null&&Jt(),n},fM=function(){var t=D.useContext(Al);return Pt(t)},hM=function(){var t=D.useContext(Pl),r=oM(t,function(n){return Ag(n.domain,Number.isFinite)});return r||Pt(t)},Dg=function(t){var r=D.useContext(Pl);r==null&&Jt();var n=r[t];return n==null&&Jt(),n},pM=function(){var t=D.useContext(Mg);return t},dM=function(){return D.useContext(Ig)},_l=function(){return D.useContext(Ng)},El=function(){return D.useContext(kg)};function Ir(e){"@babel/helpers - typeof";return Ir=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Ir(e)}function vM(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function yM(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,Lg(n.key),n)}}function mM(e,t,r){return t&&yM(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e}function gM(e,t,r){return t=ia(t),bM(e,Bg()?Reflect.construct(t,r||[],ia(e).constructor):t.apply(e,r))}function bM(e,t){if(t&&(Ir(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return xM(e)}function xM(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function Bg(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(Bg=function(){return!!e})()}function ia(e){return ia=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},ia(e)}function wM(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&xs(e,t)}function xs(e,t){return xs=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},xs(e,t)}function $v(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Mv(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?$v(Object(r),!0).forEach(function(n){Tl(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):$v(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function Tl(e,t,r){return t=Lg(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function Lg(e){var t=OM(e,"string");return Ir(t)=="symbol"?t:t+""}function OM(e,t){if(Ir(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Ir(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}function SM(e,t){return EM(e)||_M(e,t)||PM(e,t)||AM()}function AM(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function PM(e,t){if(e){if(typeof e=="string")return Iv(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Iv(e,t)}}function Iv(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function _M(e,t){var r=e==null?null:typeof Symbol!="undefined"&&e[Symbol.iterator]||e["@@iterator"];if(r!=null){var n,i,a,o,u=[],c=!0,s=!1;try{if(a=(r=r.call(e)).next,t!==0)for(;!(c=(n=a.call(r)).done)&&(u.push(n.value),u.length!==t);c=!0);}catch(f){s=!0,i=f}finally{try{if(!c&&r.return!=null&&(o=r.return(),Object(o)!==o))return}finally{if(s)throw i}}return u}}function EM(e){if(Array.isArray(e))return e}function ws(){return ws=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},ws.apply(this,arguments)}var TM=function(t,r){var n;return P.isValidElement(t)?n=P.cloneElement(t,r):X(t)?n=t(r):n=P.createElement("line",ws({},r,{className:"recharts-reference-line-line"})),n},jM=function(t,r,n,i,a,o,u,c,s){var f=a.x,l=a.y,h=a.width,d=a.height;if(n){var y=s.y,v=t.y.apply(y,{position:o});if(at(s,"discard")&&!t.y.isInRange(v))return null;var p=[{x:f+h,y:v},{x:f,y:v}];return c==="left"?p.reverse():p}if(r){var b=s.x,w=t.x.apply(b,{position:o});if(at(s,"discard")&&!t.x.isInRange(w))return null;var g=[{x:w,y:l+d},{x:w,y:l}];return u==="top"?g.reverse():g}if(i){var O=s.segment,m=O.map(function(x){return t.apply(x,{position:o})});return at(s,"discard")&&w$(m,function(x){return!t.isInRange(x)})?null:m}return null};function $M(e){var t=e.x,r=e.y,n=e.segment,i=e.xAxisId,a=e.yAxisId,o=e.shape,u=e.className,c=e.alwaysShow,s=lM(),f=Rg(i),l=Dg(a),h=pM();if(!s||!h)return null;et(c===void 0,'The alwaysShow prop is deprecated. Please use ifOverflow="extendDomain" instead.');var d=Sl({x:f.scale,y:l.scale}),y=Se(t),v=Se(r),p=n&&n.length===2,b=jM(d,y,v,p,h,e.position,f.orientation,l.orientation,e);if(!b)return null;var w=SM(b,2),g=w[0],O=g.x,m=g.y,x=w[1],S=x.x,A=x.y,_=at(e,"hidden")?"url(#".concat(s,")"):void 0,$=Mv(Mv({clipPath:_},V(e,!0)),{},{x1:O,y1:m,x2:S,y2:A});return P.createElement(ie,{className:Q("recharts-reference-line",u)},TM(o,$),_e.renderCallByParent(e,J$({x1:O,y1:m,x2:S,y2:A})))}var jl=function(e){function t(){return vM(this,t),gM(this,t,arguments)}return wM(t,e),mM(t,[{key:"render",value:function(){return P.createElement($M,this.props)}}])}(P.Component);Tl(jl,"displayName","ReferenceLine");Tl(jl,"defaultProps",{isFront:!1,ifOverflow:"discard",xAxisId:0,yAxisId:0,fill:"none",stroke:"#ccc",fillOpacity:1,strokeWidth:1,position:"middle"});function Os(){return Os=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Os.apply(this,arguments)}function Cr(e){"@babel/helpers - typeof";return Cr=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Cr(e)}function Cv(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function kv(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Cv(Object(r),!0).forEach(function(n){Fa(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Cv(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function MM(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function IM(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,Fg(n.key),n)}}function CM(e,t,r){return t&&IM(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e}function kM(e,t,r){return t=aa(t),NM(e,qg()?Reflect.construct(t,r||[],aa(e).constructor):t.apply(e,r))}function NM(e,t){if(t&&(Cr(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return RM(e)}function RM(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function qg(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(qg=function(){return!!e})()}function aa(e){return aa=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},aa(e)}function DM(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Ss(e,t)}function Ss(e,t){return Ss=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},Ss(e,t)}function Fa(e,t,r){return t=Fg(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function Fg(e){var t=BM(e,"string");return Cr(t)=="symbol"?t:t+""}function BM(e,t){if(Cr(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Cr(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}var LM=function(t){var r=t.x,n=t.y,i=t.xAxis,a=t.yAxis,o=Sl({x:i.scale,y:a.scale}),u=o.apply({x:r,y:n},{bandAware:!0});return at(t,"discard")&&!o.isInRange(u)?null:u},Wa=function(e){function t(){return MM(this,t),kM(this,t,arguments)}return DM(t,e),CM(t,[{key:"render",value:function(){var n=this.props,i=n.x,a=n.y,o=n.r,u=n.alwaysShow,c=n.clipPathId,s=Se(i),f=Se(a);if(et(u===void 0,'The alwaysShow prop is deprecated. Please use ifOverflow="extendDomain" instead.'),!s||!f)return null;var l=LM(this.props);if(!l)return null;var h=l.x,d=l.y,y=this.props,v=y.shape,p=y.className,b=at(this.props,"hidden")?"url(#".concat(c,")"):void 0,w=kv(kv({clipPath:b},V(this.props,!0)),{},{cx:h,cy:d});return P.createElement(ie,{className:Q("recharts-reference-dot",p)},t.renderDot(v,w),_e.renderCallByParent(this.props,{x:h-o,y:d-o,width:2*o,height:2*o}))}}])}(P.Component);Fa(Wa,"displayName","ReferenceDot");Fa(Wa,"defaultProps",{isFront:!1,ifOverflow:"discard",xAxisId:0,yAxisId:0,r:10,fill:"#fff",stroke:"#ccc",fillOpacity:1,strokeWidth:1});Fa(Wa,"renderDot",function(e,t){var r;return P.isValidElement(e)?r=P.cloneElement(e,t):X(e)?r=e(t):r=P.createElement(Ol,Os({},t,{cx:t.cx,cy:t.cy,className:"recharts-reference-dot-dot"})),r});function As(){return As=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},As.apply(this,arguments)}function kr(e){"@babel/helpers - typeof";return kr=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},kr(e)}function Nv(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Rv(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Nv(Object(r),!0).forEach(function(n){za(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Nv(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function qM(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function FM(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,zg(n.key),n)}}function WM(e,t,r){return t&&FM(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e}function zM(e,t,r){return t=oa(t),UM(e,Wg()?Reflect.construct(t,r||[],oa(e).constructor):t.apply(e,r))}function UM(e,t){if(t&&(kr(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return HM(e)}function HM(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function Wg(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(Wg=function(){return!!e})()}function oa(e){return oa=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},oa(e)}function KM(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Ps(e,t)}function Ps(e,t){return Ps=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},Ps(e,t)}function za(e,t,r){return t=zg(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function zg(e){var t=GM(e,"string");return kr(t)=="symbol"?t:t+""}function GM(e,t){if(kr(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(kr(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}var VM=function(t,r,n,i,a){var o=a.x1,u=a.x2,c=a.y1,s=a.y2,f=a.xAxis,l=a.yAxis;if(!f||!l)return null;var h=Sl({x:f.scale,y:l.scale}),d={x:t?h.x.apply(o,{position:"start"}):h.x.rangeMin,y:n?h.y.apply(c,{position:"start"}):h.y.rangeMin},y={x:r?h.x.apply(u,{position:"end"}):h.x.rangeMax,y:i?h.y.apply(s,{position:"end"}):h.y.rangeMax};return at(a,"discard")&&(!h.isInRange(d)||!h.isInRange(y))?null:jg(d,y)},Ua=function(e){function t(){return qM(this,t),zM(this,t,arguments)}return KM(t,e),WM(t,[{key:"render",value:function(){var n=this.props,i=n.x1,a=n.x2,o=n.y1,u=n.y2,c=n.className,s=n.alwaysShow,f=n.clipPathId;et(s===void 0,'The alwaysShow prop is deprecated. Please use ifOverflow="extendDomain" instead.');var l=Se(i),h=Se(a),d=Se(o),y=Se(u),v=this.props.shape;if(!l&&!h&&!d&&!y&&!v)return null;var p=VM(l,h,d,y,this.props);if(!p&&!v)return null;var b=at(this.props,"hidden")?"url(#".concat(f,")"):void 0;return P.createElement(ie,{className:Q("recharts-reference-area",c)},t.renderRect(v,Rv(Rv({clipPath:b},V(this.props,!0)),p)),_e.renderCallByParent(this.props,p))}}])}(P.Component);za(Ua,"displayName","ReferenceArea");za(Ua,"defaultProps",{isFront:!1,ifOverflow:"discard",xAxisId:0,yAxisId:0,r:10,fill:"#ccc",fillOpacity:.5,stroke:"none",strokeWidth:1});za(Ua,"renderRect",function(e,t){var r;return P.isValidElement(e)?r=P.cloneElement(e,t):X(e)?r=e(t):r=P.createElement(wl,As({},t,{className:"recharts-reference-area-rect"})),r});function Ug(e,t,r){if(t<1)return[];if(t===1&&r===void 0)return e;for(var n=[],i=0;i<e.length;i+=t)n.push(e[i]);return n}function XM(e,t,r){var n={width:e.width+t.width,height:e.height+t.height};return eM(n,r)}function YM(e,t,r){var n=r==="width",i=e.x,a=e.y,o=e.width,u=e.height;return t===1?{start:n?i:a,end:n?i+o:a+u}:{start:n?i+o:a+u,end:n?i:a}}function ua(e,t,r,n,i){if(e*t<e*n||e*t>e*i)return!1;var a=r();return e*(t-e*a/2-n)>=0&&e*(t+e*a/2-i)<=0}function ZM(e,t){return Ug(e,t+1)}function JM(e,t,r,n,i){for(var a=(n||[]).slice(),o=t.start,u=t.end,c=0,s=1,f=o,l=function(){var y=n==null?void 0:n[c];if(y===void 0)return{v:Ug(n,s)};var v=c,p,b=function(){return p===void 0&&(p=r(y,v)),p},w=y.coordinate,g=c===0||ua(e,w,b,f,u);g||(c=0,f=o,s+=1),g&&(f=w+e*(b()/2+i),c+=s)},h;s<=a.length;)if(h=l(),h)return h.v;return[]}function Un(e){"@babel/helpers - typeof";return Un=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Un(e)}function Dv(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function $e(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Dv(Object(r),!0).forEach(function(n){QM(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Dv(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function QM(e,t,r){return t=eI(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function eI(e){var t=tI(e,"string");return Un(t)=="symbol"?t:t+""}function tI(e,t){if(Un(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Un(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function rI(e,t,r,n,i){for(var a=(n||[]).slice(),o=a.length,u=t.start,c=t.end,s=function(h){var d=a[h],y,v=function(){return y===void 0&&(y=r(d,h)),y};if(h===o-1){var p=e*(d.coordinate+e*v()/2-c);a[h]=d=$e($e({},d),{},{tickCoord:p>0?d.coordinate-p*e:d.coordinate})}else a[h]=d=$e($e({},d),{},{tickCoord:d.coordinate});var b=ua(e,d.tickCoord,v,u,c);b&&(c=d.tickCoord-e*(v()/2+i),a[h]=$e($e({},d),{},{isShow:!0}))},f=o-1;f>=0;f--)s(f);return a}function nI(e,t,r,n,i,a){var o=(n||[]).slice(),u=o.length,c=t.start,s=t.end;if(a){var f=n[u-1],l=r(f,u-1),h=e*(f.coordinate+e*l/2-s);o[u-1]=f=$e($e({},f),{},{tickCoord:h>0?f.coordinate-h*e:f.coordinate});var d=ua(e,f.tickCoord,function(){return l},c,s);d&&(s=f.tickCoord-e*(l/2+i),o[u-1]=$e($e({},f),{},{isShow:!0}))}for(var y=a?u-1:u,v=function(w){var g=o[w],O,m=function(){return O===void 0&&(O=r(g,w)),O};if(w===0){var x=e*(g.coordinate-e*m()/2-c);o[w]=g=$e($e({},g),{},{tickCoord:x<0?g.coordinate-x*e:g.coordinate})}else o[w]=g=$e($e({},g),{},{tickCoord:g.coordinate});var S=ua(e,g.tickCoord,m,c,s);S&&(c=g.tickCoord+e*(m()/2+i),o[w]=$e($e({},g),{},{isShow:!0}))},p=0;p<y;p++)v(p);return o}function $l(e,t,r){var n=e.tick,i=e.ticks,a=e.viewBox,o=e.minTickGap,u=e.orientation,c=e.interval,s=e.tickFormatter,f=e.unit,l=e.angle;if(!i||!i.length||!n)return[];if(B(c)||vt.isSsr)return ZM(i,typeof c=="number"&&B(c)?c:0);var h=[],d=u==="top"||u==="bottom"?"width":"height",y=f&&d==="width"?on(f,{fontSize:t,letterSpacing:r}):{width:0,height:0},v=function(g,O){var m=X(s)?s(g.value,O):g.value;return d==="width"?XM(on(m,{fontSize:t,letterSpacing:r}),y,l):on(m,{fontSize:t,letterSpacing:r})[d]},p=i.length>=2?Ie(i[1].coordinate-i[0].coordinate):1,b=YM(a,p,d);return c==="equidistantPreserveStart"?JM(p,b,v,i,o):(c==="preserveStart"||c==="preserveStartEnd"?h=nI(p,b,v,i,o,c==="preserveStartEnd"):h=rI(p,b,v,i,o),h.filter(function(w){return w.isShow}))}var iI=["viewBox"],aI=["viewBox"],oI=["ticks"];function Nr(e){"@babel/helpers - typeof";return Nr=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Nr(e)}function lr(){return lr=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},lr.apply(this,arguments)}function Bv(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function we(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Bv(Object(r),!0).forEach(function(n){Ml(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Bv(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function fc(e,t){if(e==null)return{};var r=uI(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function uI(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}function cI(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function Lv(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,Kg(n.key),n)}}function sI(e,t,r){return t&&Lv(e.prototype,t),r&&Lv(e,r),Object.defineProperty(e,"prototype",{writable:!1}),e}function lI(e,t,r){return t=ca(t),fI(e,Hg()?Reflect.construct(t,r||[],ca(e).constructor):t.apply(e,r))}function fI(e,t){if(t&&(Nr(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return hI(e)}function hI(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function Hg(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(Hg=function(){return!!e})()}function ca(e){return ca=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},ca(e)}function pI(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&_s(e,t)}function _s(e,t){return _s=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},_s(e,t)}function Ml(e,t,r){return t=Kg(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function Kg(e){var t=dI(e,"string");return Nr(t)=="symbol"?t:t+""}function dI(e,t){if(Nr(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Nr(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}var Vr=function(e){function t(r){var n;return cI(this,t),n=lI(this,t,[r]),n.state={fontSize:"",letterSpacing:""},n}return pI(t,e),sI(t,[{key:"shouldComponentUpdate",value:function(n,i){var a=n.viewBox,o=fc(n,iI),u=this.props,c=u.viewBox,s=fc(u,aI);return!hr(a,c)||!hr(o,s)||!hr(i,this.state)}},{key:"componentDidMount",value:function(){var n=this.layerReference;if(n){var i=n.getElementsByClassName("recharts-cartesian-axis-tick-value")[0];i&&this.setState({fontSize:window.getComputedStyle(i).fontSize,letterSpacing:window.getComputedStyle(i).letterSpacing})}}},{key:"getTickLineCoord",value:function(n){var i=this.props,a=i.x,o=i.y,u=i.width,c=i.height,s=i.orientation,f=i.tickSize,l=i.mirror,h=i.tickMargin,d,y,v,p,b,w,g=l?-1:1,O=n.tickSize||f,m=B(n.tickCoord)?n.tickCoord:n.coordinate;switch(s){case"top":d=y=n.coordinate,p=o+ +!l*c,v=p-g*O,w=v-g*h,b=m;break;case"left":v=p=n.coordinate,y=a+ +!l*u,d=y-g*O,b=d-g*h,w=m;break;case"right":v=p=n.coordinate,y=a+ +l*u,d=y+g*O,b=d+g*h,w=m;break;default:d=y=n.coordinate,p=o+ +l*c,v=p+g*O,w=v+g*h,b=m;break}return{line:{x1:d,y1:v,x2:y,y2:p},tick:{x:b,y:w}}}},{key:"getTickTextAnchor",value:function(){var n=this.props,i=n.orientation,a=n.mirror,o;switch(i){case"left":o=a?"start":"end";break;case"right":o=a?"end":"start";break;default:o="middle";break}return o}},{key:"getTickVerticalAnchor",value:function(){var n=this.props,i=n.orientation,a=n.mirror,o="end";switch(i){case"left":case"right":o="middle";break;case"top":o=a?"start":"end";break;default:o=a?"end":"start";break}return o}},{key:"renderAxisLine",value:function(){var n=this.props,i=n.x,a=n.y,o=n.width,u=n.height,c=n.orientation,s=n.mirror,f=n.axisLine,l=we(we(we({},V(this.props,!1)),V(f,!1)),{},{fill:"none"});if(c==="top"||c==="bottom"){var h=+(c==="top"&&!s||c==="bottom"&&s);l=we(we({},l),{},{x1:i,y1:a+h*u,x2:i+o,y2:a+h*u})}else{var d=+(c==="left"&&!s||c==="right"&&s);l=we(we({},l),{},{x1:i+d*o,y1:a,x2:i+d*o,y2:a+u})}return P.createElement("line",lr({},l,{className:Q("recharts-cartesian-axis-line",We(f,"className"))}))}},{key:"renderTicks",value:function(n,i,a){var o=this,u=this.props,c=u.tickLine,s=u.stroke,f=u.tick,l=u.tickFormatter,h=u.unit,d=$l(we(we({},this.props),{},{ticks:n}),i,a),y=this.getTickTextAnchor(),v=this.getTickVerticalAnchor(),p=V(this.props,!1),b=V(f,!1),w=we(we({},p),{},{fill:"none"},V(c,!1)),g=d.map(function(O,m){var x=o.getTickLineCoord(O),S=x.line,A=x.tick,_=we(we(we(we({textAnchor:y,verticalAnchor:v},p),{},{stroke:"none",fill:s},b),A),{},{index:m,payload:O,visibleTicksCount:d.length,tickFormatter:l});return P.createElement(ie,lr({className:"recharts-cartesian-axis-tick",key:"tick-".concat(O.value,"-").concat(O.coordinate,"-").concat(O.tickCoord)},Xt(o.props,O,m)),c&&P.createElement("line",lr({},w,S,{className:Q("recharts-cartesian-axis-tick-line",We(c,"className"))})),f&&t.renderTickItem(f,_,"".concat(X(l)?l(O.value,m):O.value).concat(h||"")))});return P.createElement("g",{className:"recharts-cartesian-axis-ticks"},g)}},{key:"render",value:function(){var n=this,i=this.props,a=i.axisLine,o=i.width,u=i.height,c=i.ticksGenerator,s=i.className,f=i.hide;if(f)return null;var l=this.props,h=l.ticks,d=fc(l,oI),y=h;return X(c)&&(y=h&&h.length>0?c(this.props):c(d)),o<=0||u<=0||!y||!y.length?null:P.createElement(ie,{className:Q("recharts-cartesian-axis",s),ref:function(p){n.layerReference=p}},a&&this.renderAxisLine(),this.renderTicks(y,this.state.fontSize,this.state.letterSpacing),_e.renderCallByParent(this.props))}}],[{key:"renderTickItem",value:function(n,i,a){var o,u=Q(i.className,"recharts-cartesian-axis-tick-value");return P.isValidElement(n)?o=P.cloneElement(n,we(we({},i),{},{className:u})):X(n)?o=n(we(we({},i),{},{className:u})):o=P.createElement(Yt,lr({},i,{className:"recharts-cartesian-axis-tick-value"}),a),o}}])}(D.Component);Ml(Vr,"displayName","CartesianAxis");Ml(Vr,"defaultProps",{x:0,y:0,width:0,height:0,viewBox:{x:0,y:0,width:0,height:0},orientation:"bottom",ticks:[],stroke:"#666",tickLine:!0,axisLine:!0,tick:!0,mirror:!1,minTickGap:5,tickSize:6,tickMargin:2,interval:"preserveEnd"});var vI=["x1","y1","x2","y2","key"],yI=["offset"];function Qt(e){"@babel/helpers - typeof";return Qt=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Qt(e)}function qv(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Me(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?qv(Object(r),!0).forEach(function(n){mI(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):qv(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function mI(e,t,r){return t=gI(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function gI(e){var t=bI(e,"string");return Qt(t)=="symbol"?t:t+""}function bI(e,t){if(Qt(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Qt(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function Ht(){return Ht=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Ht.apply(this,arguments)}function Fv(e,t){if(e==null)return{};var r=xI(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function xI(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}var wI=function(t){var r=t.fill;if(!r||r==="none")return null;var n=t.fillOpacity,i=t.x,a=t.y,o=t.width,u=t.height,c=t.ry;return P.createElement("rect",{x:i,y:a,ry:c,width:o,height:u,stroke:"none",fill:r,fillOpacity:n,className:"recharts-cartesian-grid-bg"})};function Gg(e,t){var r;if(P.isValidElement(e))r=P.cloneElement(e,t);else if(X(e))r=e(t);else{var n=t.x1,i=t.y1,a=t.x2,o=t.y2,u=t.key,c=Fv(t,vI),s=V(c,!1);s.offset;var f=Fv(s,yI);r=P.createElement("line",Ht({},f,{x1:n,y1:i,x2:a,y2:o,fill:"none",key:u}))}return r}function OI(e){var t=e.x,r=e.width,n=e.horizontal,i=n===void 0?!0:n,a=e.horizontalPoints;if(!i||!a||!a.length)return null;var o=a.map(function(u,c){var s=Me(Me({},e),{},{x1:t,y1:u,x2:t+r,y2:u,key:"line-".concat(c),index:c});return Gg(i,s)});return P.createElement("g",{className:"recharts-cartesian-grid-horizontal"},o)}function SI(e){var t=e.y,r=e.height,n=e.vertical,i=n===void 0?!0:n,a=e.verticalPoints;if(!i||!a||!a.length)return null;var o=a.map(function(u,c){var s=Me(Me({},e),{},{x1:u,y1:t,x2:u,y2:t+r,key:"line-".concat(c),index:c});return Gg(i,s)});return P.createElement("g",{className:"recharts-cartesian-grid-vertical"},o)}function AI(e){var t=e.horizontalFill,r=e.fillOpacity,n=e.x,i=e.y,a=e.width,o=e.height,u=e.horizontalPoints,c=e.horizontal,s=c===void 0?!0:c;if(!s||!t||!t.length)return null;var f=u.map(function(h){return Math.round(h+i-i)}).sort(function(h,d){return h-d});i!==f[0]&&f.unshift(0);var l=f.map(function(h,d){var y=!f[d+1],v=y?i+o-h:f[d+1]-h;if(v<=0)return null;var p=d%t.length;return P.createElement("rect",{key:"react-".concat(d),y:h,x:n,height:v,width:a,stroke:"none",fill:t[p],fillOpacity:r,className:"recharts-cartesian-grid-bg"})});return P.createElement("g",{className:"recharts-cartesian-gridstripes-horizontal"},l)}function PI(e){var t=e.vertical,r=t===void 0?!0:t,n=e.verticalFill,i=e.fillOpacity,a=e.x,o=e.y,u=e.width,c=e.height,s=e.verticalPoints;if(!r||!n||!n.length)return null;var f=s.map(function(h){return Math.round(h+a-a)}).sort(function(h,d){return h-d});a!==f[0]&&f.unshift(0);var l=f.map(function(h,d){var y=!f[d+1],v=y?a+u-h:f[d+1]-h;if(v<=0)return null;var p=d%n.length;return P.createElement("rect",{key:"react-".concat(d),x:h,y:o,width:v,height:c,stroke:"none",fill:n[p],fillOpacity:i,className:"recharts-cartesian-grid-bg"})});return P.createElement("g",{className:"recharts-cartesian-gridstripes-vertical"},l)}var _I=function(t,r){var n=t.xAxis,i=t.width,a=t.height,o=t.offset;return Vm($l(Me(Me(Me({},Vr.defaultProps),n),{},{ticks:pt(n,!0),viewBox:{x:0,y:0,width:i,height:a}})),o.left,o.left+o.width,r)},EI=function(t,r){var n=t.yAxis,i=t.width,a=t.height,o=t.offset;return Vm($l(Me(Me(Me({},Vr.defaultProps),n),{},{ticks:pt(n,!0),viewBox:{x:0,y:0,width:i,height:a}})),o.top,o.top+o.height,r)},or={horizontal:!0,vertical:!0,stroke:"#ccc",fill:"none",verticalFill:[],horizontalFill:[]};function TI(e){var t,r,n,i,a,o,u=_l(),c=El(),s=dM(),f=Me(Me({},e),{},{stroke:(t=e.stroke)!==null&&t!==void 0?t:or.stroke,fill:(r=e.fill)!==null&&r!==void 0?r:or.fill,horizontal:(n=e.horizontal)!==null&&n!==void 0?n:or.horizontal,horizontalFill:(i=e.horizontalFill)!==null&&i!==void 0?i:or.horizontalFill,vertical:(a=e.vertical)!==null&&a!==void 0?a:or.vertical,verticalFill:(o=e.verticalFill)!==null&&o!==void 0?o:or.verticalFill,x:B(e.x)?e.x:s.left,y:B(e.y)?e.y:s.top,width:B(e.width)?e.width:s.width,height:B(e.height)?e.height:s.height}),l=f.x,h=f.y,d=f.width,y=f.height,v=f.syncWithTicks,p=f.horizontalValues,b=f.verticalValues,w=fM(),g=hM();if(!B(d)||d<=0||!B(y)||y<=0||!B(l)||l!==+l||!B(h)||h!==+h)return null;var O=f.verticalCoordinatesGenerator||_I,m=f.horizontalCoordinatesGenerator||EI,x=f.horizontalPoints,S=f.verticalPoints;if((!x||!x.length)&&X(m)){var A=p&&p.length,_=m({yAxis:g?Me(Me({},g),{},{ticks:A?p:g.ticks}):void 0,width:u,height:c,offset:s},A?!0:v);et(Array.isArray(_),"horizontalCoordinatesGenerator should return Array but instead it returned [".concat(Qt(_),"]")),Array.isArray(_)&&(x=_)}if((!S||!S.length)&&X(O)){var $=b&&b.length,E=O({xAxis:w?Me(Me({},w),{},{ticks:$?b:w.ticks}):void 0,width:u,height:c,offset:s},$?!0:v);et(Array.isArray(E),"verticalCoordinatesGenerator should return Array but instead it returned [".concat(Qt(E),"]")),Array.isArray(E)&&(S=E)}return P.createElement("g",{className:"recharts-cartesian-grid"},P.createElement(wI,{fill:f.fill,fillOpacity:f.fillOpacity,x:f.x,y:f.y,width:f.width,height:f.height,ry:f.ry}),P.createElement(OI,Ht({},f,{offset:s,horizontalPoints:x,xAxis:w,yAxis:g})),P.createElement(SI,Ht({},f,{offset:s,verticalPoints:S,xAxis:w,yAxis:g})),P.createElement(AI,Ht({},f,{horizontalPoints:x})),P.createElement(PI,Ht({},f,{verticalPoints:S})))}TI.displayName="CartesianGrid";function Rr(e){"@babel/helpers - typeof";return Rr=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Rr(e)}function jI(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function $I(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,Yg(n.key),n)}}function MI(e,t,r){return t&&$I(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e}function II(e,t,r){return t=sa(t),CI(e,Vg()?Reflect.construct(t,r||[],sa(e).constructor):t.apply(e,r))}function CI(e,t){if(t&&(Rr(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return kI(e)}function kI(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function Vg(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(Vg=function(){return!!e})()}function sa(e){return sa=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},sa(e)}function NI(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Es(e,t)}function Es(e,t){return Es=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},Es(e,t)}function Xg(e,t,r){return t=Yg(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function Yg(e){var t=RI(e,"string");return Rr(t)=="symbol"?t:t+""}function RI(e,t){if(Rr(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Rr(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}function Ts(){return Ts=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Ts.apply(this,arguments)}function DI(e){var t=e.xAxisId,r=_l(),n=El(),i=Rg(t);return i==null?null:D.createElement(Vr,Ts({},i,{className:Q("recharts-".concat(i.axisType," ").concat(i.axisType),i.className),viewBox:{x:0,y:0,width:r,height:n},ticksGenerator:function(o){return pt(o,!0)}}))}var Il=function(e){function t(){return jI(this,t),II(this,t,arguments)}return NI(t,e),MI(t,[{key:"render",value:function(){return D.createElement(DI,this.props)}}])}(D.Component);Xg(Il,"displayName","XAxis");Xg(Il,"defaultProps",{allowDecimals:!0,hide:!1,orientation:"bottom",width:0,height:30,mirror:!1,xAxisId:0,tickCount:5,type:"category",padding:{left:0,right:0},allowDataOverflow:!1,scale:"auto",reversed:!1,allowDuplicatedCategory:!0});function Dr(e){"@babel/helpers - typeof";return Dr=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Dr(e)}function BI(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function LI(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,Qg(n.key),n)}}function qI(e,t,r){return t&&LI(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e}function FI(e,t,r){return t=la(t),WI(e,Zg()?Reflect.construct(t,r||[],la(e).constructor):t.apply(e,r))}function WI(e,t){if(t&&(Dr(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return zI(e)}function zI(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function Zg(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(Zg=function(){return!!e})()}function la(e){return la=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},la(e)}function UI(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&js(e,t)}function js(e,t){return js=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},js(e,t)}function Jg(e,t,r){return t=Qg(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function Qg(e){var t=HI(e,"string");return Dr(t)=="symbol"?t:t+""}function HI(e,t){if(Dr(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Dr(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}function $s(){return $s=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},$s.apply(this,arguments)}var KI=function(t){var r=t.yAxisId,n=_l(),i=El(),a=Dg(r);return a==null?null:D.createElement(Vr,$s({},a,{className:Q("recharts-".concat(a.axisType," ").concat(a.axisType),a.className),viewBox:{x:0,y:0,width:n,height:i},ticksGenerator:function(u){return pt(u,!0)}}))},Cl=function(e){function t(){return BI(this,t),FI(this,t,arguments)}return UI(t,e),qI(t,[{key:"render",value:function(){return D.createElement(KI,this.props)}}])}(D.Component);Jg(Cl,"displayName","YAxis");Jg(Cl,"defaultProps",{allowDuplicatedCategory:!0,allowDecimals:!0,hide:!1,orientation:"left",width:60,height:0,mirror:!1,yAxisId:0,tickCount:5,type:"number",padding:{top:0,bottom:0},allowDataOverflow:!1,scale:"auto",reversed:!1});function Wv(e){return YI(e)||XI(e)||VI(e)||GI()}function GI(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function VI(e,t){if(e){if(typeof e=="string")return Ms(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Ms(e,t)}}function XI(e){if(typeof Symbol!="undefined"&&e[Symbol.iterator]!=null||e["@@iterator"]!=null)return Array.from(e)}function YI(e){if(Array.isArray(e))return Ms(e)}function Ms(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}var Is=function(t,r,n,i,a){var o=Ve(t,jl),u=Ve(t,Wa),c=[].concat(Wv(o),Wv(u)),s=Ve(t,Ua),f="".concat(i,"Id"),l=i[0],h=r;if(c.length&&(h=c.reduce(function(v,p){if(p.props[f]===n&&at(p.props,"extendDomain")&&B(p.props[l])){var b=p.props[l];return[Math.min(v[0],b),Math.max(v[1],b)]}return v},h)),s.length){var d="".concat(l,"1"),y="".concat(l,"2");h=s.reduce(function(v,p){if(p.props[f]===n&&at(p.props,"extendDomain")&&B(p.props[d])&&B(p.props[y])){var b=p.props[d],w=p.props[y];return[Math.min(v[0],b,w),Math.max(v[1],b,w)]}return v},h)}return a&&a.length&&(h=a.reduce(function(v,p){return B(p)?[Math.min(v[0],p),Math.max(v[1],p)]:v},h)),h},hc={exports:{}},zv;function ZI(){return zv||(zv=1,function(e){var t=Object.prototype.hasOwnProperty,r="~";function n(){}Object.create&&(n.prototype=Object.create(null),new n().__proto__||(r=!1));function i(c,s,f){this.fn=c,this.context=s,this.once=f||!1}function a(c,s,f,l,h){if(typeof f!="function")throw new TypeError("The listener must be a function");var d=new i(f,l||c,h),y=r?r+s:s;return c._events[y]?c._events[y].fn?c._events[y]=[c._events[y],d]:c._events[y].push(d):(c._events[y]=d,c._eventsCount++),c}function o(c,s){--c._eventsCount===0?c._events=new n:delete c._events[s]}function u(){this._events=new n,this._eventsCount=0}u.prototype.eventNames=function(){var s=[],f,l;if(this._eventsCount===0)return s;for(l in f=this._events)t.call(f,l)&&s.push(r?l.slice(1):l);return Object.getOwnPropertySymbols?s.concat(Object.getOwnPropertySymbols(f)):s},u.prototype.listeners=function(s){var f=r?r+s:s,l=this._events[f];if(!l)return[];if(l.fn)return[l.fn];for(var h=0,d=l.length,y=new Array(d);h<d;h++)y[h]=l[h].fn;return y},u.prototype.listenerCount=function(s){var f=r?r+s:s,l=this._events[f];return l?l.fn?1:l.length:0},u.prototype.emit=function(s,f,l,h,d,y){var v=r?r+s:s;if(!this._events[v])return!1;var p=this._events[v],b=arguments.length,w,g;if(p.fn){switch(p.once&&this.removeListener(s,p.fn,void 0,!0),b){case 1:return p.fn.call(p.context),!0;case 2:return p.fn.call(p.context,f),!0;case 3:return p.fn.call(p.context,f,l),!0;case 4:return p.fn.call(p.context,f,l,h),!0;case 5:return p.fn.call(p.context,f,l,h,d),!0;case 6:return p.fn.call(p.context,f,l,h,d,y),!0}for(g=1,w=new Array(b-1);g<b;g++)w[g-1]=arguments[g];p.fn.apply(p.context,w)}else{var O=p.length,m;for(g=0;g<O;g++)switch(p[g].once&&this.removeListener(s,p[g].fn,void 0,!0),b){case 1:p[g].fn.call(p[g].context);break;case 2:p[g].fn.call(p[g].context,f);break;case 3:p[g].fn.call(p[g].context,f,l);break;case 4:p[g].fn.call(p[g].context,f,l,h);break;default:if(!w)for(m=1,w=new Array(b-1);m<b;m++)w[m-1]=arguments[m];p[g].fn.apply(p[g].context,w)}}return!0},u.prototype.on=function(s,f,l){return a(this,s,f,l,!1)},u.prototype.once=function(s,f,l){return a(this,s,f,l,!0)},u.prototype.removeListener=function(s,f,l,h){var d=r?r+s:s;if(!this._events[d])return this;if(!f)return o(this,d),this;var y=this._events[d];if(y.fn)y.fn===f&&(!h||y.once)&&(!l||y.context===l)&&o(this,d);else{for(var v=0,p=[],b=y.length;v<b;v++)(y[v].fn!==f||h&&!y[v].once||l&&y[v].context!==l)&&p.push(y[v]);p.length?this._events[d]=p.length===1?p[0]:p:o(this,d)}return this},u.prototype.removeAllListeners=function(s){var f;return s?(f=r?r+s:s,this._events[f]&&o(this,f)):(this._events=new n,this._eventsCount=0),this},u.prototype.off=u.prototype.removeListener,u.prototype.addListener=u.prototype.on,u.prefixed=r,u.EventEmitter=u,e.exports=u}(hc)),hc.exports}var JI=ZI();const QI=oe(JI);var pc=new QI,dc="recharts.syncMouseEvents";function Hn(e){"@babel/helpers - typeof";return Hn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Hn(e)}function eC(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function tC(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,eb(n.key),n)}}function rC(e,t,r){return t&&tC(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e}function vc(e,t,r){return t=eb(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function eb(e){var t=nC(e,"string");return Hn(t)=="symbol"?t:t+""}function nC(e,t){if(Hn(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Hn(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}var iC=function(){function e(){eC(this,e),vc(this,"activeIndex",0),vc(this,"coordinateList",[]),vc(this,"layout","horizontal")}return rC(e,[{key:"setDetails",value:function(r){var n,i=r.coordinateList,a=i===void 0?null:i,o=r.container,u=o===void 0?null:o,c=r.layout,s=c===void 0?null:c,f=r.offset,l=f===void 0?null:f,h=r.mouseHandlerCallback,d=h===void 0?null:h;this.coordinateList=(n=a!=null?a:this.coordinateList)!==null&&n!==void 0?n:[],this.container=u!=null?u:this.container,this.layout=s!=null?s:this.layout,this.offset=l!=null?l:this.offset,this.mouseHandlerCallback=d!=null?d:this.mouseHandlerCallback,this.activeIndex=Math.min(Math.max(this.activeIndex,0),this.coordinateList.length-1)}},{key:"focus",value:function(){this.spoofMouse()}},{key:"keyboardEvent",value:function(r){if(this.coordinateList.length!==0)switch(r.key){case"ArrowRight":{if(this.layout!=="horizontal")return;this.activeIndex=Math.min(this.activeIndex+1,this.coordinateList.length-1),this.spoofMouse();break}case"ArrowLeft":{if(this.layout!=="horizontal")return;this.activeIndex=Math.max(this.activeIndex-1,0),this.spoofMouse();break}}}},{key:"setIndex",value:function(r){this.activeIndex=r}},{key:"spoofMouse",value:function(){var r,n;if(this.layout==="horizontal"&&this.coordinateList.length!==0){var i=this.container.getBoundingClientRect(),a=i.x,o=i.y,u=i.height,c=this.coordinateList[this.activeIndex].coordinate,s=((r=window)===null||r===void 0?void 0:r.scrollX)||0,f=((n=window)===null||n===void 0?void 0:n.scrollY)||0,l=a+c+s,h=o+this.offset.top+u/2+f;this.mouseHandlerCallback({pageX:l,pageY:h})}}}])}();function aC(e,t,r){if(r==="number"&&t===!0&&Array.isArray(e)){var n=e==null?void 0:e[0],i=e==null?void 0:e[1];if(n&&i&&B(n)&&B(i))return!0}return!1}function oC(e,t,r,n){var i=n/2;return{stroke:"none",fill:"#ccc",x:e==="horizontal"?t.x-i:r.left+.5,y:e==="horizontal"?r.top+.5:t.y-i,width:e==="horizontal"?n:r.width-1,height:e==="horizontal"?r.height-1:n}}function tb(e){var t=e.cx,r=e.cy,n=e.radius,i=e.startAngle,a=e.endAngle,o=le(t,r,n,i),u=le(t,r,n,a);return{points:[o,u],cx:t,cy:r,radius:n,startAngle:i,endAngle:a}}function uC(e,t,r){var n,i,a,o;if(e==="horizontal")n=t.x,a=n,i=r.top,o=r.top+r.height;else if(e==="vertical")i=t.y,o=i,n=r.left,a=r.left+r.width;else if(t.cx!=null&&t.cy!=null)if(e==="centric"){var u=t.cx,c=t.cy,s=t.innerRadius,f=t.outerRadius,l=t.angle,h=le(u,c,s,l),d=le(u,c,f,l);n=h.x,i=h.y,a=d.x,o=d.y}else return tb(t);return[{x:n,y:i},{x:a,y:o}]}function Kn(e){"@babel/helpers - typeof";return Kn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Kn(e)}function Uv(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function pi(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Uv(Object(r),!0).forEach(function(n){cC(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Uv(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function cC(e,t,r){return t=sC(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function sC(e){var t=lC(e,"string");return Kn(t)=="symbol"?t:t+""}function lC(e,t){if(Kn(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Kn(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function fC(e){var t,r,n=e.element,i=e.tooltipEventType,a=e.isActive,o=e.activeCoordinate,u=e.activePayload,c=e.offset,s=e.activeTooltipIndex,f=e.tooltipAxisBandSize,l=e.layout,h=e.chartName,d=(t=n.props.cursor)!==null&&t!==void 0?t:(r=n.type.defaultProps)===null||r===void 0?void 0:r.cursor;if(!n||!d||!a||!o||h!=="ScatterChart"&&i!=="axis")return null;var y,v=ts;if(h==="ScatterChart")y=o,v=HT;else if(h==="BarChart")y=oC(l,o,c,f),v=wl;else if(l==="radial"){var p=tb(o),b=p.cx,w=p.cy,g=p.radius,O=p.startAngle,m=p.endAngle;y={cx:b,cy:w,startAngle:O,endAngle:m,innerRadius:g,outerRadius:g},v=ag}else y={points:uC(l,o,c)},v=ts;var x=pi(pi(pi(pi({stroke:"#ccc",pointerEvents:"none"},c),y),V(d,!1)),{},{payload:u,payloadIndex:s,className:Q("recharts-tooltip-cursor",d.className)});return D.isValidElement(d)?D.cloneElement(d,x):D.createElement(v,x)}var hC=["item"],pC=["children","className","width","height","style","compact","title","desc"];function Br(e){"@babel/helpers - typeof";return Br=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Br(e)}function fr(){return fr=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},fr.apply(this,arguments)}function Hv(e,t){return yC(e)||vC(e,t)||nb(e,t)||dC()}function dC(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function vC(e,t){var r=e==null?null:typeof Symbol!="undefined"&&e[Symbol.iterator]||e["@@iterator"];if(r!=null){var n,i,a,o,u=[],c=!0,s=!1;try{if(a=(r=r.call(e)).next,t!==0)for(;!(c=(n=a.call(r)).done)&&(u.push(n.value),u.length!==t);c=!0);}catch(f){s=!0,i=f}finally{try{if(!c&&r.return!=null&&(o=r.return(),Object(o)!==o))return}finally{if(s)throw i}}return u}}function yC(e){if(Array.isArray(e))return e}function Kv(e,t){if(e==null)return{};var r=mC(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function mC(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}function gC(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function bC(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,ib(n.key),n)}}function xC(e,t,r){return t&&bC(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e}function wC(e,t,r){return t=fa(t),OC(e,rb()?Reflect.construct(t,r||[],fa(e).constructor):t.apply(e,r))}function OC(e,t){if(t&&(Br(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return SC(e)}function SC(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function rb(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(rb=function(){return!!e})()}function fa(e){return fa=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},fa(e)}function AC(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Cs(e,t)}function Cs(e,t){return Cs=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},Cs(e,t)}function Lr(e){return EC(e)||_C(e)||nb(e)||PC()}function PC(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function nb(e,t){if(e){if(typeof e=="string")return ks(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return ks(e,t)}}function _C(e){if(typeof Symbol!="undefined"&&e[Symbol.iterator]!=null||e["@@iterator"]!=null)return Array.from(e)}function EC(e){if(Array.isArray(e))return ks(e)}function ks(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function Gv(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function M(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Gv(Object(r),!0).forEach(function(n){H(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Gv(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function H(e,t,r){return t=ib(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function ib(e){var t=TC(e,"string");return Br(t)=="symbol"?t:t+""}function TC(e,t){if(Br(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Br(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var jC={xAxis:["bottom","top"],yAxis:["left","right"]},$C={width:"100%",height:"100%"},ab={x:0,y:0};function di(e){return e}var MC=function(t,r){return r==="horizontal"?t.x:r==="vertical"?t.y:r==="centric"?t.angle:t.radius},IC=function(t,r,n,i){var a=r.find(function(f){return f&&f.index===n});if(a){if(t==="horizontal")return{x:a.coordinate,y:i.y};if(t==="vertical")return{x:i.x,y:a.coordinate};if(t==="centric"){var o=a.coordinate,u=i.radius;return M(M(M({},i),le(i.cx,i.cy,u,o)),{},{angle:o,radius:u})}var c=a.coordinate,s=i.angle;return M(M(M({},i),le(i.cx,i.cy,c,s)),{},{angle:s,radius:c})}return ab},Ha=function(t,r){var n=r.graphicalItems,i=r.dataStartIndex,a=r.dataEndIndex,o=(n!=null?n:[]).reduce(function(u,c){var s=c.props.data;return s&&s.length?[].concat(Lr(u),Lr(s)):u},[]);return o.length>0?o:t&&t.length&&B(i)&&B(a)?t.slice(i,a+1):[]};function ob(e){return e==="number"?[0,"auto"]:void 0}var Ns=function(t,r,n,i){var a=t.graphicalItems,o=t.tooltipAxis,u=Ha(r,t);return n<0||!a||!a.length||n>=u.length?null:a.reduce(function(c,s){var f,l=(f=s.props.data)!==null&&f!==void 0?f:r;l&&t.dataStartIndex+t.dataEndIndex!==0&&t.dataEndIndex-t.dataStartIndex>=n&&(l=l.slice(t.dataStartIndex,t.dataEndIndex+1));var h;if(o.dataKey&&!o.allowDuplicatedCategory){var d=l===void 0?u:l;h=yi(d,o.dataKey,i)}else h=l&&l[n]||u[n];return h?[].concat(Lr(c),[Qm(s,h)]):c},[])},Vv=function(t,r,n,i){var a=i||{x:t.chartX,y:t.chartY},o=MC(a,n),u=t.orderedTooltipTicks,c=t.tooltipAxis,s=t.tooltipTicks,f=yP(o,u,s,c);if(f>=0&&s){var l=s[f]&&s[f].value,h=Ns(t,r,f,l),d=IC(n,u,f,a);return{activeTooltipIndex:f,activeLabel:l,activePayload:h,activeCoordinate:d}}return null},CC=function(t,r){var n=r.axes,i=r.graphicalItems,a=r.axisType,o=r.axisIdKey,u=r.stackGroups,c=r.dataStartIndex,s=r.dataEndIndex,f=t.layout,l=t.children,h=t.stackOffset,d=Gm(f,a);return n.reduce(function(y,v){var p,b=v.type.defaultProps!==void 0?M(M({},v.type.defaultProps),v.props):v.props,w=b.type,g=b.dataKey,O=b.allowDataOverflow,m=b.allowDuplicatedCategory,x=b.scale,S=b.ticks,A=b.includeHidden,_=b[o];if(y[_])return y;var $=Ha(t.data,{graphicalItems:i.filter(function(W){var G,fe=o in W.props?W.props[o]:(G=W.type.defaultProps)===null||G===void 0?void 0:G[o];return fe===_}),dataStartIndex:c,dataEndIndex:s}),E=$.length,T,j,C;aC(b.domain,O,w)&&(T=Yc(b.domain,null,O),d&&(w==="number"||x!=="auto")&&(C=cn($,g,"category")));var I=ob(w);if(!T||T.length===0){var k,N=(k=b.domain)!==null&&k!==void 0?k:I;if(g){if(T=cn($,g,w),w==="category"&&d){var L=Wb(T);m&&L?(j=T,T=Qi(0,E)):m||(T=Yp(N,T,v).reduce(function(W,G){return W.indexOf(G)>=0?W:[].concat(Lr(W),[G])},[]))}else if(w==="category")m?T=T.filter(function(W){return W!==""&&!Z(W)}):T=Yp(N,T,v).reduce(function(W,G){return W.indexOf(G)>=0||G===""||Z(G)?W:[].concat(Lr(W),[G])},[]);else if(w==="number"){var q=wP($,i.filter(function(W){var G,fe,ye=o in W.props?W.props[o]:(G=W.type.defaultProps)===null||G===void 0?void 0:G[o],Be="hide"in W.props?W.props.hide:(fe=W.type.defaultProps)===null||fe===void 0?void 0:fe.hide;return ye===_&&(A||!Be)}),g,a,f);q&&(T=q)}d&&(w==="number"||x!=="auto")&&(C=cn($,g,"category"))}else d?T=Qi(0,E):u&&u[_]&&u[_].hasStack&&w==="number"?T=h==="expand"?[0,1]:Jm(u[_].stackGroups,c,s):T=Km($,i.filter(function(W){var G=o in W.props?W.props[o]:W.type.defaultProps[o],fe="hide"in W.props?W.props.hide:W.type.defaultProps.hide;return G===_&&(A||!fe)}),w,f,!0);if(w==="number")T=Is(l,T,_,a,S),N&&(T=Yc(N,T,O));else if(w==="category"&&N){var U=N,K=T.every(function(W){return U.indexOf(W)>=0});K&&(T=U)}}return M(M({},y),{},H({},_,M(M({},b),{},{axisType:a,domain:T,categoricalDomain:C,duplicateDomain:j,originalDomain:(p=b.domain)!==null&&p!==void 0?p:I,isCategorical:d,layout:f})))},{})},kC=function(t,r){var n=r.graphicalItems,i=r.Axis,a=r.axisType,o=r.axisIdKey,u=r.stackGroups,c=r.dataStartIndex,s=r.dataEndIndex,f=t.layout,l=t.children,h=Ha(t.data,{graphicalItems:n,dataStartIndex:c,dataEndIndex:s}),d=h.length,y=Gm(f,a),v=-1;return n.reduce(function(p,b){var w=b.type.defaultProps!==void 0?M(M({},b.type.defaultProps),b.props):b.props,g=w[o],O=ob("number");if(!p[g]){v++;var m;return y?m=Qi(0,d):u&&u[g]&&u[g].hasStack?(m=Jm(u[g].stackGroups,c,s),m=Is(l,m,g,a)):(m=Yc(O,Km(h,n.filter(function(x){var S,A,_=o in x.props?x.props[o]:(S=x.type.defaultProps)===null||S===void 0?void 0:S[o],$="hide"in x.props?x.props.hide:(A=x.type.defaultProps)===null||A===void 0?void 0:A.hide;return _===g&&!$}),"number",f),i.defaultProps.allowDataOverflow),m=Is(l,m,g,a)),M(M({},p),{},H({},g,M(M({axisType:a},i.defaultProps),{},{hide:!0,orientation:We(jC,"".concat(a,".").concat(v%2),null),domain:m,originalDomain:O,isCategorical:y,layout:f})))}return p},{})},NC=function(t,r){var n=r.axisType,i=n===void 0?"xAxis":n,a=r.AxisComp,o=r.graphicalItems,u=r.stackGroups,c=r.dataStartIndex,s=r.dataEndIndex,f=t.children,l="".concat(i,"Id"),h=Ve(f,a),d={};return h&&h.length?d=CC(t,{axes:h,graphicalItems:o,axisType:i,axisIdKey:l,stackGroups:u,dataStartIndex:c,dataEndIndex:s}):o&&o.length&&(d=kC(t,{Axis:a,graphicalItems:o,axisType:i,axisIdKey:l,stackGroups:u,dataStartIndex:c,dataEndIndex:s})),d},RC=function(t){var r=Pt(t),n=pt(r,!1,!0);return{tooltipTicks:n,orderedTooltipTicks:Gs(n,function(i){return i.coordinate}),tooltipAxis:r,tooltipAxisBandSize:Fi(r,n)}},Xv=function(t){var r=t.children,n=t.defaultShowTooltip,i=qe(r,$r),a=0,o=0;return t.data&&t.data.length!==0&&(o=t.data.length-1),i&&i.props&&(i.props.startIndex>=0&&(a=i.props.startIndex),i.props.endIndex>=0&&(o=i.props.endIndex)),{chartX:0,chartY:0,dataStartIndex:a,dataEndIndex:o,activeTooltipIndex:-1,isTooltipActive:!!n}},DC=function(t){return!t||!t.length?!1:t.some(function(r){var n=dt(r&&r.type);return n&&n.indexOf("Bar")>=0})},Yv=function(t){return t==="horizontal"?{numericAxisName:"yAxis",cateAxisName:"xAxis"}:t==="vertical"?{numericAxisName:"xAxis",cateAxisName:"yAxis"}:t==="centric"?{numericAxisName:"radiusAxis",cateAxisName:"angleAxis"}:{numericAxisName:"angleAxis",cateAxisName:"radiusAxis"}},BC=function(t,r){var n=t.props,i=t.graphicalItems,a=t.xAxisMap,o=a===void 0?{}:a,u=t.yAxisMap,c=u===void 0?{}:u,s=n.width,f=n.height,l=n.children,h=n.margin||{},d=qe(l,$r),y=qe(l,pr),v=Object.keys(c).reduce(function(m,x){var S=c[x],A=S.orientation;return!S.mirror&&!S.hide?M(M({},m),{},H({},A,m[A]+S.width)):m},{left:h.left||0,right:h.right||0}),p=Object.keys(o).reduce(function(m,x){var S=o[x],A=S.orientation;return!S.mirror&&!S.hide?M(M({},m),{},H({},A,We(m,"".concat(A))+S.height)):m},{top:h.top||0,bottom:h.bottom||0}),b=M(M({},p),v),w=b.bottom;d&&(b.bottom+=d.props.height||$r.defaultProps.height),y&&r&&(b=bP(b,i,n,r));var g=s-b.left-b.right,O=f-b.top-b.bottom;return M(M({brushBottom:w},b),{},{width:Math.max(g,0),height:Math.max(O,0)})},LC=function(t,r){if(r==="xAxis")return t[r].width;if(r==="yAxis")return t[r].height},ub=function(t){var r=t.chartName,n=t.GraphicalChild,i=t.defaultTooltipEventType,a=i===void 0?"axis":i,o=t.validateTooltipEventTypes,u=o===void 0?["axis"]:o,c=t.axisComponents,s=t.legendContent,f=t.formatAxisMap,l=t.defaultProps,h=function(b,w){var g=w.graphicalItems,O=w.stackGroups,m=w.offset,x=w.updateId,S=w.dataStartIndex,A=w.dataEndIndex,_=b.barSize,$=b.layout,E=b.barGap,T=b.barCategoryGap,j=b.maxBarSize,C=Yv($),I=C.numericAxisName,k=C.cateAxisName,N=DC(g),L=[];return g.forEach(function(q,U){var K=Ha(b.data,{graphicalItems:[q],dataStartIndex:S,dataEndIndex:A}),W=q.type.defaultProps!==void 0?M(M({},q.type.defaultProps),q.props):q.props,G=W.dataKey,fe=W.maxBarSize,ye=W["".concat(I,"Id")],Be=W["".concat(k,"Id")],Ct={},Ne=c.reduce(function(kt,Nt){var Ka=w["".concat(Nt.axisType,"Map")],kl=W["".concat(Nt.axisType,"Id")];Ka&&Ka[kl]||Nt.axisType==="zAxis"||Jt();var Nl=Ka[kl];return M(M({},kt),{},H(H({},Nt.axisType,Nl),"".concat(Nt.axisType,"Ticks"),pt(Nl)))},Ct),F=Ne[k],Y=Ne["".concat(k,"Ticks")],J=O&&O[ye]&&O[ye].hasStack&&$P(q,O[ye].stackGroups),R=dt(q.type).indexOf("Bar")>=0,de=Fi(F,Y),ee=[],be=N&&mP({barSize:_,stackGroups:O,totalSize:LC(Ne,k)});if(R){var xe,Re,Ot=Z(fe)?j:fe,nr=(xe=(Re=Fi(F,Y,!0))!==null&&Re!==void 0?Re:Ot)!==null&&xe!==void 0?xe:0;ee=gP({barGap:E,barCategoryGap:T,bandSize:nr!==de?nr:de,sizeList:be[Be],maxBarSize:Ot}),nr!==de&&(ee=ee.map(function(kt){return M(M({},kt),{},{position:M(M({},kt.position),{},{offset:kt.position.offset-nr/2})})}))}var ti=q&&q.type&&q.type.getComposedData;ti&&L.push({props:M(M({},ti(M(M({},Ne),{},{displayedData:K,props:b,dataKey:G,item:q,bandSize:de,barPosition:ee,offset:m,stackedData:J,layout:$,dataStartIndex:S,dataEndIndex:A}))),{},H(H(H({key:q.key||"item-".concat(U)},I,Ne[I]),k,Ne[k]),"animationId",x)),childIndex:Qb(q,b.children),item:q})}),L},d=function(b,w){var g=b.props,O=b.dataStartIndex,m=b.dataEndIndex,x=b.updateId;if(!of({props:g}))return null;var S=g.children,A=g.layout,_=g.stackOffset,$=g.data,E=g.reverseStackOrder,T=Yv(A),j=T.numericAxisName,C=T.cateAxisName,I=Ve(S,n),k=TP($,I,"".concat(j,"Id"),"".concat(C,"Id"),_,E),N=c.reduce(function(W,G){var fe="".concat(G.axisType,"Map");return M(M({},W),{},H({},fe,NC(g,M(M({},G),{},{graphicalItems:I,stackGroups:G.axisType===j&&k,dataStartIndex:O,dataEndIndex:m}))))},{}),L=BC(M(M({},N),{},{props:g,graphicalItems:I}),w==null?void 0:w.legendBBox);Object.keys(N).forEach(function(W){N[W]=f(g,N[W],L,W.replace("Map",""),r)});var q=N["".concat(C,"Map")],U=RC(q),K=h(g,M(M({},N),{},{dataStartIndex:O,dataEndIndex:m,updateId:x,graphicalItems:I,stackGroups:k,offset:L}));return M(M({formattedGraphicalItems:K,graphicalItems:I,offset:L,stackGroups:k},U),N)},y=function(p){function b(w){var g,O,m;return gC(this,b),m=wC(this,b,[w]),H(m,"eventEmitterSymbol",Symbol("rechartsEventEmitter")),H(m,"accessibilityManager",new iC),H(m,"handleLegendBBoxUpdate",function(x){if(x){var S=m.state,A=S.dataStartIndex,_=S.dataEndIndex,$=S.updateId;m.setState(M({legendBBox:x},d({props:m.props,dataStartIndex:A,dataEndIndex:_,updateId:$},M(M({},m.state),{},{legendBBox:x}))))}}),H(m,"handleReceiveSyncEvent",function(x,S,A){if(m.props.syncId===x){if(A===m.eventEmitterSymbol&&typeof m.props.syncMethod!="function")return;m.applySyncEvent(S)}}),H(m,"handleBrushChange",function(x){var S=x.startIndex,A=x.endIndex;if(S!==m.state.dataStartIndex||A!==m.state.dataEndIndex){var _=m.state.updateId;m.setState(function(){return M({dataStartIndex:S,dataEndIndex:A},d({props:m.props,dataStartIndex:S,dataEndIndex:A,updateId:_},m.state))}),m.triggerSyncEvent({dataStartIndex:S,dataEndIndex:A})}}),H(m,"handleMouseEnter",function(x){var S=m.getMouseInfo(x);if(S){var A=M(M({},S),{},{isTooltipActive:!0});m.setState(A),m.triggerSyncEvent(A);var _=m.props.onMouseEnter;X(_)&&_(A,x)}}),H(m,"triggeredAfterMouseMove",function(x){var S=m.getMouseInfo(x),A=S?M(M({},S),{},{isTooltipActive:!0}):{isTooltipActive:!1};m.setState(A),m.triggerSyncEvent(A);var _=m.props.onMouseMove;X(_)&&_(A,x)}),H(m,"handleItemMouseEnter",function(x){m.setState(function(){return{isTooltipActive:!0,activeItem:x,activePayload:x.tooltipPayload,activeCoordinate:x.tooltipPosition||{x:x.cx,y:x.cy}}})}),H(m,"handleItemMouseLeave",function(){m.setState(function(){return{isTooltipActive:!1}})}),H(m,"handleMouseMove",function(x){x.persist(),m.throttleTriggeredAfterMouseMove(x)}),H(m,"handleMouseLeave",function(x){m.throttleTriggeredAfterMouseMove.cancel();var S={isTooltipActive:!1};m.setState(S),m.triggerSyncEvent(S);var A=m.props.onMouseLeave;X(A)&&A(S,x)}),H(m,"handleOuterEvent",function(x){var S=Jb(x),A=We(m.props,"".concat(S));if(S&&X(A)){var _,$;/.*touch.*/i.test(S)?$=m.getMouseInfo(x.changedTouches[0]):$=m.getMouseInfo(x),A((_=$)!==null&&_!==void 0?_:{},x)}}),H(m,"handleClick",function(x){var S=m.getMouseInfo(x);if(S){var A=M(M({},S),{},{isTooltipActive:!0});m.setState(A),m.triggerSyncEvent(A);var _=m.props.onClick;X(_)&&_(A,x)}}),H(m,"handleMouseDown",function(x){var S=m.props.onMouseDown;if(X(S)){var A=m.getMouseInfo(x);S(A,x)}}),H(m,"handleMouseUp",function(x){var S=m.props.onMouseUp;if(X(S)){var A=m.getMouseInfo(x);S(A,x)}}),H(m,"handleTouchMove",function(x){x.changedTouches!=null&&x.changedTouches.length>0&&m.throttleTriggeredAfterMouseMove(x.changedTouches[0])}),H(m,"handleTouchStart",function(x){x.changedTouches!=null&&x.changedTouches.length>0&&m.handleMouseDown(x.changedTouches[0])}),H(m,"handleTouchEnd",function(x){x.changedTouches!=null&&x.changedTouches.length>0&&m.handleMouseUp(x.changedTouches[0])}),H(m,"handleDoubleClick",function(x){var S=m.props.onDoubleClick;if(X(S)){var A=m.getMouseInfo(x);S(A,x)}}),H(m,"handleContextMenu",function(x){var S=m.props.onContextMenu;if(X(S)){var A=m.getMouseInfo(x);S(A,x)}}),H(m,"triggerSyncEvent",function(x){m.props.syncId!==void 0&&pc.emit(dc,m.props.syncId,x,m.eventEmitterSymbol)}),H(m,"applySyncEvent",function(x){var S=m.props,A=S.layout,_=S.syncMethod,$=m.state.updateId,E=x.dataStartIndex,T=x.dataEndIndex;if(x.dataStartIndex!==void 0||x.dataEndIndex!==void 0)m.setState(M({dataStartIndex:E,dataEndIndex:T},d({props:m.props,dataStartIndex:E,dataEndIndex:T,updateId:$},m.state)));else if(x.activeTooltipIndex!==void 0){var j=x.chartX,C=x.chartY,I=x.activeTooltipIndex,k=m.state,N=k.offset,L=k.tooltipTicks;if(!N)return;if(typeof _=="function")I=_(L,x);else if(_==="value"){I=-1;for(var q=0;q<L.length;q++)if(L[q].value===x.activeLabel){I=q;break}}var U=M(M({},N),{},{x:N.left,y:N.top}),K=Math.min(j,U.x+U.width),W=Math.min(C,U.y+U.height),G=L[I]&&L[I].value,fe=Ns(m.state,m.props.data,I),ye=L[I]?{x:A==="horizontal"?L[I].coordinate:K,y:A==="horizontal"?W:L[I].coordinate}:ab;m.setState(M(M({},x),{},{activeLabel:G,activeCoordinate:ye,activePayload:fe,activeTooltipIndex:I}))}else m.setState(x)}),H(m,"renderCursor",function(x){var S,A=m.state,_=A.isTooltipActive,$=A.activeCoordinate,E=A.activePayload,T=A.offset,j=A.activeTooltipIndex,C=A.tooltipAxisBandSize,I=m.getTooltipEventType(),k=(S=x.props.active)!==null&&S!==void 0?S:_,N=m.props.layout,L=x.key||"_recharts-cursor";return P.createElement(fC,{key:L,activeCoordinate:$,activePayload:E,activeTooltipIndex:j,chartName:r,element:x,isActive:k,layout:N,offset:T,tooltipAxisBandSize:C,tooltipEventType:I})}),H(m,"renderPolarAxis",function(x,S,A){var _=We(x,"type.axisType"),$=We(m.state,"".concat(_,"Map")),E=x.type.defaultProps,T=E!==void 0?M(M({},E),x.props):x.props,j=$&&$[T["".concat(_,"Id")]];return D.cloneElement(x,M(M({},j),{},{className:Q(_,j.className),key:x.key||"".concat(S,"-").concat(A),ticks:pt(j,!0)}))}),H(m,"renderPolarGrid",function(x){var S=x.props,A=S.radialLines,_=S.polarAngles,$=S.polarRadius,E=m.state,T=E.radiusAxisMap,j=E.angleAxisMap,C=Pt(T),I=Pt(j),k=I.cx,N=I.cy,L=I.innerRadius,q=I.outerRadius;return D.cloneElement(x,{polarAngles:Array.isArray(_)?_:pt(I,!0).map(function(U){return U.coordinate}),polarRadius:Array.isArray($)?$:pt(C,!0).map(function(U){return U.coordinate}),cx:k,cy:N,innerRadius:L,outerRadius:q,key:x.key||"polar-grid",radialLines:A})}),H(m,"renderLegend",function(){var x=m.state.formattedGraphicalItems,S=m.props,A=S.children,_=S.width,$=S.height,E=m.props.margin||{},T=_-(E.left||0)-(E.right||0),j=Um({children:A,formattedGraphicalItems:x,legendWidth:T,legendContent:s});if(!j)return null;var C=j.item,I=Kv(j,hC);return D.cloneElement(C,M(M({},I),{},{chartWidth:_,chartHeight:$,margin:E,onBBoxUpdate:m.handleLegendBBoxUpdate}))}),H(m,"renderTooltip",function(){var x,S=m.props,A=S.children,_=S.accessibilityLayer,$=qe(A,st);if(!$)return null;var E=m.state,T=E.isTooltipActive,j=E.activeCoordinate,C=E.activePayload,I=E.activeLabel,k=E.offset,N=(x=$.props.active)!==null&&x!==void 0?x:T;return D.cloneElement($,{viewBox:M(M({},k),{},{x:k.left,y:k.top}),active:N,label:I,payload:N?C:[],coordinate:j,accessibilityLayer:_})}),H(m,"renderBrush",function(x){var S=m.props,A=S.margin,_=S.data,$=m.state,E=$.offset,T=$.dataStartIndex,j=$.dataEndIndex,C=$.updateId;return D.cloneElement(x,{key:x.key||"_recharts-brush",onChange:si(m.handleBrushChange,x.props.onChange),data:_,x:B(x.props.x)?x.props.x:E.left,y:B(x.props.y)?x.props.y:E.top+E.height+E.brushBottom-(A.bottom||0),width:B(x.props.width)?x.props.width:E.width,startIndex:T,endIndex:j,updateId:"brush-".concat(C)})}),H(m,"renderReferenceElement",function(x,S,A){if(!x)return null;var _=m,$=_.clipPathId,E=m.state,T=E.xAxisMap,j=E.yAxisMap,C=E.offset,I=x.type.defaultProps||{},k=x.props,N=k.xAxisId,L=N===void 0?I.xAxisId:N,q=k.yAxisId,U=q===void 0?I.yAxisId:q;return D.cloneElement(x,{key:x.key||"".concat(S,"-").concat(A),xAxis:T[L],yAxis:j[U],viewBox:{x:C.left,y:C.top,width:C.width,height:C.height},clipPathId:$})}),H(m,"renderActivePoints",function(x){var S=x.item,A=x.activePoint,_=x.basePoint,$=x.childIndex,E=x.isRange,T=[],j=S.props.key,C=S.item.type.defaultProps!==void 0?M(M({},S.item.type.defaultProps),S.item.props):S.item.props,I=C.activeDot,k=C.dataKey,N=M(M({index:$,dataKey:k,cx:A.x,cy:A.y,r:4,fill:xl(S.item),strokeWidth:2,stroke:"#fff",payload:A.payload,value:A.value},V(I,!1)),mi(I));return T.push(b.renderActiveDot(I,N,"".concat(j,"-activePoint-").concat($))),_?T.push(b.renderActiveDot(I,M(M({},N),{},{cx:_.x,cy:_.y}),"".concat(j,"-basePoint-").concat($))):E&&T.push(null),T}),H(m,"renderGraphicChild",function(x,S,A){var _=m.filterFormatItem(x,S,A);if(!_)return null;var $=m.getTooltipEventType(),E=m.state,T=E.isTooltipActive,j=E.tooltipAxis,C=E.activeTooltipIndex,I=E.activeLabel,k=m.props.children,N=qe(k,st),L=_.props,q=L.points,U=L.isRange,K=L.baseLine,W=_.item.type.defaultProps!==void 0?M(M({},_.item.type.defaultProps),_.item.props):_.item.props,G=W.activeDot,fe=W.hide,ye=W.activeBar,Be=W.activeShape,Ct=!!(!fe&&T&&N&&(G||ye||Be)),Ne={};$!=="axis"&&N&&N.props.trigger==="click"?Ne={onClick:si(m.handleItemMouseEnter,x.props.onClick)}:$!=="axis"&&(Ne={onMouseLeave:si(m.handleItemMouseLeave,x.props.onMouseLeave),onMouseEnter:si(m.handleItemMouseEnter,x.props.onMouseEnter)});var F=D.cloneElement(x,M(M({},_.props),Ne));function Y(Nt){return typeof j.dataKey=="function"?j.dataKey(Nt.payload):null}if(Ct)if(C>=0){var J,R;if(j.dataKey&&!j.allowDuplicatedCategory){var de=typeof j.dataKey=="function"?Y:"payload.".concat(j.dataKey.toString());J=yi(q,de,I),R=U&&K&&yi(K,de,I)}else J=q==null?void 0:q[C],R=U&&K&&K[C];if(Be||ye){var ee=x.props.activeIndex!==void 0?x.props.activeIndex:C;return[D.cloneElement(x,M(M(M({},_.props),Ne),{},{activeIndex:ee})),null,null]}if(!Z(J))return[F].concat(Lr(m.renderActivePoints({item:_,activePoint:J,basePoint:R,childIndex:C,isRange:U})))}else{var be,xe=(be=m.getItemByXY(m.state.activeCoordinate))!==null&&be!==void 0?be:{graphicalItem:F},Re=xe.graphicalItem,Ot=Re.item,nr=Ot===void 0?x:Ot,ti=Re.childIndex,kt=M(M(M({},_.props),Ne),{},{activeIndex:ti});return[D.cloneElement(nr,kt),null,null]}return U?[F,null,null]:[F,null]}),H(m,"renderCustomized",function(x,S,A){return D.cloneElement(x,M(M({key:"recharts-customized-".concat(A)},m.props),m.state))}),H(m,"renderMap",{CartesianGrid:{handler:di,once:!0},ReferenceArea:{handler:m.renderReferenceElement},ReferenceLine:{handler:di},ReferenceDot:{handler:m.renderReferenceElement},XAxis:{handler:di},YAxis:{handler:di},Brush:{handler:m.renderBrush,once:!0},Bar:{handler:m.renderGraphicChild},Line:{handler:m.renderGraphicChild},Area:{handler:m.renderGraphicChild},Radar:{handler:m.renderGraphicChild},RadialBar:{handler:m.renderGraphicChild},Scatter:{handler:m.renderGraphicChild},Pie:{handler:m.renderGraphicChild},Funnel:{handler:m.renderGraphicChild},Tooltip:{handler:m.renderCursor,once:!0},PolarGrid:{handler:m.renderPolarGrid,once:!0},PolarAngleAxis:{handler:m.renderPolarAxis},PolarRadiusAxis:{handler:m.renderPolarAxis},Customized:{handler:m.renderCustomized}}),m.clipPathId="".concat((g=w.id)!==null&&g!==void 0?g:Yn("recharts"),"-clip"),m.throttleTriggeredAfterMouseMove=zy(m.triggeredAfterMouseMove,(O=w.throttleDelay)!==null&&O!==void 0?O:1e3/60),m.state={},m}return AC(b,p),xC(b,[{key:"componentDidMount",value:function(){var g,O;this.addListener(),this.accessibilityManager.setDetails({container:this.container,offset:{left:(g=this.props.margin.left)!==null&&g!==void 0?g:0,top:(O=this.props.margin.top)!==null&&O!==void 0?O:0},coordinateList:this.state.tooltipTicks,mouseHandlerCallback:this.triggeredAfterMouseMove,layout:this.props.layout}),this.displayDefaultTooltip()}},{key:"displayDefaultTooltip",value:function(){var g=this.props,O=g.children,m=g.data,x=g.height,S=g.layout,A=qe(O,st);if(A){var _=A.props.defaultIndex;if(!(typeof _!="number"||_<0||_>this.state.tooltipTicks.length-1)){var $=this.state.tooltipTicks[_]&&this.state.tooltipTicks[_].value,E=Ns(this.state,m,_,$),T=this.state.tooltipTicks[_].coordinate,j=(this.state.offset.top+x)/2,C=S==="horizontal",I=C?{x:T,y:j}:{y:T,x:j},k=this.state.formattedGraphicalItems.find(function(L){var q=L.item;return q.type.name==="Scatter"});k&&(I=M(M({},I),k.props.points[_].tooltipPosition),E=k.props.points[_].tooltipPayload);var N={activeTooltipIndex:_,isTooltipActive:!0,activeLabel:$,activePayload:E,activeCoordinate:I};this.setState(N),this.renderCursor(A),this.accessibilityManager.setIndex(_)}}}},{key:"getSnapshotBeforeUpdate",value:function(g,O){if(!this.props.accessibilityLayer)return null;if(this.state.tooltipTicks!==O.tooltipTicks&&this.accessibilityManager.setDetails({coordinateList:this.state.tooltipTicks}),this.props.layout!==g.layout&&this.accessibilityManager.setDetails({layout:this.props.layout}),this.props.margin!==g.margin){var m,x;this.accessibilityManager.setDetails({offset:{left:(m=this.props.margin.left)!==null&&m!==void 0?m:0,top:(x=this.props.margin.top)!==null&&x!==void 0?x:0}})}return null}},{key:"componentDidUpdate",value:function(g){gc([qe(g.children,st)],[qe(this.props.children,st)])||this.displayDefaultTooltip()}},{key:"componentWillUnmount",value:function(){this.removeListener(),this.throttleTriggeredAfterMouseMove.cancel()}},{key:"getTooltipEventType",value:function(){var g=qe(this.props.children,st);if(g&&typeof g.props.shared=="boolean"){var O=g.props.shared?"axis":"item";return u.indexOf(O)>=0?O:a}return a}},{key:"getMouseInfo",value:function(g){if(!this.container)return null;var O=this.container,m=O.getBoundingClientRect(),x=sO(m),S={chartX:Math.round(g.pageX-x.left),chartY:Math.round(g.pageY-x.top)},A=m.width/O.offsetWidth||1,_=this.inRange(S.chartX,S.chartY,A);if(!_)return null;var $=this.state,E=$.xAxisMap,T=$.yAxisMap,j=this.getTooltipEventType(),C=Vv(this.state,this.props.data,this.props.layout,_);if(j!=="axis"&&E&&T){var I=Pt(E).scale,k=Pt(T).scale,N=I&&I.invert?I.invert(S.chartX):null,L=k&&k.invert?k.invert(S.chartY):null;return M(M({},S),{},{xValue:N,yValue:L},C)}return C?M(M({},S),C):null}},{key:"inRange",value:function(g,O){var m=arguments.length>2&&arguments[2]!==void 0?arguments[2]:1,x=this.props.layout,S=g/m,A=O/m;if(x==="horizontal"||x==="vertical"){var _=this.state.offset,$=S>=_.left&&S<=_.left+_.width&&A>=_.top&&A<=_.top+_.height;return $?{x:S,y:A}:null}var E=this.state,T=E.angleAxisMap,j=E.radiusAxisMap;if(T&&j){var C=Pt(T);return Qp({x:S,y:A},C)}return null}},{key:"parseEventsOfWrapper",value:function(){var g=this.props.children,O=this.getTooltipEventType(),m=qe(g,st),x={};m&&O==="axis"&&(m.props.trigger==="click"?x={onClick:this.handleClick}:x={onMouseEnter:this.handleMouseEnter,onDoubleClick:this.handleDoubleClick,onMouseMove:this.handleMouseMove,onMouseLeave:this.handleMouseLeave,onTouchMove:this.handleTouchMove,onTouchStart:this.handleTouchStart,onTouchEnd:this.handleTouchEnd,onContextMenu:this.handleContextMenu});var S=mi(this.props,this.handleOuterEvent);return M(M({},S),x)}},{key:"addListener",value:function(){pc.on(dc,this.handleReceiveSyncEvent)}},{key:"removeListener",value:function(){pc.removeListener(dc,this.handleReceiveSyncEvent)}},{key:"filterFormatItem",value:function(g,O,m){for(var x=this.state.formattedGraphicalItems,S=0,A=x.length;S<A;S++){var _=x[S];if(_.item===g||_.props.key===g.key||O===dt(_.item.type)&&m===_.childIndex)return _}return null}},{key:"renderClipPath",value:function(){var g=this.clipPathId,O=this.state.offset,m=O.left,x=O.top,S=O.height,A=O.width;return P.createElement("defs",null,P.createElement("clipPath",{id:g},P.createElement("rect",{x:m,y:x,height:S,width:A})))}},{key:"getXScales",value:function(){var g=this.state.xAxisMap;return g?Object.entries(g).reduce(function(O,m){var x=Hv(m,2),S=x[0],A=x[1];return M(M({},O),{},H({},S,A.scale))},{}):null}},{key:"getYScales",value:function(){var g=this.state.yAxisMap;return g?Object.entries(g).reduce(function(O,m){var x=Hv(m,2),S=x[0],A=x[1];return M(M({},O),{},H({},S,A.scale))},{}):null}},{key:"getXScaleByAxisId",value:function(g){var O;return(O=this.state.xAxisMap)===null||O===void 0||(O=O[g])===null||O===void 0?void 0:O.scale}},{key:"getYScaleByAxisId",value:function(g){var O;return(O=this.state.yAxisMap)===null||O===void 0||(O=O[g])===null||O===void 0?void 0:O.scale}},{key:"getItemByXY",value:function(g){var O=this.state,m=O.formattedGraphicalItems,x=O.activeItem;if(m&&m.length)for(var S=0,A=m.length;S<A;S++){var _=m[S],$=_.props,E=_.item,T=E.type.defaultProps!==void 0?M(M({},E.type.defaultProps),E.props):E.props,j=dt(E.type);if(j==="Bar"){var C=($.data||[]).find(function(L){return PT(g,L)});if(C)return{graphicalItem:_,payload:C}}else if(j==="RadialBar"){var I=($.data||[]).find(function(L){return Qp(g,L)});if(I)return{graphicalItem:_,payload:I}}else if(Ba(_,x)||La(_,x)||qn(_,x)){var k=Vj({graphicalItem:_,activeTooltipItem:x,itemData:T.data}),N=T.activeIndex===void 0?k:T.activeIndex;return{graphicalItem:M(M({},_),{},{childIndex:N}),payload:qn(_,x)?T.data[k]:_.props.data[k]}}}return null}},{key:"render",value:function(){var g=this;if(!of(this))return null;var O=this.props,m=O.children,x=O.className,S=O.width,A=O.height,_=O.style,$=O.compact,E=O.title,T=O.desc,j=Kv(O,pC),C=V(j,!1);if($)return P.createElement(jv,{state:this.state,width:this.props.width,height:this.props.height,clipPathId:this.clipPathId},P.createElement(xc,fr({},C,{width:S,height:A,title:E,desc:T}),this.renderClipPath(),cf(m,this.renderMap)));if(this.props.accessibilityLayer){var I,k;C.tabIndex=(I=this.props.tabIndex)!==null&&I!==void 0?I:0,C.role=(k=this.props.role)!==null&&k!==void 0?k:"application",C.onKeyDown=function(L){g.accessibilityManager.keyboardEvent(L)},C.onFocus=function(){g.accessibilityManager.focus()}}var N=this.parseEventsOfWrapper();return P.createElement(jv,{state:this.state,width:this.props.width,height:this.props.height,clipPathId:this.clipPathId},P.createElement("div",fr({className:Q("recharts-wrapper",x),style:M({position:"relative",cursor:"default",width:S,height:A},_)},N,{ref:function(q){g.container=q}}),P.createElement(xc,fr({},C,{width:S,height:A,title:E,desc:T,style:$C}),this.renderClipPath(),cf(m,this.renderMap)),this.renderLegend(),this.renderTooltip()))}}])}(D.Component);H(y,"displayName",r),H(y,"defaultProps",M({layout:"horizontal",stackOffset:"none",barCategoryGap:"10%",barGap:4,margin:{top:5,right:5,bottom:5,left:5},reverseStackOrder:!1,syncMethod:"index"},l)),H(y,"getDerivedStateFromProps",function(p,b){var w=p.dataKey,g=p.data,O=p.children,m=p.width,x=p.height,S=p.layout,A=p.stackOffset,_=p.margin,$=b.dataStartIndex,E=b.dataEndIndex;if(b.updateId===void 0){var T=Xv(p);return M(M(M({},T),{},{updateId:0},d(M(M({props:p},T),{},{updateId:0}),b)),{},{prevDataKey:w,prevData:g,prevWidth:m,prevHeight:x,prevLayout:S,prevStackOffset:A,prevMargin:_,prevChildren:O})}if(w!==b.prevDataKey||g!==b.prevData||m!==b.prevWidth||x!==b.prevHeight||S!==b.prevLayout||A!==b.prevStackOffset||!hr(_,b.prevMargin)){var j=Xv(p),C={chartX:b.chartX,chartY:b.chartY,isTooltipActive:b.isTooltipActive},I=M(M({},Vv(b,g,S)),{},{updateId:b.updateId+1}),k=M(M(M({},j),C),I);return M(M(M({},k),d(M({props:p},k),b)),{},{prevDataKey:w,prevData:g,prevWidth:m,prevHeight:x,prevLayout:S,prevStackOffset:A,prevMargin:_,prevChildren:O})}if(!gc(O,b.prevChildren)){var N,L,q,U,K=qe(O,$r),W=K&&(N=(L=K.props)===null||L===void 0?void 0:L.startIndex)!==null&&N!==void 0?N:$,G=K&&(q=(U=K.props)===null||U===void 0?void 0:U.endIndex)!==null&&q!==void 0?q:E,fe=W!==$||G!==E,ye=!Z(g),Be=ye&&!fe?b.updateId:b.updateId+1;return M(M({updateId:Be},d(M(M({props:p},b),{},{updateId:Be,dataStartIndex:W,dataEndIndex:G}),b)),{},{prevChildren:O,dataStartIndex:W,dataEndIndex:G})}return null}),H(y,"renderActiveDot",function(p,b,w){var g;return D.isValidElement(p)?g=D.cloneElement(p,b):X(p)?g=p(b):g=P.createElement(Ol,b),P.createElement(ie,{className:"recharts-active-dot",key:w},g)});var v=D.forwardRef(function(b,w){return P.createElement(y,fr({},b,{ref:w}))});return v.displayName=y.displayName,v},VC=ub({chartName:"BarChart",GraphicalChild:Gr,defaultTooltipEventType:"axis",validateTooltipEventTypes:["axis","item"],axisComponents:[{axisType:"xAxis",AxisComp:Il},{axisType:"yAxis",AxisComp:Cl}],formatAxisMap:Z$}),XC=ub({chartName:"PieChart",GraphicalChild:It,validateTooltipEventTypes:["item"],defaultTooltipEventType:"item",legendContent:"children",axisComponents:[{axisType:"angleAxis",AxisComp:Da},{axisType:"radiusAxis",AxisComp:Na}],formatAxisMap:qP,defaultProps:{layout:"centric",startAngle:0,endAngle:360,cx:"50%",cy:"50%",innerRadius:0,outerRadius:"80%"}});export{qe as $,xt as A,zC as B,Xs as C,Ol as D,Ca as E,ub as F,vt as G,Z$ as H,O$ as I,yj as J,Ls as K,ie as L,o0 as M,iy as N,l_ as O,RT as P,pa as Q,mj as R,mg as S,ky as T,Qx as U,nw as V,Bs as W,Il as X,Cl as Y,on as Z,wl as _,St as a,st as a0,We as a1,of as a2,xc as a3,HC as a4,ut as a5,hr as a6,VT as a7,ja as a8,Da as a9,Na as aa,qP as ab,Gr as ac,ki as ad,ag as ae,Yt as af,Fj as ag,Lb as ah,Vt as ai,pr as aj,Hs as ak,yw as al,KC as am,_e as an,HT as ao,It as ap,$r as aq,jl as ar,Wa as as,Ua as at,Vr as au,TI as av,VC as aw,XC as ax,Ij as ay,$a as b,Tt as c,Z as d,Xt as e,V as f,je as g,OP as h,X as i,jP as j,Ve as k,h_ as l,Ie as m,Gp as n,Qm as o,le as p,ts as q,UC as r,GC as s,SP as t,Yn as u,Ta as v,et as w,B as x,Xn as y,Us as z};
