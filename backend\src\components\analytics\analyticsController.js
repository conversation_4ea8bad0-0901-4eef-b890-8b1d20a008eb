import Analytics from './analyticsModel.js';
import Visitor from '../visitor/visitorModel.js';
import User from '../user/userModel.js';
import jwt from 'jsonwebtoken';
import fetch from 'node-fetch';

// Helper function to check if user should be excluded from analytics
const shouldExcludeFromAnalytics = async (req) => {
  try {
    let token;

    // Check for token in Authorization header
    if (req.headers.authorization && req.headers.authorization.startsWith('Bearer')) {
      token = req.headers.authorization.split(' ')[1];
    }
    // Check for token in cookies
    else if (req.cookies && req.cookies.token) {
      token = req.cookies.token;
    }

    // No token means anonymous user - always allow tracking
    if (!token) {return false;
    }

    // Verify token and get user
    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    const user = await User.findById(decoded.id).select('role email');

    if (!user) {return false;
    }

    // Only exclude staff users (admin/marketing_responsible)
    // Track everyone else: regular users, clients, etc.
    const excludedRoles = ['admin', 'marketing_responsible'];
    if (excludedRoles.includes(user.role)) {return true;
    }return false;
  } catch (error) {
    // If there's any error in authentication check, allow tracking (probably anonymous user)return false;
  }
};

// Helper function to update visitor tracking
const updateVisitorTracking = async (data) => {
  const { ip, path, duration, timestamp, referrer, userAgent, source, sessionId, type, pageName, buttonLabel, blogTitle, serviceName, subServiceName, country, region } = data;

  // Find or create visitor
  let visitor = await Visitor.findOne({ ipAddress: ip }).populate('userId', 'role email');

  // Check if visitor is associated with staff user and skip tracking
  // Only exclude admin/marketing_responsible, track everyone else
  if (visitor && visitor.userId && ['admin', 'marketing_responsible'].includes(visitor.userId.role)) {return visitor;
  }

  if (!visitor) {
    // Get location data if not provided
    let locationData = null;
    if (country && region) {
      locationData = { country, region };
    }

    // Create new visitor
    visitor = new Visitor({
      ipAddress: ip,
      location: locationData,
      visitHistory: []
    });
  }

  // Update last visit
  visitor.lastVisit = new Date(timestamp);

  // Find current session or create new one
  let currentVisit = visitor.visitHistory.find(visit => visit.sessionId === sessionId);

  if (!currentVisit) {
    // New session
    currentVisit = {
      sessionId,
      timestamp: new Date(timestamp),
      duration: duration || 0,
      pages: [],
      referrer,
      userAgent,
      source: source || 'direct',
      clicks: [],
      formSubmissions: []
    };
    visitor.visitHistory.push(currentVisit);
    visitor.totalVisits += 1;
  }

  // Update session duration
  if (duration) {
    currentVisit.duration = Math.max(currentVisit.duration, duration);
    visitor.totalTimeSpent += duration;
  }

  // Add page view if it's a visit or pageview
  if (type === 'visit' || type === 'pageview') {
    const pageExists = currentVisit.pages.some(page => page.path === path);
    if (!pageExists) {
      currentVisit.pages.push({
        path,
        timestamp: new Date(timestamp),
        duration: duration || 0,
        pageName
      });
    }
  }

  // Add click tracking
  if (type === 'click') {
    currentVisit.clicks.push({
      component: data.component,
      buttonLabel,
      timestamp: new Date(timestamp),
      path
    });
  }

  // Update interests based on page visits
  if (path.includes('/services')) {
    if (!visitor.interests.includes('services')) {
      visitor.interests.push('services');
    }
    if (serviceName && !visitor.preferredContent.includes(serviceName)) {
      visitor.preferredContent.push(serviceName);
    }
  }

  if (path.includes('/blog')) {
    if (!visitor.interests.includes('blog')) {
      visitor.interests.push('blog');
    }
    if (blogTitle && !visitor.preferredContent.includes(blogTitle)) {
      visitor.preferredContent.push(blogTitle);
    }
  }

  await visitor.save();
  return visitor;
};

// POST /api/analytics/track
export const track = async (req, res) => {
  try {
    // Check if user should be excluded from analytics tracking
    if (await shouldExcludeFromAnalytics(req)) {return res.status(200).json({ success: true, message: 'Tracking skipped for admin/staff user' });
    }

    const { path, duration, timestamp, referrer, userAgent, source, sessionId, type, pageName, buttonLabel } = req.body;if (!sessionId) {
      return res.status(400).json({ success: false, error: 'sessionId required' });
    }

    // Forward to new visitor tracking system
    const visitorTrackingData = {
      path,
      duration,
      timestamp,
      referrer,
      userAgent,
      source,
      sessionId,
      type,
      pageName,
      buttonLabel,
      country: req.body.country,
      region: req.body.region
    };

    // Import visitor controller function
    const { trackVisitor } = await import('../visitor/visitorController.js');

    // Track visitor using new system (this handles the response)
    // Create a proper request object for visitor tracking
    const visitorReq = {
      ...req,
      body: visitorTrackingData,
      headers: req.headers,
      socket: req.socket,
      connection: req.connection,
      ip: req.ip
    };

    await trackVisitor(visitorReq, res);
  } catch (err) {res.status(500).json({ success: false, error: err.message });
  }
};

// GET /api/marketing/traffic
export const getTraffic = async (req, res) => {
  try {
    // Get date range from query params (default to last 30 days)
    const { period = '30d' } = req.query;
    const endDate = new Date();
    let startDate = new Date();
    
    switch (period) {
      case '7d':
        startDate.setDate(startDate.getDate() - 7);
        break;
      case '30d':
        startDate.setDate(startDate.getDate() - 30);
        break;
      case '90d':
        startDate.setDate(startDate.getDate() - 90);
        break;
      case '1y':
        startDate.setFullYear(startDate.getFullYear() - 1);
        break;
      default:
        startDate.setDate(startDate.getDate() - 30);
    }
    
    // Total visits = unique sessions in date range (regardless of type)
    const totalVisits = await Analytics.distinct('sessionId', {
      timestamp: { $gte: startDate, $lte: endDate }
    }).then(sessions => sessions.length);
    
    // Page views = all records with type 'pageview' in date range
    const pageViews = await Analytics.countDocuments({ 
      type: 'pageview',
      timestamp: { $gte: startDate, $lte: endDate }
    });
    
    // Average session duration
    const avgSession = await Analytics.aggregate([
      { 
        $match: { 
          type: { $in: ['visit', 'pageview'] },
          timestamp: { $gte: startDate, $lte: endDate }
        } 
      },
      { $group: { _id: '$sessionId', totalDuration: { $sum: '$duration' } } },
      { $group: { _id: null, avgDuration: { $avg: '$totalDuration' } } },
    ]);
    
    res.json({
      totalVisits,
      pageViews,
      avgSessionDuration: avgSession[0]?.avgDuration || 0,
    });
  } catch (err) {
    res.status(500).json({ success: false, error: err.message });
  }
};

// GET /api/marketing/sources
export const getSources = async (req, res) => {
  try {
    const sources = await Analytics.aggregate([
      { $group: { _id: '$source', count: { $sum: 1 } } },
    ]);
    res.json(sources);
  } catch (err) {
    res.status(500).json({ success: false, error: err.message });
  }
};

// GET /api/marketing/seo
export const getSEO = async (req, res) => {
  // Placeholder for future SEO stats
  res.json({});
};

// POST /api/analytics/click
export const trackClick = async (req, res) => {
  try {
    // Check if user should be excluded from analytics tracking
    if (await shouldExcludeFromAnalytics(req)) {return res.status(200).json({ success: true, message: 'Click tracking skipped for admin/staff user' });
    }

    const { component, timestamp, referrer, userAgent, source, path, pageName, buttonLabel, blogTitle, serviceName, subServiceName, sessionId, country, region } = req.body;// Validate required fields
    if (!sessionId) {return res.status(400).json({ success: false, error: 'sessionId is required' });
    }

    const analytics = new Analytics({
      component,
      type: 'click',
      timestamp,
      referrer,
      userAgent,
      source,
      path: path || '',
      duration: 0,
      pageName,
      buttonLabel,
      blogTitle,
      serviceName,
      subServiceName,
      sessionId,
      country,
      region
    });

    await analytics.save();res.status(201).json({ success: true });
  } catch (err) {res.status(500).json({ success: false, error: err.message });
  }
};

// GET /api/marketing/ctr
export const getCTR = async (req, res) => {
  try {
    // Count clicks per component
    const clicks = await Analytics.aggregate([
      { $match: { type: 'click', component: { $exists: true, $ne: null } } },
      { $group: { _id: '$component', clicks: { $sum: 1 } } },
    ]);
    // Count unique sessions per path (visits)
    const visits = await Analytics.aggregate([
      { $match: { path: { $exists: true, $ne: null } } },
      { $group: { _id: { path: '$path', sessionId: '$sessionId' } } },
      { $group: { _id: '$_id.path', visits: { $sum: 1 } } },
    ]);
    // Merge clicks and visits for CTR
    const ctrTable = clicks.map(click => {
      const visit = visits.find(v => v._id.replace('/', '') === click._id);
      const ctr = visit && visit.visits > 0 ? (click.clicks / visit.visits) * 100 : 0;
      return {
        component: click._id,
        clicks: click.clicks,
        visits: visit ? visit.visits : 0,
        ctr: ctr.toFixed(2),
      };
    });
    res.json(ctrTable);
  } catch (err) {
    res.status(500).json({ success: false, error: err.message });
  }
};

// GET /api/marketing/bounce-rate
export const getBounceRate = async (req, res) => {
  try {
    // Get date range from query params (default to last 30 days)
    const { period = '30d' } = req.query;
    const endDate = new Date();
    let startDate = new Date();
    
    switch (period) {
      case '7d':
        startDate.setDate(startDate.getDate() - 7);
        break;
      case '30d':
        startDate.setDate(startDate.getDate() - 30);
        break;
      case '90d':
        startDate.setDate(startDate.getDate() - 90);
        break;
      case '1y':
        startDate.setFullYear(startDate.getFullYear() - 1);
        break;
      default:
        startDate.setDate(startDate.getDate() - 30);
    }
    
    // Get all sessions in the specified date range
    const sessions = await Analytics.aggregate([
      { 
        $match: { 
          timestamp: { $gte: startDate, $lte: endDate }, 
          type: { $in: ['visit', 'pageview'] } 
        } 
      },
      { $group: { _id: '$sessionId', pageViews: { $sum: 1 } } },
    ]);
    const totalSessions = sessions.length;
    const bounces = sessions.filter(s => s.pageViews === 1).length;
    const bounceRate = totalSessions > 0 ? (bounces / totalSessions) * 100 : 0;
    res.json({ totalSessions, bounces, bounceRate: bounceRate.toFixed(2) });
  } catch (err) {
    res.status(500).json({ success: false, error: err.message });
  }
};

// GET /api/marketing/traffic-breakdown
export const getTrafficBreakdown = async (req, res) => {
  try {
    // Get date range from query params (default to last 30 days)
    const { period = '30d' } = req.query;
    const endDate = new Date();
    let startDate = new Date();
    
    switch (period) {
      case '7d':
        startDate.setDate(startDate.getDate() - 7);
        break;
      case '30d':
        startDate.setDate(startDate.getDate() - 30);
        break;
      case '90d':
        startDate.setDate(startDate.getDate() - 90);
        break;
      case '1y':
        startDate.setFullYear(startDate.getFullYear() - 1);
        break;
      default:
        startDate.setDate(startDate.getDate() - 30);
    }
    
    // Per day breakdown, exclude /dashboard/*
    const excludeDashboard = { $not: /^\/dashboard/ };
    const perDay = await Analytics.aggregate([
      { 
        $match: { 
          timestamp: { $gte: startDate, $lte: endDate }, 
          type: { $in: ['visit', 'pageview'] }, 
          path: excludeDashboard 
        } 
      },
      { $addFields: { day: { $dateToString: { format: "%Y-%m-%d", date: "$timestamp" } } } },
      { $group: {
        _id: "$day",
        pageViews: { $sum: 1 },
        uniqueSessions: { $addToSet: "$sessionId" },
      } },
      { $project: {
        day: "$_id",
        pageViews: 1,
        totalVisits: { $size: "$uniqueSessions" },
        _id: 0
      } },
      { $sort: { day: 1 } }
    ]);

    // Per page breakdown, exclude /dashboard/*
    const perPage = await Analytics.aggregate([
      { 
        $match: { 
          timestamp: { $gte: startDate, $lte: endDate }, 
          type: { $in: ['visit', 'pageview'] }, 
          path: excludeDashboard 
        } 
      },
      { $group: {
        _id: "$path",
        pageViews: { $sum: 1 },
        uniqueSessions: { $addToSet: "$sessionId" },
      } },
      { $project: {
        path: "$_id",
        pageViews: 1,
        totalVisits: { $size: "$uniqueSessions" },
        _id: 0
      } },
      { $sort: { pageViews: -1 } }
    ]);

    res.json({ perDay, perPage });
  } catch (err) {
    res.status(500).json({ success: false, error: err.message });
  }
};

// GET /api/analytics/visitor-stats - Get visitor-based analytics
export const getVisitorStats = async (req, res) => {
  try {
    // Get admin/staff user IDs to exclude from lead scoring
    const adminStaffUserIds = await User.find({ role: { $in: ['admin', 'marketing_responsible'] } }).distinct('_id');

    // Base query to exclude admin/staff users
    const baseQuery = {
      $or: [
        { userId: { $exists: false } }, // Visitors without user accounts
        { userId: { $not: { $in: adminStaffUserIds } } } // Visitors not linked to admin/staff
      ]
    };

    // Get visitor statistics (excluding admin/staff)
    const totalVisitors = await Visitor.countDocuments(baseQuery);

    const visitorsByStatus = await Visitor.aggregate([
      { $match: baseQuery },
      { $group: { _id: '$leadStatus', count: { $sum: 1 } } }
    ]);

    const visitorsByRegion = await Visitor.aggregate([
      { $match: baseQuery },
      {
        $group: {
          _id: {
            country: '$location.country',
            region: '$location.region'
          },
          count: { $sum: 1 },
          avgLeadScore: { $avg: '$leadScore' }
        }
      },
      { $sort: { count: -1 } }
    ]);

    const leadScoreDistribution = await Visitor.aggregate([
      { $match: baseQuery },
      {
        $bucket: {
          groupBy: '$leadScore',
          boundaries: [0, 25, 50, 75, 100],
          default: 'other',
          output: { count: { $sum: 1 } }
        }
      }
    ]);

    const businessDomainStats = await Visitor.aggregate([
      { $match: { businessDomain: { $exists: true, $ne: null } } },
      { $group: { _id: '$businessDomain', count: { $sum: 1 } } },
      { $sort: { count: -1 } }
    ]);

    const clientSegmentStats = await Visitor.aggregate([
      { $match: { clientSegment: { $exists: true, $ne: null } } },
      { $group: { _id: '$clientSegment', count: { $sum: 1 } } },
      { $sort: { count: -1 } }
    ]);

    res.json({
      totalVisitors,
      visitorsByStatus,
      visitorsByRegion,
      leadScoreDistribution,
      businessDomainStats,
      clientSegmentStats
    });

  } catch (error) {
    res.status(500).json({ success: false, error: error.message });
  }
};

// Debug endpoint to check analytics and visitor data
export const debugAnalytics = async (req, res) => {
  try {
    const totalRecords = await Analytics.countDocuments();
    const visitRecords = await Analytics.countDocuments({ type: 'visit' });
    const clickRecords = await Analytics.countDocuments({ type: 'click' });
    const pageviewRecords = await Analytics.countDocuments({ type: 'pageview' });

    const sampleRecords = await Analytics.find().limit(5).sort({ timestamp: -1 });

    // Also get visitor data
    const totalVisitors = await Visitor.countDocuments();
    const sampleVisitors = await Visitor.find().limit(3).sort({ lastVisit: -1 });

    res.json({
      analytics: {
        totalRecords,
        visitRecords,
        clickRecords,
        pageviewRecords,
        sampleRecords
      },
      visitors: {
        totalVisitors,
        sampleVisitors
      }
    });
  } catch (err) {
    res.status(500).json({ success: false, error: err.message });
  }
};

// GET /api/analytics/country-stats
export const getCountryStats = async (req, res) => {
  try {
    // Get date range from query params (default to last 30 days)
    const { period = '30d' } = req.query;
    const endDate = new Date();
    let startDate = new Date();

    switch (period) {
      case '7d':
        startDate.setDate(startDate.getDate() - 7);
        break;
      case '30d':
        startDate.setDate(startDate.getDate() - 30);
        break;
      case '90d':
        startDate.setDate(startDate.getDate() - 90);
        break;
      case '1y':
        startDate.setFullYear(startDate.getFullYear() - 1);
        break;
      default:
        startDate.setDate(startDate.getDate() - 30);
    }

    // Get admin/staff user IDs to exclude their sessions
    const adminStaffUserIds = await User.find({ role: { $in: ['admin', 'marketing_responsible'] } }).distinct('_id');
    const adminStaffVisitors = await Visitor.find({ userId: { $in: adminStaffUserIds } }).distinct('ipAddress');

    const countryStats = await Analytics.aggregate([
      // Match records in date range and exclude records where both country and region are empty/null
      // Also exclude sessions from admin/staff users by checking visitor IP addresses
      {
        $match: {
          timestamp: { $gte: startDate, $lte: endDate },
          $nor: [
            { country: { $in: [null, ''] }, region: { $in: [null, ''] } }
          ]
        }
      },
      // Lookup visitor data to check if session belongs to admin/staff user
      {
        $lookup: {
          from: 'visitors',
          let: { sessionId: '$sessionId' },
          pipeline: [
            {
              $match: {
                $expr: {
                  $and: [
                    { $in: ['$$sessionId', '$visitHistory.sessionId'] },
                    { $not: { $in: ['$userId', adminStaffUserIds] } }
                  ]
                }
              }
            }
          ],
          as: 'visitorData'
        }
      },
      // Only include sessions that either have no visitor data or belong to non-admin users
      {
        $match: {
          $or: [
            { visitorData: { $size: 0 } }, // No visitor data found (anonymous)
            { visitorData: { $ne: [] } }   // Visitor data exists and passed the filter
          ]
        }
      },
      // Group by sessionId, country, and region to get unique sessions per location
      {
        $group: {
          _id: {
            sessionId: "$sessionId",
            country: "$country",
            region: "$region"
          }
        }
      },
      // Group by country and region to count unique sessions (visitors)
      {
        $group: {
          _id: {
            country: "$_id.country",
            region: "$_id.region"
          },
          count: { $sum: 1 }
        }
      },
      { $sort: { count: -1 } }
    ]);res.json(countryStats);
  } catch (err) {
    res.status(500).json({ success: false, error: err.message });
  }
};

// GET /api/analytics/events
export const getAllEvents = async (req, res) => {
  try {
    const { period = '30d', type } = req.query;
    const endDate = new Date();
    let startDate = new Date();
    switch (period) {
      case '7d': startDate.setDate(startDate.getDate() - 7); break;
      case '30d': startDate.setDate(startDate.getDate() - 30); break;
      case '90d': startDate.setDate(startDate.getDate() - 90); break;
      case '1y': startDate.setFullYear(startDate.getFullYear() - 1); break;
      default: startDate.setDate(startDate.getDate() - 30);
    }
    const query = {
      timestamp: { $gte: startDate, $lte: endDate },
    };
    if (type) query.type = type;
    const events = await Analytics.find(query).sort({ timestamp: -1 }).limit(1000);
    res.json(events);
  } catch (err) {
    res.status(500).json({ success: false, error: err.message });
  }
};

// GET /api/analytics/debug - Debug endpoint to check analytics data
export const getDebugInfo = async (req, res) => {
  try {
    const { period = '7d' } = req.query;
    const endDate = new Date();
    let startDate = new Date();

    switch (period) {
      case '7d':
        startDate.setDate(startDate.getDate() - 7);
        break;
      case '30d':
        startDate.setDate(startDate.getDate() - 30);
        break;
      default:
        startDate.setDate(startDate.getDate() - 7);
    }

    // Get total records
    const totalRecords = await Analytics.countDocuments({
      timestamp: { $gte: startDate, $lte: endDate }
    });

    // Get records by type
    const recordsByType = await Analytics.aggregate([
      { $match: { timestamp: { $gte: startDate, $lte: endDate } } },
      { $group: { _id: '$type', count: { $sum: 1 } } },
      { $sort: { count: -1 } }
    ]);

    // Get unique sessions
    const uniqueSessions = await Analytics.distinct('sessionId', {
      timestamp: { $gte: startDate, $lte: endDate }
    });

    // Get sessions with location data
    const sessionsWithLocation = await Analytics.aggregate([
      {
        $match: {
          timestamp: { $gte: startDate, $lte: endDate },
          $nor: [
            { country: { $in: [null, ''] }, region: { $in: [null, ''] } }
          ]
        }
      },
      { $group: { _id: '$sessionId' } }
    ]);

    // Get sample records
    const sampleRecords = await Analytics.find({
      timestamp: { $gte: startDate, $lte: endDate }
    }).sort({ timestamp: -1 }).limit(10);

    res.json({
      period,
      dateRange: { startDate, endDate },
      totalRecords,
      recordsByType,
      uniqueSessionsCount: uniqueSessions.length,
      sessionsWithLocationCount: sessionsWithLocation.length,
      sampleRecords
    });
  } catch (err) {
    res.status(500).json({ success: false, error: err.message });
  }
};
