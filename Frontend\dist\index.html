<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Click4You</title>
    <meta name="description" content="Click4You - Your platform description" />

    <!-- Performance optimizations -->
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link rel="dns-prefetch" href="//fonts.googleapis.com" />
    <link rel="dns-prefetch" href="//fonts.gstatic.com" />

    <!-- Favicon -->
    <link rel="icon" type="image/jpg" href="./logo.jpg" />
    <link rel="shortcut icon" type="image/jpg" href="./logo.jpg" />
    <link rel="apple-touch-icon" href="./logo.jpg" />
    <meta name="theme-color" content="#ffffff" />

    <!-- Critical CSS inline to prevent render blocking -->
    <style>
      /* Critical above-the-fold styles */
      body {
        margin: 0;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
          'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
          sans-serif;
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
        background-color: #ffffff;
      }

      #root {
        min-height: 100vh;
      }

      /* Loading spinner styles */
      .loading-spinner {
        display: flex;
        justify-content: center;
        align-items: center;
        height: 100vh;
      }

      .spinner {
        width: 40px;
        height: 40px;
        border: 4px solid #f3f3f3;
        border-top: 4px solid #3498db;
        border-radius: 50%;
        animation: spin 1s linear infinite;
      }

      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }
    </style>

    <!-- Google Analytics script removed, using react-ga4 instead -->
    <script type="module" crossorigin src="./assets/index-hEW_vQ3f.js"></script>
    <link rel="stylesheet" crossorigin href="./assets/style-i5PAo3Ou.css">
  </head>

  <body>
    <div id="root">
      <!-- Fallback loading content -->
      <div class="loading-spinner">
        <div class="spinner"></div>
      </div>
    </div>


  </body>
</html>
