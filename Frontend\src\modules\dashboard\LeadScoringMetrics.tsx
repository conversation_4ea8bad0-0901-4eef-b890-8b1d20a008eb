import React, { useState, useEffect } from 'react';
import axios from 'axios';
import { <PERSON><PERSON>hart, Pie, Cell, ResponsiveContainer, BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, Legend } from 'recharts';

interface LeadScoringData {
  totalVisitors: number;
  visitorsByStatus: Array<{ _id: string; count: number }>;
  leadScoreDistribution: Array<{ _id: string; count: number }>;
  businessDomainStats: Array<{ _id: string; count: number }>;
  clientSegmentStats: Array<{ _id: string; count: number }>;
}

interface LeadScoringMetricsProps {
  refreshTrigger?: number;
}

const LeadScoringMetrics: React.FC<LeadScoringMetricsProps> = ({ refreshTrigger }) => {
  const [data, setData] = useState<LeadScoringData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchData = async () => {
    try {
      setLoading(true);
      const response = await axios.get('/api/analytics/visitor-stats');
      setData(response.data);
      setError(null);
    } catch (err) {
      setError('Failed to fetch lead scoring data');
      console.error('Lead scoring fetch error:', err);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchData();
  }, [refreshTrigger]);

  if (loading) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {[1, 2, 3, 4].map((i) => (
          <div key={i} className="bg-brand-grey-900 border border-brand-grey-700 rounded-xl p-6">
            <div className="flex items-center justify-center h-32">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            </div>
          </div>
        ))}
      </div>
    );
  }

  if (error || !data) {
    return (
      <div className="bg-brand-grey-900 border border-brand-grey-700 rounded-xl p-6">
        <div className="text-red-500 text-center">{error || 'No data available'}</div>
      </div>
    );
  }

  // Prepare data for charts
  const statusData = data.visitorsByStatus.map(item => ({
    name: item._id.replace('_', ' '),
    value: item.count,
    percentage: ((item.count / data.totalVisitors) * 100).toFixed(1)
  }));

  const scoreData = data.leadScoreDistribution.map(item => {
    let label = '';
    if (item._id === 'other') label = 'No Score';
    else if (typeof item._id === 'number') {
      if (item._id < 25) label = '0-24';
      else if (item._id < 50) label = '25-49';
      else if (item._id < 75) label = '50-74';
      else label = '75-100';
    } else {
      label = item._id.toString();
    }
    return {
      name: label,
      value: item.count
    };
  });

  const domainData = data.businessDomainStats.slice(0, 8).map(item => ({
    name: item._id.replace('_', ' '),
    value: item.count
  }));

  const segmentData = data.clientSegmentStats.slice(0, 6).map(item => ({
    name: item._id.replace('_', ' '),
    value: item.count
  }));

  // Colors for charts
  const statusColors = ['#3B82F6', '#F59E0B', '#10B981', '#8B5CF6', '#EF4444'];
  const scoreColors = ['#EF4444', '#F59E0B', '#10B981', '#3B82F6'];

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
      {/* Lead Status Distribution */}
      <div className="bg-brand-grey-900 border border-brand-grey-700 rounded-xl p-6">
        <h3 className="text-lg font-bold text-brand-white mb-4">Lead Status Distribution</h3>
        <div className="h-64">
          <ResponsiveContainer width="100%" height="100%">
            <PieChart>
              <Pie
                data={statusData}
                cx="50%"
                cy="50%"
                labelLine={false}
                label={({ name, percentage }) => `${name}: ${percentage}%`}
                outerRadius={80}
                fill="#8884d8"
                dataKey="value"
              >
                {statusData.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={statusColors[index % statusColors.length]} />
                ))}
              </Pie>
              <Tooltip />
            </PieChart>
          </ResponsiveContainer>
        </div>
        <div className="mt-4 grid grid-cols-2 gap-2 text-sm">
          {statusData.map((item, index) => (
            <div key={item.name} className="flex items-center">
              <div 
                className="w-3 h-3 rounded-full mr-2" 
                style={{ backgroundColor: statusColors[index % statusColors.length] }}
              ></div>
              <span className="text-brand-grey-300">{item.name}: {item.value}</span>
            </div>
          ))}
        </div>
      </div>

      {/* Lead Score Distribution */}
      <div className="bg-brand-grey-900 border border-brand-grey-700 rounded-xl p-6">
        <h3 className="text-lg font-bold text-brand-white mb-4">Lead Score Distribution</h3>
        <div className="h-64">
          <ResponsiveContainer width="100%" height="100%">
            <BarChart data={scoreData}>
              <CartesianGrid strokeDasharray="3 3" stroke="#374151" />
              <XAxis dataKey="name" stroke="#cbd5e1" />
              <YAxis stroke="#cbd5e1" />
              <Tooltip 
                contentStyle={{ 
                  background: '#1f2937', 
                  border: 'none', 
                  color: '#fff',
                  borderRadius: '8px'
                }} 
              />
              <Bar dataKey="value" fill="#3B82F6" />
            </BarChart>
          </ResponsiveContainer>
        </div>
      </div>

      {/* Business Domain Analytics */}
      <div className="bg-brand-grey-900 border border-brand-grey-700 rounded-xl p-6">
        <h3 className="text-lg font-bold text-brand-white mb-4">Business Domains</h3>
        <div className="h-64">
          <ResponsiveContainer width="100%" height="100%">
            <BarChart data={domainData} layout="horizontal">
              <CartesianGrid strokeDasharray="3 3" stroke="#374151" />
              <XAxis type="number" stroke="#cbd5e1" />
              <YAxis dataKey="name" type="category" stroke="#cbd5e1" width={80} />
              <Tooltip 
                contentStyle={{ 
                  background: '#1f2937', 
                  border: 'none', 
                  color: '#fff',
                  borderRadius: '8px'
                }} 
              />
              <Bar dataKey="value" fill="#10B981" />
            </BarChart>
          </ResponsiveContainer>
        </div>
      </div>

      {/* Client Segment Analytics */}
      <div className="bg-brand-grey-900 border border-brand-grey-700 rounded-xl p-6">
        <h3 className="text-lg font-bold text-brand-white mb-4">Client Segments</h3>
        <div className="h-64">
          <ResponsiveContainer width="100%" height="100%">
            <BarChart data={segmentData}>
              <CartesianGrid strokeDasharray="3 3" stroke="#374151" />
              <XAxis dataKey="name" stroke="#cbd5e1" />
              <YAxis stroke="#cbd5e1" />
              <Tooltip 
                contentStyle={{ 
                  background: '#1f2937', 
                  border: 'none', 
                  color: '#fff',
                  borderRadius: '8px'
                }} 
              />
              <Bar dataKey="value" fill="#8B5CF6" />
            </BarChart>
          </ResponsiveContainer>
        </div>
      </div>

      {/* Summary Cards */}
      <div className="md:col-span-2 grid grid-cols-1 md:grid-cols-4 gap-4">
        <div className="bg-brand-grey-900 border border-brand-grey-700 rounded-xl p-4">
          <div className="text-2xl font-bold text-brand-white">{data.totalVisitors}</div>
          <div className="text-brand-grey-400 text-sm">Total Visitors</div>
        </div>
        
        <div className="bg-brand-grey-900 border border-brand-grey-700 rounded-xl p-4">
          <div className="text-2xl font-bold text-green-400">
            {statusData.find(s => s.name === 'qualified')?.value || 0}
          </div>
          <div className="text-brand-grey-400 text-sm">Qualified Leads</div>
        </div>
        
        <div className="bg-brand-grey-900 border border-brand-grey-700 rounded-xl p-4">
          <div className="text-2xl font-bold text-purple-400">
            {statusData.find(s => s.name === 'converted')?.value || 0}
          </div>
          <div className="text-brand-grey-400 text-sm">Converted</div>
        </div>
        
        <div className="bg-brand-grey-900 border border-brand-grey-700 rounded-xl p-4">
          <div className="text-2xl font-bold text-blue-400">
            {((statusData.find(s => s.name === 'converted')?.value || 0) / data.totalVisitors * 100).toFixed(1)}%
          </div>
          <div className="text-brand-grey-400 text-sm">Conversion Rate</div>
        </div>
      </div>
    </div>
  );
};

export default LeadScoringMetrics;
