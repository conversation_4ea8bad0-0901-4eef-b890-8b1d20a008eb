import React, { useState, useEffect, Suspense } from 'react';
import { Routes, Route } from 'react-router-dom';
import DashboardLayout from './DashboardLayout';
import {
  Users,
  Eye,
  Clock,
  TrendingUp,
  ArrowUpRight,
  ArrowDownRight,
  Globe,
  Activity,
  Zap,
  MapPin,
  UserCheck,
  AlertTriangle,
  Download,
  Building2
} from 'lucide-react';
import { geocodeRegion } from '@/shared/utils/geocodeRegion';

// Lazy load components
const CalendarWrapper = React.lazy(() => import('./CalendarWrapper'));
const BlogManagement = React.lazy(() => import('./BlogManagement'));
const ServiceManagement = React.lazy(() => import('./ServiceManagement'));
const FinancialHealthDashboard = React.lazy(() => import('./FinancialHealthDashboard'));

const VisitorAnalyticsTable = React.lazy(() => import('./VisitorAnalyticsTable'));
const LeadScoringMetrics = React.lazy(() => import('./LeadScoringMetrics'));
const MapComponents = React.lazy(() => import('./MapComponents'));

interface AnalyticsData {
  totalVisits: number;
  pageViews: number;
  uniqueVisitors: number;
  avgSessionDuration: number;
}

interface CacheEntry {
  data: any;
  timestamp: number;
}

const CACHE_DURATION = 5 * 60 * 1000; // 5 minutes
const cache = new Map<string, CacheEntry>();

// Function to process visitor data and create geo coordinates
const processVisitorGeoData = async (countryStats: any[]): Promise<Array<{lat: number; lon: number; region: string; country: string; count: number}>> => {
  console.log('Processing visitor geo data:', countryStats);
  const geoData: Array<{lat: number; lon: number; region: string; country: string; count: number}> = [];

  for (const stat of countryStats) {
    // Handle the actual data structure from backend: { _id: { country: 'TN', region: 'Sfax Governorate' }, count: 15 }
    const country = stat._id?.country || stat.country || 'Unknown';
    const region = stat._id?.region || stat.region || 'Unknown';
    const count = stat.count || stat.visits || 0;

    console.log(`Processing location: ${region}, ${country} (${count} visits)`);

    if (country !== 'Unknown' && region !== 'Unknown' && count > 0) {
      try {
        console.log(`Geocoding ${region}, ${country}...`);
        const coordinates = await geocodeRegion(region, country);
        if (coordinates && coordinates.length === 2) {
          console.log(`✅ Geocoded ${region}, ${country} to:`, coordinates);
          geoData.push({
            lat: coordinates[0],
            lon: coordinates[1],
            region,
            country,
            count
          });
        } else {
          console.warn(`❌ No coordinates found for ${region}, ${country}`);
        }
      } catch (error) {
        console.error(`❌ Failed to geocode ${region}, ${country}:`, error);
      }
    } else {
      console.log(`⏭️ Skipping ${region}, ${country} (unknown or 0 visits)`);
    }
  }

  console.log('Final geo data:', geoData);
  return geoData;
};

const MarketingDashboard: React.FC = () => {
  console.log('MarketingDashboard component loaded');

  const [traffic, setTraffic] = useState<AnalyticsData | null>(null);
  const [countryStats, setCountryStats] = useState<any[]>([]);
  const [visitorData, setVisitorData] = useState<any>(null);
  const [geoData, setGeoData] = useState<Array<{lat: number; lon: number; region: string; country: string; count: number}>>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [dateRange, setDateRange] = useState('Last 7 days');

  const getCachedData = (key: string) => {
    const cached = cache.get(key);
    if (cached && Date.now() - cached.timestamp < CACHE_DURATION) {
      return cached.data;
    }
    return null;
  };

  const setCachedData = (key: string, data: any) => {
    cache.set(key, { data, timestamp: Date.now() });
  };

  const fetchWithRetry = async (url: string, retries = 3): Promise<any> => {
    for (let i = 0; i < retries; i++) {
      try {
        const response = await fetch(url, {
          credentials: 'include', // Include HTTP-only cookies for authentication
          headers: {
            'Content-Type': 'application/json',
          },
        });

        if (response.status === 429) {
          const delay = Math.pow(2, i) * 1000; // Exponential backoff
          console.warn(`Rate limited. Retrying in ${delay}ms...`);
          await new Promise(resolve => setTimeout(resolve, delay));
          continue;
        }

        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        return await response.json();
      } catch (error) {
        if (i === retries - 1) throw error;
        await new Promise(resolve => setTimeout(resolve, 1000 * (i + 1)));
      }
    }
  };

  const fetchAnalyticsData = async (isManualRefresh = false) => {
    try {
      setLoading(true);
      setError(null);

      if (!isManualRefresh) {
        // Check cache first
        const cacheKey = `analytics_${dateRange}`;
        const cachedData = getCachedData(cacheKey);
        if (cachedData) {
          setTraffic(cachedData.traffic);
          setCountryStats(cachedData.countryStats);
          setVisitorData(cachedData.visitorAnalytics);
          setGeoData(cachedData.geoData);
          setLoading(false);
          return;
        }
      } else {
        // Clear cache on manual refresh
        cache.clear();
      }

      // Sequential API calls with delays to prevent rate limiting
      const delay = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));

      console.log('Fetching visitor traffic...');
      const trafficData = await fetchWithRetry(`http://localhost:5000/api/visitor/traffic?period=${dateRange}`);
      await delay(500);

      console.log('Fetching visitor country stats...');
      const countryData = await fetchWithRetry(`http://localhost:5000/api/visitor/country-stats?period=${dateRange}`);
      await delay(500);

      console.log('Fetching visitor analytics...');
      const visitorAnalyticsData = await fetchWithRetry(`http://localhost:5000/api/visitor/analytics?period=${dateRange}`);

      // Process visitor data to create geoData for the map
      console.log('Processing geo data with country data:', countryData);
      const processedGeoData = await processVisitorGeoData(countryData);
      console.log('Processed geo data result:', processedGeoData);

      // Cache the results
      const cacheKey = `analytics_${dateRange}`;
      setCachedData(cacheKey, {
        traffic: trafficData,
        countryStats: countryData,
        visitorAnalytics: visitorAnalyticsData,
        geoData: processedGeoData
      });

      setTraffic(trafficData);
      setCountryStats(countryData);
      setVisitorData(visitorAnalyticsData);
      setGeoData(processedGeoData);

    } catch (error: any) {
      console.error('Error fetching analytics data:', error);
      
      if (error.message.includes('429')) {
        setError('Too many requests. Please wait a moment and try again.');
      } else if (error.message.includes('Failed to fetch')) {
        setError('Unable to connect to analytics server. Please check if the backend is running.');
      } else {
        setError(`Failed to load analytics data: ${error.message}`);
      }
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    // Add a small delay before initial fetch to prevent immediate requests
    const timer = setTimeout(() => {
      fetchAnalyticsData();
    }, 1000);

    return () => clearTimeout(timer);
  }, [dateRange]);

  const handleRefresh = () => {
    fetchAnalyticsData(true);
  };

  const MetricCard = ({ title, value, change, icon: Icon, color }: any) => (
    <div className="bg-brand-grey-900 rounded-xl p-4 border border-brand-grey-700">
      <div className="flex items-center justify-between">
        <div>
          <p className="text-brand-grey-400 text-sm font-medium">{title}</p>
          <p className="text-2xl font-bold text-brand-white mt-1">{value}</p>
          {change && (
            <div className={`flex items-center gap-1 mt-2 ${change >= 0 ? 'text-green-400' : 'text-red-400'}`}>
              {change >= 0 ? <ArrowUpRight size={16} /> : <ArrowDownRight size={16} />}
              <span className="text-sm font-medium">{Math.abs(change)}%</span>
            </div>
          )}
        </div>
        <div className={`p-3 rounded-lg ${color}`}>
          <Icon size={24} className="text-white" />
        </div>
      </div>
    </div>
  );

  return (
    <DashboardLayout>
      <Routes>
        <Route
          path="/calendar"
          element={
            <Suspense fallback={<div className="flex items-center justify-center h-64"><div className="animate-spin rounded-full h-8 w-8 border-b-2 border-brand-accent-100"></div></div>}>
              <CalendarWrapper />
            </Suspense>
          }
        />
        <Route
          path="/"
          element={
            <div className="p-4 space-y-4">
              {/* Header */}
              <div className="flex items-center justify-between">
                <div>
                  <h1 className="text-2xl font-bold text-brand-white">Marketing Analytics</h1>
                  <p className="text-brand-grey-400 text-sm">Visitor tracking & marketing performance</p>
                </div>
                <div className="flex items-center gap-3">
                  <button
                    onClick={handleRefresh}
                    disabled={loading}
                    className="flex items-center gap-2 px-3 py-2 bg-brand-grey-800 hover:bg-brand-grey-700 disabled:opacity-50 disabled:cursor-not-allowed rounded-lg border border-brand-grey-700 text-brand-grey-300 hover:text-brand-white transition-colors"
                  >
                    <Download size={16} className={loading ? "animate-spin" : ""} />
                    {loading ? "Loading..." : "Refresh"}
                  </button>
                  <div className="flex bg-brand-grey-800 rounded-lg p-1">
                    {(['7d', '30d', '90d', '1y'] as const).map((period) => (
                      <button
                        key={period}
                        onClick={() => setDateRange(period === '7d' ? 'Last 7 days' : period === '30d' ? 'Last 30 days' : period === '90d' ? 'Last 3 months' : 'Last year')}
                        className={`px-3 py-1 rounded text-sm font-medium transition-all ${
                          (period === '7d' && dateRange === 'Last 7 days') ||
                          (period === '30d' && dateRange === 'Last 30 days') ||
                          (period === '90d' && dateRange === 'Last 3 months') ||
                          (period === '1y' && dateRange === 'Last year')
                            ? 'bg-brand-accent-100 text-brand-grey-950'
                            : 'text-brand-grey-400 hover:text-brand-white'
                        }`}
                      >
                        {period}
                      </button>
                    ))}
                  </div>
                </div>
              </div>

              {/* Error Message */}
              {error && (
                <div className="bg-red-900/20 border border-red-700 rounded-lg p-4 flex items-center gap-3">
                  <AlertTriangle size={20} className="text-red-400" />
                  <div>
                    <p className="text-red-400 font-medium">Analytics Error</p>
                    <p className="text-red-300 text-sm">{error}</p>
                  </div>
                </div>
              )}

              {/* Key Metrics Row */}
              <div className="grid grid-cols-2 md:grid-cols-4 gap-3 mb-6">
                {[
                  {
                    title: "Total Visits",
                    value: traffic?.totalVisits?.toLocaleString() ?? "0",
                    change: 12.5,
                    icon: Users,
                    color: "bg-blue-600"
                  },
                  {
                    title: "Page Views",
                    value: traffic?.pageViews?.toLocaleString() ?? "0",
                    change: 8.2,
                    icon: Eye,
                    color: "bg-green-600"
                  },
                  {
                    title: "Unique Visitors",
                    value: traffic?.uniqueVisitors?.toLocaleString() ?? "0",
                    change: 15.3,
                    icon: UserCheck,
                    color: "bg-purple-600"
                  },
                  {
                    title: "Avg. Session",
                    value: traffic?.avgSessionDuration ? `${Math.round(traffic.avgSessionDuration / 1000)}s` : "0s",
                    change: -2.1,
                    icon: Clock,
                    color: "bg-orange-600"
                  }
                ].map((metric, index) => (
                  <MetricCard key={index} {...metric} />
                ))}
              </div>

              {/* Main Content Grid */}
              <div className="grid lg:grid-cols-2 gap-6">
                {/* Traffic Analytics & Visitor Insights */}
                <div className="space-y-6">
                  <div className="bg-brand-grey-900 rounded-xl p-6 border border-brand-grey-700">
                    <div className="flex items-center justify-between mb-4">
                      <h3 className="text-lg font-semibold text-brand-white flex items-center gap-2">
                        <Activity size={20} className="text-blue-400" />
                        Traffic Trends
                      </h3>
                    </div>
                    <div className="space-y-4">
                      <h4 className="text-brand-grey-300 font-medium">Visitor Status Distribution</h4>
                      <div className="space-y-3">
                        {visitorData?.visitorsByStatus?.map((status: any, index: number) => {
                          const statusLabels: { [key: string]: string } = {
                            'new': 'New Visitors',
                            'in_progress': 'In Progress',
                            'qualified': 'Qualified Leads',
                            'converted': 'Converted',
                            'lost': 'Lost'
                          };
                          const statusColors: { [key: string]: string } = {
                            'new': 'text-blue-400',
                            'in_progress': 'text-yellow-400',
                            'qualified': 'text-green-400',
                            'converted': 'text-purple-400',
                            'lost': 'text-red-400'
                          };
                          return (
                            <div key={index} className="flex items-center justify-between">
                              <span className="text-brand-grey-300">
                                {statusLabels[status._id] || status._id || 'Unknown'}
                              </span>
                              <span className={`font-medium ${statusColors[status._id] || 'text-brand-white'}`}>
                                {status.count || 0}
                              </span>
                            </div>
                          );
                        })}
                        {(!visitorData?.visitorsByStatus || visitorData.visitorsByStatus.length === 0) && (
                          <div className="text-brand-grey-400 text-center py-4">
                            No visitor status data available
                          </div>
                        )}
                      </div>
                    </div>
                  </div>

                  <div className="bg-brand-grey-900 rounded-xl p-6 border border-brand-grey-700">
                    <h3 className="text-lg font-semibold text-brand-white mb-4 flex items-center gap-2">
                      <Zap size={20} className="text-green-400" />
                      Lead Score Distribution
                    </h3>
                    <div className="space-y-3">
                      {visitorData?.leadScoreDistribution?.map((bucket: any, index: number) => {
                        const ranges = ['0-25', '25-50', '50-75', '75-100'];
                        const colors = ['text-red-400', 'text-yellow-400', 'text-blue-400', 'text-green-400'];
                        return (
                          <div key={index} className="flex items-center justify-between">
                            <span className="text-brand-grey-300">{ranges[index] || 'Other'}</span>
                            <div className="flex items-center gap-2">
                              <span className={`font-medium ${colors[index] || 'text-brand-white'}`}>
                                {bucket.count || 0}
                              </span>
                              <span className="text-brand-grey-400 text-sm">visitors</span>
                            </div>
                          </div>
                        );
                      })}
                      {(!visitorData?.leadScoreDistribution || visitorData.leadScoreDistribution.length === 0) && (
                        <div className="text-brand-grey-400 text-center py-4">
                          No lead score data available
                        </div>
                      )}
                    </div>
                  </div>
                </div>

                {/* Visitor Analytics & Geographic Data */}
                <div className="space-y-6">
                  <div className="bg-brand-grey-900 rounded-xl p-6 border border-brand-grey-700">
                    <h3 className="text-lg font-semibold text-brand-white mb-4 flex items-center gap-2">
                      <Globe size={20} className="text-purple-400" />
                      Visitor Location Map
                    </h3>
                    <div className="text-brand-grey-400 text-sm mb-4">
                      Real-time visitor locations based on analytics data
                    </div>
                    <Suspense fallback={<div className="h-96 bg-brand-grey-800 rounded-lg animate-pulse flex items-center justify-center"><span className="text-brand-grey-400">Loading map...</span></div>}>
                      {geoData.length > 0 ? (
                        <MapComponents geoData={geoData} />
                      ) : (
                        <div className="h-96 bg-brand-grey-800 rounded-lg flex items-center justify-center">
                          <div className="text-center">
                            <Globe size={48} className="text-brand-grey-600 mx-auto mb-2" />
                            <p className="text-brand-grey-400">Processing visitor locations...</p>
                            <p className="text-brand-grey-500 text-sm">Map will appear once location data is geocoded</p>
                          </div>
                        </div>
                      )}
                    </Suspense>
                  </div>

                  <div className="bg-brand-grey-900 rounded-xl p-6 border border-brand-grey-700">
                    <h3 className="text-lg font-semibold text-brand-white mb-4 flex items-center gap-2">
                      <MapPin size={20} className="text-orange-400" />
                      Regional Visitor Statistics
                    </h3>
                    <div className="space-y-3 max-h-64 overflow-y-auto">
                      {countryStats.length > 0 ? (
                        countryStats.map((stat, index) => {
                          console.log('Processing stat for table:', stat);
                          const country = stat._id?.country || stat.country || 'Unknown';
                          const region = stat._id?.region || stat.region || 'Unknown';
                          const count = stat.count || 0;
                          const maxCount = Math.max(...countryStats.map(s => s.count || 0));
                          const percentage = maxCount > 0 ? Math.round((count / maxCount) * 100) : 0;

                          console.log(`Table display: ${country} - ${region} (${count} visits, ${percentage}%)`);

                          return (
                            <div key={index} className="flex items-center justify-between p-3 bg-brand-grey-800 rounded-lg hover:bg-brand-grey-700 transition-colors">
                              <div className="flex items-center gap-3">
                                <div className="w-8 h-8 bg-purple-600 rounded-full flex items-center justify-center text-white text-sm font-bold">
                                  {country?.slice(0, 2).toUpperCase() || 'UN'}
                                </div>
                                <div>
                                  <span className="text-brand-white font-medium">{country}</span>
                                  {region && region !== 'Unknown' && (
                                    <div className="text-brand-grey-400 text-sm">{region}</div>
                                  )}
                                </div>
                              </div>
                              <div className="flex items-center gap-2">
                                <span className="text-brand-white font-medium">{count}</span>
                                <span className="text-brand-grey-400 text-sm">({percentage}%)</span>
                                <div className="w-16 bg-brand-grey-700 rounded-full h-2">
                                  <div
                                    className="bg-purple-500 h-2 rounded-full"
                                    style={{ width: `${Math.min(100, percentage)}%` }}
                                  ></div>
                                </div>
                              </div>
                            </div>
                          );
                        })
                      ) : (
                        <div className="text-center py-8 text-brand-grey-400">
                          No regional data available
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </div>

              {/* Business Intelligence Charts */}
              <div className="grid lg:grid-cols-2 gap-6 mt-6">
                <div className="bg-brand-grey-900 rounded-xl p-6 border border-brand-grey-700">
                  <h3 className="text-lg font-semibold text-brand-white mb-4 flex items-center gap-2">
                    <Building2 size={20} className="text-cyan-400" />
                    Business Domains Distribution
                  </h3>
                  <div className="space-y-3 max-h-64 overflow-y-auto">
                    {visitorData?.businessDomainStats?.length > 0 ? (
                      visitorData.businessDomainStats.map((domain, index) => {
                        const colors = ['text-cyan-400', 'text-blue-400', 'text-purple-400', 'text-pink-400', 'text-orange-400'];
                        const bgColors = ['bg-cyan-500', 'bg-blue-500', 'bg-purple-500', 'bg-pink-500', 'bg-orange-500'];
                        const maxCount = Math.max(...visitorData.businessDomainStats.map(d => d.count || 0));
                        const percentage = maxCount > 0 ? Math.round((domain.count / maxCount) * 100) : 0;

                        return (
                          <div key={index} className="flex items-center justify-between p-3 bg-brand-grey-800 rounded-lg">
                            <div className="flex items-center gap-3">
                              <div className={`w-3 h-3 rounded-full ${bgColors[index % bgColors.length]}`}></div>
                              <span className="text-brand-white font-medium">
                                {domain._id || 'Not Specified'}
                              </span>
                            </div>
                            <div className="flex items-center gap-2">
                              <span className={`font-medium ${colors[index % colors.length]}`}>
                                {domain.count || 0}
                              </span>
                              <span className="text-brand-grey-400 text-sm">({percentage}%)</span>
                              <div className="w-16 bg-brand-grey-700 rounded-full h-2">
                                <div
                                  className={`${bgColors[index % bgColors.length]} h-2 rounded-full`}
                                  style={{ width: `${Math.min(100, percentage)}%` }}
                                ></div>
                              </div>
                            </div>
                          </div>
                        );
                      })
                    ) : (
                      <div className="text-brand-grey-400 text-center py-4">
                        No business domain data available
                      </div>
                    )}
                  </div>
                </div>

                <div className="bg-brand-grey-900 rounded-xl p-6 border border-brand-grey-700">
                  <h3 className="text-lg font-semibold text-brand-white mb-4 flex items-center gap-2">
                    <Users size={20} className="text-emerald-400" />
                    Client Segments Distribution
                  </h3>
                  <div className="space-y-3 max-h-64 overflow-y-auto">
                    {visitorData?.clientSegmentStats?.length > 0 ? (
                      visitorData.clientSegmentStats.map((segment, index) => {
                        const colors = ['text-emerald-400', 'text-teal-400', 'text-green-400', 'text-lime-400', 'text-yellow-400'];
                        const bgColors = ['bg-emerald-500', 'bg-teal-500', 'bg-green-500', 'bg-lime-500', 'bg-yellow-500'];
                        const maxCount = Math.max(...visitorData.clientSegmentStats.map(s => s.count || 0));
                        const percentage = maxCount > 0 ? Math.round((segment.count / maxCount) * 100) : 0;

                        return (
                          <div key={index} className="flex items-center justify-between p-3 bg-brand-grey-800 rounded-lg">
                            <div className="flex items-center gap-3">
                              <div className={`w-3 h-3 rounded-full ${bgColors[index % bgColors.length]}`}></div>
                              <span className="text-brand-white font-medium">
                                {segment._id || 'Not Specified'}
                              </span>
                            </div>
                            <div className="flex items-center gap-2">
                              <span className={`font-medium ${colors[index % colors.length]}`}>
                                {segment.count || 0}
                              </span>
                              <span className="text-brand-grey-400 text-sm">({percentage}%)</span>
                              <div className="w-16 bg-brand-grey-700 rounded-full h-2">
                                <div
                                  className={`${bgColors[index % bgColors.length]} h-2 rounded-full`}
                                  style={{ width: `${Math.min(100, percentage)}%` }}
                                ></div>
                              </div>
                            </div>
                          </div>
                        );
                      })
                    ) : (
                      <div className="text-brand-grey-400 text-center py-4">
                        No client segment data available
                      </div>
                    )}
                  </div>
                </div>
              </div>

              {/* Additional Analytics Blocks */}
              <div className="grid lg:grid-cols-2 gap-6 mt-6">
                <div className="bg-brand-grey-900 rounded-xl p-6 border border-brand-grey-700">
                  <h3 className="text-lg font-semibold text-brand-white mb-4 flex items-center gap-2">
                    <UserCheck size={20} className="text-blue-400" />
                    Detailed Visitor Analytics
                  </h3>
                  <Suspense fallback={<div className="h-64 bg-brand-grey-800 rounded-lg animate-pulse" />}>
                    <VisitorAnalyticsTable />
                  </Suspense>
                </div>

                <div className="bg-brand-grey-900 rounded-xl p-6 border border-brand-grey-700">
                  <h3 className="text-lg font-semibold text-brand-white mb-4 flex items-center gap-2">
                    <TrendingUp size={20} className="text-green-400" />
                    Lead Scoring & Performance
                  </h3>
                  <Suspense fallback={<div className="h-64 bg-brand-grey-800 rounded-lg animate-pulse" />}>
                    <LeadScoringMetrics />
                  </Suspense>
                </div>
              </div>

              {/* Action Buttons */}
              <div className="flex gap-3 mt-6">
                <button className="px-4 py-2 bg-brand-accent-100 hover:bg-brand-accent-200 text-brand-grey-950 rounded-lg font-medium transition-colors">
                  Export Report
                </button>
                <button className="px-4 py-2 bg-brand-grey-800 hover:bg-brand-grey-700 text-brand-white rounded-lg font-medium border border-brand-grey-700 transition-colors">
                  Email Campaign
                </button>
              </div>
            </div>
          }
        />
        <Route path="blogs" element={<BlogManagement />} />
        <Route path="services" element={<ServiceManagement />} />
        <Route path="financial-health" element={<FinancialHealthDashboard />} />
      </Routes>
    </DashboardLayout>
  );
};

export default MarketingDashboard;
