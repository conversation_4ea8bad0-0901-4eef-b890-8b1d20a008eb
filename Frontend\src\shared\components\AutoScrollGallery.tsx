import { memo, useEffect, useState } from 'react';
import { motion } from 'framer-motion';

interface GalleryImage {
  id: string;
  src: string;
  alt: string;
  title?: string;
  description?: string;
}

interface AutoScrollGalleryProps {
  images: GalleryImage[];
  speed?: number;
  direction?: 'left' | 'right';
  pauseOnHover?: boolean;
  showOverlay?: boolean;
}

const AutoScrollGallery = memo(({
  images,
  speed = 20,
  direction = 'left',
  pauseOnHover = true,
  showOverlay = true
}: AutoScrollGalleryProps) => {
  const [isPaused, setIsPaused] = useState(false);
  const [hoveredImage, setHoveredImage] = useState<string | null>(null);

  // Duplicate images for seamless loop
  const duplicatedImages = [...images, ...images];

  return (
    <div className="relative overflow-hidden py-8">
      {/* Gradient overlays */}
      <div className="absolute left-0 top-0 bottom-0 w-32 bg-gradient-to-r from-brand-black via-brand-black/80 to-transparent z-10" />
      <div className="absolute right-0 top-0 bottom-0 w-32 bg-gradient-to-l from-brand-black via-brand-black/80 to-transparent z-10" />
      
      <div 
        className={`flex gap-6 ${direction === 'left' ? 'animate-auto-scroll' : 'animate-auto-scroll-reverse'}`}
        style={{
          animationDuration: `${speed}s`,
          animationPlayState: isPaused ? 'paused' : 'running'
        }}
        onMouseEnter={() => pauseOnHover && setIsPaused(true)}
        onMouseLeave={() => {
          pauseOnHover && setIsPaused(false);
          setHoveredImage(null);
        }}
      >
        {duplicatedImages.map((image, index) => (
          <motion.div
            key={`${image.id}-${index}`}
            className="relative flex-shrink-0 group cursor-pointer"
            onMouseEnter={() => setHoveredImage(image.id)}
            onMouseLeave={() => setHoveredImage(null)}
            whileHover={{ scale: 1.05 }}
            transition={{ duration: 0.3 }}
          >
            <div className="relative w-80 h-60 rounded-2xl overflow-hidden shadow-xl">
              <img
                src={image.src}
                alt={image.alt}
                className="w-full h-full object-cover transition-transform duration-700 group-hover:scale-110"
                loading="lazy"
              />
              
              {/* Purple gradient overlay */}
              <div className="absolute inset-0 bg-gradient-to-t from-brand-purple-900/80 via-brand-purple-600/20 to-transparent opacity-60 group-hover:opacity-80 transition-opacity duration-300" />
              
              {/* Glow effect */}
              <div className="absolute inset-0 rounded-2xl ring-2 ring-brand-purple-500/0 group-hover:ring-brand-purple-500/50 transition-all duration-300" />
              
              {/* Content overlay */}
              {showOverlay && (image.title || image.description) && (
                <motion.div
                  className="absolute bottom-0 left-0 right-0 p-6 text-white"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ 
                    opacity: hoveredImage === image.id ? 1 : 0,
                    y: hoveredImage === image.id ? 0 : 20
                  }}
                  transition={{ duration: 0.3 }}
                >
                  {image.title && (
                    <h3 className="text-xl font-bold mb-2 text-shadow-lg">
                      {image.title}
                    </h3>
                  )}
                  {image.description && (
                    <p className="text-sm text-brand-purple-100 text-shadow">
                      {image.description}
                    </p>
                  )}
                </motion.div>
              )}
              
              {/* Shimmer effect on hover */}
              <div className="absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity duration-500">
                <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent shimmer-effect" />
              </div>
            </div>
          </motion.div>
        ))}
      </div>
    </div>
  );
});

AutoScrollGallery.displayName = 'AutoScrollGallery';

export default AutoScrollGallery;
