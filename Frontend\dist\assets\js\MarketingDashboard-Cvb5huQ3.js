const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/js/CalendarWrapper-XAo8sEwI.js","assets/js/vendor-OXu-rwpf.js","assets/js/react-vendor-Dq0qSR31.js","assets/js/utils-vendor-DSNVchvY.js","assets/react-vendor-DZrYvFpI.css","assets/vendor-Dgihpmma.css","assets/js/BlogManagement-B9uNlAlV.js","assets/js/index-CasGuY6o.js","assets/js/state-vendor-DU4y5LsH.js","assets/index-Cpgs4Ywp.css","assets/js/dialog-BVPp5ZjS.js","assets/js/ServiceManagement-DkljYidy.js","assets/js/FinancialHealthDashboard-CLtqKIL-.js","assets/js/VisitorAnalyticsTable-BAKkue_k.js","assets/js/LeadScoringMetrics-DsV6FA-l.js","assets/js/charts-D1H5f2EZ.js","assets/js/MapComponents-CjMFJQAH.js"])))=>i.map(i=>d[i]);
import{b3 as j}from"./vendor-OXu-rwpf.js";import{r as m,n as F,o as I,j as e,aW as B,aX as q,v as G,O as z,a6 as H,aY as J,Q as T,F as D,X as K,Y as W,aZ as Y,a_ as Q,a$ as X,aS as Z,aT as S,R as v,b0 as ee,a3 as se,a8 as te,b1 as M,a7 as ae,b2 as re,a9 as ne,G as R,$ as ie,b3 as le,aJ as oe,b4 as ce,b5 as de}from"./react-vendor-Dq0qSR31.js";import{c as xe,d as A}from"./index-CasGuY6o.js";const me=({children:x})=>{const[g,o]=m.useState(!1),c=F(),n=I(),{user:h,logout:i}=xe(),p=[{name:"Dashboard",href:"/dashboard",icon:e.jsx(B,{size:20})},{name:"Financial Health",href:"/dashboard/financial-health",icon:e.jsx(q,{size:20})},{name:"Campaigns",href:"/dashboard/campaigns",icon:e.jsx(G,{size:20})},{name:"Leads",href:"/dashboard/leads",icon:e.jsx(z,{size:20})},{name:"Calendar",href:"/dashboard/calendar",icon:e.jsx(H,{size:20})},...h?.role==="admin"?[{name:"Blogs",href:"/dashboard/blogs",icon:e.jsx(J,{size:20})},{name:"Services",href:"/dashboard/services",icon:e.jsx(T,{size:20})}]:[]];return e.jsxs("div",{className:"min-h-screen bg-gradient-to-br from-brand-black via-brand-grey-900 to-brand-grey-800 flex",children:[e.jsxs("aside",{className:A("fixed inset-y-0 left-0 z-50 w-72 bg-brand-grey-950 border-r border-brand-grey-800 shadow-xl transform transition-transform duration-300 flex flex-col lg:translate-x-0",g?"translate-x-0":"-translate-x-full lg:translate-x-0"),children:[e.jsxs("div",{className:"flex items-center justify-between h-20 px-8 border-b border-brand-grey-800 bg-brand-grey-950",children:[e.jsxs(D,{to:"/",className:"text-2xl font-extrabold tracking-tight text-brand-white hover:text-brand-grey-300 transition-colors",children:[e.jsx("span",{className:"inline-block align-middle",children:"Click"}),e.jsx("span",{className:"text-brand-grey-300 inline-block align-middle",children:"4You"})]}),e.jsx("button",{onClick:()=>o(!1),className:"lg:hidden text-brand-grey-400 hover:text-brand-white",children:e.jsx(K,{size:24})})]}),e.jsx("nav",{className:"flex-1 p-6 space-y-2 overflow-y-auto custom-scrollbar",children:p.map(b=>e.jsxs(D,{to:b.href,className:A("flex items-center gap-4 px-4 py-3 rounded-xl text-base font-semibold transition-all duration-200",c.pathname===b.href?"bg-brand-grey-800 text-brand-white shadow-lg":"text-brand-grey-400 hover:text-brand-white hover:bg-brand-grey-800 hover:shadow-md"),children:[b.icon,e.jsx("span",{children:b.name})]},b.name))}),e.jsxs("div",{className:"p-6 border-t border-brand-grey-800 bg-brand-grey-950 flex flex-col gap-3",children:[e.jsxs(D,{to:"/dashboard/settings",className:"flex items-center gap-3 px-4 py-2 rounded-lg text-base font-medium text-brand-grey-400 hover:text-brand-white hover:bg-brand-grey-800 transition-colors",children:[e.jsx(T,{size:20}),e.jsx("span",{children:"Settings"})]}),e.jsx("button",{onClick:async()=>{await i(),n("/login")},className:"flex items-center gap-3 px-4 py-2 rounded-lg text-base font-medium text-red-500 hover:text-white hover:bg-red-600 transition-colors",children:e.jsx("span",{children:"Logout"})})]})]}),e.jsxs("div",{className:"flex-1 flex flex-col lg:ml-72 min-h-screen",children:[e.jsxs("header",{className:"h-20 bg-brand-grey-950 border-b border-brand-grey-800 flex items-center justify-between px-8 shadow-sm sticky top-0 z-30",children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx("button",{onClick:()=>o(!0),className:"lg:hidden text-brand-grey-400 hover:text-brand-white",children:e.jsx(W,{size:24})}),e.jsxs("div",{className:"relative hidden md:block",children:[e.jsx(Y,{size:18,className:"absolute left-4 top-1/2 transform -translate-y-1/2 text-brand-grey-500"}),e.jsx("input",{type:"text",placeholder:"Search dashboard...",className:"bg-brand-grey-900 border border-brand-grey-700 text-brand-white pl-12 pr-4 py-3 rounded-lg focus:border-brand-grey-500 focus:outline-none w-72 shadow-sm"})]})]}),e.jsxs("div",{className:"flex items-center gap-6",children:[e.jsxs("button",{className:"relative text-brand-grey-400 hover:text-brand-white",children:[e.jsx(Q,{size:22}),e.jsx("span",{className:"absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full border-2 border-brand-grey-950"})]}),e.jsxs(D,{to:"/",className:"flex items-center gap-2 text-brand-grey-400 hover:text-brand-white transition-colors",children:[e.jsx(X,{size:18}),e.jsx("span",{className:"hidden sm:inline",children:"Main Site"})]}),e.jsx("div",{className:"w-10 h-10 bg-brand-grey-700 rounded-full flex items-center justify-center shadow-md",children:e.jsx("span",{className:"text-brand-white text-lg font-bold uppercase",children:h?.name?.[0]||"U"})})]})]}),e.jsx("main",{className:"flex-1 bg-gradient-to-br from-brand-grey-900/80 to-brand-black/90 p-8 min-h-[calc(100vh-5rem)] overflow-x-auto",children:x})]}),g&&e.jsx("div",{className:"fixed inset-0 bg-black/60 z-40 lg:hidden",onClick:()=>o(!1)})]})};async function ge(x,g){const o=`geocode_${x}_${g}`,c=localStorage.getItem(o);if(c)try{const i=JSON.parse(c);if(Array.isArray(i)&&i.length===2&&typeof i[0]=="number"&&typeof i[1]=="number")return[i[0],i[1]]}catch{}const h=`https://nominatim.openstreetmap.org/search?format=json&q=${encodeURIComponent(`${x}, ${g}`)}&limit=1`;try{const i=await fetch(h,{headers:{"Accept-Language":"en"}});if(!i.ok)return null;const p=await i.json();if(p&&p.length>0){const b=parseFloat(p[0].lat),N=parseFloat(p[0].lon);return localStorage.setItem(o,JSON.stringify([b,N])),[b,N]}}catch{}return null}const be=v.lazy(()=>j(()=>import("./CalendarWrapper-XAo8sEwI.js"),__vite__mapDeps([0,1,2,3,4,5]))),he=v.lazy(()=>j(()=>import("./BlogManagement-B9uNlAlV.js"),__vite__mapDeps([6,2,1,5,3,4,7,8,9,10]))),ue=v.lazy(()=>j(()=>import("./ServiceManagement-DkljYidy.js"),__vite__mapDeps([11,2,1,5,3,4,7,8,9]))),pe=v.lazy(()=>j(()=>import("./FinancialHealthDashboard-CLtqKIL-.js"),__vite__mapDeps([12,2,1,5,3,4,7,8,9]))),ye=v.lazy(()=>j(()=>import("./VisitorAnalyticsTable-BAKkue_k.js"),__vite__mapDeps([13,2,1,5,3,4]))),fe=v.lazy(()=>j(()=>import("./LeadScoringMetrics-DsV6FA-l.js"),__vite__mapDeps([14,2,1,5,3,4,15]))),je=v.lazy(()=>j(()=>import("./MapComponents-CjMFJQAH.js"),__vite__mapDeps([16,2,1,5,3,4,7,8,9]))),ve=5*60*1e3,k=new Map,Ne=async x=>{console.log("Processing visitor geo data:",x);const g=[];for(const o of x){const c=o._id?.country||o.country||"Unknown",n=o._id?.region||o.region||"Unknown",h=o.count||o.visits||0;if(console.log(`Processing location: ${n}, ${c} (${h} visits)`),c!=="Unknown"&&n!=="Unknown")try{console.log(`Geocoding ${n}, ${c}...`);const i=await ge(n,c);i?(console.log(`Geocoded ${n}, ${c} to:`,i),g.push({lat:i[0],lon:i[1],region:n,country:c,count:h})):console.warn(`No coordinates found for ${n}, ${c}`)}catch(i){console.warn(`Failed to geocode ${n}, ${c}:`,i)}}return console.log("Final geo data:",g),g},we=()=>{console.log("MarketingDashboard component loaded");const[x,g]=m.useState(null),[o,c]=m.useState([]),[n,h]=m.useState(null),[i,p]=m.useState([]),[b,N]=m.useState(!0),[L,_]=m.useState(null),[u,P]=m.useState("Last 7 days"),E=s=>{const t=k.get(s);return t&&Date.now()-t.timestamp<ve?t.data:null},V=(s,t)=>{k.set(s,{data:t,timestamp:Date.now()})},$=async(s,t=3)=>{for(let r=0;r<t;r++)try{const a=await fetch(s,{credentials:"include",headers:{"Content-Type":"application/json"}});if(a.status===429){const l=Math.pow(2,r)*1e3;console.warn(`Rate limited. Retrying in ${l}ms...`),await new Promise(d=>setTimeout(d,l));continue}if(!a.ok)throw new Error(`HTTP ${a.status}: ${a.statusText}`);return await a.json()}catch(a){if(r===t-1)throw a;await new Promise(l=>setTimeout(l,1e3*(r+1)))}},C=async(s=!1)=>{try{if(N(!0),_(null),s)k.clear();else{const w=`analytics_${u}`,f=E(w);if(f){g(f.traffic),c(f.countryStats),h(f.visitorAnalytics),p(f.geoData),N(!1);return}}const t=w=>new Promise(f=>setTimeout(f,w));console.log("Fetching visitor traffic...");const r=await $(`http://localhost:5000/api/visitor/traffic?period=${u}`);await t(500),console.log("Fetching visitor country stats...");const a=await $(`http://localhost:5000/api/visitor/country-stats?period=${u}`);await t(500),console.log("Fetching visitor analytics...");const l=await $(`http://localhost:5000/api/visitor/analytics?period=${u}`);console.log("Processing geo data with country data:",a);const d=await Ne(a);console.log("Processed geo data result:",d);const y=`analytics_${u}`;V(y,{traffic:r,countryStats:a,visitorAnalytics:l,geoData:d}),g(r),c(a),h(l),p(d)}catch(t){console.error("Error fetching analytics data:",t),t.message.includes("429")?_("Too many requests. Please wait a moment and try again."):t.message.includes("Failed to fetch")?_("Unable to connect to analytics server. Please check if the backend is running."):_(`Failed to load analytics data: ${t.message}`)}finally{N(!1)}};m.useEffect(()=>{const s=setTimeout(()=>{C()},1e3);return()=>clearTimeout(s)},[u]);const U=()=>{C(!0)},O=({title:s,value:t,change:r,icon:a,color:l})=>e.jsx("div",{className:"bg-brand-grey-900 rounded-xl p-4 border border-brand-grey-700",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("p",{className:"text-brand-grey-400 text-sm font-medium",children:s}),e.jsx("p",{className:"text-2xl font-bold text-brand-white mt-1",children:t}),r&&e.jsxs("div",{className:`flex items-center gap-1 mt-2 ${r>=0?"text-green-400":"text-red-400"}`,children:[r>=0?e.jsx(ce,{size:16}):e.jsx(de,{size:16}),e.jsxs("span",{className:"text-sm font-medium",children:[Math.abs(r),"%"]})]})]}),e.jsx("div",{className:`p-3 rounded-lg ${l}`,children:e.jsx(a,{size:24,className:"text-white"})})]})});return e.jsx(me,{children:e.jsxs(Z,{children:[e.jsx(S,{path:"/calendar",element:e.jsx(m.Suspense,{fallback:e.jsx("div",{className:"flex items-center justify-center h-64",children:e.jsx("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-brand-accent-100"})}),children:e.jsx(be,{})})}),e.jsx(S,{path:"/",element:e.jsxs("div",{className:"p-4 space-y-4",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("h1",{className:"text-2xl font-bold text-brand-white",children:"Marketing Analytics"}),e.jsx("p",{className:"text-brand-grey-400 text-sm",children:"Visitor tracking & marketing performance"})]}),e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsxs("button",{onClick:U,disabled:b,className:"flex items-center gap-2 px-3 py-2 bg-brand-grey-800 hover:bg-brand-grey-700 disabled:opacity-50 disabled:cursor-not-allowed rounded-lg border border-brand-grey-700 text-brand-grey-300 hover:text-brand-white transition-colors",children:[e.jsx(ee,{size:16,className:b?"animate-spin":""}),b?"Loading...":"Refresh"]}),e.jsx("div",{className:"flex bg-brand-grey-800 rounded-lg p-1",children:["7d","30d","90d","1y"].map(s=>e.jsx("button",{onClick:()=>P(s==="7d"?"Last 7 days":s==="30d"?"Last 30 days":s==="90d"?"Last 3 months":"Last year"),className:`px-3 py-1 rounded text-sm font-medium transition-all ${s==="7d"&&u==="Last 7 days"||s==="30d"&&u==="Last 30 days"||s==="90d"&&u==="Last 3 months"||s==="1y"&&u==="Last year"?"bg-brand-accent-100 text-brand-grey-950":"text-brand-grey-400 hover:text-brand-white"}`,children:s},s))})]})]}),L&&e.jsxs("div",{className:"bg-red-900/20 border border-red-700 rounded-lg p-4 flex items-center gap-3",children:[e.jsx(se,{size:20,className:"text-red-400"}),e.jsxs("div",{children:[e.jsx("p",{className:"text-red-400 font-medium",children:"Analytics Error"}),e.jsx("p",{className:"text-red-300 text-sm",children:L})]})]}),e.jsx("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-3 mb-6",children:[{title:"Total Visits",value:x?.totalVisits?.toLocaleString()??"0",change:12.5,icon:z,color:"bg-blue-600"},{title:"Page Views",value:x?.pageViews?.toLocaleString()??"0",change:8.2,icon:te,color:"bg-green-600"},{title:"Unique Visitors",value:x?.uniqueVisitors?.toLocaleString()??"0",change:15.3,icon:M,color:"bg-purple-600"},{title:"Avg. Session",value:x?.avgSessionDuration?`${Math.round(x.avgSessionDuration/1e3)}s`:"0s",change:-2.1,icon:ae,color:"bg-orange-600"}].map((s,t)=>e.jsx(O,{...s},t))}),e.jsxs("div",{className:"grid lg:grid-cols-2 gap-6",children:[e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"bg-brand-grey-900 rounded-xl p-6 border border-brand-grey-700",children:[e.jsx("div",{className:"flex items-center justify-between mb-4",children:e.jsxs("h3",{className:"text-lg font-semibold text-brand-white flex items-center gap-2",children:[e.jsx(re,{size:20,className:"text-blue-400"}),"Traffic Trends"]})}),e.jsxs("div",{className:"space-y-4",children:[e.jsx("h4",{className:"text-brand-grey-300 font-medium",children:"Visitor Status Distribution"}),e.jsxs("div",{className:"space-y-3",children:[n?.visitorsByStatus?.map((s,t)=>{const r={new:"New Visitors",in_progress:"In Progress",qualified:"Qualified Leads",converted:"Converted",lost:"Lost"},a={new:"text-blue-400",in_progress:"text-yellow-400",qualified:"text-green-400",converted:"text-purple-400",lost:"text-red-400"};return e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"text-brand-grey-300",children:r[s._id]||s._id||"Unknown"}),e.jsx("span",{className:`font-medium ${a[s._id]||"text-brand-white"}`,children:s.count||0})]},t)}),(!n?.visitorsByStatus||n.visitorsByStatus.length===0)&&e.jsx("div",{className:"text-brand-grey-400 text-center py-4",children:"No visitor status data available"})]})]})]}),e.jsxs("div",{className:"bg-brand-grey-900 rounded-xl p-6 border border-brand-grey-700",children:[e.jsxs("h3",{className:"text-lg font-semibold text-brand-white mb-4 flex items-center gap-2",children:[e.jsx(ne,{size:20,className:"text-green-400"}),"Lead Score Distribution"]}),e.jsxs("div",{className:"space-y-3",children:[n?.leadScoreDistribution?.map((s,t)=>{const r=["0-25","25-50","50-75","75-100"],a=["text-red-400","text-yellow-400","text-blue-400","text-green-400"];return e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"text-brand-grey-300",children:r[t]||"Other"}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("span",{className:`font-medium ${a[t]||"text-brand-white"}`,children:s.count||0}),e.jsx("span",{className:"text-brand-grey-400 text-sm",children:"visitors"})]})]},t)}),(!n?.leadScoreDistribution||n.leadScoreDistribution.length===0)&&e.jsx("div",{className:"text-brand-grey-400 text-center py-4",children:"No lead score data available"})]})]})]}),e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"bg-brand-grey-900 rounded-xl p-6 border border-brand-grey-700",children:[e.jsxs("h3",{className:"text-lg font-semibold text-brand-white mb-4 flex items-center gap-2",children:[e.jsx(R,{size:20,className:"text-purple-400"}),"Visitor Location Map"]}),e.jsx("div",{className:"text-brand-grey-400 text-sm mb-4",children:"Real-time visitor locations based on analytics data"}),e.jsx(m.Suspense,{fallback:e.jsx("div",{className:"h-96 bg-brand-grey-800 rounded-lg animate-pulse flex items-center justify-center",children:e.jsx("span",{className:"text-brand-grey-400",children:"Loading map..."})}),children:i.length>0?e.jsx(je,{geoData:i}):e.jsx("div",{className:"h-96 bg-brand-grey-800 rounded-lg flex items-center justify-center",children:e.jsxs("div",{className:"text-center",children:[e.jsx(R,{size:48,className:"text-brand-grey-600 mx-auto mb-2"}),e.jsx("p",{className:"text-brand-grey-400",children:"Processing visitor locations..."}),e.jsx("p",{className:"text-brand-grey-500 text-sm",children:"Map will appear once location data is geocoded"})]})})})]}),e.jsxs("div",{className:"bg-brand-grey-900 rounded-xl p-6 border border-brand-grey-700",children:[e.jsxs("h3",{className:"text-lg font-semibold text-brand-white mb-4 flex items-center gap-2",children:[e.jsx(ie,{size:20,className:"text-orange-400"}),"Regional Visitor Statistics"]}),e.jsx("div",{className:"space-y-3 max-h-64 overflow-y-auto",children:o.length>0?o.map((s,t)=>{console.log("Processing stat for table:",s);const r=s._id?.country||s.country||"Unknown",a=s._id?.region||s.region||"Unknown",l=s.count||0,d=Math.max(...o.map(w=>w.count||0)),y=d>0?Math.round(l/d*100):0;return console.log(`Table display: ${r} - ${a} (${l} visits, ${y}%)`),e.jsxs("div",{className:"flex items-center justify-between p-3 bg-brand-grey-800 rounded-lg hover:bg-brand-grey-700 transition-colors",children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("div",{className:"w-8 h-8 bg-purple-600 rounded-full flex items-center justify-center text-white text-sm font-bold",children:r?.slice(0,2).toUpperCase()||"UN"}),e.jsxs("div",{children:[e.jsx("span",{className:"text-brand-white font-medium",children:r}),a!=="Unknown"&&e.jsx("div",{className:"text-brand-grey-400 text-sm",children:a})]})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("span",{className:"text-brand-white font-medium",children:l}),e.jsxs("span",{className:"text-brand-grey-400 text-sm",children:["(",y,"%)"]}),e.jsx("div",{className:"w-16 bg-brand-grey-700 rounded-full h-2",children:e.jsx("div",{className:"bg-purple-500 h-2 rounded-full",style:{width:`${Math.min(100,y)}%`}})})]})]},t)}):e.jsx("div",{className:"text-center py-8 text-brand-grey-400",children:"No regional data available"})})]})]})]}),e.jsxs("div",{className:"grid lg:grid-cols-2 gap-6 mt-6",children:[e.jsxs("div",{className:"bg-brand-grey-900 rounded-xl p-6 border border-brand-grey-700",children:[e.jsxs("h3",{className:"text-lg font-semibold text-brand-white mb-4 flex items-center gap-2",children:[e.jsx(le,{size:20,className:"text-cyan-400"}),"Business Domains Distribution"]}),e.jsx("div",{className:"space-y-3 max-h-64 overflow-y-auto",children:n?.businessDomainStats?.length>0?n.businessDomainStats.map((s,t)=>{const r=["text-cyan-400","text-blue-400","text-purple-400","text-pink-400","text-orange-400"],a=["bg-cyan-500","bg-blue-500","bg-purple-500","bg-pink-500","bg-orange-500"],l=Math.max(...n.businessDomainStats.map(y=>y.count||0)),d=l>0?Math.round(s.count/l*100):0;return e.jsxs("div",{className:"flex items-center justify-between p-3 bg-brand-grey-800 rounded-lg",children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("div",{className:`w-3 h-3 rounded-full ${a[t%a.length]}`}),e.jsx("span",{className:"text-brand-white font-medium",children:s._id||"Not Specified"})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("span",{className:`font-medium ${r[t%r.length]}`,children:s.count||0}),e.jsxs("span",{className:"text-brand-grey-400 text-sm",children:["(",d,"%)"]}),e.jsx("div",{className:"w-16 bg-brand-grey-700 rounded-full h-2",children:e.jsx("div",{className:`${a[t%a.length]} h-2 rounded-full`,style:{width:`${Math.min(100,d)}%`}})})]})]},t)}):e.jsx("div",{className:"text-brand-grey-400 text-center py-4",children:"No business domain data available"})})]}),e.jsxs("div",{className:"bg-brand-grey-900 rounded-xl p-6 border border-brand-grey-700",children:[e.jsxs("h3",{className:"text-lg font-semibold text-brand-white mb-4 flex items-center gap-2",children:[e.jsx(z,{size:20,className:"text-emerald-400"}),"Client Segments Distribution"]}),e.jsx("div",{className:"space-y-3 max-h-64 overflow-y-auto",children:n?.clientSegmentStats?.length>0?n.clientSegmentStats.map((s,t)=>{const r=["text-emerald-400","text-teal-400","text-green-400","text-lime-400","text-yellow-400"],a=["bg-emerald-500","bg-teal-500","bg-green-500","bg-lime-500","bg-yellow-500"],l=Math.max(...n.clientSegmentStats.map(y=>y.count||0)),d=l>0?Math.round(s.count/l*100):0;return e.jsxs("div",{className:"flex items-center justify-between p-3 bg-brand-grey-800 rounded-lg",children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("div",{className:`w-3 h-3 rounded-full ${a[t%a.length]}`}),e.jsx("span",{className:"text-brand-white font-medium",children:s._id||"Not Specified"})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("span",{className:`font-medium ${r[t%r.length]}`,children:s.count||0}),e.jsxs("span",{className:"text-brand-grey-400 text-sm",children:["(",d,"%)"]}),e.jsx("div",{className:"w-16 bg-brand-grey-700 rounded-full h-2",children:e.jsx("div",{className:`${a[t%a.length]} h-2 rounded-full`,style:{width:`${Math.min(100,d)}%`}})})]})]},t)}):e.jsx("div",{className:"text-brand-grey-400 text-center py-4",children:"No client segment data available"})})]})]}),e.jsxs("div",{className:"grid lg:grid-cols-2 gap-6 mt-6",children:[e.jsxs("div",{className:"bg-brand-grey-900 rounded-xl p-6 border border-brand-grey-700",children:[e.jsxs("h3",{className:"text-lg font-semibold text-brand-white mb-4 flex items-center gap-2",children:[e.jsx(M,{size:20,className:"text-blue-400"}),"Detailed Visitor Analytics"]}),e.jsx(m.Suspense,{fallback:e.jsx("div",{className:"h-64 bg-brand-grey-800 rounded-lg animate-pulse"}),children:e.jsx(ye,{})})]}),e.jsxs("div",{className:"bg-brand-grey-900 rounded-xl p-6 border border-brand-grey-700",children:[e.jsxs("h3",{className:"text-lg font-semibold text-brand-white mb-4 flex items-center gap-2",children:[e.jsx(oe,{size:20,className:"text-green-400"}),"Lead Scoring & Performance"]}),e.jsx(m.Suspense,{fallback:e.jsx("div",{className:"h-64 bg-brand-grey-800 rounded-lg animate-pulse"}),children:e.jsx(fe,{})})]})]}),e.jsxs("div",{className:"flex gap-3 mt-6",children:[e.jsx("button",{className:"px-4 py-2 bg-brand-accent-100 hover:bg-brand-accent-200 text-brand-grey-950 rounded-lg font-medium transition-colors",children:"Export Report"}),e.jsx("button",{className:"px-4 py-2 bg-brand-grey-800 hover:bg-brand-grey-700 text-brand-white rounded-lg font-medium border border-brand-grey-700 transition-colors",children:"Email Campaign"})]})]})}),e.jsx(S,{path:"blogs",element:e.jsx(he,{})}),e.jsx(S,{path:"services",element:e.jsx(ue,{})}),e.jsx(S,{path:"financial-health",element:e.jsx(pe,{})})]})})},$e=Object.freeze(Object.defineProperty({__proto__:null,default:we},Symbol.toStringTag,{value:"Module"}));export{$e as M,ge as g};
