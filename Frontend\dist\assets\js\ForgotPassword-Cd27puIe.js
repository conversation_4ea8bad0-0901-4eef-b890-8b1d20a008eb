import{r,ai as v,j as e,b6 as C,aj as m,F as d}from"./react-vendor-Dq0qSR31.js";import{b6 as F,b4 as k,b5 as S}from"./vendor-OXu-rwpf.js";import{C as x,e as h,f as j,h as u,i as f,A as p,j as E,k as g,l as y,m as w,F as I,n as A,o as L,p as R,q as T,I as D,r as P,s as B}from"./index-CasGuY6o.js";import"./utils-vendor-DSNVchvY.js";import"./state-vendor-DU4y5LsH.js";const M=k({email:S().email("Invalid email address")});function G(){const[N,l]=r.useState(!1),[i,n]=r.useState(!1),[o,c]=r.useState(""),s=v({resolver:F(M),defaultValues:{email:""}}),b=async t=>{c(""),n(!0);try{await B(t.email),l(!0)}catch(a){c(a?.response?.data?.message||(a instanceof Error?a.message:"Failed to send reset email"))}finally{n(!1)}};return N?e.jsx("div",{className:"min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8",children:e.jsxs(x,{className:"w-full max-w-md",children:[e.jsxs(h,{children:[e.jsx("div",{className:"mx-auto flex h-12 w-12 items-center justify-center rounded-full bg-green-100",children:e.jsx(C,{className:"h-6 w-6 text-green-600"})}),e.jsx(j,{className:"text-center text-2xl font-bold tracking-tight",children:"Check your email"}),e.jsxs(u,{className:"text-center",children:["We've sent a password reset link to ",s.watch("email")]})]}),e.jsx(f,{children:e.jsxs(p,{children:[e.jsx(m,{className:"h-4 w-4"}),e.jsx(E,{children:"Email sent"}),e.jsx(g,{children:"If you don't see the email, check your spam folder or try again."})]})}),e.jsxs(y,{className:"flex flex-col gap-4",children:[e.jsx(w,{asChild:!0,className:"w-full",children:e.jsx(d,{to:"/login",children:"Back to login"})}),e.jsxs("p",{className:"text-center text-sm text-muted-foreground",children:["Didn't receive the email?"," ",e.jsx("button",{onClick:()=>l(!1),className:"font-medium text-indigo-600 hover:text-indigo-500",children:"Resend"})]})]})]})}):e.jsx("div",{className:"min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8",children:e.jsxs("div",{className:"max-w-md w-full space-y-8",children:[e.jsxs("div",{className:"text-center",children:[e.jsx("h2",{className:"mt-6 text-3xl font-extrabold text-gray-900",children:"Forgot your password?"}),e.jsx("p",{className:"mt-2 text-sm text-gray-600",children:"Enter your email and we'll send you a link to reset your password"})]}),e.jsxs(x,{className:"w-full max-w-md",children:[e.jsxs(h,{children:[e.jsx(j,{children:"Reset password"}),e.jsx(u,{children:"Enter your email to receive a reset link"})]}),e.jsxs(f,{children:[o&&e.jsxs(p,{variant:"destructive",className:"mb-4",children:[e.jsx(m,{className:"h-4 w-4"}),e.jsx(g,{children:o})]}),e.jsx(I,{...s,children:e.jsxs("form",{onSubmit:s.handleSubmit(b),className:"space-y-4",children:[e.jsx(A,{control:s.control,name:"email",render:({field:t})=>e.jsxs(L,{children:[e.jsx(R,{children:"Email address"}),e.jsx(T,{children:e.jsx(D,{type:"email",placeholder:"<EMAIL>",...t})}),e.jsx(P,{})]})}),e.jsx(w,{type:"submit",className:"w-full",disabled:i,children:i?"Sending reset link...":"Send reset link"})]})})]}),e.jsx(y,{className:"flex flex-col items-start gap-2 text-sm",children:e.jsxs("p",{className:"text-center text-sm text-gray-600 w-full",children:["Remember your password?"," ",e.jsx(d,{to:"/login",className:"font-medium text-indigo-600 hover:text-indigo-500",children:"Sign in"})]})})]})]})})}export{G as default};
