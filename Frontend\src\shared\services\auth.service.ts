import api from '@/shared/services/api/axios';
import { removeToken } from '@/shared/services/api/auth';

export interface ApiResponse {
  success: boolean;
  message: string;
  data?: any;
}

export type UserRole = 'user' | 'admin' | 'marketing_responsible';

export interface User {
  _id: string;
  name: string;
  email: string;
  role: UserRole;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface LoginCredentials {
  email: string;
  password: string;
}

export interface RegisterData {
  name: string;
  email: string;
  password: string;
  role: UserRole;
  businessDomain: string;
  clientSegment: string;
  companyName?: string;
  companySize?: string;
}

export interface AuthResponse {
  success: boolean;
  user: User;
  token?: string; // Optional token for clients that need it (fallback)
}

export const login = async (credentials: LoginCredentials): Promise<AuthResponse> => {
  // Get the current sessionId to match analytics tracking
  const getSessionId = () => {
    const now = Date.now();
    let sessionId = sessionStorage.getItem('sessionId');
    let sessionTimestamp = sessionStorage.getItem('sessionTimestamp');
    if (!sessionId || !sessionTimestamp) {
      sessionId = Math.random().toString(36).substring(2) + now.toString(36);
      sessionStorage.setItem('sessionId', sessionId);
    }
    sessionStorage.setItem('sessionTimestamp', now.toString());
    return sessionId;
  };

  // Include sessionId in login request for visitor linking
  const loginData = {
    ...credentials,
    sessionId: getSessionId()
  };

  const { data } = await api.post<AuthResponse>('/auth/login', loginData, {
    withCredentials: true, // Ensure cookies are sent with the request
  });

  // If the token is included in the response (for clients that need it),
  // we'll use it, but the primary method is still the HTTP-only cookie
  if (data.token) {
    // Store the token in memory (not in localStorage) for this session
    // This is a fallback for cases where cookies might not be available
    sessionStorage.setItem('temp_token', data.token);
  }

  return data;
};

export const register = async (userData: RegisterData): Promise<AuthResponse> => {
  const response = await api.post<AuthResponse>('/auth/register', userData, {
    withCredentials: true,
  });
  return response.data;
};

export const logout = async (): Promise<void> => {
  try {
    await api.post('/auth/logout', {}, { withCredentials: true });
  } catch (error) {
    throw error;
  } finally {
    // Clear any client-side auth state
    removeToken();
    // Clear the temporary token from session storage
    sessionStorage.removeItem('temp_token');
  }
};

export const getCurrentUser = async (): Promise<User | null> => {
  try {
    
    const response = await api.get<{ 
      success: boolean; 
      data: User; 
      token?: string;
      message?: string;
    }>('/auth/me', {
      withCredentials: true, // Important: this sends cookies with the request
      timeout: 10000, // 10 second timeout
    });
    
    // Log response without sensitive data
   
    // Handle non-successful responses
    if (!response.data?.success) {
      return null;
    }
    
    // The user data is in the data.data property
    const userData = response.data.data;
    
    if (!userData) {
      return null;
    }
    
    // If the token is included in the response, store it in session storage
    if (response.data.token) {
      sessionStorage.setItem('temp_token', response.data.token);
    }
    
   
    
    return userData;
  } catch (error: any) {
    // Handle Axios errors
    if (error.code === 'ECONNABORTED') {
      console.error('Request to /auth/me timed out');
    } else if (error.response) {
      // The request was made and the server responded with a status code
      // that falls out of the range of 2xx
      console.error('Error response from server:', {
        status: error.response.status,
        statusText: error.response.statusText,
        data: error.response.data,
      });
      
      // If we get a 401, clear any existing tokens
      if (error.response.status === 401) {
        sessionStorage.removeItem('temp_token');
        localStorage.removeItem('auth_token');
      }
    } else if (error.request) {
      // The request was made but no response was received
      console.error('No response received:', error.request);
    } else {
      // Something happened in setting up the request that triggered an Error
      console.error('Error setting up request:', error.message);
    }
    
    return null;
  }
};

export const forgotPassword = async (email: string): Promise<ApiResponse> => {
  const response = await api.post<ApiResponse>('/auth/forgotpassword', { email });
  return response.data;
};

export const resetPassword = async (token: string, password: string): Promise<ApiResponse> => {
  const response = await api.put<ApiResponse>(`/auth/resetpassword/${token}`, { password });
  return response.data;
};
