import { motion } from 'framer-motion';
import { useEffect, useState } from 'react';

interface MorphingBlobProps {
  size?: number;
  color?: string;
  className?: string;
  speed?: number;
}

const MorphingBlob = ({ 
  size = 200, 
  color = '#9333ea', 
  className = '',
  speed = 8 
}: MorphingBlobProps) => {
  const [currentPath, setCurrentPath] = useState(0);

  // Different blob shapes
  const blobPaths = [
    "M60,-60C80,-40,100,-20,100,0C100,20,80,40,60,60C40,80,20,100,0,100C-20,100,-40,80,-60,60C-80,40,-100,20,-100,0C-100,-20,-80,-40,-60,-60C-40,-80,-20,-100,0,-100C20,-100,40,-80,60,-60Z",
    "M40,-60C60,-50,90,-30,90,0C90,30,60,50,40,60C20,70,0,80,-20,60C-40,40,-60,20,-80,0C-100,-20,-80,-40,-60,-60C-40,-80,-20,-100,0,-90C20,-80,20,-70,40,-60Z",
    "M50,-70C70,-60,90,-40,100,-10C110,20,100,50,80,70C60,90,30,100,0,100C-30,100,-60,90,-80,70C-100,50,-110,20,-100,-10C-90,-40,-70,-60,-50,-70C-30,-80,0,-90,20,-80C40,-70,30,-80,50,-70Z",
    "M30,-50C50,-40,80,-20,90,10C100,40,80,70,50,80C20,90,-10,80,-30,60C-50,40,-60,10,-60,-20C-60,-50,-50,-80,-30,-90C-10,-100,10,-90,30,-80C50,-70,10,-60,30,-50Z",
    "M70,-50C80,-30,70,-10,60,10C50,30,40,50,20,60C0,70,-20,70,-40,60C-60,50,-80,30,-90,10C-100,-10,-90,-30,-70,-50C-50,-70,-20,-90,10,-80C40,-70,60,-70,70,-50Z"
  ];

  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentPath((prev) => (prev + 1) % blobPaths.length);
    }, speed * 1000);

    return () => clearInterval(interval);
  }, [blobPaths.length, speed]);

  return (
    <div className={`relative ${className}`} style={{ width: size, height: size }}>
      <svg
        width={size}
        height={size}
        viewBox="-100 -100 200 200"
        className="absolute inset-0"
      >
        <defs>
          <linearGradient id="blobGradient" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" stopColor={color} stopOpacity="0.8" />
            <stop offset="50%" stopColor={color} stopOpacity="0.6" />
            <stop offset="100%" stopColor={color} stopOpacity="0.4" />
          </linearGradient>
          <filter id="glow">
            <feGaussianBlur stdDeviation="3" result="coloredBlur"/>
            <feMerge> 
              <feMergeNode in="coloredBlur"/>
              <feMergeNode in="SourceGraphic"/>
            </feMerge>
          </filter>
        </defs>
        
        <motion.path
          d={blobPaths[currentPath]}
          fill="url(#blobGradient)"
          filter="url(#glow)"
          animate={{
            d: blobPaths[currentPath],
            rotate: [0, 360],
          }}
          transition={{
            d: { duration: speed * 0.8, ease: "easeInOut" },
            rotate: { duration: speed * 4, repeat: Infinity, ease: "linear" }
          }}
        />
      </svg>
      
      {/* Additional glow effect */}
      <motion.div
        className="absolute inset-0 rounded-full blur-xl opacity-30"
        style={{ backgroundColor: color }}
        animate={{
          scale: [1, 1.2, 1],
          opacity: [0.3, 0.6, 0.3],
        }}
        transition={{
          duration: speed,
          repeat: Infinity,
          ease: "easeInOut",
        }}
      />
    </div>
  );
};

export default MorphingBlob;
