import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { useCart } from '../../core/contexts/CartContext';
import { useAuth } from '../../core/providers/AuthContext';
import { ShoppingCart } from 'lucide-react';

interface CartIconProps {
  onClick?: () => void;
  className?: string;
  showCount?: boolean;
  size?: 'sm' | 'md' | 'lg';
}

const CartIcon: React.FC<CartIconProps> = ({
  onClick,
  className = '',
  showCount = true,
  size = 'md'
}) => {
  const { totalItems } = useCart();
  const itemCount = totalItems;

  const sizeClasses = {
    sm: 'w-5 h-5',
    md: 'w-6 h-6',
    lg: 'w-8 h-8'
  };

  const badgeSizeClasses = {
    sm: 'w-4 h-4 text-xs',
    md: 'w-5 h-5 text-sm',
    lg: 'w-6 h-6 text-base'
  };

  return (
    <motion.div
      className={`relative cursor-pointer ${className}`}
      onClick={onClick}
      whileHover={{ scale: 1.1 }}
      whileTap={{ scale: 0.95 }}
      transition={{ duration: 0.2 }}
    >
      {/* Cart Icon with Emoji */}
      <div className="relative flex items-center justify-center">
        {/* Shopping Cart Emoji */}
        <span className="text-2xl filter drop-shadow-lg">🛒</span>
        
        {/* Alternative Lucide Icon (commented out) */}
        {/* <ShoppingCart className={`${sizeClasses[size]} text-brand-white`} /> */}
        
        {/* Item Count Badge */}
        {showCount && itemCount > 0 && (
          <motion.div
            className={`absolute -top-2 -right-2 ${badgeSizeClasses[size]} bg-gradient-to-r from-brand-purple-500 to-brand-purple-600 text-white rounded-full flex items-center justify-center font-bold shadow-lg border-2 border-white`}
            initial={{ scale: 0 }}
            animate={{ scale: 1 }}
            transition={{ 
              type: "spring", 
              stiffness: 500, 
              damping: 30 
            }}
          >
            {itemCount > 99 ? '99+' : itemCount}
          </motion.div>
        )}
        
        {/* Pulse Animation for New Items */}
        {itemCount > 0 && (
          <motion.div
            className="absolute inset-0 rounded-full bg-brand-purple-400/30"
            animate={{
              scale: [1, 1.2, 1],
              opacity: [0.5, 0, 0.5],
            }}
            transition={{
              duration: 2,
              repeat: Infinity,
              ease: "easeInOut",
            }}
          />
        )}
      </div>
    </motion.div>
  );
};

export default CartIcon;
