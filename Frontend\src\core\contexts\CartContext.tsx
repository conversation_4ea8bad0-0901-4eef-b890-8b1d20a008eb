import React, { createContext, useContext, useState, useEffect } from 'react';
import { useAuth } from '@/core/providers/AuthContext';
import { useAppSelector } from '@/store/hooks';
import * as cartService from '@/shared/services/cart.service';
import { trackCartAction, getSessionId } from '@/shared/services/cart-tracking.service';
import { useToast } from '@/core/contexts/ToastContext';

// Re-export types from cart service
export type { CartItem, Cart } from '@/shared/services/cart.service';

interface CartContextType {
  items: cartService.CartItem[];
  totalItems: number;
  totalPrice: number;
  isAuthenticated: boolean;
  loading: boolean;
  addToCart: (item: Omit<cartService.CartItem, 'id' | 'quantity'>) => Promise<boolean>;
  removeFromCart: (serviceId: string, subServiceName?: string) => Promise<void>;
  updateQuantity: (serviceId: string, subServiceId: string | undefined, quantity: number) => Promise<void>;
  clearCart: () => Promise<void>;
  refreshCart: () => Promise<void>;
}

// Create Context
const CartContext = createContext<CartContextType | undefined>(undefined);

// Provider with backend integration
export const CartProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { user: authUser } = useAuth();
  const reduxUser = useAppSelector((state) => state.auth.user);
  const reduxIsAuthenticated = useAppSelector((state) => state.auth.isAuthenticated);
  const [items, setItems] = useState<cartService.CartItem[]>([]);
  const [loading, setLoading] = useState(false);

  // Use either auth source for user data
  const user = reduxUser || authUser;

  // Simple authentication check - user exists and has ID
  const isUserAuthenticated = Boolean(user && user._id && reduxIsAuthenticated);
  const { showSuccess, showError, showWarning } = useToast();

  // Listen for logout events to clear cart
  useEffect(() => {
    const handleUserLogout = () => {
      setItems([]);
    };

    window.addEventListener('userLoggedOut', handleUserLogout);
    return () => window.removeEventListener('userLoggedOut', handleUserLogout);
  }, []);

  // Load cart from backend on mount
  const loadCart = async () => {
    // Only load if user is authenticated
    if (!isUserAuthenticated) {
      setItems([]);
      return;
    }

    try {
      setLoading(true);
      const cart = await cartService.getCart();
      setItems(cart.items || []);
    } catch (error) {
      console.error('Error loading cart:', error);
      setItems([]);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadCart();
  }, [isUserAuthenticated, user?._id]); // Refresh when authentication status or user changes

  const totalItems = items.reduce((sum, item) => sum + item.quantity, 0);
  const totalPrice = items.reduce((sum, item) => sum + (item.price * item.quantity), 0);

  const addToCart = async (newItem: Omit<cartService.CartItem, 'id' | 'quantity'>): Promise<boolean> => {
    // Always return true - let components handle auth check
    try {
      if (!isUserAuthenticated || !user?._id) {
        // Don't add to cart, but don't show error here
        // Components will handle showing login popup
        return false;
      }

      // Check if item already exists in cart
      const existingItem = items.find(item =>
        item.serviceId === newItem.serviceId &&
        item.subServiceId === newItem.subServiceId
      );

      if (existingItem) {
        console.log('🚫 Item already exists in cart:', {
          serviceName: newItem.serviceName,
          subServiceName: newItem.subServiceName,
          existingQuantity: existingItem.quantity
        });

        const itemName = newItem.subServiceName || newItem.serviceName;
        showWarning(`"${itemName}" is already in your cart! Current quantity: ${existingItem.quantity}`, 2000);
        return false;
      }

      setLoading(true);
      const updatedCart = await cartService.addToCart(newItem);
      setItems(updatedCart.items || []);

      // Track cart action for analytics
      try {
        await trackCartAction({
          action: 'add',
          serviceName: newItem.serviceName,
          subServiceName: newItem.subServiceName,
          subServiceDescription: newItem.description,
          sessionId: getSessionId(),
          userEmail: user?.email,
          userName: user?.name
        });
      } catch (trackingError) {
        console.error('Error tracking cart add action:', trackingError);
        // Don't fail the cart operation if tracking fails
      }

      return true;
    } catch (error) {
      console.error('Error adding to cart:', error);
      return false;
    } finally {
      setLoading(false);
    }
  };

  const removeFromCart = async (serviceId: string | any, subServiceName?: string): Promise<void> => {
    try {
      // Fix serviceId if it's an object (convert to string)
      const actualServiceId = typeof serviceId === 'object' ? serviceId._id || serviceId.id || String(serviceId) : serviceId;

      console.log('🗑️ CartContext - Removing item:', {
        originalServiceId: serviceId,
        actualServiceId,
        subServiceName
      });

      // Find the item being removed for tracking - match by subServiceId (which contains the name)
      const itemToRemove = items.find(item => {
        const itemServiceId = typeof item.serviceId === 'object'
          ? item.serviceId._id || item.serviceId.id || String(item.serviceId)
          : item.serviceId;

        const serviceMatch = itemServiceId === actualServiceId;
        const subServiceMatch = !subServiceName || item.subServiceId === subServiceName;

        console.log('🔍 Comparing item:', {
          itemServiceId,
          actualServiceId,
          serviceMatch,
          itemSubServiceId: item.subServiceId,
          targetSubServiceName: subServiceName,
          subServiceMatch,
          overallMatch: serviceMatch && subServiceMatch
        });

        return serviceMatch && subServiceMatch;
      });

      if (!itemToRemove) {
        console.error('❌ Item not found in cart:', { actualServiceId, subServiceName });
        showError('Item not found in cart', 2000);
        return;
      }

      console.log('📦 Found item to remove:', itemToRemove);

      setLoading(true);
      await cartService.removeFromCart(actualServiceId, subServiceName);

      // Immediately refresh cart from server
      const freshCart = await cartService.getCart();
      setItems(freshCart.items || []);
      console.log('✅ Cart auto-refreshed after deletion:', freshCart);

      const itemName = itemToRemove.subServiceName || itemToRemove.serviceName;
      showSuccess(`"${itemName}" removed from cart`, 1500);

      // Track cart action for analytics
      if (itemToRemove) {
        try {
          await trackCartAction({
            action: 'remove',
            serviceName: itemToRemove.serviceName,
            subServiceName: itemToRemove.subServiceName,
            subServiceDescription: itemToRemove.description,
            sessionId: getSessionId(),
            userEmail: user?.email,
            userName: user?.name
          });
        } catch (trackingError) {
          console.error('Error tracking cart remove action:', trackingError);
          // Don't fail the cart operation if tracking fails
        }
      }
    } catch (error) {
      console.error('❌ Error removing from cart:', error);
      showError('Error removing item from cart', 3000);
    } finally {
      setLoading(false);
    }
  };

  const updateQuantity = async (serviceId: string, subServiceId: string | undefined, quantity: number): Promise<void> => {
    if (quantity <= 0) {
      await removeFromCart(serviceId, subServiceId);
      return;
    }

    try {
      setLoading(true);
      const updatedCart = await cartService.updateCartItemQuantity(serviceId, subServiceId, quantity);
      setItems(updatedCart.items || []);
    } catch (error) {
      console.error('Error updating cart quantity:', error);
    } finally {
      setLoading(false);
    }
  };

  const clearCart = async (): Promise<void> => {
    try {
      setLoading(true);
      await cartService.clearCart();
      setItems([]);
    } catch (error) {
      console.error('Error clearing cart:', error);
    } finally {
      setLoading(false);
    }
  };

  const refreshCart = async (): Promise<void> => {
    try {
      setLoading(true);
      const cart = await cartService.getCart();
      setItems(cart.items || []);
      console.log('🔄 Manual cart refresh completed:', cart);
    } catch (error) {
      console.error('Error refreshing cart:', error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <CartContext.Provider value={{
      items,
      totalItems,
      totalPrice,
      isAuthenticated: isUserAuthenticated,
      loading,
      addToCart,
      removeFromCart,
      updateQuantity,
      clearCart,
      refreshCart
    }}>
      {children}
    </CartContext.Provider>
  );
};



// Simple Hook
export const useCart = () => {
  const context = useContext(CartContext);
  if (context === undefined) {
    throw new Error('useCart must be used within a CartProvider');
  }
  return context;
};
