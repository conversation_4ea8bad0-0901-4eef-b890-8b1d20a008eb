import { motion } from 'framer-motion';
import { useEffect, useState } from 'react';

interface AnimatedBackgroundProps {
  variant?: 'gradient' | 'orbs' | 'waves';
  className?: string;
}

const AnimatedBackground = ({ variant = 'gradient', className = '' }: AnimatedBackgroundProps) => {
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });

  useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      setMousePosition({
        x: e.clientX / window.innerWidth,
        y: e.clientY / window.innerHeight,
      });
    };

    window.addEventListener('mousemove', handleMouseMove);
    return () => window.removeEventListener('mousemove', handleMouseMove);
  }, []);

  if (variant === 'gradient') {
    return (
      <div className={`absolute inset-0 overflow-hidden ${className}`}>
        <motion.div
          className="absolute inset-0 opacity-30"
          style={{
            background: `
              radial-gradient(circle at ${mousePosition.x * 100}% ${mousePosition.y * 100}%, 
                rgba(147, 51, 234, 0.3) 0%, 
                rgba(168, 85, 247, 0.2) 25%, 
                rgba(196, 181, 253, 0.1) 50%, 
                transparent 70%
              )
            `,
          }}
          animate={{
            scale: [1, 1.1, 1],
            rotate: [0, 5, -5, 0],
          }}
          transition={{
            duration: 20,
            repeat: Infinity,
            ease: "easeInOut",
          }}
        />
        
        <motion.div
          className="absolute inset-0 opacity-20"
          animate={{
            background: [
              'radial-gradient(circle at 20% 80%, rgba(147, 51, 234, 0.4) 0%, transparent 50%)',
              'radial-gradient(circle at 80% 20%, rgba(168, 85, 247, 0.4) 0%, transparent 50%)',
              'radial-gradient(circle at 40% 40%, rgba(196, 181, 253, 0.4) 0%, transparent 50%)',
              'radial-gradient(circle at 20% 80%, rgba(147, 51, 234, 0.4) 0%, transparent 50%)',
            ],
          }}
          transition={{
            duration: 15,
            repeat: Infinity,
            ease: "easeInOut",
          }}
        />
      </div>
    );
  }

  if (variant === 'orbs') {
    return (
      <div className={`absolute inset-0 overflow-hidden ${className}`}>
        {[...Array(5)].map((_, i) => (
          <motion.div
            key={i}
            className="absolute rounded-full blur-3xl"
            style={{
              width: `${Math.random() * 300 + 200}px`,
              height: `${Math.random() * 300 + 200}px`,
              background: `radial-gradient(circle, ${
                ['rgba(147, 51, 234, 0.3)', 'rgba(168, 85, 247, 0.3)', 'rgba(196, 181, 253, 0.3)'][i % 3]
              } 0%, transparent 70%)`,
            }}
            animate={{
              x: [
                Math.random() * window.innerWidth,
                Math.random() * window.innerWidth,
                Math.random() * window.innerWidth,
              ],
              y: [
                Math.random() * window.innerHeight,
                Math.random() * window.innerHeight,
                Math.random() * window.innerHeight,
              ],
              scale: [1, 1.5, 1],
            }}
            transition={{
              duration: Math.random() * 20 + 20,
              repeat: Infinity,
              ease: "easeInOut",
              delay: i * 2,
            }}
          />
        ))}
      </div>
    );
  }

  if (variant === 'waves') {
    return (
      <div className={`absolute inset-0 overflow-hidden ${className}`}>
        <svg
          className="absolute inset-0 w-full h-full"
          viewBox="0 0 1200 800"
          preserveAspectRatio="xMidYMid slice"
        >
          <defs>
            <linearGradient id="wave-gradient" x1="0%" y1="0%" x2="100%" y2="100%">
              <stop offset="0%" stopColor="rgba(147, 51, 234, 0.3)" />
              <stop offset="50%" stopColor="rgba(168, 85, 247, 0.2)" />
              <stop offset="100%" stopColor="rgba(196, 181, 253, 0.1)" />
            </linearGradient>
          </defs>
          
          <motion.path
            d="M0,400 C300,300 600,500 1200,400 L1200,800 L0,800 Z"
            fill="url(#wave-gradient)"
            animate={{
              d: [
                "M0,400 C300,300 600,500 1200,400 L1200,800 L0,800 Z",
                "M0,450 C300,350 600,550 1200,450 L1200,800 L0,800 Z",
                "M0,400 C300,300 600,500 1200,400 L1200,800 L0,800 Z",
              ],
            }}
            transition={{
              duration: 8,
              repeat: Infinity,
              ease: "easeInOut",
            }}
          />
          
          <motion.path
            d="M0,500 C400,400 800,600 1200,500 L1200,800 L0,800 Z"
            fill="rgba(147, 51, 234, 0.1)"
            animate={{
              d: [
                "M0,500 C400,400 800,600 1200,500 L1200,800 L0,800 Z",
                "M0,550 C400,450 800,650 1200,550 L1200,800 L0,800 Z",
                "M0,500 C400,400 800,600 1200,500 L1200,800 L0,800 Z",
              ],
            }}
            transition={{
              duration: 12,
              repeat: Infinity,
              ease: "easeInOut",
              delay: 2,
            }}
          />
        </svg>
      </div>
    );
  }

  return null;
};

export default AnimatedBackground;
