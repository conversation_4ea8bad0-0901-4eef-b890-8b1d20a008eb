import{bh as ie,r,j as e,bi as J,bj as W,bk as X,bl as Y,ai as ne,a1 as D,aY as oe,a8 as ce,X as de,bm as ge,G as q,bn as xe,bo as me,E as he,y as be,b9 as K,bp as U,bq as ue,br as fe}from"./react-vendor-Dq0qSR31.js";import{d as E,u as ye,m as x,I as w,S as $,z as G,D as H,E as V,G as m,g as pe,H as je,J as ve,K as Ne}from"./index-CasGuY6o.js";import{T as Q,D as we,a as ke,b as Se,c as Ee}from"./dialog-BVPp5ZjS.js";import{G as Ce}from"./utils-vendor-DSNVchvY.js";import"./vendor-OXu-rwpf.js";import"./state-vendor-DU4y5LsH.js";const Te=ie,Z=r.forwardRef(({className:a,...n},o)=>e.jsx(J,{ref:o,className:E("inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground",a),...n}));Z.displayName=J.displayName;const I=r.forwardRef(({className:a,...n},o)=>e.jsx(W,{ref:o,className:E("inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm",a),...n}));I.displayName=W.displayName;const A=r.forwardRef(({className:a,...n},o)=>e.jsx(X,{ref:o,className:E("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",a),...n}));A.displayName=X.displayName;const Be=Ce("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function De({className:a,variant:n,...o}){return e.jsx("div",{className:E(Be({variant:n}),a),...o})}const ee=r.forwardRef(({className:a,orientation:n="horizontal",decorative:o=!0,...u},f)=>e.jsx(Y,{ref:f,decorative:o,orientation:n,className:E("shrink-0 bg-border",n==="horizontal"?"h-[1px] w-full":"h-full w-[1px]",a),...u}));ee.displayName=Y.displayName;const Ie=({initialValues:a,onSubmit:n,onCancel:o,loading:u})=>{const[f,y]=r.useState("editor"),[B,g]=r.useState(!1),[l,k]=r.useState("image"),[p,S]=r.useState(""),v=r.useRef({en:null,ar:null}),[c,N]=r.useState("en"),[j,C]=r.useState(""),{trackClick:M}=ye(),T=t=>d=>{v.current&&(v.current[t]=d)},{register:s,handleSubmit:h,watch:R,setValue:b,formState:{errors:se}}=ne({defaultValues:{title:a?.title||{en:"",ar:""},content:a?.content||{en:"",ar:""},excerpt:a?.excerpt||{en:"",ar:""},image:a?.image||"",category:a?.category||"General",status:a?.status||"draft",tags:a?.tags||[],seoTitle:a?.seoTitle||"",seoDescription:a?.seoDescription||"",seoKeywords:a?.seoKeywords||[]}}),i=R(),F=R("image"),L={toolbar:[[{header:[1,2,!1]}],["bold","italic","underline","strike"],[{list:"ordered"},{list:"bullet"}],[{align:[]},{color:[]},{background:[]}],["link","image","video"],["clean"]],clipboard:{matchVisual:!1}},_=["header","font","size","bold","italic","underline","strike","blockquote","list","bullet","indent","link","image","video","color","background","align","direction"],P=(t,d)=>{b(`content.${d}`,t,{shouldValidate:!0})};r.useEffect(()=>{if(!a?.excerpt){const t=i.content?.en||"",d=i.content?.ar||"";t&&b("excerpt.en",t.replace(/<[^>]*>/g,"").slice(0,120)),d&&b("excerpt.ar",d.replace(/<[^>]*>/g,"").slice(0,120))}},[i.content?.en,i.content?.ar,b,a]);const te=()=>{if(!p.trim())return;const t=v.current?.[c];if(!t)return;const d=t.getSelection();if(l==="image")t.insertEmbed(d?.index||0,"image",p);else if(l==="video"){const O=p.match(/(?:youtube\.com\/watch\?v=|youtu\.be\/)([^&\n?#]+)/)?.[1];if(O){const le=`https://www.youtube.com/embed/${O}`;t.insertEmbed(d?.index||0,"video",le)}else t.insertEmbed(d?.index||0,"video",p)}g(!1),S("")},z=()=>{j.trim()&&!i.tags?.includes(j.trim())&&(b("tags",[...i.tags||[],j.trim()]),C(""))},ae=t=>{b("tags",i.tags?.filter(d=>d!==t)||[])},re=h(t=>{n(t),D.event({category:"Blog",action:a?"update_blog":"create_blog",label:t.title.en,value:1})});return e.jsxs("div",{className:"fixed inset-0 z-50 bg-gray-900 flex flex-col",children:[e.jsxs("div",{className:"flex items-center justify-between p-4 border-b border-gray-800 bg-gray-900",children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx(oe,{className:"h-6 w-6 text-blue-400"}),e.jsx("h1",{className:"text-xl font-bold text-white",children:a?"Edit Blog Post":"Create New Blog Post"})]}),e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsxs(x,{variant:"outline",onClick:()=>y("preview"),className:"text-gray-300 border-gray-600 hover:bg-gray-700",children:[" ",e.jsx(ce,{className:"h-4 w-4"})," Preview "]}),e.jsxs(x,{variant:"outline",onClick:o,className:"text-gray-300 border-gray-600 hover:bg-gray-700",children:[" ",e.jsx(de,{className:"h-4 w-4"})," Cancel "]}),e.jsxs(x,{onClick:re,disabled:u,className:"bg-blue-600 hover:bg-blue-700 text-white",children:[" ",e.jsx(ge,{className:"h-4 w-4"})," ",u?"Saving...":"Save"," "]})]})]}),e.jsxs("div",{className:"flex-1 flex overflow-hidden",children:[e.jsx("div",{className:"w-80 border-r border-gray-800 bg-gray-800 overflow-y-auto",children:e.jsxs(Te,{value:f,onValueChange:y,className:"w-full",children:[e.jsxs(Z,{className:"grid w-full grid-cols-2 bg-gray-800",children:[e.jsx(I,{value:"editor",className:"text-gray-300 data-[state=active]:bg-gray-600 data-[state=active]:text-white",children:"Editor"}),e.jsx(I,{value:"settings",className:"text-gray-300 data-[state=active]:bg-gray-600 data-[state=active]:text-white",children:"Settings"})]}),e.jsxs(A,{value:"editor",className:"space-y-4 mt-4 p-4",children:[e.jsxs("div",{className:"flex gap-2",children:[e.jsxs(x,{variant:c==="en"?"default":"outline",size:"sm",onClick:()=>N("en"),className:"flex-1 bg-blue-600 hover:bg-blue-700 text-white border-gray-600",children:[" ",e.jsx(q,{className:"h-4 w-4 mr-2"})," English "]}),e.jsxs(x,{variant:c==="ar"?"default":"outline",size:"sm",onClick:()=>N("ar"),className:"flex-1 bg-blue-600 hover:bg-blue-700 text-white border-gray-600",children:[" ",e.jsx(q,{className:"h-4 w-4 mr-2"})," العربية "]})]}),e.jsxs("div",{children:[e.jsxs("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:["Title (",c==="en"?"English":"Arabic",")"]}),e.jsx(w,{...s(`title.${c}`,{required:!0}),placeholder:`Enter title in ${c==="en"?"English":"Arabic"}`,className:"w-full"}),se.title?.[c]&&e.jsx("span",{className:"text-red-400 text-sm",children:"Title is required"})]}),e.jsxs("div",{children:[e.jsxs("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:["Excerpt (",c==="en"?"English":"Arabic",")"]}),e.jsx(Q,{...s(`excerpt.${c}`),placeholder:`Enter excerpt in ${c==="en"?"English":"Arabic"}`,rows:3,className:"w-full"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Insert Media"}),e.jsxs("div",{className:"flex gap-2",children:[e.jsxs(x,{variant:"outline",size:"sm",onClick:()=>{k("image"),g(!0)},className:"flex-1 border-gray-600 text-gray-300 hover:bg-gray-700",children:[" ",e.jsx(xe,{className:"h-4 w-4 mr-2"})," Image "]}),e.jsxs(x,{variant:"outline",size:"sm",onClick:()=>{k("video"),g(!0)},className:"flex-1 border-gray-600 text-gray-300 hover:bg-gray-700",children:[" ",e.jsx(me,{className:"h-4 w-4 mr-2"})," Video "]})]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Featured Image"}),e.jsx(w,{...s("image"),placeholder:"Enter image URL",className:"w-full"}),F&&e.jsx("img",{src:F,alt:"Featured",className:"mt-2 w-full h-32 object-cover rounded border border-gray-600"})]})]}),e.jsxs(A,{value:"settings",className:"space-y-4 mt-4 p-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Category"}),e.jsxs($,{value:i.category,onValueChange:t=>b("category",t),children:[e.jsx(G,{className:"border-gray-600 bg-gray-800 text-white",children:e.jsx(H,{placeholder:"Select category"})}),e.jsxs(V,{className:"bg-gray-800 border-gray-600",children:[e.jsx(m,{value:"General",className:"text-white hover:bg-gray-700",children:"General"}),e.jsx(m,{value:"Marketing",className:"text-white hover:bg-gray-700",children:"Marketing"}),e.jsx(m,{value:"SEO",className:"text-white hover:bg-gray-700",children:"SEO"}),e.jsx(m,{value:"Social Media",className:"text-white hover:bg-gray-700",children:"Social Media"}),e.jsx(m,{value:"Strategy",className:"text-white hover:bg-gray-700",children:"Strategy"}),e.jsx(m,{value:"Technology",className:"text-white hover:bg-gray-700",children:"Technology"}),e.jsx(m,{value:"Business",className:"text-white hover:bg-gray-700",children:"Business"})]})]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Status"}),e.jsxs($,{value:i.status,onValueChange:t=>b("status",t),children:[e.jsx(G,{className:"border-gray-600 bg-gray-800 text-white",children:e.jsx(H,{placeholder:"Select status"})}),e.jsxs(V,{className:"bg-gray-800 border-gray-600",children:[e.jsx(m,{value:"draft",className:"text-white hover:bg-gray-700",children:"Draft"}),e.jsx(m,{value:"published",className:"text-white hover:bg-gray-700",children:"Published"}),e.jsx(m,{value:"archived",className:"text-white hover:bg-gray-700",children:"Archived"})]})]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Tags"}),e.jsxs("div",{className:"flex gap-2 mb-2",children:[e.jsx(w,{value:j,onChange:t=>C(t.target.value),placeholder:"Add tag",onKeyPress:t=>t.key==="Enter"&&z()}),e.jsxs(x,{size:"sm",onClick:z,className:"bg-blue-600 hover:bg-blue-700",children:[" ",e.jsx(he,{className:"h-4 w-4"})," "]})]}),e.jsx("div",{className:"flex flex-wrap gap-2",children:i.tags?.map(t=>e.jsxs(De,{variant:"secondary",className:"flex items-center gap-1 bg-gray-700",children:[t,e.jsx("button",{onClick:()=>ae(t),className:"ml-1 hover:text-red-400",children:e.jsx(be,{className:"h-3 w-3"})})]},t))})]}),e.jsx(ee,{className:"bg-gray-600"}),e.jsxs("div",{children:[e.jsx("h3",{className:"text-sm font-medium text-gray-300 mb-3",children:"SEO Settings"}),e.jsxs("div",{className:"space-y-3",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-xs text-gray-400 mb-1",children:"SEO Title"}),e.jsx(w,{...s("seoTitle"),placeholder:"SEO optimized title",className:"w-full text-sm"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-xs text-gray-400 mb-1",children:"SEO Description"}),e.jsx(Q,{...s("seoDescription"),placeholder:"SEO meta description",rows:2,className:"w-full text-sm"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-xs text-gray-400 mb-1",children:"SEO Keywords"}),e.jsx(w,{...s("seoKeywords"),placeholder:"keyword1, keyword2, keyword3",className:"w-full text-sm"})]})]})]})]})]})}),e.jsx("div",{className:"flex-1 flex flex-col bg-gray-900",children:f==="editor"?e.jsxs("div",{className:"flex-1 flex",children:[e.jsxs("div",{className:"flex-1 flex flex-col border-r border-gray-800",children:[e.jsx("div",{className:"p-4 border-b border-gray-800 bg-gray-900",children:e.jsx("h2",{className:"text-lg font-semibold text-white",children:"English Content"})}),e.jsx("div",{className:"flex-1 p-4 bg-gray-900",children:e.jsx(K,{ref:T("en"),value:i.content?.en||"",onChange:t=>P(t,"en"),modules:L,formats:_,theme:"snow",placeholder:"Write your content in English...",className:"h-full [&_.ql-editor]:text-white [&_.ql-editor]:bg-gray-800",style:{height:"calc(100vh - 300px)"}})})]}),e.jsxs("div",{className:"flex-1 flex flex-col",children:[e.jsx("div",{className:"p-4 border-b border-gray-800 bg-gray-900",children:e.jsx("h2",{className:"text-lg font-semibold text-white",children:"المحتوى العربي"})}),e.jsx("div",{className:"flex-1 p-4 bg-gray-900",children:e.jsx(K,{ref:T("ar"),value:i.content?.ar||"",onChange:t=>P(t,"ar"),modules:L,formats:_,theme:"snow",placeholder:"اكتب محتواك باللغة العربية...",className:"h-full [&_.ql-editor]:text-white [&_.ql-editor]:bg-gray-800",style:{height:"calc(100vh - 300px)"}})})]})]}):e.jsx("div",{className:"flex-1 p-6 overflow-y-auto bg-gray-900",children:e.jsxs("div",{className:"max-w-4xl mx-auto",children:[e.jsx("h2",{className:"text-2xl font-bold mb-4 text-white",children:"Preview"}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-8",children:[e.jsxs("div",{className:"border border-gray-600 rounded-lg p-6 bg-gray-800",children:[e.jsx("h3",{className:"text-lg font-semibold mb-4 text-white",children:"English Version"}),e.jsx("h1",{className:"text-2xl font-bold mb-4 text-white",children:i.title?.en||"Untitled"}),e.jsx("p",{className:"text-gray-300",children:i.excerpt?.en}),e.jsx("div",{className:"prose max-w-none prose-invert",dangerouslySetInnerHTML:{__html:i.content?.en||""}})]}),e.jsxs("div",{className:"border border-gray-600 rounded-lg p-6 bg-gray-800",dir:"rtl",children:[e.jsx("h3",{className:"text-lg font-semibold mb-4 text-white",children:"النسخة العربية"}),e.jsx("h1",{className:"text-2xl font-bold mb-4 text-white",children:i.title?.ar||"بدون عنوان"}),e.jsx("p",{className:"text-gray-300",children:i.excerpt?.ar}),e.jsx("div",{className:"prose max-w-none prose-invert",dangerouslySetInnerHTML:{__html:i.content?.ar||""}})]})]})]})})})]}),e.jsx(we,{open:B,onOpenChange:g,children:e.jsxs(ke,{className:"bg-gray-800 border-gray-600",children:[e.jsx(Se,{children:e.jsxs(Ee,{className:"text-white",children:["Insert ",l==="image"?"Image":"Video"]})}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{children:[e.jsxs("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:[l==="image"?"Image":"Video"," URL"]}),e.jsx(w,{value:p,onChange:t=>S(t.target.value),placeholder:`Enter ${l} URL`,className:"w-full"})]}),e.jsxs("div",{className:"flex justify-end gap-2",children:[e.jsx(x,{variant:"outline",onClick:()=>g(!1),className:"border-gray-600 text-gray-300 hover:bg-gray-700",children:"Cancel"}),e.jsx(x,{onClick:te,className:"bg-blue-600 hover:bg-blue-700",children:"Insert"})]})]})]})})]})},Ae=()=>e.jsxs("div",{className:"animate-pulse bg-brand-grey-900 rounded-xl p-4 flex flex-col gap-4 shadow-md min-h-[180px]",children:[e.jsx("div",{className:"h-6 bg-brand-grey-800 rounded w-2/3 mb-2"}),e.jsx("div",{className:"h-4 bg-brand-grey-800 rounded w-1/2 mb-2"}),e.jsx("div",{className:"h-3 bg-brand-grey-800 rounded w-1/3"}),e.jsxs("div",{className:"flex gap-2 mt-4",children:[e.jsx("div",{className:"h-8 w-8 bg-brand-grey-800 rounded-full"}),e.jsx("div",{className:"h-8 w-8 bg-brand-grey-800 rounded-full"})]})]}),Me=({status:a})=>{const n=a==="published"?"bg-green-600":a==="draft"?"bg-yellow-500":"bg-gray-500";return e.jsx("span",{className:`px-2 py-1 text-xs rounded ${n} text-white font-semibold`,children:a})},Oe=()=>{const[a,n]=r.useState([]),[o,u]=r.useState(!1),[f,y]=r.useState(null),[B,g]=r.useState(!1),[l,k]=r.useState(null),[p,S]=r.useState(!1),[v,c]=r.useState(null);r.useEffect(()=>{N()},[]);const N=async()=>{u(!0),y(null);try{const s=await pe();n(s.data)}catch{y("Failed to fetch blogs")}finally{u(!1)}},j=()=>{k(null),g(!0)},C=s=>{k(s),g(!0),D.event({category:"Blog",action:"edit_blog",label:typeof s.title=="string"?s.title:s.title?.en||"Untitled",value:1})},M=async s=>{if(window.confirm("Are you sure you want to delete this blog?")){c(s);try{await je(s),N(),D.event({category:"Blog",action:"delete_blog",label:s,value:1})}catch{y("Failed to delete blog")}finally{c(null)}}},T=async s=>{S(!0);try{const h={title:s.title,content:s.content,excerpt:s.excerpt,image:s.image,category:s.category,status:s.status,tags:s.tags,seoTitle:s.seoTitle,seoDescription:s.seoDescription,seoKeywords:s.seoKeywords};l?await ve(l.id,h):await Ne(h),g(!1),N()}catch{y("Failed to save blog")}finally{S(!1)}};return e.jsxs("div",{className:"p-6 max-w-7xl mx-auto",children:[e.jsxs("div",{className:"flex flex-col sm:flex-row justify-between items-center mb-8 gap-4",children:[e.jsx("h2",{className:"text-3xl font-bold text-brand-white tracking-tight",children:"Blog Management"}),e.jsxs("button",{className:"flex items-center gap-2 px-4 py-2 rounded-lg bg-brand-primary hover:bg-brand-primary-dark text-white font-semibold shadow transition",onClick:j,children:[e.jsx(U,{className:"text-lg"})," Add Blog"]})]}),f&&e.jsx("div",{className:"text-red-500 mb-4",children:f}),o?e.jsx("div",{className:"grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-6",children:[...Array(6)].map((s,h)=>e.jsx(Ae,{},h))}):a.length===0?e.jsxs("div",{className:"flex flex-col items-center justify-center py-20 text-center text-brand-grey-400",children:[e.jsx("img",{src:"/placeholder.svg",alt:"No blogs",className:"w-32 mb-6 opacity-60"}),e.jsx("h3",{className:"text-xl font-semibold mb-2",children:"No blogs found"}),e.jsx("p",{className:"mb-4",children:"Start by adding your first blog post!"}),e.jsxs("button",{className:"flex items-center gap-2 px-4 py-2 rounded-lg bg-brand-primary hover:bg-brand-primary-dark text-white font-semibold shadow transition",onClick:j,children:[e.jsx(U,{className:"text-lg"})," Add Blog"]})]}):e.jsx("div",{className:"grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-6",children:a.map(s=>e.jsxs("div",{className:"bg-brand-grey-900 rounded-xl shadow-md p-5 flex flex-col gap-3 hover:shadow-lg transition group border border-brand-grey-800",children:[e.jsxs("div",{className:"flex items-center gap-2 mb-2",children:[e.jsx(Me,{status:s.status}),s.category&&e.jsx("span",{className:"ml-2 px-2 py-1 text-xs rounded bg-brand-primary/20 text-brand-primary font-semibold",children:s.category})]}),e.jsx("h3",{className:"text-lg font-bold text-brand-white group-hover:text-brand-primary transition line-clamp-2",children:typeof s.title=="string"?s.title:s.title?.en||"Untitled"}),e.jsx("p",{className:"text-brand-grey-300 text-sm line-clamp-3 mb-2",children:typeof s.excerpt=="string"?s.excerpt:s.excerpt?.en||""}),e.jsxs("div",{className:"flex items-center justify-between mt-auto pt-2",children:[e.jsxs("span",{className:"text-xs text-brand-grey-400",children:[s.author?.name||"Unknown"," • ",new Date(s.createdAt).toLocaleDateString()]}),e.jsxs("div",{className:"flex gap-2",children:[e.jsx("button",{className:"p-2 rounded-full hover:bg-brand-grey-800 transition",title:"Edit",onClick:()=>C(s),children:e.jsx(ue,{className:"text-brand-primary text-lg"})}),e.jsx("button",{className:`p-2 rounded-full hover:bg-red-600/20 transition ${v===s.id?"opacity-50 pointer-events-none":""}`,title:"Delete",onClick:()=>M(s.id),disabled:v===s.id,children:e.jsx(fe,{className:"text-red-500 text-lg"})})]})]})]},s.id))}),B&&e.jsx(Ie,{initialValues:l?{title:l.title,content:l.content,excerpt:l.excerpt,image:l.image,category:l.category,status:l.status,tags:l.tags,seoTitle:l.seoTitle,seoDescription:l.seoDescription,seoKeywords:l.seoKeywords}:void 0,onSubmit:T,onCancel:()=>g(!1),loading:p})]})};export{Oe as default};
