import{o as w,n as S,r as n,j as e,X as k,b8 as C,b9 as U}from"./react-vendor-Dq0qSR31.js";import{I as g,m as u}from"./index-CasGuY6o.js";import"./vendor-OXu-rwpf.js";import"./utils-vendor-DSNVchvY.js";import"./state-vendor-DU4y5LsH.js";const E=async s=>new Promise(r=>{setTimeout(()=>r(URL.createObjectURL(s)),1e3)}),D=()=>{const s=w(),r=S(),l=r.state?.subService,h=r.state?.idx,b=r.state?.onSave,[c,f]=n.useState(l?.title||""),[i,x]=n.useState(l?.image||""),[d,p]=n.useState(l?.description||""),[o,m]=n.useState(!1),[a,v]=n.useState(!1),j=async t=>{m(!0);const N=await E(t);x(N),m(!1)},y=()=>{!c.trim()||!d.trim()||(v(!0),b&&b({title:c,image:i,description:d},h),s(-1))};return e.jsxs("div",{className:"min-h-screen bg-brand-grey-950 flex flex-col",children:[e.jsxs("div",{className:"flex items-center justify-between p-6 border-b border-brand-grey-800 bg-brand-grey-950",children:[e.jsx("h1",{className:"text-2xl md:text-3xl font-bold text-brand-accent-100",children:l?"Edit Sub-Service":"Add Sub-Service"}),e.jsx("button",{onClick:()=>s(-1),className:"text-brand-grey-400 hover:text-brand-accent-100",children:e.jsx(k,{className:"w-6 h-6"})})]}),e.jsx("div",{className:"flex-1 flex flex-col items-center justify-center p-6",children:e.jsxs("div",{className:"w-full max-w-2xl bg-brand-grey-900 rounded-2xl shadow-2xl p-8 border border-brand-grey-800 flex flex-col gap-8",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block mb-1 font-semibold text-brand-accent-100 text-lg",children:"Title"}),e.jsx(g,{value:c,onChange:t=>f(t.target.value),placeholder:"Enter sub-service title",disabled:a,className:"bg-brand-grey-900 border-brand-grey-700 text-brand-white placeholder:text-brand-grey-400 focus:ring-2 focus:ring-brand-accent-100 font-semibold text-lg"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block mb-1 font-semibold text-brand-accent-100 text-lg",children:"Image"}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(g,{type:"text",value:i,onChange:t=>x(t.target.value),placeholder:"Paste image URL or upload",disabled:a||o,className:"bg-brand-grey-900 border-brand-grey-700 text-brand-white placeholder:text-brand-grey-400 focus:ring-2 focus:ring-brand-accent-100 flex-1"}),e.jsxs("label",{className:"inline-flex items-center cursor-pointer",children:[e.jsx("input",{type:"file",accept:"image/*",className:"hidden",disabled:a||o,onChange:async t=>{t.target.files&&t.target.files[0]&&await j(t.target.files[0])}}),e.jsx("span",{className:"p-2 bg-brand-grey-700 rounded hover:bg-brand-accent-100 hover:text-brand-black transition",children:e.jsx(C,{className:"w-4 h-4"})})]}),o&&e.jsx("span",{className:"ml-2 text-xs text-brand-accent-100 animate-pulse",children:"Uploading..."})]}),i&&e.jsx("img",{src:i,alt:"Preview",className:"mt-2 max-h-32 rounded-xl border border-brand-grey-700 w-full object-cover shadow"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block mb-1 font-semibold text-brand-accent-100 text-lg",children:"Description"}),e.jsx(U,{value:d,onChange:p,theme:"snow",className:"bg-brand-grey-900 text-brand-white rounded-xl",placeholder:"Enter sub-service description",style:{minHeight:120}})]}),e.jsxs("div",{className:"flex flex-col md:flex-row gap-4 justify-end",children:[e.jsx(u,{type:"button",variant:"secondary",onClick:()=>s(-1),disabled:a,className:"btn-secondary text-brand-white bg-brand-grey-800 hover:bg-brand-grey-900 hover:text-brand-accent-100 transition-all duration-200 w-full md:w-auto font-semibold",children:"Cancel"}),e.jsx(u,{type:"button",onClick:y,disabled:a||!c.trim()||!d.trim(),className:"btn-primary bg-brand-accent-100 text-brand-black hover:bg-brand-accent-200 hover:text-brand-black transition-all duration-200 w-full md:w-auto font-bold",children:a?"Saving...":"Save"})]})]})})]})};export{D as default};
