import mongoose from 'mongoose';
import dotenv from 'dotenv';
import Service from '../src/components/service/serviceModel.js';

// Load environment variables
dotenv.config();

// Sample sub-services data for different service types
const subServicesData = {
  'Digital Marketing': [
    {
      title: 'Social Media Marketing',
      description: 'Boost your brand presence across all social platforms with our comprehensive social media strategies. We create engaging content, manage communities, and drive meaningful interactions that convert followers into loyal customers.',
      image: 'https://images.unsplash.com/photo-1611224923853-80b023f02d71?w=500&h=300&fit=crop'
    },
    {
      title: 'Pay-Per-Click Advertising',
      description: 'Maximize your ROI with targeted PPC campaigns across Google Ads, Facebook, and other platforms. Our data-driven approach ensures every click counts towards your business growth and revenue generation.',
      image: 'https://images.unsplash.com/photo-1460925895917-afdab827c52f?w=500&h=300&fit=crop'
    },
    {
      title: 'Email Marketing Automation',
      description: 'Transform your email campaigns into powerful revenue generators. We design automated sequences that nurture leads, retain customers, and drive repeat purchases through personalized messaging.',
      image: 'https://images.unsplash.com/photo-1596526131083-e8c633c948d2?w=500&h=300&fit=crop'
    }
  ],
  'SEO Services': [
    {
      title: 'Technical SEO Audit',
      description: 'Uncover hidden technical issues that are holding your website back from ranking higher. Our comprehensive audit reveals optimization opportunities that can dramatically improve your search visibility.',
      image: 'https://images.unsplash.com/photo-1551288049-bebda4e38f71?w=500&h=300&fit=crop'
    },
    {
      title: 'Content Optimization',
      description: 'Transform your content into a search engine magnet. We optimize every piece of content to rank higher, engage readers, and convert visitors into customers through strategic keyword integration.',
      image: 'https://images.unsplash.com/photo-1542435503-956c469947f6?w=500&h=300&fit=crop'
    },
    {
      title: 'Local SEO Domination',
      description: 'Dominate your local market with our proven local SEO strategies. We help businesses appear in local searches, manage online reviews, and attract nearby customers who are ready to buy.',
      image: 'https://images.unsplash.com/photo-1521737604893-d14cc237f11d?w=500&h=300&fit=crop'
    }
  ],
  'Web Development': [
    {
      title: 'E-commerce Development',
      description: 'Build a powerful online store that converts visitors into customers. Our e-commerce solutions include payment integration, inventory management, and conversion optimization features.',
      image: 'https://images.unsplash.com/photo-1556742049-0cfed4f6a45d?w=500&h=300&fit=crop'
    },
    {
      title: 'Mobile App Development',
      description: 'Reach your customers wherever they are with custom mobile applications. We create intuitive, fast, and engaging apps that enhance user experience and drive business growth.',
      image: 'https://images.unsplash.com/photo-1512941937669-90a1b58e7e9c?w=500&h=300&fit=crop'
    },
    {
      title: 'Website Performance Optimization',
      description: 'Speed up your website and improve user experience with our performance optimization services. Faster websites rank better, convert more, and provide superior user satisfaction.',
      image: 'https://images.unsplash.com/photo-1551650975-87deedd944c3?w=500&h=300&fit=crop'
    }
  ],
  'Branding': [
    {
      title: 'Logo Design & Brand Identity',
      description: 'Create a memorable brand identity that stands out in the marketplace. Our designers craft logos and visual identities that communicate your values and attract your ideal customers.',
      image: 'https://images.unsplash.com/photo-1561070791-2526d30994b5?w=500&h=300&fit=crop'
    },
    {
      title: 'Brand Strategy Development',
      description: 'Develop a comprehensive brand strategy that positions your business for success. We help define your brand voice, messaging, and positioning to resonate with your target audience.',
      image: 'https://images.unsplash.com/photo-1552664730-d307ca884978?w=500&h=300&fit=crop'
    },
    {
      title: 'Brand Guidelines & Assets',
      description: 'Ensure consistent brand representation across all touchpoints with comprehensive brand guidelines. We create style guides, templates, and assets that maintain brand integrity.',
      image: 'https://images.unsplash.com/photo-1558655146-d09347e92766?w=500&h=300&fit=crop'
    }
  ]
};

const addSubServicesToExistingServices = async () => {
  try {
    // Connect to MongoDB
    await mongoose.connect(process.env.MONGODB_URI);
    console.log('Connected to MongoDB');

    // Get all existing services
    const services = await Service.find({});
    console.log(`Found ${services.length} existing services`);

    for (const service of services) {
      // Check if service already has sub-services
      if (service.subServices && service.subServices.length > 0) {
        console.log(`Service "${service.title}" already has sub-services, skipping...`);
        continue;
      }

      // Find matching sub-services based on service title
      let matchingSubServices = null;
      
      // Try to match service title with our sub-services data
      for (const [key, subServices] of Object.entries(subServicesData)) {
        if (service.title.toLowerCase().includes(key.toLowerCase()) || 
            key.toLowerCase().includes(service.title.toLowerCase())) {
          matchingSubServices = subServices;
          break;
        }
      }

      // If no direct match, assign based on keywords
      if (!matchingSubServices) {
        const title = service.title.toLowerCase();
        if (title.includes('marketing') || title.includes('digital')) {
          matchingSubServices = subServicesData['Digital Marketing'];
        } else if (title.includes('seo') || title.includes('search')) {
          matchingSubServices = subServicesData['SEO Services'];
        } else if (title.includes('web') || title.includes('development')) {
          matchingSubServices = subServicesData['Web Development'];
        } else if (title.includes('brand') || title.includes('design')) {
          matchingSubServices = subServicesData['Branding'];
        }
      }

      if (matchingSubServices) {
        // Update service with sub-services
        service.subServices = matchingSubServices;
        await service.save();
        console.log(`Added ${matchingSubServices.length} sub-services to "${service.title}"`);
      } else {
        console.log(`No matching sub-services found for "${service.title}"`);
      }
    }

    console.log('Sub-services addition completed!');
    process.exit(0);
  } catch (error) {
    console.error('Error adding sub-services:', error);
    process.exit(1);
  }
};

// Run the script
addSubServicesToExistingServices();
