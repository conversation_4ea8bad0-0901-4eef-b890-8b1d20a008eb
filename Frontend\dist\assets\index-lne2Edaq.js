var U=Object.defineProperty,Y=Object.defineProperties;var Z=Object.getOwnPropertyDescriptors;var R=Object.getOwnPropertySymbols;var A=Object.prototype.hasOwnProperty,T=Object.prototype.propertyIsEnumerable;var j=(e,t,n)=>t in e?U(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n,u=(e,t)=>{for(var n in t||(t={}))A.call(t,n)&&j(e,n,t[n]);if(R)for(var n of R(t))T.call(t,n)&&j(e,n,t[n]);return e},d=(e,t)=>Y(e,Z(t));var f=(e,t)=>{var n={};for(var r in e)A.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(e!=null&&R)for(var r of R(e))t.indexOf(r)<0&&T.call(e,r)&&(n[r]=e[r]);return n};import{r as c,aj as z,j as i,ac as J,ae as E,al as N,aM as X,af as C,ag as m,ah as P,aN as ee,aO as te,aP as oe,aQ as ne,aR as re,aS as ae,aT as se}from"./index-hEW_vQ3f.js";var h="Dialog",[b,Ce]=J(h),[ie,g]=b(h),M=e=>{const{__scopeDialog:t,children:n,open:r,defaultOpen:a,onOpenChange:o,modal:s=!0}=e,l=c.useRef(null),p=c.useRef(null),[v,x]=z({prop:r,defaultProp:a!=null?a:!1,onChange:o,caller:h});return i.jsx(ie,{scope:t,triggerRef:l,contentRef:p,contentId:E(),titleId:E(),descriptionId:E(),open:v,onOpenChange:x,onOpenToggle:c.useCallback(()=>x(Q=>!Q),[x]),modal:s,children:n})};M.displayName=h;var S="DialogTrigger",ce=c.forwardRef((e,t)=>{const s=e,{__scopeDialog:n}=s,r=f(s,["__scopeDialog"]),a=g(S,n),o=P(t,a.triggerRef);return i.jsx(C.button,d(u({type:"button","aria-haspopup":"dialog","aria-expanded":a.open,"aria-controls":a.contentId,"data-state":I(a.open)},r),{ref:o,onClick:m(e.onClick,a.onOpenToggle)}))});ce.displayName=S;var y="DialogPortal",[le,w]=b(y,{forceMount:void 0}),F=e=>{const{__scopeDialog:t,forceMount:n,children:r,container:a}=e,o=g(y,t);return i.jsx(le,{scope:t,forceMount:n,children:c.Children.map(r,s=>i.jsx(N,{present:n||o.open,children:i.jsx(X,{asChild:!0,container:a,children:s})}))})};F.displayName=y;var _="DialogOverlay",W=c.forwardRef((e,t)=>{const n=w(_,e.__scopeDialog),s=e,{forceMount:r=n.forceMount}=s,a=f(s,["forceMount"]),o=g(_,e.__scopeDialog);return o.modal?i.jsx(N,{present:r||o.open,children:i.jsx(de,d(u({},a),{ref:t}))}):null});W.displayName=_;var ue=oe("DialogOverlay.RemoveScroll"),de=c.forwardRef((e,t)=>{const o=e,{__scopeDialog:n}=o,r=f(o,["__scopeDialog"]),a=g(_,n);return i.jsx(te,{as:ue,allowPinchZoom:!0,shards:[a.contentRef],children:i.jsx(C.div,d(u({"data-state":I(a.open)},r),{ref:t,style:u({pointerEvents:"auto"},r.style)}))})}),D="DialogContent",k=c.forwardRef((e,t)=>{const n=w(D,e.__scopeDialog),s=e,{forceMount:r=n.forceMount}=s,a=f(s,["forceMount"]),o=g(D,e.__scopeDialog);return i.jsx(N,{present:r||o.open,children:o.modal?i.jsx(ge,d(u({},a),{ref:t})):i.jsx(fe,d(u({},a),{ref:t}))})});k.displayName=D;var ge=c.forwardRef((e,t)=>{const n=g(D,e.__scopeDialog),r=c.useRef(null),a=P(t,n.contentRef,r);return c.useEffect(()=>{const o=r.current;if(o)return ee(o)},[]),i.jsx(G,d(u({},e),{ref:a,trapFocus:n.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:m(e.onCloseAutoFocus,o=>{var s;o.preventDefault(),(s=n.triggerRef.current)==null||s.focus()}),onPointerDownOutside:m(e.onPointerDownOutside,o=>{const s=o.detail.originalEvent,l=s.button===0&&s.ctrlKey===!0;(s.button===2||l)&&o.preventDefault()}),onFocusOutside:m(e.onFocusOutside,o=>o.preventDefault())}))}),fe=c.forwardRef((e,t)=>{const n=g(D,e.__scopeDialog),r=c.useRef(!1),a=c.useRef(!1);return i.jsx(G,d(u({},e),{ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:o=>{var s,l;(s=e.onCloseAutoFocus)==null||s.call(e,o),o.defaultPrevented||(r.current||(l=n.triggerRef.current)==null||l.focus(),o.preventDefault()),r.current=!1,a.current=!1},onInteractOutside:o=>{var p,v;(p=e.onInteractOutside)==null||p.call(e,o),o.defaultPrevented||(r.current=!0,o.detail.originalEvent.type==="pointerdown"&&(a.current=!0));const s=o.target;((v=n.triggerRef.current)==null?void 0:v.contains(s))&&o.preventDefault(),o.detail.originalEvent.type==="focusin"&&a.current&&o.preventDefault()}}))}),G=c.forwardRef((e,t)=>{const x=e,{__scopeDialog:n,trapFocus:r,onOpenAutoFocus:a,onCloseAutoFocus:o}=x,s=f(x,["__scopeDialog","trapFocus","onOpenAutoFocus","onCloseAutoFocus"]),l=g(D,n),p=c.useRef(null),v=P(t,p);return ne(),i.jsxs(i.Fragment,{children:[i.jsx(re,{asChild:!0,loop:!0,trapped:r,onMountAutoFocus:a,onUnmountAutoFocus:o,children:i.jsx(ae,d(u({role:"dialog",id:l.contentId,"aria-describedby":l.descriptionId,"aria-labelledby":l.titleId,"data-state":I(l.open)},s),{ref:v,onDismiss:()=>l.onOpenChange(!1)}))}),i.jsxs(i.Fragment,{children:[i.jsx(pe,{titleId:l.titleId}),i.jsx(ve,{contentRef:p,descriptionId:l.descriptionId})]})]})}),O="DialogTitle",L=c.forwardRef((e,t)=>{const o=e,{__scopeDialog:n}=o,r=f(o,["__scopeDialog"]),a=g(O,n);return i.jsx(C.h2,d(u({id:a.titleId},r),{ref:t}))});L.displayName=O;var $="DialogDescription",B=c.forwardRef((e,t)=>{const o=e,{__scopeDialog:n}=o,r=f(o,["__scopeDialog"]),a=g($,n);return i.jsx(C.p,d(u({id:a.descriptionId},r),{ref:t}))});B.displayName=$;var H="DialogClose",V=c.forwardRef((e,t)=>{const o=e,{__scopeDialog:n}=o,r=f(o,["__scopeDialog"]),a=g(H,n);return i.jsx(C.button,d(u({type:"button"},r),{ref:t,onClick:m(e.onClick,()=>a.onOpenChange(!1))}))});V.displayName=H;function I(e){return e?"open":"closed"}var q="DialogTitleWarning",[Re,K]=se(q,{contentName:D,titleName:O,docsSlug:"dialog"}),pe=({titleId:e})=>{const t=K(q),n=`\`${t.contentName}\` requires a \`${t.titleName}\` for the component to be accessible for screen reader users.

If you want to hide the \`${t.titleName}\`, you can wrap it with our VisuallyHidden component.

For more information, see https://radix-ui.com/primitives/docs/components/${t.docsSlug}`;return c.useEffect(()=>{e&&(document.getElementById(e)||console.error(n))},[n,e]),null},De="DialogDescriptionWarning",ve=({contentRef:e,descriptionId:t})=>{const r=`Warning: Missing \`Description\` or \`aria-describedby={undefined}\` for {${K(De).contentName}}.`;return c.useEffect(()=>{var o;const a=(o=e.current)==null?void 0:o.getAttribute("aria-describedby");t&&a&&(document.getElementById(t)||console.warn(r))},[r,e,t]),null},_e=M,he=F,Ee=W,Ne=k,Pe=L,ye=B,Oe=V;export{Ne as C,ye as D,Ee as O,he as P,_e as R,Pe as T,Oe as a};
