import { motion } from 'framer-motion';
import { Spa<PERSON><PERSON>, Zap, Star, Circle, Triangle, Square } from 'lucide-react';

interface FloatingElementsProps {
  count?: number;
  className?: string;
}

const FloatingElements = ({ count = 8, className = '' }: FloatingElementsProps) => {
  const icons = [Sparkles, Zap, Star, Circle, Triangle, Square];
  
  const elements = Array.from({ length: count }, (_, i) => {
    const Icon = icons[i % icons.length];
    const size = Math.random() * 20 + 15;
    const delay = Math.random() * 5;
    const duration = Math.random() * 10 + 15;
    const x = Math.random() * 100;
    const y = Math.random() * 100;
    
    return {
      id: i,
      Icon,
      size,
      delay,
      duration,
      x,
      y,
      opacity: Math.random() * 0.3 + 0.1
    };
  });

  return (
    <div className={`absolute inset-0 overflow-hidden pointer-events-none ${className}`}>
      {elements.map((element) => (
        <motion.div
          key={element.id}
          className="absolute"
          style={{
            left: `${element.x}%`,
            top: `${element.y}%`,
          }}
          animate={{
            y: [0, -30, 0],
            x: [0, Math.random() * 20 - 10, 0],
            rotate: [0, 360],
            scale: [1, 1.2, 1],
            opacity: [element.opacity, element.opacity * 2, element.opacity],
          }}
          transition={{
            duration: element.duration,
            repeat: Infinity,
            ease: "easeInOut",
            delay: element.delay,
          }}
        >
          <element.Icon
            size={element.size}
            className="text-brand-purple-400/30"
          />
        </motion.div>
      ))}
    </div>
  );
};

export default FloatingElements;
