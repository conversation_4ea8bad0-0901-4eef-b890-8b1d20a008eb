import express from 'express';
import compression from 'compression';
import cors from 'cors';
import helmet from 'helmet';
import morgan from 'morgan';
import rateLimit from 'express-rate-limit';
import cookieParser from 'cookie-parser';
import colors from 'colors';
import { StatusCodes } from 'http-status-codes';
import connectDB from './shared/config/db.js';
import { errorHandler } from './shared/middleware/errorMiddleware.js';
import { RATE_LIMIT } from './shared/config/constants.js';
import routes from './routes/index.js';
import path from 'path';
import { fileURLToPath } from 'url';
import dotenv from 'dotenv';

dotenv.config();

// Get directory name
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Initialize express
const app = express();

// Connect to MongoDB
connectDB();

// Enable compression for all responses
app.use(compression({
  level: 6, // Compression level (1-9, 6 is default)
  threshold: 1024, // Only compress responses larger than 1KB
  filter: (req, res) => {
    // Don't compress if the request includes a cache-control no-transform directive
    if (req.headers['cache-control'] && req.headers['cache-control'].includes('no-transform')) {
      return false;
    }
    // Use compression filter function
    return compression.filter(req, res);
  }
}));

// Set security headers
app.use(helmet());

// Cookie parser - must be before CORS
app.use(cookieParser());

// CORS configuration
const allowedOrigins = (process.env.FRONTEND_URL || '')
  .split(',')
  .map(o => o.trim())
  .filter(Boolean);

// Add common development origins if in development
if (process.env.NODE_ENV === 'development') {
  allowedOrigins.push(
    'http://localhost:3000',
    'http://127.0.0.1:3000',
    'http://localhost:3001',
    'http://127.0.0.1:3001'
  );
}const corsOptions = {
  origin: function (origin, callback) {
    // In development, allow all origins but log them
    if (process.env.NODE_ENV === 'development') {return callback(null, true);
    }

    // Allow requests with no origin (like mobile apps or curl requests)
    if (!origin) {return callback(null, true);
    }

    // Check if the origin is in the allowed list
    if (allowedOrigins.includes(origin)) {return callback(null, true);
    }const msg = `The CORS policy for this site does not allow access from the specified Origin: ${origin}. Allowed origins: ${allowedOrigins.join(', ')}`;
    return callback(new Error(msg), false);
  },
  credentials: true, // Required for cookies, authorization headers with HTTPS
  methods: ['GET', 'POST', 'PUT', 'PATCH', 'DELETE', 'OPTIONS'],
  allowedHeaders: [
    'Content-Type',
    'Authorization',
    'X-Requested-With',
    'Accept',
    'x-auth-token',
    'x-xsrf-token',
    'X-CSRF-Token',
    'cache-control',
    'pragma',
    'expires',
    'session-id',
  ],
  exposedHeaders: [
    'Content-Length',
    'X-Foo',
    'X-Bar',
    'Set-Cookie',
    'Content-Disposition',
  ],
  maxAge: 86400, // 24 hours
  preflightContinue: false,
  optionsSuccessStatus: 204
};

// Log CORS options for debugging// Enable CORS with the configured options
app.use(cors(corsOptions));

// Handle preflight requests
app.options('*', cors(corsOptions));

// Add CORS headers to all responses
app.use((req, res, next) => {
  const origin = req.headers.origin;
  if (allowedOrigins.includes(origin) || process.env.NODE_ENV === 'development') {
    res.header('Access-Control-Allow-Origin', origin || '*');
    res.header('Access-Control-Allow-Credentials', 'true');
  }
  next();
});

// Logging middleware
if (process.env.NODE_ENV === 'development') {
  app.use(morgan('dev'));
}

// Rate limiting
const limiter = rateLimit({
  windowMs: RATE_LIMIT.WINDOW_MS,
  max: RATE_LIMIT.MAX_REQUESTS,
  message: {
    success: false,
    message: `Too many requests, please try again later.`,
  },
  standardHeaders: true,
  legacyHeaders: false,
});

// Apply limiter only to analytics endpoints
app.use('/api/analytics', limiter);

// Body parser
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// Cookie parser
app.use(cookieParser());

// Set static folder with caching
app.use(express.static(path.join(__dirname, '../public'), {
  maxAge: '1y', // Cache static assets for 1 year
  etag: true,
  lastModified: true,
  setHeaders: (res, path) => {
    // Set different cache headers based on file type
    if (path.endsWith('.html')) {
      res.setHeader('Cache-Control', 'no-cache'); // HTML files should not be cached
    } else if (path.match(/\.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$/)) {
      res.setHeader('Cache-Control', 'public, max-age=31536000, immutable'); // 1 year cache for assets
    }
  }
}));

// Mount routers
app.use('/api', routes);

// Serve static assets in production
if (process.env.NODE_ENV === 'production') {
  // Set static folder with optimized caching
  app.use(express.static(path.join(__dirname, '../../Frontend/dist'), {
    maxAge: '1y',
    etag: true,
    lastModified: true,
    setHeaders: (res, path) => {
      if (path.endsWith('.html')) {
        res.setHeader('Cache-Control', 'no-cache');
      } else if (path.match(/\.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$/)) {
        res.setHeader('Cache-Control', 'public, max-age=31536000, immutable');
      }
    }
  }));

  app.get('*', (req, res) => {
    res.sendFile(path.resolve(__dirname, '../../Frontend/dist', 'index.html'));
  });
}

// Basic route for health check
app.get('/api/health', (req, res) => {
  res.status(StatusCodes.OK).json({
    success: true,
    message: 'API is running',
    version: '1.0.0',
    timestamp: new Date().toISOString(),
  });
});

// Handle 404
app.use('*', (req, res) => {
  res.status(StatusCodes.NOT_FOUND).json({
    success: false,
    message: `Can't find ${req.originalUrl} on this server!`,
  });
});

// Error handling middleware - must be after all other middleware and routes
app.use(errorHandler);

// Start server
const PORT = process.env.PORT || 5000;
const server = app.listen(
  PORT,);

// Handle unhandled promise rejections
process.on('unhandledRejection', (err, promise) => {// Close server & exit process
  server.close(() => process.exit(1));
});

export default app;
