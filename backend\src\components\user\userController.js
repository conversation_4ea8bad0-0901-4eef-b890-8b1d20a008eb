import asyncHandler from 'express-async-handler';
import User from './userModel.js';
import Admin from '../auth/authModel.js';
import { StatusCodes } from 'http-status-codes';
import ErrorResponse from '../../shared/utils/errorResponse.js';
import { sendTokenResponse } from '../../shared/utils/tokenUtils.js';
import { ROLES } from '../../shared/config/constants.js';

// @desc    Get all users
// @route   GET /api/users
// @access  Private/Admin
const getUsers = asyncHandler(async (req, res) => {
  // Get users from both collections
  const regularUsers = await User.find().select('-password');
  const adminUsers = await Admin.find().select('-password');

  // Combine both arrays
  const allUsers = [...regularUsers, ...adminUsers];

  res.status(StatusCodes.OK).json({
    success: true,
    count: allUsers.length,
    data: allUsers,
  });
});

// @desc    Get single user
// @route   GET /api/users/:id
// @access  Private/Admin
const getUser = asyncHandler(async (req, res) => {
  // Check both collections
  let user = await User.findById(req.params.id).select('-password');

  if (!user) {
    user = await Admin.findById(req.params.id).select('-password');
  }

  if (!user) {
    throw new ErrorResponse(
      `User not found with id of ${req.params.id}`,
      StatusCodes.NOT_FOUND
    );
  }

  res.status(StatusCodes.OK).json({
    success: true,
    data: user,
  });
});

// @desc    Create user
// @route   POST /api/users
// @access  Private/Admin
const createUser = asyncHandler(async (req, res) => {
  const { name, email, password, role, businessDomain, clientSegment, companyName, companySize } = req.body;

  // Get IP address for registration tracking
  const registrationIP = req.headers['x-forwarded-for']?.split(',')[0] || req.connection.remoteAddress;

  // Create user
  const user = await User.create({
    name,
    email,
    password,
    role: role || 'user',
    businessDomain,
    clientSegment,
    companyName,
    companySize,
    registrationIP,
  });

  // Link user to visitor using new system
  try {
    const { linkUserToVisitor } = await import('../visitor/visitorController.js');
    await linkUserToVisitor(registrationIP, user);} catch (error) {}

  sendTokenResponse(user, StatusCodes.CREATED, res);
});

// @desc    Update user
// @route   PUT /api/users/:id
// @access  Private/Admin
const updateUser = asyncHandler(async (req, res) => {
  const fieldsToUpdate = {
    name: req.body.name,
    email: req.body.email,
    role: req.body.role,
  };

  const user = await User.findByIdAndUpdate(req.params.id, fieldsToUpdate, {
    new: true,
    runValidators: true,
  }).select('-password');

  if (!user) {
    throw new ErrorResponse(
      `User not found with id of ${req.params.id}`,
      StatusCodes.NOT_FOUND
    );
  }

  res.status(StatusCodes.OK).json({
    success: true,
    data: user,
  });
});

// @desc    Delete user
// @route   DELETE /api/users/:id
// @access  Private/Admin
const deleteUser = asyncHandler(async (req, res) => {
  const user = await User.findById(req.params.id);

  if (!user) {
    throw new ErrorResponse(
      `User not found with id of ${req.params.id}`,
      StatusCodes.NOT_FOUND
    );
  }

  // Prevent deleting own account
  if (user._id.toString() === req.user.id) {
    throw new ErrorResponse(
      'You cannot delete your own account',
      StatusCodes.BAD_REQUEST
    );
  }

  await user.remove();

  res.status(StatusCodes.OK).json({
    success: true,
    data: {},
  });
});

// @desc    Update password
// @route   PUT /api/users/:id/updatepassword
// @access  Private/Admin
const updatePassword = asyncHandler(async (req, res) => {
  const user = await User.findById(req.params.id).select('+password');

  if (!user) {
    throw new ErrorResponse(
      `User not found with id of ${req.params.id}`,
      StatusCodes.NOT_FOUND
    );
  }

  // Set new password
  user.password = req.body.password;
  await user.save();

  sendTokenResponse(user, StatusCodes.OK, res);
});

export {
  getUsers,
  getUser,
  createUser,
  updateUser,
  deleteUser,
  updatePassword,
};
