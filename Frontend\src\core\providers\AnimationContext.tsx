import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { AnimationControls, useAnimation } from 'framer-motion';

interface AnimationContextType {
  // Global animation controls
  globalControls: AnimationControls;
  
  // Animation preferences
  isReducedMotion: boolean;
  animationSpeed: number;
  
  // Page transition states
  isPageTransitioning: boolean;
  setIsPageTransitioning: (transitioning: boolean) => void;
  
  // Performance monitoring
  performanceMode: 'high' | 'medium' | 'low';
  setPerformanceMode: (mode: 'high' | 'medium' | 'low') => void;
  
  // Animation queue for complex sequences
  animationQueue: Array<{
    id: string;
    priority: number;
    animation: () => Promise<void>;
  }>;
  addToQueue: (animation: {
    id: string;
    priority: number;
    animation: () => Promise<void>;
  }) => void;
  removeFromQueue: (id: string) => void;
  
  // Global animation triggers
  triggerGlobalAnimation: (animationType: string) => void;
  
  // Mouse position for interactive effects
  mousePosition: { x: number; y: number };
  
  // Scroll position for parallax effects
  scrollPosition: number;
}

const AnimationContext = createContext<AnimationContextType | undefined>(undefined);

interface AnimationProviderProps {
  children: ReactNode;
}

export const AnimationProvider: React.FC<AnimationProviderProps> = ({ children }) => {
  const globalControls = useAnimation();
  
  // Animation preferences
  const [isReducedMotion, setIsReducedMotion] = useState(false);
  const [animationSpeed, setAnimationSpeed] = useState(1);
  const [performanceMode, setPerformanceMode] = useState<'high' | 'medium' | 'low'>('high');
  
  // Page transition state
  const [isPageTransitioning, setIsPageTransitioning] = useState(false);
  
  // Animation queue
  const [animationQueue, setAnimationQueue] = useState<Array<{
    id: string;
    priority: number;
    animation: () => Promise<void>;
  }>>([]);
  
  // Mouse and scroll tracking
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });
  const [scrollPosition, setScrollPosition] = useState(0);

  // Detect reduced motion preference
  useEffect(() => {
    const mediaQuery = window.matchMedia('(prefers-reduced-motion: reduce)');
    setIsReducedMotion(mediaQuery.matches);
    
    const handleChange = (e: MediaQueryListEvent) => {
      setIsReducedMotion(e.matches);
    };
    
    mediaQuery.addEventListener('change', handleChange);
    return () => mediaQuery.removeEventListener('change', handleChange);
  }, []);

  // Performance monitoring
  useEffect(() => {
    const checkPerformance = () => {
      const connection = (navigator as any).connection;
      if (connection) {
        if (connection.effectiveType === '4g' && connection.downlink > 10) {
          setPerformanceMode('high');
        } else if (connection.effectiveType === '3g' || connection.downlink > 1.5) {
          setPerformanceMode('medium');
        } else {
          setPerformanceMode('low');
        }
      }
    };

    checkPerformance();
    
    // Also check based on device capabilities
    const checkDeviceCapabilities = () => {
      const canvas = document.createElement('canvas');
      const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl');
      
      if (!gl) {
        setPerformanceMode('low');
        return;
      }
      
      const debugInfo = gl.getExtension('WEBGL_debug_renderer_info');
      if (debugInfo) {
        const renderer = gl.getParameter(debugInfo.UNMASKED_RENDERER_WEBGL);
        if (renderer.includes('Intel') || renderer.includes('Software')) {
          setPerformanceMode('medium');
        }
      }
    };

    checkDeviceCapabilities();
  }, []);

  // Mouse tracking
  useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      setMousePosition({ x: e.clientX, y: e.clientY });
    };

    window.addEventListener('mousemove', handleMouseMove);
    return () => window.removeEventListener('mousemove', handleMouseMove);
  }, []);

  // Scroll tracking
  useEffect(() => {
    const handleScroll = () => {
      setScrollPosition(window.pageYOffset);
    };

    window.addEventListener('scroll', handleScroll, { passive: true });
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  // Animation queue processing
  useEffect(() => {
    const processQueue = async () => {
      if (animationQueue.length === 0) return;
      
      // Sort by priority (higher priority first)
      const sortedQueue = [...animationQueue].sort((a, b) => b.priority - a.priority);
      const nextAnimation = sortedQueue[0];
      
      try {
        await nextAnimation.animation();
        removeFromQueue(nextAnimation.id);
      } catch (error) {
        console.error('Animation error:', error);
        removeFromQueue(nextAnimation.id);
      }
    };

    if (animationQueue.length > 0 && !isPageTransitioning) {
      processQueue();
    }
  }, [animationQueue, isPageTransitioning]);

  // Queue management functions
  const addToQueue = (animation: {
    id: string;
    priority: number;
    animation: () => Promise<void>;
  }) => {
    setAnimationQueue(prev => {
      // Remove existing animation with same ID
      const filtered = prev.filter(item => item.id !== animation.id);
      return [...filtered, animation];
    });
  };

  const removeFromQueue = (id: string) => {
    setAnimationQueue(prev => prev.filter(item => item.id !== id));
  };

  // Global animation triggers
  const triggerGlobalAnimation = async (animationType: string) => {
    switch (animationType) {
      case 'pageEnter':
        await globalControls.start({
          opacity: [0, 1],
          y: [20, 0],
          transition: { duration: 0.6, ease: 'easeOut' }
        });
        break;
      case 'pageExit':
        await globalControls.start({
          opacity: [1, 0],
          y: [0, -20],
          transition: { duration: 0.4, ease: 'easeIn' }
        });
        break;
      case 'heroReveal':
        await globalControls.start({
          scale: [0.95, 1],
          opacity: [0, 1],
          transition: { duration: 1.2, ease: 'easeOut' }
        });
        break;
      default:
        break;
    }
  };

  // Adjust animation speed based on performance mode
  useEffect(() => {
    switch (performanceMode) {
      case 'high':
        setAnimationSpeed(1);
        break;
      case 'medium':
        setAnimationSpeed(0.7);
        break;
      case 'low':
        setAnimationSpeed(0.5);
        break;
    }
  }, [performanceMode]);

  const value: AnimationContextType = {
    globalControls,
    isReducedMotion,
    animationSpeed,
    isPageTransitioning,
    setIsPageTransitioning,
    performanceMode,
    setPerformanceMode,
    animationQueue,
    addToQueue,
    removeFromQueue,
    triggerGlobalAnimation,
    mousePosition,
    scrollPosition
  };

  return (
    <AnimationContext.Provider value={value}>
      {children}
    </AnimationContext.Provider>
  );
};

export const useAnimationContext = (): AnimationContextType => {
  const context = useContext(AnimationContext);
  if (!context) {
    throw new Error('useAnimationContext must be used within an AnimationProvider');
  }
  return context;
};
