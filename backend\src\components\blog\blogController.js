import asyncHandler from 'express-async-handler';
import Blog from './blogModel.js';
import { StatusCodes } from 'http-status-codes';
import ErrorResponse from '../../shared/utils/errorResponse.js';

// @desc    Get all blogs
// @route   GET /api/blogs
// @access  Public
const getBlogs = asyncHandler(async (req, res) => {
  // Copy req.query
  const reqQuery = { ...req.query };

  // Fields to exclude
  const removeFields = ['select', 'sort', 'page', 'limit', 'language'];

  // Loop over removeFields and delete them from reqQuery
  removeFields.forEach(param => delete reqQuery[param]);

  // Create query string
  let queryStr = JSON.stringify(reqQuery);

  // Create operators ($gt, $gte, etc)
  queryStr = queryStr.replace(/\b(gt|gte|lt|lte|in)\b/g, match => `$${match}`);

  // Finding resource
  let query = Blog.find(JSON.parse(queryStr)).populate('author', 'name');

  // Select Fields
  if (req.query.select) {
    const fields = req.query.select.split(',').join(' ');
    query = query.select(fields);
  }

  // Sort
  if (req.query.sort) {
    const sortBy = req.query.sort.split(',').join(' ');
    query = query.sort(sortBy);
  } else {
    query = query.sort('-createdAt');
  }

  // Pagination
  const page = parseInt(req.query.page, 10) || 1;
  const limit = parseInt(req.query.limit, 10) || 10;
  const startIndex = (page - 1) * limit;
  const endIndex = page * limit;
  const total = await Blog.countDocuments(JSON.parse(queryStr));

  query = query.skip(startIndex).limit(limit);

  // Executing query
  const blogs = await query;

  // Transform blogs based on language
  const language = req.query.language || 'en';
  const transformedBlogs = blogs.map(blog => {
    const blogObj = blog.toObject();
    return {
      ...blogObj,
      title: blogObj.title[language] || blogObj.title.en,
      content: blogObj.content[language] || blogObj.content.en,
      excerpt: blogObj.excerpt[language] || blogObj.excerpt.en,
    };
  });

  // Pagination result
  const pagination = {};

  if (endIndex < total) {
    pagination.next = {
      page: page + 1,
      limit,
    };
  }

  if (startIndex > 0) {
    pagination.prev = {
      page: page - 1,
      limit,
    };
  }

  res.status(StatusCodes.OK).json({
    success: true,
    count: transformedBlogs.length,
    pagination,
    data: transformedBlogs,
  });
});

// @desc    Get single blog
// @route   GET /api/blogs/:id
// @access  Public
const getBlog = asyncHandler(async (req, res) => {
  const blog = await Blog.findById(req.params.id).populate('author', 'name');

  if (!blog) {
    res.status(StatusCodes.NOT_FOUND);
    throw new Error('Blog not found');
  }

  // Transform blog based on language
  const language = req.query.language || 'en';
  const blogObj = blog.toObject();
  const transformedBlog = {
    ...blogObj,
    title: blogObj.title[language] || blogObj.title.en,
    content: blogObj.content[language] || blogObj.content.en,
    excerpt: blogObj.excerpt[language] || blogObj.excerpt.en,
  };

  res.status(StatusCodes.OK).json({
    success: true,
    data: transformedBlog,
  });
});

// @desc    Create new blog
// @route   POST /api/blogs
// @access  Private/Admin
const createBlog = asyncHandler(async (req, res) => {
  // Add user to req.body
  req.body.author = req.user.id;

  const blog = await Blog.create(req.body);

  res.status(StatusCodes.CREATED).json({
    success: true,
    data: blog,
  });
});

// @desc    Update blog
// @route   PUT /api/blogs/:id
// @access  Private/Admin
const updateBlog = asyncHandler(async (req, res) => {
  let blog = await Blog.findById(req.params.id);

  if (!blog) {
    res.status(StatusCodes.NOT_FOUND);
    throw new Error('Blog not found');
  }

  // Make sure user is blog owner or admin
  if (blog.author.toString() !== req.user.id && req.user.role !== 'admin') {
    res.status(StatusCodes.UNAUTHORIZED);
    throw new Error('Not authorized to update this blog');
  }

  blog = await Blog.findByIdAndUpdate(req.params.id, req.body, {
    new: true,
    runValidators: true,
  });

  res.status(StatusCodes.OK).json({
    success: true,
    data: blog,
  });
});

// @desc    Delete blog
// @route   DELETE /api/blogs/:id
// @access  Private/Admin
const deleteBlog = asyncHandler(async (req, res) => {
  const blog = await Blog.findById(req.params.id);

  if (!blog) {
    res.status(StatusCodes.NOT_FOUND);
    throw new Error('Blog not found');
  }

  // Make sure user is blog owner or admin
  if (blog.author.toString() !== req.user.id && req.user.role !== 'admin') {
    res.status(StatusCodes.UNAUTHORIZED);
    throw new Error('Not authorized to delete this blog');
  }

  await Blog.findByIdAndDelete(req.params.id);

  res.status(StatusCodes.OK).json({
    success: true,
    data: {},
  });
});

// @desc    Upload photo for blog
// @route   PUT /api/blogs/:id/photo
// @access  Private
const blogPhotoUpload = asyncHandler(async (req, res) => {
  const blog = await Blog.findById(req.params.id);

  if (!blog) {
    res.status(StatusCodes.NOT_FOUND);
    throw new Error(`Blog not found with id of ${req.params.id}`);
  }

  // Make sure user is blog owner or admin
  if (blog.author.toString() !== req.user.id && req.user.role !== 'admin') {
    res.status(StatusCodes.UNAUTHORIZED);
    throw new Error('Not authorized to update this blog');
  }

  if (!req.files) {
    res.status(StatusCodes.BAD_REQUEST);
    throw new Error('Please upload a file');
  }

  const file = req.files.file;

  // Make sure the image is a photo
  if (!file.mimetype.startsWith('image')) {
    res.status(StatusCodes.BAD_REQUEST);
    throw new Error('Please upload an image file');
  }

  // Check filesize
  if (file.size > process.env.MAX_FILE_UPLOAD) {
    res.status(StatusCodes.BAD_REQUEST);
    throw new Error(`Please upload an image less than ${process.env.MAX_FILE_UPLOAD}`);
  }

  // Create custom filename
  file.name = `photo_${blog._id}${path.parse(file.name).ext}`;

  file.mv(`${process.env.FILE_UPLOAD_PATH}/${file.name}`, async err => {
    if (err) {
      
      res.status(StatusCodes.INTERNAL_SERVER_ERROR);
      throw new Error('Problem with file upload');
    }

    await Blog.findByIdAndUpdate(req.params.id, { photo: file.name });

    res.status(StatusCodes.OK).json({
      success: true,
      data: file.name,
    });
  });
});
const getBlogBySlug = asyncHandler(async (req, res) => {
  const blog = await Blog.findOne({ slug: req.params.slug }).populate('author', 'name');

  if (!blog) {
    res.status(StatusCodes.NOT_FOUND);
    throw new Error('Blog not found');
  }

  // Transform blog based on language
  const language = req.query.language || 'en';
  const blogObj = blog.toObject();
  const transformedBlog = {
    ...blogObj,
    title: blogObj.title[language] || blogObj.title.en,
    content: blogObj.content[language] || blogObj.content.en,
    excerpt: blogObj.excerpt[language] || blogObj.excerpt.en,
  };

  res.status(StatusCodes.OK).json({
    success: true,
    data: transformedBlog,
  });
});

export {
  getBlogs,
  getBlog,
  createBlog,
  updateBlog,
  deleteBlog,
  blogPhotoUpload,
  getBlogBySlug,
};
