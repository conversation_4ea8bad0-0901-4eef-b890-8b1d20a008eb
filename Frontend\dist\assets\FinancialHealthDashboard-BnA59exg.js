var p=(x,n,i)=>new Promise((o,g)=>{var u=a=>{try{r(i.next(a))}catch(d){g(d)}},s=a=>{try{r(i.throw(a))}catch(d){g(d)}},r=a=>a.done?o(a.value):Promise.resolve(a.value).then(u,s);r((i=i.apply(x,n)).next())});import{c,aB as N,a as w,r as R,aC as z,j as e,l as j,U as v,T as y,aD as C,C as M,aE as T,p as S,Z as k,aF as $}from"./index-hEW_vQ3f.js";import{B as f,A as D,a as P,U,b as A}from"./MarketingDashboard-B-b1bZoZ.js";/**
 * @license lucide-react v0.336.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const b=c("DollarSign",[["line",{x1:"12",x2:"12",y1:"2",y2:"22",key:"7eqyqh"}],["path",{d:"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6",key:"1b0p4s"}]]);/**
 * @license lucide-react v0.336.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const F=c("MessageCircle",[["path",{d:"M7.9 20A9 9 0 1 0 4 16.1L2 22Z",key:"vv11sd"}]]);/**
 * @license lucide-react v0.336.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const L=c("Percent",[["line",{x1:"19",x2:"5",y1:"5",y2:"19",key:"1x9vlm"}],["circle",{cx:"6.5",cy:"6.5",r:"2.5",key:"4mh3h7"}],["circle",{cx:"17.5",cy:"17.5",r:"2.5",key:"1mdrzq"}]]);/**
 * @license lucide-react v0.336.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const H=c("ThumbsUp",[["path",{d:"M7 10v12",key:"1qc93n"}],["path",{d:"M15 5.88 14 10h5.83a2 2 0 0 1 1.92 2.56l-2.33 8A2 2 0 0 1 17.5 22H4a2 2 0 0 1-2-2v-8a2 2 0 0 1 2-2h2.76a2 2 0 0 0 1.79-1.11L12 2h0a3.13 3.13 0 0 1 3 3.88Z",key:"y3tblf"}]]);/**
 * @license lucide-react v0.336.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const q=c("TrendingDown",[["polyline",{points:"22 17 13.5 8.5 8.5 13.5 2 7",key:"1r2t7k"}],["polyline",{points:"16 17 22 17 22 11",key:"11uiuu"}]]),O=x=>p(null,null,function*(){const{data:n}=yield N.get("/financial-metrics/overview",{params:x});return n}),I=()=>{var h;const{t:x}=w(),[n,i]=R.useState("30d"),{data:o,isLoading:g,error:u}=z({queryKey:["financial-overview",n],queryFn:()=>O({period:n}),staleTime:5*60*1e3}),s=((h=o==null?void 0:o.data)==null?void 0:h.currentPeriod)||{},r=t=>t==null||isNaN(t)?"0%":`${t.toFixed(1)}%`,a=t=>t==null||isNaN(t)?"$0":new Intl.NumberFormat("en-US",{style:"currency",currency:"USD"}).format(t),d=t=>t==null||isNaN(t)?"0 days":`${t.toFixed(1)} days`,m=t=>t==null||isNaN(t)?"0":new Intl.NumberFormat("en-US").format(t);return g?e.jsx("div",{className:"flex items-center justify-center h-64",children:e.jsx("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-brand-accent-100"})}):u?e.jsxs("div",{className:"flex items-center justify-center h-64",children:[e.jsx(j,{className:"h-8 w-8 text-red-400"}),e.jsx("span",{className:"ml-2 text-brand-grey-400",children:"Error loading data"})]}):e.jsxs("div",{className:"p-4 space-y-4",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("h1",{className:"text-2xl font-bold text-brand-white",children:"Financial Health"}),e.jsx("p",{className:"text-brand-grey-400 text-sm",children:"Client success & financial performance"})]}),e.jsx("div",{className:"flex bg-brand-grey-800 rounded-lg p-1",children:["7d","30d","90d","1y"].map(t=>e.jsx("button",{onClick:()=>i(t),className:`px-3 py-1 rounded text-sm font-medium transition-all ${n===t?"bg-brand-accent-100 text-brand-grey-950":"text-brand-grey-400 hover:text-brand-white"}`,children:t},t))})]}),e.jsx("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-3",children:[{title:"Revenue",value:a(s.totalRevenue||0),change:"+12.5%",trend:"up",icon:e.jsx(b,{size:16}),color:"green"},{title:"Customers",value:m(s.totalCustomers||0),change:"+8.2%",trend:"up",icon:e.jsx(v,{size:16}),color:"blue"},{title:"Conversion",value:r(s.conversionRate||0),change:"+3.1%",trend:"up",icon:e.jsx(y,{size:16}),color:"purple"},{title:"Profit Margin",value:r(s.profitMargin||0),change:"+5.7%",trend:"up",icon:e.jsx(f,{size:16}),color:"orange"}].map((t,l)=>e.jsxs("div",{className:"bg-brand-grey-800 rounded-lg p-3 border border-brand-grey-700",children:[e.jsxs("div",{className:"flex items-center justify-between mb-2",children:[e.jsx("div",{className:`p-1.5 rounded bg-${t.color}-500/10`,children:e.jsx("div",{className:`text-${t.color}-400`,children:t.icon})}),e.jsxs("div",{className:`flex items-center text-xs font-medium ${t.trend==="up"?"text-green-400":"text-red-400"}`,children:[t.trend==="up"?e.jsx(D,{size:12}):e.jsx(P,{size:12}),e.jsx("span",{className:"ml-1",children:t.change})]})]}),e.jsx("h3",{className:"text-sm font-medium text-brand-grey-300 mb-1",children:t.title}),e.jsx("p",{className:"text-xl font-bold text-brand-white",children:t.value})]},l))}),e.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-4",children:[e.jsxs("div",{className:"bg-brand-grey-800 rounded-lg border border-brand-grey-700 p-4",children:[e.jsxs("h2",{className:"text-lg font-semibold text-brand-white mb-3 flex items-center",children:[e.jsx(C,{className:"mr-2 text-red-400",size:18}),"Client Success"]}),e.jsx("div",{className:"grid grid-cols-2 gap-3",children:[{title:"Onboarding",value:d(s.clientOnboardingTime||0),status:(s.clientOnboardingTime||0)<7?"excellent":(s.clientOnboardingTime||0)<14?"good":"poor",icon:e.jsx(M,{size:14})},{title:"Retention",value:r(s.clientRetentionRate||0),status:(s.clientRetentionRate||0)>85?"excellent":(s.clientRetentionRate||0)>70?"good":"poor",icon:e.jsx(U,{size:14})},{title:"Churn Rate",value:r(s.churnRate||0),status:(s.churnRate||0)<5?"excellent":(s.churnRate||0)<10?"good":"poor",icon:e.jsx(q,{size:14})},{title:"Satisfaction",value:r(s.clientSatisfactionRate||0),status:(s.clientSatisfactionRate||0)>90?"excellent":(s.clientSatisfactionRate||0)>80?"good":"poor",icon:e.jsx(H,{size:14})},{title:"Referrals",value:r(s.referralRate||0),status:(s.referralRate||0)>25?"excellent":(s.referralRate||0)>15?"good":"poor",icon:e.jsx(F,{size:14})},{title:"NPS",value:m(s.netPromoterScore||0),status:(s.netPromoterScore||0)>50?"excellent":(s.netPromoterScore||0)>30?"good":"poor",icon:e.jsx(T,{size:14})}].map((t,l)=>e.jsxs("div",{className:"bg-brand-grey-700 rounded p-2",children:[e.jsxs("div",{className:"flex items-center justify-between mb-1",children:[e.jsx("div",{className:"text-brand-grey-300",children:t.icon}),e.jsx("div",{className:`px-1.5 py-0.5 rounded text-xs font-medium ${t.status==="excellent"?"bg-green-500/20 text-green-400":t.status==="good"?"bg-yellow-500/20 text-yellow-400":"bg-red-500/20 text-red-400"}`,children:t.status})]}),e.jsx("h3",{className:"text-xs font-medium text-brand-grey-300 mb-1",children:t.title}),e.jsx("p",{className:"text-sm font-bold text-brand-white",children:t.value})]},l))})]}),e.jsxs("div",{className:"bg-brand-grey-800 rounded-lg border border-brand-grey-700 p-4",children:[e.jsxs("h2",{className:"text-lg font-semibold text-brand-white mb-3 flex items-center",children:[e.jsx(b,{className:"mr-2 text-green-400",size:18}),"Financial Health"]}),e.jsx("div",{className:"grid grid-cols-2 gap-3",children:[{title:"CPL",value:a(s.cpl||0),status:(s.cpl||0)<50?"excellent":(s.cpl||0)<100?"good":"poor",icon:e.jsx(y,{size:14})},{title:"CAC",value:a(s.cac||0),status:(s.cac||0)<200?"excellent":(s.cac||0)<400?"good":"poor",icon:e.jsx(v,{size:14})},{title:"CLTV",value:a(s.cltv||0),status:(s.cltv||0)>1e3?"excellent":(s.cltv||0)>500?"good":"poor",icon:e.jsx(b,{size:14})},{title:"ROMI",value:r(s.romi||0),status:(s.romi||0)>300?"excellent":(s.romi||0)>150?"good":"poor",icon:e.jsx(S,{size:14})},{title:"MRR",value:a(s.mrr||0),status:(s.mrr||0)>5e4?"excellent":(s.mrr||0)>25e3?"good":"poor",icon:e.jsx(A,{size:14})},{title:"Growth",value:r(s.revenueGrowth||0),status:(s.revenueGrowth||0)>20?"excellent":(s.revenueGrowth||0)>10?"good":"poor",icon:e.jsx(k,{size:14})}].map((t,l)=>e.jsxs("div",{className:"bg-brand-grey-700 rounded p-2",children:[e.jsxs("div",{className:"flex items-center justify-between mb-1",children:[e.jsx("div",{className:"text-brand-grey-300",children:t.icon}),e.jsx("div",{className:`px-1.5 py-0.5 rounded text-xs font-medium ${t.status==="excellent"?"bg-green-500/20 text-green-400":t.status==="good"?"bg-yellow-500/20 text-yellow-400":"bg-red-500/20 text-red-400"}`,children:t.status})]}),e.jsx("h3",{className:"text-xs font-medium text-brand-grey-300 mb-1",children:t.title}),e.jsx("p",{className:"text-sm font-bold text-brand-white",children:t.value})]},l))})]})]}),e.jsxs("div",{className:"bg-brand-grey-800 rounded-lg border border-brand-grey-700 p-4",children:[e.jsxs("h2",{className:"text-lg font-semibold text-brand-white mb-3 flex items-center",children:[e.jsx(f,{className:"mr-2 text-blue-400",size:18}),"Budget Management"]}),e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-3",children:[{title:"Budget Deviation",value:r(s.budgetDeviation||0),status:Math.abs(s.budgetDeviation||0)<10?"excellent":Math.abs(s.budgetDeviation||0)<20?"good":"poor",icon:e.jsx(j,{size:16}),color:"red"},{title:"CLTV/CAC Ratio",value:`${m(s.cltvToCacRatio||0)}:1`,status:(s.cltvToCacRatio||0)>3?"excellent":(s.cltvToCacRatio||0)>1.5?"good":"poor",icon:e.jsx($,{size:16}),color:"green"},{title:"Profit Margin",value:r(s.profitMargin||0),status:(s.profitMargin||0)>20?"excellent":(s.profitMargin||0)>10?"good":"poor",icon:e.jsx(L,{size:16}),color:"blue"}].map((t,l)=>e.jsxs("div",{className:"bg-brand-grey-700 rounded p-3",children:[e.jsxs("div",{className:"flex items-center justify-between mb-2",children:[e.jsx("div",{className:`p-1.5 rounded bg-${t.color}-500/10`,children:e.jsx("div",{className:`text-${t.color}-400`,children:t.icon})}),e.jsx("div",{className:`px-2 py-1 rounded text-xs font-medium ${t.status==="excellent"?"bg-green-500/20 text-green-400":t.status==="good"?"bg-yellow-500/20 text-yellow-400":"bg-red-500/20 text-red-400"}`,children:t.status})]}),e.jsx("h3",{className:"text-sm font-medium text-brand-grey-300 mb-1",children:t.title}),e.jsx("p",{className:"text-lg font-bold text-brand-white",children:t.value})]},l))})]})]})};export{I as default};
