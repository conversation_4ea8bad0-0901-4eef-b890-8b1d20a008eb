import { useEffect, useRef, useState } from "react";
import axios from "axios";
import { useLocation } from "react-router-dom";
import { useAuth } from "@/core/providers/AuthContext";

// Helper: Get or create sessionId with sessionStorage (new session on tab/browser close)
function getSessionId() {
  const now = Date.now();
  let sessionId = sessionStorage.getItem('sessionId');
  let sessionTimestamp = sessionStorage.getItem('sessionTimestamp');
  if (!sessionId || !sessionTimestamp) {
    sessionId = Math.random().toString(36).substring(2) + now.toString(36);
    sessionStorage.setItem('sessionId', sessionId);
  }
  sessionStorage.setItem('sessionTimestamp', now.toString());
  return sessionId;
}

// Helper: Get a new sessionId (for this browser open only)
function createSessionId() {
  const now = Date.now();
  return Math.random().toString(36).substring(2) + now.toString(36);
}

// Helper: Get traffic source
function getSource() {
  const url = new URL(window.location.href);
  if (url.searchParams.get("utm_source")) {
    const src = url.searchParams.get("utm_source")?.toLowerCase();
    if (["google", "bing", "yahoo"].includes(src!)) return "organic";
    if (["facebook", "twitter", "linkedin", "instagram"].includes(src!)) return "social";
    if (["adwords", "ads", "paid"].includes(src!)) return "paid";
    if (["referral"].includes(src!)) return "referral";
    return src;
  }
  return document.referrer ? "referral" : "direct";
}

// Utility to extract page metadata (stub, to be improved per route/component)
function getPageMetadata(location: any, extra: any = {}) {
  // Default: use pathname as pageName
  let pageName = location.pathname;
  let blogTitle, serviceName, subServiceName;
  // Example: check for blog or service routes
  if (location.pathname.startsWith('/blog/') && extra.blogTitle) {
    pageName = 'Blog Article';
    blogTitle = extra.blogTitle;
  } else if (location.pathname.startsWith('/services/') && extra.serviceName) {
    pageName = 'Service Detail';
    serviceName = extra.serviceName;
    if (extra.subServiceName) subServiceName = extra.subServiceName;
  } else if (location.pathname === '/blog') {
    pageName = 'Blog List';
  } else if (location.pathname === '/services') {
    pageName = 'Service List';
  }
  return { pageName, blogTitle, serviceName, subServiceName };
}

const publicAuthRoutes = ["/login", "/register", "/forgot-password"];

const THROTTLE_INTERVAL = 5000; // 5 seconds
let lastAnalyticsSent = 0;

const AnalyticsTracker = () => {
  const location = useLocation();
  const sessionStart = useRef(Date.now());
  const [region, setRegion] = useState<string | null>(null);
  const [country, setCountry] = useState<string | null>(null);
  const { user, isAuthenticated } = useAuth();

  // Helper function to check if current user should be excluded from analytics
  const shouldExcludeFromAnalytics = () => {
    const authenticated = isAuthenticated();
    const userRole = user?.role;


    // Only exclude staff users (admin/marketing_responsible)
    // Track everyone else: anonymous visitors, regular users, clients
    if (authenticated && user?.role) {
      const excludedRoles = ['admin', 'marketing_responsible'];

      if (excludedRoles.includes(userRole)) {
        return true;
      }
    }

    return false;
  };

  // Fetch region/country on first load with multiple fallback services
  useEffect(() => {
    const getLocationFromMultipleSources = async () => {
      const sources = [
        {
          name: 'ipinfo',
          url: 'https://ipinfo.io/json',
          parser: (data: any) => ({
            country: data.country,
            region: data.region,
            city: data.city
          })
        },
        {
          name: 'ipapi',
          url: 'https://ipapi.co/json/',
          parser: (data: any) => ({
            country: data.country_code,
            region: data.region,
            city: data.city
          })
        },
        {
          name: 'ipgeolocation',
          url: 'https://api.ipgeolocation.io/ipgeo?apiKey=********************************',
          parser: (data: any) => ({
            country: data.country_code2,
            region: data.state_prov,
            city: data.city
          })
        }
      ];

      for (const source of sources) {
        try {
          const res = await fetch(source.url);
          if (!res.ok) continue;

          const data = await res.json();
          const location = source.parser(data);


          if (location.country && location.region) {
            setCountry(location.country);
            setRegion(location.region);
            return; // Success, stop trying other sources
          }
        } catch (error) {
          continue;
        }
      }

      // If all sources fail
      setRegion(null);
      setCountry(null);
    };

    getLocationFromMultipleSources();
  }, []);

  // Track page view on route change (exclude dashboard and auth pages)
  useEffect(() => {
    // Skip analytics tracking for admin/staff users
    if (shouldExcludeFromAnalytics()) {
      return;
    }

    if (!location.pathname.startsWith("/dashboard") && !publicAuthRoutes.includes(location.pathname)) {
      const now = Date.now();
      if (now - lastAnalyticsSent > THROTTLE_INTERVAL) {
        lastAnalyticsSent = now;

        // Check if this is the first page view in this session
        const sessionId = getSessionId();
        const sessionVisitKey = `visitTracked_${sessionId}`;
        const isFirstVisit = !sessionStorage.getItem(sessionVisitKey);

        const meta = getPageMetadata(location);
        const payload = {
          path: location.pathname,
          timestamp: new Date().toISOString(),
          duration: 0,
          referrer: document.referrer,
          userAgent: navigator.userAgent,
          source: getSource(),
          sessionId,
          region,
          country,
          type: isFirstVisit ? 'visit' : 'pageview', // Only send 'visit' for first page in session
          ...meta,
        };

        axios.post("/api/analytics/track", payload).catch(() => {});

        // Mark that we've tracked a visit for this session
        if (isFirstVisit) {
          sessionStorage.setItem(sessionVisitKey, '1');
        }
      }
    }
  }, [location.pathname, region, country, user, isAuthenticated]);

  // Track session duration on unload
  useEffect(() => {
    const handleUnload = () => {
      // Skip analytics tracking for admin/staff users
      if (shouldExcludeFromAnalytics()) return;

      // Skip if on dashboard (dashboard handles its own tracking)
      if (location.pathname.startsWith("/dashboard")) return;
      // Throttle: only send if at least 5 seconds since lastAnalyticsSent
      const now = Date.now();
      if (now - lastAnalyticsSent < THROTTLE_INTERVAL) return;
      lastAnalyticsSent = now;
      const duration = now - sessionStart.current;
      const payload = {
        path: location.pathname,
        timestamp: new Date().toISOString(),
        duration,
        referrer: document.referrer,
        userAgent: navigator.userAgent,
        source: getSource(),
        sessionId: getSessionId(),
        region,
        country,
        type: 'pageview',
      };
      axios.post("/api/analytics/track", payload).catch(() => {});
    };
    window.addEventListener("beforeunload", handleUnload);
    return () => {
      window.removeEventListener("beforeunload", handleUnload);
      handleUnload();
    };
    // eslint-disable-next-line
  }, [region, country, location.pathname]);

  return null;
};

export default AnalyticsTracker;
