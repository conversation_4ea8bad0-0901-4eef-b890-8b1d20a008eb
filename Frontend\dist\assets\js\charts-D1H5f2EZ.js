import{i as pt,a as Mu,b as vt,g as he,c as G,d as $r,e as K,S as tp,u as Ri,s as rp,f as np,h as ip,j as ap,k as op,l as cp,m as Nu,n as ic,o as No,t as Ru,D as Q,p as kt,q as ac,r as Oa,v as Kn,w as oc,x as jn,y as lt,z as lp,A as up,B as sp,C as fp,E as pp,F as dp,G as vp,H as Le,I as Bu,J as Dn,K as hp,L as yp,M as mp,N as gp,O as bp,P as Op,Q as xp,R as Lu,T as wp,U as Ap,V as Pp,W as Sp,X as jp,Y as Ep,Z as Wu,_ as $p,$ as _p,a0 as Tp,a1 as Ip,a2 as Vn,a3 as zu,a4 as kp,a5 as Dp,a6 as Cp,a7 as Mp,a8 as Np,a9 as Ku,aa as Rp}from"./vendor-OXu-rwpf.js";import{r as N,a as Bp,R as b,A as $e}from"./react-vendor-Dq0qSR31.js";import{c as X}from"./utils-vendor-DSNVchvY.js";var ye=function(e){return e===0?0:e>0?1:-1},Et=function(e){return pt(e)&&e.indexOf("%")===e.length-1},R=function(e){return Mu(e)&&!vt(e)},Lp=function(e){return G(e)},pe=function(e){return R(e)||pt(e)},Wp=0,nt=function(e){var n=++Wp;return"".concat(e||"").concat(n)},Pe=function(e,n){var r=arguments.length>2&&arguments[2]!==void 0?arguments[2]:0,i=arguments.length>3&&arguments[3]!==void 0?arguments[3]:!1;if(!R(e)&&!pt(e))return r;var a;if(Et(e)){var o=e.indexOf("%");a=n*parseFloat(e.slice(0,o))/100}else a=+e;return vt(a)&&(a=r),i&&a>n&&(a=n),a},ct=function(e){if(!e)return null;var n=Object.keys(e);return n&&n.length?e[n[0]]:null},zp=function(e){if(!Array.isArray(e))return!1;for(var n=e.length,r={},i=0;i<n;i++)if(!r[e[i]])r[e[i]]=!0;else return!0;return!1},q=function(e,n){return R(e)&&R(n)?function(r){return e+r*(n-e)}:function(){return n}};function Xn(t,e,n){return!t||!t.length?null:t.find(function(r){return r&&(typeof e=="function"?e(r):he(r,e))===n})}var Kp=function(e){if(!e||!e.length)return null;for(var n=e.length,r=0,i=0,a=0,o=0,c=1/0,l=-1/0,u=0,s=0,f=0;f<n;f++)u=e[f].cx||0,s=e[f].cy||0,r+=u,i+=s,a+=u*s,o+=u*u,c=Math.min(c,u),l=Math.max(l,u);var p=n*o!==r*r?(n*a-r*i)/(n*o-r*r):0;return{xmin:c,xmax:l,a:p,b:(i-p*r)/n}},Fp=function(e,n){return R(e)&&R(n)?e-n:pt(e)&&pt(n)?e.localeCompare(n):e instanceof Date&&n instanceof Date?e.getTime()-n.getTime():String(e).localeCompare(String(n))};function Tt(t,e){for(var n in t)if({}.hasOwnProperty.call(t,n)&&(!{}.hasOwnProperty.call(e,n)||t[n]!==e[n]))return!1;for(var r in e)if({}.hasOwnProperty.call(e,r)&&!{}.hasOwnProperty.call(t,r))return!1;return!0}function xa(t){"@babel/helpers - typeof";return xa=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},xa(t)}var Vp=["viewBox","children"],Xp=["aria-activedescendant","aria-atomic","aria-autocomplete","aria-busy","aria-checked","aria-colcount","aria-colindex","aria-colspan","aria-controls","aria-current","aria-describedby","aria-details","aria-disabled","aria-errormessage","aria-expanded","aria-flowto","aria-haspopup","aria-hidden","aria-invalid","aria-keyshortcuts","aria-label","aria-labelledby","aria-level","aria-live","aria-modal","aria-multiline","aria-multiselectable","aria-orientation","aria-owns","aria-placeholder","aria-posinset","aria-pressed","aria-readonly","aria-relevant","aria-required","aria-roledescription","aria-rowcount","aria-rowindex","aria-rowspan","aria-selected","aria-setsize","aria-sort","aria-valuemax","aria-valuemin","aria-valuenow","aria-valuetext","className","color","height","id","lang","max","media","method","min","name","style","target","width","role","tabIndex","accentHeight","accumulate","additive","alignmentBaseline","allowReorder","alphabetic","amplitude","arabicForm","ascent","attributeName","attributeType","autoReverse","azimuth","baseFrequency","baselineShift","baseProfile","bbox","begin","bias","by","calcMode","capHeight","clip","clipPath","clipPathUnits","clipRule","colorInterpolation","colorInterpolationFilters","colorProfile","colorRendering","contentScriptType","contentStyleType","cursor","cx","cy","d","decelerate","descent","diffuseConstant","direction","display","divisor","dominantBaseline","dur","dx","dy","edgeMode","elevation","enableBackground","end","exponent","externalResourcesRequired","fill","fillOpacity","fillRule","filter","filterRes","filterUnits","floodColor","floodOpacity","focusable","fontFamily","fontSize","fontSizeAdjust","fontStretch","fontStyle","fontVariant","fontWeight","format","from","fx","fy","g1","g2","glyphName","glyphOrientationHorizontal","glyphOrientationVertical","glyphRef","gradientTransform","gradientUnits","hanging","horizAdvX","horizOriginX","href","ideographic","imageRendering","in2","in","intercept","k1","k2","k3","k4","k","kernelMatrix","kernelUnitLength","kerning","keyPoints","keySplines","keyTimes","lengthAdjust","letterSpacing","lightingColor","limitingConeAngle","local","markerEnd","markerHeight","markerMid","markerStart","markerUnits","markerWidth","mask","maskContentUnits","maskUnits","mathematical","mode","numOctaves","offset","opacity","operator","order","orient","orientation","origin","overflow","overlinePosition","overlineThickness","paintOrder","panose1","pathLength","patternContentUnits","patternTransform","patternUnits","pointerEvents","pointsAtX","pointsAtY","pointsAtZ","preserveAlpha","preserveAspectRatio","primitiveUnits","r","radius","refX","refY","renderingIntent","repeatCount","repeatDur","requiredExtensions","requiredFeatures","restart","result","rotate","rx","ry","seed","shapeRendering","slope","spacing","specularConstant","specularExponent","speed","spreadMethod","startOffset","stdDeviation","stemh","stemv","stitchTiles","stopColor","stopOpacity","strikethroughPosition","strikethroughThickness","string","stroke","strokeDasharray","strokeDashoffset","strokeLinecap","strokeLinejoin","strokeMiterlimit","strokeOpacity","strokeWidth","surfaceScale","systemLanguage","tableValues","targetX","targetY","textAnchor","textDecoration","textLength","textRendering","to","transform","u1","u2","underlinePosition","underlineThickness","unicode","unicodeBidi","unicodeRange","unitsPerEm","vAlphabetic","values","vectorEffect","version","vertAdvY","vertOriginX","vertOriginY","vHanging","vIdeographic","viewTarget","visibility","vMathematical","widths","wordSpacing","writingMode","x1","x2","x","xChannelSelector","xHeight","xlinkActuate","xlinkArcrole","xlinkHref","xlinkRole","xlinkShow","xlinkTitle","xlinkType","xmlBase","xmlLang","xmlns","xmlnsXlink","xmlSpace","y1","y2","y","yChannelSelector","z","zoomAndPan","ref","key","angle"],cc=["points","pathLength"],aa={svg:Vp,polygon:cc,polyline:cc},Ro=["dangerouslySetInnerHTML","onCopy","onCopyCapture","onCut","onCutCapture","onPaste","onPasteCapture","onCompositionEnd","onCompositionEndCapture","onCompositionStart","onCompositionStartCapture","onCompositionUpdate","onCompositionUpdateCapture","onFocus","onFocusCapture","onBlur","onBlurCapture","onChange","onChangeCapture","onBeforeInput","onBeforeInputCapture","onInput","onInputCapture","onReset","onResetCapture","onSubmit","onSubmitCapture","onInvalid","onInvalidCapture","onLoad","onLoadCapture","onError","onErrorCapture","onKeyDown","onKeyDownCapture","onKeyPress","onKeyPressCapture","onKeyUp","onKeyUpCapture","onAbort","onAbortCapture","onCanPlay","onCanPlayCapture","onCanPlayThrough","onCanPlayThroughCapture","onDurationChange","onDurationChangeCapture","onEmptied","onEmptiedCapture","onEncrypted","onEncryptedCapture","onEnded","onEndedCapture","onLoadedData","onLoadedDataCapture","onLoadedMetadata","onLoadedMetadataCapture","onLoadStart","onLoadStartCapture","onPause","onPauseCapture","onPlay","onPlayCapture","onPlaying","onPlayingCapture","onProgress","onProgressCapture","onRateChange","onRateChangeCapture","onSeeked","onSeekedCapture","onSeeking","onSeekingCapture","onStalled","onStalledCapture","onSuspend","onSuspendCapture","onTimeUpdate","onTimeUpdateCapture","onVolumeChange","onVolumeChangeCapture","onWaiting","onWaitingCapture","onAuxClick","onAuxClickCapture","onClick","onClickCapture","onContextMenu","onContextMenuCapture","onDoubleClick","onDoubleClickCapture","onDrag","onDragCapture","onDragEnd","onDragEndCapture","onDragEnter","onDragEnterCapture","onDragExit","onDragExitCapture","onDragLeave","onDragLeaveCapture","onDragOver","onDragOverCapture","onDragStart","onDragStartCapture","onDrop","onDropCapture","onMouseDown","onMouseDownCapture","onMouseEnter","onMouseLeave","onMouseMove","onMouseMoveCapture","onMouseOut","onMouseOutCapture","onMouseOver","onMouseOverCapture","onMouseUp","onMouseUpCapture","onSelect","onSelectCapture","onTouchCancel","onTouchCancelCapture","onTouchEnd","onTouchEndCapture","onTouchMove","onTouchMoveCapture","onTouchStart","onTouchStartCapture","onPointerDown","onPointerDownCapture","onPointerMove","onPointerMoveCapture","onPointerUp","onPointerUpCapture","onPointerCancel","onPointerCancelCapture","onPointerEnter","onPointerEnterCapture","onPointerLeave","onPointerLeaveCapture","onPointerOver","onPointerOverCapture","onPointerOut","onPointerOutCapture","onGotPointerCapture","onGotPointerCaptureCapture","onLostPointerCapture","onLostPointerCaptureCapture","onScroll","onScrollCapture","onWheel","onWheelCapture","onAnimationStart","onAnimationStartCapture","onAnimationEnd","onAnimationEndCapture","onAnimationIteration","onAnimationIterationCapture","onTransitionEnd","onTransitionEndCapture"],Gn=function(e,n){if(!e||typeof e=="function"||typeof e=="boolean")return null;var r=e;if(N.isValidElement(e)&&(r=e.props),!$r(r))return null;var i={};return Object.keys(r).forEach(function(a){Ro.includes(a)&&(i[a]=n||function(o){return r[a](r,o)})}),i},Gp=function(e,n,r){return function(i){return e(n,r,i),null}},We=function(e,n,r){if(!$r(e)||xa(e)!=="object")return null;var i=null;return Object.keys(e).forEach(function(a){var o=e[a];Ro.includes(a)&&typeof o=="function"&&(i||(i={}),i[a]=Gp(o,n,r))}),i},Hp=["children"],Up=["children"];function lc(t,e){if(t==null)return{};var n=Yp(t,e),r,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(t);for(i=0;i<a.length;i++)r=a[i],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(n[r]=t[r])}return n}function Yp(t,e){if(t==null)return{};var n={};for(var r in t)if(Object.prototype.hasOwnProperty.call(t,r)){if(e.indexOf(r)>=0)continue;n[r]=t[r]}return n}function wa(t){"@babel/helpers - typeof";return wa=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},wa(t)}var uc={click:"onClick",mousedown:"onMouseDown",mouseup:"onMouseUp",mouseover:"onMouseOver",mousemove:"onMouseMove",mouseout:"onMouseOut",mouseenter:"onMouseEnter",mouseleave:"onMouseLeave",touchcancel:"onTouchCancel",touchend:"onTouchEnd",touchmove:"onTouchMove",touchstart:"onTouchStart",contextmenu:"onContextMenu",dblclick:"onDoubleClick"},Qe=function(e){return typeof e=="string"?e:e?e.displayName||e.name||"Component":""},sc=null,oa=null,Bi=function t(e){if(e===sc&&Array.isArray(oa))return oa;var n=[];return N.Children.forEach(e,function(r){G(r)||(Bp.isFragment(r)?n=n.concat(t(r.props.children)):n.push(r))}),oa=n,sc=e,n};function ge(t,e){var n=[],r=[];return Array.isArray(e)?r=e.map(function(i){return Qe(i)}):r=[Qe(e)],Bi(t).forEach(function(i){var a=he(i,"type.displayName")||he(i,"type.name");r.indexOf(a)!==-1&&n.push(i)}),n}function oe(t,e){var n=ge(t,e);return n&&n[0]}var Hn=function(e){if(!e||!e.props)return!1;var n=e.props,r=n.width,i=n.height;return!(!R(r)||r<=0||!R(i)||i<=0)},qp=["a","altGlyph","altGlyphDef","altGlyphItem","animate","animateColor","animateMotion","animateTransform","circle","clipPath","color-profile","cursor","defs","desc","ellipse","feBlend","feColormatrix","feComponentTransfer","feComposite","feConvolveMatrix","feDiffuseLighting","feDisplacementMap","feDistantLight","feFlood","feFuncA","feFuncB","feFuncG","feFuncR","feGaussianBlur","feImage","feMerge","feMergeNode","feMorphology","feOffset","fePointLight","feSpecularLighting","feSpotLight","feTile","feTurbulence","filter","font","font-face","font-face-format","font-face-name","font-face-url","foreignObject","g","glyph","glyphRef","hkern","image","line","lineGradient","marker","mask","metadata","missing-glyph","mpath","path","pattern","polygon","polyline","radialGradient","rect","script","set","stop","style","svg","switch","symbol","text","textPath","title","tref","tspan","use","view","vkern"],Fu=function(e){return e&&e.type&&pt(e.type)&&qp.indexOf(e.type)>=0},Vu=function(e){return e&&wa(e)==="object"&&"clipDot"in e},Zp=function(e,n,r,i){var a,o=(a=aa?.[i])!==null&&a!==void 0?a:[];return n.startsWith("data-")||!K(e)&&(i&&o.includes(n)||Xp.includes(n))||r&&Ro.includes(n)},Xu=function(e){var n=[];return Bi(e).forEach(function(r){Fu(r)&&n.push(r)}),n},L=function(e,n,r){if(!e||typeof e=="function"||typeof e=="boolean")return null;var i=e;if(N.isValidElement(e)&&(i=e.props),!$r(i))return null;var a={};return Object.keys(i).forEach(function(o){var c;Zp((c=i)===null||c===void 0?void 0:c[o],o,n,r)&&(a[o]=i[o])}),a},Aa=function t(e,n){if(e===n)return!0;var r=N.Children.count(e);if(r!==N.Children.count(n))return!1;if(r===0)return!0;if(r===1)return fc(Array.isArray(e)?e[0]:e,Array.isArray(n)?n[0]:n);for(var i=0;i<r;i++){var a=e[i],o=n[i];if(Array.isArray(a)||Array.isArray(o)){if(!t(a,o))return!1}else if(!fc(a,o))return!1}return!0},fc=function(e,n){if(G(e)&&G(n))return!0;if(!G(e)&&!G(n)){var r=e.props||{},i=r.children,a=lc(r,Hp),o=n.props||{},c=o.children,l=lc(o,Up);return i&&c?Tt(a,l)&&Aa(i,c):!i&&!c?Tt(a,l):!1}return!1},pc=function(e,n){var r=[],i={};return Bi(e).forEach(function(a,o){if(Fu(a))r.push(a);else if(a){var c=Qe(a.type),l=n[c]||{},u=l.handler,s=l.once;if(u&&(!s||!i[c])){var f=u(a,c,o);r.push(f),i[c]=!0}}}),r},Jp=function(e){var n=e&&e.type;return n&&uc[n]?uc[n]:null},Qp=function(e,n){return Bi(n).indexOf(e)},ed=["children","width","height","viewBox","className","style","title","desc"];function Pa(){return Pa=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var n=arguments[e];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(t[r]=n[r])}return t},Pa.apply(this,arguments)}function td(t,e){if(t==null)return{};var n=rd(t,e),r,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(t);for(i=0;i<a.length;i++)r=a[i],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(n[r]=t[r])}return n}function rd(t,e){if(t==null)return{};var n={};for(var r in t)if(Object.prototype.hasOwnProperty.call(t,r)){if(e.indexOf(r)>=0)continue;n[r]=t[r]}return n}function Zt(t){var e=t.children,n=t.width,r=t.height,i=t.viewBox,a=t.className,o=t.style,c=t.title,l=t.desc,u=td(t,ed),s=i||{width:n,height:r,x:0,y:0},f=X("recharts-surface",a);return b.createElement("svg",Pa({},L(u,!0,"svg"),{className:f,width:n,height:r,style:o,viewBox:"".concat(s.x," ").concat(s.y," ").concat(s.width," ").concat(s.height)}),b.createElement("title",null,c),b.createElement("desc",null,l),e)}var nd=["children","className"];function Sa(){return Sa=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var n=arguments[e];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(t[r]=n[r])}return t},Sa.apply(this,arguments)}function id(t,e){if(t==null)return{};var n=ad(t,e),r,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(t);for(i=0;i<a.length;i++)r=a[i],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(n[r]=t[r])}return n}function ad(t,e){if(t==null)return{};var n={};for(var r in t)if(Object.prototype.hasOwnProperty.call(t,r)){if(e.indexOf(r)>=0)continue;n[r]=t[r]}return n}var F=b.forwardRef(function(t,e){var n=t.children,r=t.className,i=id(t,nd),a=X("recharts-layer",r);return b.createElement("g",Sa({className:a},L(i,!0),{ref:e}),n)}),Ne=function(e,n){for(var r=arguments.length,i=new Array(r>2?r-2:0),a=2;a<r;a++)i[a-2]=arguments[a]};function Gr(t){"@babel/helpers - typeof";return Gr=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Gr(t)}var od=["type","size","sizeType"];function ja(){return ja=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var n=arguments[e];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(t[r]=n[r])}return t},ja.apply(this,arguments)}function dc(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter(function(i){return Object.getOwnPropertyDescriptor(t,i).enumerable})),n.push.apply(n,r)}return n}function vc(t){for(var e=1;e<arguments.length;e++){var n=arguments[e]!=null?arguments[e]:{};e%2?dc(Object(n),!0).forEach(function(r){cd(t,r,n[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):dc(Object(n)).forEach(function(r){Object.defineProperty(t,r,Object.getOwnPropertyDescriptor(n,r))})}return t}function cd(t,e,n){return e=ld(e),e in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function ld(t){var e=ud(t,"string");return Gr(e)=="symbol"?e:e+""}function ud(t,e){if(Gr(t)!="object"||!t)return t;var n=t[Symbol.toPrimitive];if(n!==void 0){var r=n.call(t,e);if(Gr(r)!="object")return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(t)}function sd(t,e){if(t==null)return{};var n=fd(t,e),r,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(t);for(i=0;i<a.length;i++)r=a[i],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(n[r]=t[r])}return n}function fd(t,e){if(t==null)return{};var n={};for(var r in t)if(Object.prototype.hasOwnProperty.call(t,r)){if(e.indexOf(r)>=0)continue;n[r]=t[r]}return n}var Gu={symbolCircle:Nu,symbolCross:cp,symbolDiamond:op,symbolSquare:ap,symbolStar:ip,symbolTriangle:np,symbolWye:rp},pd=Math.PI/180,dd=function(e){var n="symbol".concat(Ri(e));return Gu[n]||Nu},vd=function(e,n,r){if(n==="area")return e;switch(r){case"cross":return 5*e*e/9;case"diamond":return .5*e*e/Math.sqrt(3);case"square":return e*e;case"star":{var i=18*pd;return 1.25*e*e*(Math.tan(i)-Math.tan(i*2)*Math.pow(Math.tan(i),2))}case"triangle":return Math.sqrt(3)*e*e/4;case"wye":return(21-10*Math.sqrt(3))*e*e/8;default:return Math.PI*e*e/4}},hd=function(e,n){Gu["symbol".concat(Ri(e))]=n},Li=function(e){var n=e.type,r=n===void 0?"circle":n,i=e.size,a=i===void 0?64:i,o=e.sizeType,c=o===void 0?"area":o,l=sd(e,od),u=vc(vc({},l),{},{type:r,size:a,sizeType:c}),s=function(){var m=dd(r),O=tp().type(m).size(vd(a,c,r));return O()},f=u.className,p=u.cx,d=u.cy,v=L(u,!0);return p===+p&&d===+d&&a===+a?b.createElement("path",ja({},v,{className:X("recharts-symbols",f),transform:"translate(".concat(p,", ").concat(d,")"),d:s()})):null};Li.registerSymbol=hd;function Jt(t){"@babel/helpers - typeof";return Jt=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Jt(t)}function Ea(){return Ea=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var n=arguments[e];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(t[r]=n[r])}return t},Ea.apply(this,arguments)}function hc(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter(function(i){return Object.getOwnPropertyDescriptor(t,i).enumerable})),n.push.apply(n,r)}return n}function yd(t){for(var e=1;e<arguments.length;e++){var n=arguments[e]!=null?arguments[e]:{};e%2?hc(Object(n),!0).forEach(function(r){Hr(t,r,n[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):hc(Object(n)).forEach(function(r){Object.defineProperty(t,r,Object.getOwnPropertyDescriptor(n,r))})}return t}function md(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function gd(t,e){for(var n=0;n<e.length;n++){var r=e[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,Uu(r.key),r)}}function bd(t,e,n){return e&&gd(t.prototype,e),Object.defineProperty(t,"prototype",{writable:!1}),t}function Od(t,e,n){return e=Un(e),xd(t,Hu()?Reflect.construct(e,n||[],Un(t).constructor):e.apply(t,n))}function xd(t,e){if(e&&(Jt(e)==="object"||typeof e=="function"))return e;if(e!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return wd(t)}function wd(t){if(t===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}function Hu(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(Hu=function(){return!!t})()}function Un(t){return Un=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(n){return n.__proto__||Object.getPrototypeOf(n)},Un(t)}function Ad(t,e){if(typeof e!="function"&&e!==null)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&$a(t,e)}function $a(t,e){return $a=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(r,i){return r.__proto__=i,r},$a(t,e)}function Hr(t,e,n){return e=Uu(e),e in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function Uu(t){var e=Pd(t,"string");return Jt(e)=="symbol"?e:e+""}function Pd(t,e){if(Jt(t)!="object"||!t)return t;var n=t[Symbol.toPrimitive];if(n!==void 0){var r=n.call(t,e);if(Jt(r)!="object")return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}var De=32,Bo=function(t){function e(){return md(this,e),Od(this,e,arguments)}return Ad(e,t),bd(e,[{key:"renderIcon",value:function(r){var i=this.props.inactiveColor,a=De/2,o=De/6,c=De/3,l=r.inactive?i:r.color;if(r.type==="plainline")return b.createElement("line",{strokeWidth:4,fill:"none",stroke:l,strokeDasharray:r.payload.strokeDasharray,x1:0,y1:a,x2:De,y2:a,className:"recharts-legend-icon"});if(r.type==="line")return b.createElement("path",{strokeWidth:4,fill:"none",stroke:l,d:"M0,".concat(a,"h").concat(c,`
            A`).concat(o,",").concat(o,",0,1,1,").concat(2*c,",").concat(a,`
            H`).concat(De,"M").concat(2*c,",").concat(a,`
            A`).concat(o,",").concat(o,",0,1,1,").concat(c,",").concat(a),className:"recharts-legend-icon"});if(r.type==="rect")return b.createElement("path",{stroke:"none",fill:l,d:"M0,".concat(De/8,"h").concat(De,"v").concat(De*3/4,"h").concat(-De,"z"),className:"recharts-legend-icon"});if(b.isValidElement(r.legendIcon)){var u=yd({},r);return delete u.legendIcon,b.cloneElement(r.legendIcon,u)}return b.createElement(Li,{fill:l,cx:a,cy:a,size:De,sizeType:"diameter",type:r.type})}},{key:"renderItems",value:function(){var r=this,i=this.props,a=i.payload,o=i.iconSize,c=i.layout,l=i.formatter,u=i.inactiveColor,s={x:0,y:0,width:De,height:De},f={display:c==="horizontal"?"inline-block":"block",marginRight:10},p={display:"inline-block",verticalAlign:"middle",marginRight:4};return a.map(function(d,v){var h=d.formatter||l,m=X(Hr(Hr({"recharts-legend-item":!0},"legend-item-".concat(v),!0),"inactive",d.inactive));if(d.type==="none")return null;var O=K(d.value)?null:d.value;Ne(!K(d.value),`The name property is also required when using a function for the dataKey of a chart's cartesian components. Ex: <Bar name="Name of my Data"/>`);var w=d.inactive?u:d.color;return b.createElement("li",Ea({className:m,style:f,key:"legend-item-".concat(v)},We(r.props,d,v)),b.createElement(Zt,{width:o,height:o,viewBox:s,style:p},r.renderIcon(d)),b.createElement("span",{className:"recharts-legend-item-text",style:{color:w}},h?h(O,d,v):O))})}},{key:"render",value:function(){var r=this.props,i=r.payload,a=r.layout,o=r.align;if(!i||!i.length)return null;var c={padding:0,margin:0,textAlign:a==="horizontal"?o:"left"};return b.createElement("ul",{className:"recharts-default-legend",style:c},this.renderItems())}}])}(N.PureComponent);Hr(Bo,"displayName","Legend");Hr(Bo,"defaultProps",{iconSize:14,layout:"horizontal",align:"center",verticalAlign:"middle",inactiveColor:"#ccc"});function Yu(t,e,n){return e===!0?ic(t,n):K(e)?ic(t,e):t}function Qt(t){"@babel/helpers - typeof";return Qt=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Qt(t)}var Sd=["ref"];function yc(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter(function(i){return Object.getOwnPropertyDescriptor(t,i).enumerable})),n.push.apply(n,r)}return n}function Ye(t){for(var e=1;e<arguments.length;e++){var n=arguments[e]!=null?arguments[e]:{};e%2?yc(Object(n),!0).forEach(function(r){Wi(t,r,n[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):yc(Object(n)).forEach(function(r){Object.defineProperty(t,r,Object.getOwnPropertyDescriptor(n,r))})}return t}function jd(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function mc(t,e){for(var n=0;n<e.length;n++){var r=e[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,Zu(r.key),r)}}function Ed(t,e,n){return e&&mc(t.prototype,e),n&&mc(t,n),Object.defineProperty(t,"prototype",{writable:!1}),t}function $d(t,e,n){return e=Yn(e),_d(t,qu()?Reflect.construct(e,n||[],Yn(t).constructor):e.apply(t,n))}function _d(t,e){if(e&&(Qt(e)==="object"||typeof e=="function"))return e;if(e!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return Td(t)}function Td(t){if(t===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}function qu(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(qu=function(){return!!t})()}function Yn(t){return Yn=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(n){return n.__proto__||Object.getPrototypeOf(n)},Yn(t)}function Id(t,e){if(typeof e!="function"&&e!==null)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&_a(t,e)}function _a(t,e){return _a=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(r,i){return r.__proto__=i,r},_a(t,e)}function Wi(t,e,n){return e=Zu(e),e in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function Zu(t){var e=kd(t,"string");return Qt(e)=="symbol"?e:e+""}function kd(t,e){if(Qt(t)!="object"||!t)return t;var n=t[Symbol.toPrimitive];if(n!==void 0){var r=n.call(t,e);if(Qt(r)!="object")return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}function Dd(t,e){if(t==null)return{};var n=Cd(t,e),r,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(t);for(i=0;i<a.length;i++)r=a[i],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(n[r]=t[r])}return n}function Cd(t,e){if(t==null)return{};var n={};for(var r in t)if(Object.prototype.hasOwnProperty.call(t,r)){if(e.indexOf(r)>=0)continue;n[r]=t[r]}return n}function Md(t){return t.value}function Nd(t,e){if(b.isValidElement(t))return b.cloneElement(t,e);if(typeof t=="function")return b.createElement(t,e);e.ref;var n=Dd(e,Sd);return b.createElement(Bo,n)}var gc=1,Ut=function(t){function e(){var n;jd(this,e);for(var r=arguments.length,i=new Array(r),a=0;a<r;a++)i[a]=arguments[a];return n=$d(this,e,[].concat(i)),Wi(n,"lastBoundingBox",{width:-1,height:-1}),n}return Id(e,t),Ed(e,[{key:"componentDidMount",value:function(){this.updateBBox()}},{key:"componentDidUpdate",value:function(){this.updateBBox()}},{key:"getBBox",value:function(){if(this.wrapperNode&&this.wrapperNode.getBoundingClientRect){var r=this.wrapperNode.getBoundingClientRect();return r.height=this.wrapperNode.offsetHeight,r.width=this.wrapperNode.offsetWidth,r}return null}},{key:"updateBBox",value:function(){var r=this.props.onBBoxUpdate,i=this.getBBox();i?(Math.abs(i.width-this.lastBoundingBox.width)>gc||Math.abs(i.height-this.lastBoundingBox.height)>gc)&&(this.lastBoundingBox.width=i.width,this.lastBoundingBox.height=i.height,r&&r(i)):(this.lastBoundingBox.width!==-1||this.lastBoundingBox.height!==-1)&&(this.lastBoundingBox.width=-1,this.lastBoundingBox.height=-1,r&&r(null))}},{key:"getBBoxSnapshot",value:function(){return this.lastBoundingBox.width>=0&&this.lastBoundingBox.height>=0?Ye({},this.lastBoundingBox):{width:0,height:0}}},{key:"getDefaultPosition",value:function(r){var i=this.props,a=i.layout,o=i.align,c=i.verticalAlign,l=i.margin,u=i.chartWidth,s=i.chartHeight,f,p;if(!r||(r.left===void 0||r.left===null)&&(r.right===void 0||r.right===null))if(o==="center"&&a==="vertical"){var d=this.getBBoxSnapshot();f={left:((u||0)-d.width)/2}}else f=o==="right"?{right:l&&l.right||0}:{left:l&&l.left||0};if(!r||(r.top===void 0||r.top===null)&&(r.bottom===void 0||r.bottom===null))if(c==="middle"){var v=this.getBBoxSnapshot();p={top:((s||0)-v.height)/2}}else p=c==="bottom"?{bottom:l&&l.bottom||0}:{top:l&&l.top||0};return Ye(Ye({},f),p)}},{key:"render",value:function(){var r=this,i=this.props,a=i.content,o=i.width,c=i.height,l=i.wrapperStyle,u=i.payloadUniqBy,s=i.payload,f=Ye(Ye({position:"absolute",width:o||"auto",height:c||"auto"},this.getDefaultPosition(l)),l);return b.createElement("div",{className:"recharts-legend-wrapper",style:f,ref:function(d){r.wrapperNode=d}},Nd(a,Ye(Ye({},this.props),{},{payload:Yu(s,u,Md)})))}}],[{key:"getWithHeight",value:function(r,i){var a=Ye(Ye({},this.defaultProps),r.props),o=a.layout;return o==="vertical"&&R(r.props.height)?{height:r.props.height}:o==="horizontal"?{width:r.props.width||i}:null}}])}(N.PureComponent);Wi(Ut,"displayName","Legend");Wi(Ut,"defaultProps",{iconSize:14,layout:"horizontal",align:"center",verticalAlign:"bottom"});function Ur(t){"@babel/helpers - typeof";return Ur=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Ur(t)}function Ta(){return Ta=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var n=arguments[e];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(t[r]=n[r])}return t},Ta.apply(this,arguments)}function Rd(t,e){return zd(t)||Wd(t,e)||Ld(t,e)||Bd()}function Bd(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function Ld(t,e){if(t){if(typeof t=="string")return bc(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);if(n==="Object"&&t.constructor&&(n=t.constructor.name),n==="Map"||n==="Set")return Array.from(t);if(n==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return bc(t,e)}}function bc(t,e){(e==null||e>t.length)&&(e=t.length);for(var n=0,r=new Array(e);n<e;n++)r[n]=t[n];return r}function Wd(t,e){var n=t==null?null:typeof Symbol<"u"&&t[Symbol.iterator]||t["@@iterator"];if(n!=null){var r,i,a,o,c=[],l=!0,u=!1;try{if(a=(n=n.call(t)).next,e!==0)for(;!(l=(r=a.call(n)).done)&&(c.push(r.value),c.length!==e);l=!0);}catch(s){u=!0,i=s}finally{try{if(!l&&n.return!=null&&(o=n.return(),Object(o)!==o))return}finally{if(u)throw i}}return c}}function zd(t){if(Array.isArray(t))return t}function Oc(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter(function(i){return Object.getOwnPropertyDescriptor(t,i).enumerable})),n.push.apply(n,r)}return n}function ca(t){for(var e=1;e<arguments.length;e++){var n=arguments[e]!=null?arguments[e]:{};e%2?Oc(Object(n),!0).forEach(function(r){Kd(t,r,n[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):Oc(Object(n)).forEach(function(r){Object.defineProperty(t,r,Object.getOwnPropertyDescriptor(n,r))})}return t}function Kd(t,e,n){return e=Fd(e),e in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function Fd(t){var e=Vd(t,"string");return Ur(e)=="symbol"?e:e+""}function Vd(t,e){if(Ur(t)!="object"||!t)return t;var n=t[Symbol.toPrimitive];if(n!==void 0){var r=n.call(t,e);if(Ur(r)!="object")return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(t)}function Xd(t){return Array.isArray(t)&&pe(t[0])&&pe(t[1])?t.join(" ~ "):t}var Gd=function(e){var n=e.separator,r=n===void 0?" : ":n,i=e.contentStyle,a=i===void 0?{}:i,o=e.itemStyle,c=o===void 0?{}:o,l=e.labelStyle,u=l===void 0?{}:l,s=e.payload,f=e.formatter,p=e.itemSorter,d=e.wrapperClassName,v=e.labelClassName,h=e.label,m=e.labelFormatter,O=e.accessibilityLayer,w=O===void 0?!1:O,x=function(){if(s&&s.length){var _={padding:0,margin:0},k=(p?No(s,p):s).map(function(D,T){if(D.type==="none")return null;var C=ca({display:"block",paddingTop:4,paddingBottom:4,color:D.color||"#000"},c),M=D.formatter||f||Xd,B=D.value,W=D.name,V=B,H=W;if(M&&V!=null&&H!=null){var z=M(B,W,D,T,s);if(Array.isArray(z)){var Y=Rd(z,2);V=Y[0],H=Y[1]}else V=z}return b.createElement("li",{className:"recharts-tooltip-item",key:"tooltip-item-".concat(T),style:C},pe(H)?b.createElement("span",{className:"recharts-tooltip-item-name"},H):null,pe(H)?b.createElement("span",{className:"recharts-tooltip-item-separator"},r):null,b.createElement("span",{className:"recharts-tooltip-item-value"},V),b.createElement("span",{className:"recharts-tooltip-item-unit"},D.unit||""))});return b.createElement("ul",{className:"recharts-tooltip-item-list",style:_},k)}return null},A=ca({margin:0,padding:10,backgroundColor:"#fff",border:"1px solid #ccc",whiteSpace:"nowrap"},a),y=ca({margin:0},u),g=!G(h),P=g?h:"",S=X("recharts-default-tooltip",d),j=X("recharts-tooltip-label",v);g&&m&&s!==void 0&&s!==null&&(P=m(h,s));var E=w?{role:"status","aria-live":"assertive"}:{};return b.createElement("div",Ta({className:S,style:A},E),b.createElement("p",{className:j,style:y},b.isValidElement(P)?P:"".concat(P)),x())};function Yr(t){"@babel/helpers - typeof";return Yr=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Yr(t)}function Cn(t,e,n){return e=Hd(e),e in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function Hd(t){var e=Ud(t,"string");return Yr(e)=="symbol"?e:e+""}function Ud(t,e){if(Yr(t)!="object"||!t)return t;var n=t[Symbol.toPrimitive];if(n!==void 0){var r=n.call(t,e);if(Yr(r)!="object")return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(t)}var Nr="recharts-tooltip-wrapper",Yd={visibility:"hidden"};function qd(t){var e=t.coordinate,n=t.translateX,r=t.translateY;return X(Nr,Cn(Cn(Cn(Cn({},"".concat(Nr,"-right"),R(n)&&e&&R(e.x)&&n>=e.x),"".concat(Nr,"-left"),R(n)&&e&&R(e.x)&&n<e.x),"".concat(Nr,"-bottom"),R(r)&&e&&R(e.y)&&r>=e.y),"".concat(Nr,"-top"),R(r)&&e&&R(e.y)&&r<e.y))}function xc(t){var e=t.allowEscapeViewBox,n=t.coordinate,r=t.key,i=t.offsetTopLeft,a=t.position,o=t.reverseDirection,c=t.tooltipDimension,l=t.viewBox,u=t.viewBoxDimension;if(a&&R(a[r]))return a[r];var s=n[r]-c-i,f=n[r]+i;if(e[r])return o[r]?s:f;if(o[r]){var p=s,d=l[r];return p<d?Math.max(f,l[r]):Math.max(s,l[r])}var v=f+c,h=l[r]+u;return v>h?Math.max(s,l[r]):Math.max(f,l[r])}function Zd(t){var e=t.translateX,n=t.translateY,r=t.useTranslate3d;return{transform:r?"translate3d(".concat(e,"px, ").concat(n,"px, 0)"):"translate(".concat(e,"px, ").concat(n,"px)")}}function Jd(t){var e=t.allowEscapeViewBox,n=t.coordinate,r=t.offsetTopLeft,i=t.position,a=t.reverseDirection,o=t.tooltipBox,c=t.useTranslate3d,l=t.viewBox,u,s,f;return o.height>0&&o.width>0&&n?(s=xc({allowEscapeViewBox:e,coordinate:n,key:"x",offsetTopLeft:r,position:i,reverseDirection:a,tooltipDimension:o.width,viewBox:l,viewBoxDimension:l.width}),f=xc({allowEscapeViewBox:e,coordinate:n,key:"y",offsetTopLeft:r,position:i,reverseDirection:a,tooltipDimension:o.height,viewBox:l,viewBoxDimension:l.height}),u=Zd({translateX:s,translateY:f,useTranslate3d:c})):u=Yd,{cssProperties:u,cssClasses:qd({translateX:s,translateY:f,coordinate:n})}}function er(t){"@babel/helpers - typeof";return er=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},er(t)}function wc(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter(function(i){return Object.getOwnPropertyDescriptor(t,i).enumerable})),n.push.apply(n,r)}return n}function Ac(t){for(var e=1;e<arguments.length;e++){var n=arguments[e]!=null?arguments[e]:{};e%2?wc(Object(n),!0).forEach(function(r){ka(t,r,n[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):wc(Object(n)).forEach(function(r){Object.defineProperty(t,r,Object.getOwnPropertyDescriptor(n,r))})}return t}function Qd(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function ev(t,e){for(var n=0;n<e.length;n++){var r=e[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,Qu(r.key),r)}}function tv(t,e,n){return e&&ev(t.prototype,e),Object.defineProperty(t,"prototype",{writable:!1}),t}function rv(t,e,n){return e=qn(e),nv(t,Ju()?Reflect.construct(e,n||[],qn(t).constructor):e.apply(t,n))}function nv(t,e){if(e&&(er(e)==="object"||typeof e=="function"))return e;if(e!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return iv(t)}function iv(t){if(t===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}function Ju(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(Ju=function(){return!!t})()}function qn(t){return qn=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(n){return n.__proto__||Object.getPrototypeOf(n)},qn(t)}function av(t,e){if(typeof e!="function"&&e!==null)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&Ia(t,e)}function Ia(t,e){return Ia=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(r,i){return r.__proto__=i,r},Ia(t,e)}function ka(t,e,n){return e=Qu(e),e in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function Qu(t){var e=ov(t,"string");return er(e)=="symbol"?e:e+""}function ov(t,e){if(er(t)!="object"||!t)return t;var n=t[Symbol.toPrimitive];if(n!==void 0){var r=n.call(t,e);if(er(r)!="object")return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}var Pc=1,cv=function(t){function e(){var n;Qd(this,e);for(var r=arguments.length,i=new Array(r),a=0;a<r;a++)i[a]=arguments[a];return n=rv(this,e,[].concat(i)),ka(n,"state",{dismissed:!1,dismissedAtCoordinate:{x:0,y:0},lastBoundingBox:{width:-1,height:-1}}),ka(n,"handleKeyDown",function(o){if(o.key==="Escape"){var c,l,u,s;n.setState({dismissed:!0,dismissedAtCoordinate:{x:(c=(l=n.props.coordinate)===null||l===void 0?void 0:l.x)!==null&&c!==void 0?c:0,y:(u=(s=n.props.coordinate)===null||s===void 0?void 0:s.y)!==null&&u!==void 0?u:0}})}}),n}return av(e,t),tv(e,[{key:"updateBBox",value:function(){if(this.wrapperNode&&this.wrapperNode.getBoundingClientRect){var r=this.wrapperNode.getBoundingClientRect();(Math.abs(r.width-this.state.lastBoundingBox.width)>Pc||Math.abs(r.height-this.state.lastBoundingBox.height)>Pc)&&this.setState({lastBoundingBox:{width:r.width,height:r.height}})}else(this.state.lastBoundingBox.width!==-1||this.state.lastBoundingBox.height!==-1)&&this.setState({lastBoundingBox:{width:-1,height:-1}})}},{key:"componentDidMount",value:function(){document.addEventListener("keydown",this.handleKeyDown),this.updateBBox()}},{key:"componentWillUnmount",value:function(){document.removeEventListener("keydown",this.handleKeyDown)}},{key:"componentDidUpdate",value:function(){var r,i;this.props.active&&this.updateBBox(),this.state.dismissed&&(((r=this.props.coordinate)===null||r===void 0?void 0:r.x)!==this.state.dismissedAtCoordinate.x||((i=this.props.coordinate)===null||i===void 0?void 0:i.y)!==this.state.dismissedAtCoordinate.y)&&(this.state.dismissed=!1)}},{key:"render",value:function(){var r=this,i=this.props,a=i.active,o=i.allowEscapeViewBox,c=i.animationDuration,l=i.animationEasing,u=i.children,s=i.coordinate,f=i.hasPayload,p=i.isAnimationActive,d=i.offset,v=i.position,h=i.reverseDirection,m=i.useTranslate3d,O=i.viewBox,w=i.wrapperStyle,x=Jd({allowEscapeViewBox:o,coordinate:s,offsetTopLeft:d,position:v,reverseDirection:h,tooltipBox:this.state.lastBoundingBox,useTranslate3d:m,viewBox:O}),A=x.cssClasses,y=x.cssProperties,g=Ac(Ac({transition:p&&a?"transform ".concat(c,"ms ").concat(l):void 0},y),{},{pointerEvents:"none",visibility:!this.state.dismissed&&a&&f?"visible":"hidden",position:"absolute",top:0,left:0},w);return b.createElement("div",{tabIndex:-1,className:A,style:g,ref:function(S){r.wrapperNode=S}},u)}}])}(N.PureComponent),lv=function(){return!(typeof window<"u"&&window.document&&window.document.createElement&&window.setTimeout)},be={isSsr:lv(),get:function(e){return be[e]},set:function(e,n){if(typeof e=="string")be[e]=n;else{var r=Object.keys(e);r&&r.length&&r.forEach(function(i){be[i]=e[i]})}}};function tr(t){"@babel/helpers - typeof";return tr=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},tr(t)}function Sc(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter(function(i){return Object.getOwnPropertyDescriptor(t,i).enumerable})),n.push.apply(n,r)}return n}function jc(t){for(var e=1;e<arguments.length;e++){var n=arguments[e]!=null?arguments[e]:{};e%2?Sc(Object(n),!0).forEach(function(r){Lo(t,r,n[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):Sc(Object(n)).forEach(function(r){Object.defineProperty(t,r,Object.getOwnPropertyDescriptor(n,r))})}return t}function uv(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function sv(t,e){for(var n=0;n<e.length;n++){var r=e[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,ts(r.key),r)}}function fv(t,e,n){return e&&sv(t.prototype,e),Object.defineProperty(t,"prototype",{writable:!1}),t}function pv(t,e,n){return e=Zn(e),dv(t,es()?Reflect.construct(e,n||[],Zn(t).constructor):e.apply(t,n))}function dv(t,e){if(e&&(tr(e)==="object"||typeof e=="function"))return e;if(e!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return vv(t)}function vv(t){if(t===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}function es(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(es=function(){return!!t})()}function Zn(t){return Zn=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(n){return n.__proto__||Object.getPrototypeOf(n)},Zn(t)}function hv(t,e){if(typeof e!="function"&&e!==null)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&Da(t,e)}function Da(t,e){return Da=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(r,i){return r.__proto__=i,r},Da(t,e)}function Lo(t,e,n){return e=ts(e),e in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function ts(t){var e=yv(t,"string");return tr(e)=="symbol"?e:e+""}function yv(t,e){if(tr(t)!="object"||!t)return t;var n=t[Symbol.toPrimitive];if(n!==void 0){var r=n.call(t,e);if(tr(r)!="object")return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}function mv(t){return t.dataKey}function gv(t,e){return b.isValidElement(t)?b.cloneElement(t,e):typeof t=="function"?b.createElement(t,e):b.createElement(Gd,e)}var me=function(t){function e(){return uv(this,e),pv(this,e,arguments)}return hv(e,t),fv(e,[{key:"render",value:function(){var r=this,i=this.props,a=i.active,o=i.allowEscapeViewBox,c=i.animationDuration,l=i.animationEasing,u=i.content,s=i.coordinate,f=i.filterNull,p=i.isAnimationActive,d=i.offset,v=i.payload,h=i.payloadUniqBy,m=i.position,O=i.reverseDirection,w=i.useTranslate3d,x=i.viewBox,A=i.wrapperStyle,y=v??[];f&&y.length&&(y=Yu(v.filter(function(P){return P.value!=null&&(P.hide!==!0||r.props.includeHidden)}),h,mv));var g=y.length>0;return b.createElement(cv,{allowEscapeViewBox:o,animationDuration:c,animationEasing:l,isAnimationActive:p,active:a,coordinate:s,hasPayload:g,offset:d,position:m,reverseDirection:O,useTranslate3d:w,viewBox:x,wrapperStyle:A},gv(u,jc(jc({},this.props),{},{payload:y})))}}])}(N.PureComponent);Lo(me,"displayName","Tooltip");Lo(me,"defaultProps",{accessibilityLayer:!1,allowEscapeViewBox:{x:!1,y:!1},animationDuration:400,animationEasing:"ease",contentStyle:{},coordinate:{x:0,y:0},cursor:!0,cursorStyle:{},filterNull:!0,isAnimationActive:!be.isSsr,itemStyle:{},labelStyle:{},offset:10,reverseDirection:{x:!1,y:!1},separator:" : ",trigger:"hover",useTranslate3d:!1,viewBox:{x:0,y:0,height:0,width:0},wrapperStyle:{}});function qr(t){"@babel/helpers - typeof";return qr=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},qr(t)}function Ec(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter(function(i){return Object.getOwnPropertyDescriptor(t,i).enumerable})),n.push.apply(n,r)}return n}function Mn(t){for(var e=1;e<arguments.length;e++){var n=arguments[e]!=null?arguments[e]:{};e%2?Ec(Object(n),!0).forEach(function(r){bv(t,r,n[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):Ec(Object(n)).forEach(function(r){Object.defineProperty(t,r,Object.getOwnPropertyDescriptor(n,r))})}return t}function bv(t,e,n){return e=Ov(e),e in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function Ov(t){var e=xv(t,"string");return qr(e)=="symbol"?e:e+""}function xv(t,e){if(qr(t)!="object"||!t)return t;var n=t[Symbol.toPrimitive];if(n!==void 0){var r=n.call(t,e);if(qr(r)!="object")return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(t)}function wv(t,e){return jv(t)||Sv(t,e)||Pv(t,e)||Av()}function Av(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function Pv(t,e){if(t){if(typeof t=="string")return $c(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);if(n==="Object"&&t.constructor&&(n=t.constructor.name),n==="Map"||n==="Set")return Array.from(t);if(n==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return $c(t,e)}}function $c(t,e){(e==null||e>t.length)&&(e=t.length);for(var n=0,r=new Array(e);n<e;n++)r[n]=t[n];return r}function Sv(t,e){var n=t==null?null:typeof Symbol<"u"&&t[Symbol.iterator]||t["@@iterator"];if(n!=null){var r,i,a,o,c=[],l=!0,u=!1;try{if(a=(n=n.call(t)).next,e!==0)for(;!(l=(r=a.call(n)).done)&&(c.push(r.value),c.length!==e);l=!0);}catch(s){u=!0,i=s}finally{try{if(!l&&n.return!=null&&(o=n.return(),Object(o)!==o))return}finally{if(u)throw i}}return c}}function jv(t){if(Array.isArray(t))return t}var WP=N.forwardRef(function(t,e){var n=t.aspect,r=t.initialDimension,i=r===void 0?{width:-1,height:-1}:r,a=t.width,o=a===void 0?"100%":a,c=t.height,l=c===void 0?"100%":c,u=t.minWidth,s=u===void 0?0:u,f=t.minHeight,p=t.maxHeight,d=t.children,v=t.debounce,h=v===void 0?0:v,m=t.id,O=t.className,w=t.onResize,x=t.style,A=x===void 0?{}:x,y=N.useRef(null),g=N.useRef();g.current=w,N.useImperativeHandle(e,function(){return Object.defineProperty(y.current,"current",{get:function(){return console.warn("The usage of ref.current.current is deprecated and will no longer be supported."),y.current},configurable:!0})});var P=N.useState({containerWidth:i.width,containerHeight:i.height}),S=wv(P,2),j=S[0],E=S[1],$=N.useCallback(function(k,D){E(function(T){var C=Math.round(k),M=Math.round(D);return T.containerWidth===C&&T.containerHeight===M?T:{containerWidth:C,containerHeight:M}})},[]);N.useEffect(function(){var k=function(W){var V,H=W[0].contentRect,z=H.width,Y=H.height;$(z,Y),(V=g.current)===null||V===void 0||V.call(g,z,Y)};h>0&&(k=Ru(k,h,{trailing:!0,leading:!1}));var D=new ResizeObserver(k),T=y.current.getBoundingClientRect(),C=T.width,M=T.height;return $(C,M),D.observe(y.current),function(){D.disconnect()}},[$,h]);var _=N.useMemo(function(){var k=j.containerWidth,D=j.containerHeight;if(k<0||D<0)return null;Ne(Et(o)||Et(l),`The width(%s) and height(%s) are both fixed numbers,
       maybe you don't need to use a ResponsiveContainer.`,o,l),Ne(!n||n>0,"The aspect(%s) must be greater than zero.",n);var T=Et(o)?k:o,C=Et(l)?D:l;n&&n>0&&(T?C=T/n:C&&(T=C*n),p&&C>p&&(C=p)),Ne(T>0||C>0,`The width(%s) and height(%s) of chart should be greater than 0,
       please check the style of container, or the props width(%s) and height(%s),
       or add a minWidth(%s) or minHeight(%s) or use aspect(%s) to control the
       height and width.`,T,C,o,l,s,f,n);var M=!Array.isArray(d)&&Qe(d.type).endsWith("Chart");return b.Children.map(d,function(B){return b.isValidElement(B)?N.cloneElement(B,Mn({width:T,height:C},M?{style:Mn({height:"100%",width:"100%",maxHeight:C,maxWidth:T},B.props.style)}:{})):B})},[n,d,l,p,f,s,j,o]);return b.createElement("div",{id:m?"".concat(m):void 0,className:X("recharts-responsive-container",O),style:Mn(Mn({},A),{},{width:o,height:l,minWidth:s,minHeight:f,maxHeight:p}),ref:y},_)}),_r=function(e){return null};_r.displayName="Cell";function Zr(t){"@babel/helpers - typeof";return Zr=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Zr(t)}function _c(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter(function(i){return Object.getOwnPropertyDescriptor(t,i).enumerable})),n.push.apply(n,r)}return n}function Ca(t){for(var e=1;e<arguments.length;e++){var n=arguments[e]!=null?arguments[e]:{};e%2?_c(Object(n),!0).forEach(function(r){Ev(t,r,n[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):_c(Object(n)).forEach(function(r){Object.defineProperty(t,r,Object.getOwnPropertyDescriptor(n,r))})}return t}function Ev(t,e,n){return e=$v(e),e in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function $v(t){var e=_v(t,"string");return Zr(e)=="symbol"?e:e+""}function _v(t,e){if(Zr(t)!="object"||!t)return t;var n=t[Symbol.toPrimitive];if(n!==void 0){var r=n.call(t,e);if(Zr(r)!="object")return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(t)}var Wt={widthCache:{},cacheCount:0},Tv=2e3,Iv={position:"absolute",top:"-20000px",left:0,padding:0,margin:0,border:"none",whiteSpace:"pre"},Tc="recharts_measurement_span";function kv(t){var e=Ca({},t);return Object.keys(e).forEach(function(n){e[n]||delete e[n]}),e}var Yt=function(e){var n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};if(e==null||be.isSsr)return{width:0,height:0};var r=kv(n),i=JSON.stringify({text:e,copyStyle:r});if(Wt.widthCache[i])return Wt.widthCache[i];try{var a=document.getElementById(Tc);a||(a=document.createElement("span"),a.setAttribute("id",Tc),a.setAttribute("aria-hidden","true"),document.body.appendChild(a));var o=Ca(Ca({},Iv),r);Object.assign(a.style,o),a.textContent="".concat(e);var c=a.getBoundingClientRect(),l={width:c.width,height:c.height};return Wt.widthCache[i]=l,++Wt.cacheCount>Tv&&(Wt.cacheCount=0,Wt.widthCache={}),l}catch{return{width:0,height:0}}},Dv=function(e){return{top:e.top+window.scrollY-document.documentElement.clientTop,left:e.left+window.scrollX-document.documentElement.clientLeft}};function Jr(t){"@babel/helpers - typeof";return Jr=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Jr(t)}function Jn(t,e){return Rv(t)||Nv(t,e)||Mv(t,e)||Cv()}function Cv(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function Mv(t,e){if(t){if(typeof t=="string")return Ic(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);if(n==="Object"&&t.constructor&&(n=t.constructor.name),n==="Map"||n==="Set")return Array.from(t);if(n==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return Ic(t,e)}}function Ic(t,e){(e==null||e>t.length)&&(e=t.length);for(var n=0,r=new Array(e);n<e;n++)r[n]=t[n];return r}function Nv(t,e){var n=t==null?null:typeof Symbol<"u"&&t[Symbol.iterator]||t["@@iterator"];if(n!=null){var r,i,a,o,c=[],l=!0,u=!1;try{if(a=(n=n.call(t)).next,e===0){if(Object(n)!==n)return;l=!1}else for(;!(l=(r=a.call(n)).done)&&(c.push(r.value),c.length!==e);l=!0);}catch(s){u=!0,i=s}finally{try{if(!l&&n.return!=null&&(o=n.return(),Object(o)!==o))return}finally{if(u)throw i}}return c}}function Rv(t){if(Array.isArray(t))return t}function Bv(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function kc(t,e){for(var n=0;n<e.length;n++){var r=e[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,Wv(r.key),r)}}function Lv(t,e,n){return e&&kc(t.prototype,e),n&&kc(t,n),Object.defineProperty(t,"prototype",{writable:!1}),t}function Wv(t){var e=zv(t,"string");return Jr(e)=="symbol"?e:e+""}function zv(t,e){if(Jr(t)!="object"||!t)return t;var n=t[Symbol.toPrimitive];if(n!==void 0){var r=n.call(t,e);if(Jr(r)!="object")return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}var Dc=/(-?\d+(?:\.\d+)?[a-zA-Z%]*)([*/])(-?\d+(?:\.\d+)?[a-zA-Z%]*)/,Cc=/(-?\d+(?:\.\d+)?[a-zA-Z%]*)([+-])(-?\d+(?:\.\d+)?[a-zA-Z%]*)/,Kv=/^px|cm|vh|vw|em|rem|%|mm|in|pt|pc|ex|ch|vmin|vmax|Q$/,Fv=/(-?\d+(?:\.\d+)?)([a-zA-Z%]+)?/,rs={cm:96/2.54,mm:96/25.4,pt:96/72,pc:96/6,in:96,Q:96/(2.54*40),px:1},Vv=Object.keys(rs),Ft="NaN";function Xv(t,e){return t*rs[e]}var Nn=function(){function t(e,n){Bv(this,t),this.num=e,this.unit=n,this.num=e,this.unit=n,Number.isNaN(e)&&(this.unit=""),n!==""&&!Kv.test(n)&&(this.num=NaN,this.unit=""),Vv.includes(n)&&(this.num=Xv(e,n),this.unit="px")}return Lv(t,[{key:"add",value:function(n){return this.unit!==n.unit?new t(NaN,""):new t(this.num+n.num,this.unit)}},{key:"subtract",value:function(n){return this.unit!==n.unit?new t(NaN,""):new t(this.num-n.num,this.unit)}},{key:"multiply",value:function(n){return this.unit!==""&&n.unit!==""&&this.unit!==n.unit?new t(NaN,""):new t(this.num*n.num,this.unit||n.unit)}},{key:"divide",value:function(n){return this.unit!==""&&n.unit!==""&&this.unit!==n.unit?new t(NaN,""):new t(this.num/n.num,this.unit||n.unit)}},{key:"toString",value:function(){return"".concat(this.num).concat(this.unit)}},{key:"isNaN",value:function(){return Number.isNaN(this.num)}}],[{key:"parse",value:function(n){var r,i=(r=Fv.exec(n))!==null&&r!==void 0?r:[],a=Jn(i,3),o=a[1],c=a[2];return new t(parseFloat(o),c??"")}}])}();function ns(t){if(t.includes(Ft))return Ft;for(var e=t;e.includes("*")||e.includes("/");){var n,r=(n=Dc.exec(e))!==null&&n!==void 0?n:[],i=Jn(r,4),a=i[1],o=i[2],c=i[3],l=Nn.parse(a??""),u=Nn.parse(c??""),s=o==="*"?l.multiply(u):l.divide(u);if(s.isNaN())return Ft;e=e.replace(Dc,s.toString())}for(;e.includes("+")||/.-\d+(?:\.\d+)?/.test(e);){var f,p=(f=Cc.exec(e))!==null&&f!==void 0?f:[],d=Jn(p,4),v=d[1],h=d[2],m=d[3],O=Nn.parse(v??""),w=Nn.parse(m??""),x=h==="+"?O.add(w):O.subtract(w);if(x.isNaN())return Ft;e=e.replace(Cc,x.toString())}return e}var Mc=/\(([^()]*)\)/;function Gv(t){for(var e=t;e.includes("(");){var n=Mc.exec(e),r=Jn(n,2),i=r[1];e=e.replace(Mc,ns(i))}return e}function Hv(t){var e=t.replace(/\s+/g,"");return e=Gv(e),e=ns(e),e}function Uv(t){try{return Hv(t)}catch{return Ft}}function la(t){var e=Uv(t.slice(5,-1));return e===Ft?"":e}var Yv=["x","y","lineHeight","capHeight","scaleToFit","textAnchor","verticalAnchor","fill"],qv=["dx","dy","angle","className","breakAll"];function Ma(){return Ma=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var n=arguments[e];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(t[r]=n[r])}return t},Ma.apply(this,arguments)}function Nc(t,e){if(t==null)return{};var n=Zv(t,e),r,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(t);for(i=0;i<a.length;i++)r=a[i],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(n[r]=t[r])}return n}function Zv(t,e){if(t==null)return{};var n={};for(var r in t)if(Object.prototype.hasOwnProperty.call(t,r)){if(e.indexOf(r)>=0)continue;n[r]=t[r]}return n}function Rc(t,e){return th(t)||eh(t,e)||Qv(t,e)||Jv()}function Jv(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function Qv(t,e){if(t){if(typeof t=="string")return Bc(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);if(n==="Object"&&t.constructor&&(n=t.constructor.name),n==="Map"||n==="Set")return Array.from(t);if(n==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return Bc(t,e)}}function Bc(t,e){(e==null||e>t.length)&&(e=t.length);for(var n=0,r=new Array(e);n<e;n++)r[n]=t[n];return r}function eh(t,e){var n=t==null?null:typeof Symbol<"u"&&t[Symbol.iterator]||t["@@iterator"];if(n!=null){var r,i,a,o,c=[],l=!0,u=!1;try{if(a=(n=n.call(t)).next,e===0){if(Object(n)!==n)return;l=!1}else for(;!(l=(r=a.call(n)).done)&&(c.push(r.value),c.length!==e);l=!0);}catch(s){u=!0,i=s}finally{try{if(!l&&n.return!=null&&(o=n.return(),Object(o)!==o))return}finally{if(u)throw i}}return c}}function th(t){if(Array.isArray(t))return t}var is=/[ \f\n\r\t\v\u2028\u2029]+/,as=function(e){var n=e.children,r=e.breakAll,i=e.style;try{var a=[];G(n)||(r?a=n.toString().split(""):a=n.toString().split(is));var o=a.map(function(l){return{word:l,width:Yt(l,i).width}}),c=r?0:Yt(" ",i).width;return{wordsWithComputedWidth:o,spaceWidth:c}}catch{return null}},rh=function(e,n,r,i,a){var o=e.maxLines,c=e.children,l=e.style,u=e.breakAll,s=R(o),f=c,p=function(){var T=arguments.length>0&&arguments[0]!==void 0?arguments[0]:[];return T.reduce(function(C,M){var B=M.word,W=M.width,V=C[C.length-1];if(V&&(i==null||a||V.width+W+r<Number(i)))V.words.push(B),V.width+=W+r;else{var H={words:[B],width:W};C.push(H)}return C},[])},d=p(n),v=function(T){return T.reduce(function(C,M){return C.width>M.width?C:M})};if(!s)return d;for(var h="…",m=function(T){var C=f.slice(0,T),M=as({breakAll:u,style:l,children:C+h}).wordsWithComputedWidth,B=p(M),W=B.length>o||v(B).width>Number(i);return[W,B]},O=0,w=f.length-1,x=0,A;O<=w&&x<=f.length-1;){var y=Math.floor((O+w)/2),g=y-1,P=m(g),S=Rc(P,2),j=S[0],E=S[1],$=m(y),_=Rc($,1),k=_[0];if(!j&&!k&&(O=y+1),j&&k&&(w=y-1),!j&&k){A=E;break}x++}return A||d},Lc=function(e){var n=G(e)?[]:e.toString().split(is);return[{words:n}]},nh=function(e){var n=e.width,r=e.scaleToFit,i=e.children,a=e.style,o=e.breakAll,c=e.maxLines;if((n||r)&&!be.isSsr){var l,u,s=as({breakAll:o,children:i,style:a});if(s){var f=s.wordsWithComputedWidth,p=s.spaceWidth;l=f,u=p}else return Lc(i);return rh({breakAll:o,children:i,maxLines:c,style:a},l,u,n,r)}return Lc(i)},Wc="#808080",dt=function(e){var n=e.x,r=n===void 0?0:n,i=e.y,a=i===void 0?0:i,o=e.lineHeight,c=o===void 0?"1em":o,l=e.capHeight,u=l===void 0?"0.71em":l,s=e.scaleToFit,f=s===void 0?!1:s,p=e.textAnchor,d=p===void 0?"start":p,v=e.verticalAnchor,h=v===void 0?"end":v,m=e.fill,O=m===void 0?Wc:m,w=Nc(e,Yv),x=N.useMemo(function(){return nh({breakAll:w.breakAll,children:w.children,maxLines:w.maxLines,scaleToFit:f,style:w.style,width:w.width})},[w.breakAll,w.children,w.maxLines,f,w.style,w.width]),A=w.dx,y=w.dy,g=w.angle,P=w.className,S=w.breakAll,j=Nc(w,qv);if(!pe(r)||!pe(a))return null;var E=r+(R(A)?A:0),$=a+(R(y)?y:0),_;switch(h){case"start":_=la("calc(".concat(u,")"));break;case"middle":_=la("calc(".concat((x.length-1)/2," * -").concat(c," + (").concat(u," / 2))"));break;default:_=la("calc(".concat(x.length-1," * -").concat(c,")"));break}var k=[];if(f){var D=x[0].width,T=w.width;k.push("scale(".concat((R(T)?T/D:1)/D,")"))}return g&&k.push("rotate(".concat(g,", ").concat(E,", ").concat($,")")),k.length&&(j.transform=k.join(" ")),b.createElement("text",Ma({},L(j,!0),{x:E,y:$,className:X("recharts-text",P),textAnchor:d,fill:O.includes("url")?Wc:O}),x.map(function(C,M){var B=C.words.join(S?"":" ");return b.createElement("tspan",{x:E,dy:M===0?_:c,key:"".concat(B,"-").concat(M)},B)}))};function ih(t){return lh(t)||ch(t)||oh(t)||ah()}function ah(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function oh(t,e){if(t){if(typeof t=="string")return Na(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);if(n==="Object"&&t.constructor&&(n=t.constructor.name),n==="Map"||n==="Set")return Array.from(t);if(n==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return Na(t,e)}}function ch(t){if(typeof Symbol<"u"&&Symbol.iterator in Object(t))return Array.from(t)}function lh(t){if(Array.isArray(t))return Na(t)}function Na(t,e){(e==null||e>t.length)&&(e=t.length);for(var n=0,r=new Array(e);n<e;n++)r[n]=t[n];return r}var uh=function(e){return e},os={},cs=function(e){return e===os},zc=function(e){return function n(){return arguments.length===0||arguments.length===1&&cs(arguments.length<=0?void 0:arguments[0])?n:e.apply(void 0,arguments)}},sh=function t(e,n){return e===1?n:zc(function(){for(var r=arguments.length,i=new Array(r),a=0;a<r;a++)i[a]=arguments[a];var o=i.filter(function(c){return c!==os}).length;return o>=e?n.apply(void 0,i):t(e-o,zc(function(){for(var c=arguments.length,l=new Array(c),u=0;u<c;u++)l[u]=arguments[u];var s=i.map(function(f){return cs(f)?l.shift():f});return n.apply(void 0,ih(s).concat(l))}))})},zi=function(e){return sh(e.length,e)},Ra=function(e,n){for(var r=[],i=e;i<n;++i)r[i-e]=i;return r},fh=zi(function(t,e){return Array.isArray(e)?e.map(t):Object.keys(e).map(function(n){return e[n]}).map(t)}),ph=function(){for(var e=arguments.length,n=new Array(e),r=0;r<e;r++)n[r]=arguments[r];if(!n.length)return uh;var i=n.reverse(),a=i[0],o=i.slice(1);return function(){return o.reduce(function(c,l){return l(c)},a.apply(void 0,arguments))}},Ba=function(e){return Array.isArray(e)?e.reverse():e.split("").reverse.join("")},ls=function(e){var n=null,r=null;return function(){for(var i=arguments.length,a=new Array(i),o=0;o<i;o++)a[o]=arguments[o];return n&&a.every(function(c,l){return c===n[l]})||(n=a,r=e.apply(void 0,a)),r}};function dh(t){var e;return t===0?e=1:e=Math.floor(new Q(t).abs().log(10).toNumber())+1,e}function vh(t,e,n){for(var r=new Q(t),i=0,a=[];r.lt(e)&&i<1e5;)a.push(r.toNumber()),r=r.add(n),i++;return a}var hh=zi(function(t,e,n){var r=+t,i=+e;return r+n*(i-r)}),yh=zi(function(t,e,n){var r=e-+t;return r=r||1/0,(n-t)/r}),mh=zi(function(t,e,n){var r=e-+t;return r=r||1/0,Math.max(0,Math.min(1,(n-t)/r))});const Ki={rangeStep:vh,getDigitCount:dh,interpolateNumber:hh,uninterpolateNumber:yh,uninterpolateTruncation:mh};function La(t){return Oh(t)||bh(t)||us(t)||gh()}function gh(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function bh(t){if(typeof Symbol<"u"&&Symbol.iterator in Object(t))return Array.from(t)}function Oh(t){if(Array.isArray(t))return Wa(t)}function Qr(t,e){return Ah(t)||wh(t,e)||us(t,e)||xh()}function xh(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function us(t,e){if(t){if(typeof t=="string")return Wa(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);if(n==="Object"&&t.constructor&&(n=t.constructor.name),n==="Map"||n==="Set")return Array.from(t);if(n==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return Wa(t,e)}}function Wa(t,e){(e==null||e>t.length)&&(e=t.length);for(var n=0,r=new Array(e);n<e;n++)r[n]=t[n];return r}function wh(t,e){if(!(typeof Symbol>"u"||!(Symbol.iterator in Object(t)))){var n=[],r=!0,i=!1,a=void 0;try{for(var o=t[Symbol.iterator](),c;!(r=(c=o.next()).done)&&(n.push(c.value),!(e&&n.length===e));r=!0);}catch(l){i=!0,a=l}finally{try{!r&&o.return!=null&&o.return()}finally{if(i)throw a}}return n}}function Ah(t){if(Array.isArray(t))return t}function ss(t){var e=Qr(t,2),n=e[0],r=e[1],i=n,a=r;return n>r&&(i=r,a=n),[i,a]}function fs(t,e,n){if(t.lte(0))return new Q(0);var r=Ki.getDigitCount(t.toNumber()),i=new Q(10).pow(r),a=t.div(i),o=r!==1?.05:.1,c=new Q(Math.ceil(a.div(o).toNumber())).add(n).mul(o),l=c.mul(i);return e?l:new Q(Math.ceil(l))}function Ph(t,e,n){var r=1,i=new Q(t);if(!i.isint()&&n){var a=Math.abs(t);a<1?(r=new Q(10).pow(Ki.getDigitCount(t)-1),i=new Q(Math.floor(i.div(r).toNumber())).mul(r)):a>1&&(i=new Q(Math.floor(t)))}else t===0?i=new Q(Math.floor((e-1)/2)):n||(i=new Q(Math.floor(t)));var o=Math.floor((e-1)/2),c=ph(fh(function(l){return i.add(new Q(l-o).mul(r)).toNumber()}),Ra);return c(0,e)}function ps(t,e,n,r){var i=arguments.length>4&&arguments[4]!==void 0?arguments[4]:0;if(!Number.isFinite((e-t)/(n-1)))return{step:new Q(0),tickMin:new Q(0),tickMax:new Q(0)};var a=fs(new Q(e).sub(t).div(n-1),r,i),o;t<=0&&e>=0?o=new Q(0):(o=new Q(t).add(e).div(2),o=o.sub(new Q(o).mod(a)));var c=Math.ceil(o.sub(t).div(a).toNumber()),l=Math.ceil(new Q(e).sub(o).div(a).toNumber()),u=c+l+1;return u>n?ps(t,e,n,r,i+1):(u<n&&(l=e>0?l+(n-u):l,c=e>0?c:c+(n-u)),{step:a,tickMin:o.sub(new Q(c).mul(a)),tickMax:o.add(new Q(l).mul(a))})}function Sh(t){var e=Qr(t,2),n=e[0],r=e[1],i=arguments.length>1&&arguments[1]!==void 0?arguments[1]:6,a=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!0,o=Math.max(i,2),c=ss([n,r]),l=Qr(c,2),u=l[0],s=l[1];if(u===-1/0||s===1/0){var f=s===1/0?[u].concat(La(Ra(0,i-1).map(function(){return 1/0}))):[].concat(La(Ra(0,i-1).map(function(){return-1/0})),[s]);return n>r?Ba(f):f}if(u===s)return Ph(u,i,a);var p=ps(u,s,o,a),d=p.step,v=p.tickMin,h=p.tickMax,m=Ki.rangeStep(v,h.add(new Q(.1).mul(d)),d);return n>r?Ba(m):m}function jh(t,e){var n=Qr(t,2),r=n[0],i=n[1],a=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!0,o=ss([r,i]),c=Qr(o,2),l=c[0],u=c[1];if(l===-1/0||u===1/0)return[r,i];if(l===u)return[l];var s=Math.max(e,2),f=fs(new Q(u).sub(l).div(s-1),a,0),p=[].concat(La(Ki.rangeStep(new Q(l),new Q(u).sub(new Q(.99).mul(f)),f)),[u]);return r>i?Ba(p):p}var Eh=ls(Sh),$h=ls(jh),_h=["offset","layout","width","dataKey","data","dataPointFormatter","xAxis","yAxis"];function rr(t){"@babel/helpers - typeof";return rr=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},rr(t)}function Qn(){return Qn=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var n=arguments[e];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(t[r]=n[r])}return t},Qn.apply(this,arguments)}function Th(t,e){return Ch(t)||Dh(t,e)||kh(t,e)||Ih()}function Ih(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function kh(t,e){if(t){if(typeof t=="string")return Kc(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);if(n==="Object"&&t.constructor&&(n=t.constructor.name),n==="Map"||n==="Set")return Array.from(t);if(n==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return Kc(t,e)}}function Kc(t,e){(e==null||e>t.length)&&(e=t.length);for(var n=0,r=new Array(e);n<e;n++)r[n]=t[n];return r}function Dh(t,e){var n=t==null?null:typeof Symbol<"u"&&t[Symbol.iterator]||t["@@iterator"];if(n!=null){var r,i,a,o,c=[],l=!0,u=!1;try{if(a=(n=n.call(t)).next,e!==0)for(;!(l=(r=a.call(n)).done)&&(c.push(r.value),c.length!==e);l=!0);}catch(s){u=!0,i=s}finally{try{if(!l&&n.return!=null&&(o=n.return(),Object(o)!==o))return}finally{if(u)throw i}}return c}}function Ch(t){if(Array.isArray(t))return t}function Mh(t,e){if(t==null)return{};var n=Nh(t,e),r,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(t);for(i=0;i<a.length;i++)r=a[i],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(n[r]=t[r])}return n}function Nh(t,e){if(t==null)return{};var n={};for(var r in t)if(Object.prototype.hasOwnProperty.call(t,r)){if(e.indexOf(r)>=0)continue;n[r]=t[r]}return n}function Rh(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function Bh(t,e){for(var n=0;n<e.length;n++){var r=e[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,hs(r.key),r)}}function Lh(t,e,n){return e&&Bh(t.prototype,e),Object.defineProperty(t,"prototype",{writable:!1}),t}function Wh(t,e,n){return e=ei(e),zh(t,ds()?Reflect.construct(e,n||[],ei(t).constructor):e.apply(t,n))}function zh(t,e){if(e&&(rr(e)==="object"||typeof e=="function"))return e;if(e!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return Kh(t)}function Kh(t){if(t===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}function ds(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(ds=function(){return!!t})()}function ei(t){return ei=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(n){return n.__proto__||Object.getPrototypeOf(n)},ei(t)}function Fh(t,e){if(typeof e!="function"&&e!==null)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&za(t,e)}function za(t,e){return za=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(r,i){return r.__proto__=i,r},za(t,e)}function vs(t,e,n){return e=hs(e),e in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function hs(t){var e=Vh(t,"string");return rr(e)=="symbol"?e:e+""}function Vh(t,e){if(rr(t)!="object"||!t)return t;var n=t[Symbol.toPrimitive];if(n!==void 0){var r=n.call(t,e);if(rr(r)!="object")return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}var Tr=function(t){function e(){return Rh(this,e),Wh(this,e,arguments)}return Fh(e,t),Lh(e,[{key:"render",value:function(){var r=this.props,i=r.offset,a=r.layout,o=r.width,c=r.dataKey,l=r.data,u=r.dataPointFormatter,s=r.xAxis,f=r.yAxis,p=Mh(r,_h),d=L(p,!1);this.props.direction==="x"&&s.type!=="number"&&kt();var v=l.map(function(h){var m=u(h,c),O=m.x,w=m.y,x=m.value,A=m.errorVal;if(!A)return null;var y=[],g,P;if(Array.isArray(A)){var S=Th(A,2);g=S[0],P=S[1]}else g=P=A;if(a==="vertical"){var j=s.scale,E=w+i,$=E+o,_=E-o,k=j(x-g),D=j(x+P);y.push({x1:D,y1:$,x2:D,y2:_}),y.push({x1:k,y1:E,x2:D,y2:E}),y.push({x1:k,y1:$,x2:k,y2:_})}else if(a==="horizontal"){var T=f.scale,C=O+i,M=C-o,B=C+o,W=T(x-g),V=T(x+P);y.push({x1:M,y1:V,x2:B,y2:V}),y.push({x1:C,y1:W,x2:C,y2:V}),y.push({x1:M,y1:W,x2:B,y2:W})}return b.createElement(F,Qn({className:"recharts-errorBar",key:"bar-".concat(y.map(function(H){return"".concat(H.x1,"-").concat(H.x2,"-").concat(H.y1,"-").concat(H.y2)}))},d),y.map(function(H){return b.createElement("line",Qn({},H,{key:"line-".concat(H.x1,"-").concat(H.x2,"-").concat(H.y1,"-").concat(H.y2)}))}))});return b.createElement(F,{className:"recharts-errorBars"},v)}}])}(b.Component);vs(Tr,"defaultProps",{stroke:"black",strokeWidth:1.5,width:5,offset:0,layout:"horizontal"});vs(Tr,"displayName","ErrorBar");function en(t){"@babel/helpers - typeof";return en=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},en(t)}function Fc(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter(function(i){return Object.getOwnPropertyDescriptor(t,i).enumerable})),n.push.apply(n,r)}return n}function wt(t){for(var e=1;e<arguments.length;e++){var n=arguments[e]!=null?arguments[e]:{};e%2?Fc(Object(n),!0).forEach(function(r){Xh(t,r,n[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):Fc(Object(n)).forEach(function(r){Object.defineProperty(t,r,Object.getOwnPropertyDescriptor(n,r))})}return t}function Xh(t,e,n){return e=Gh(e),e in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function Gh(t){var e=Hh(t,"string");return en(e)=="symbol"?e:e+""}function Hh(t,e){if(en(t)!="object"||!t)return t;var n=t[Symbol.toPrimitive];if(n!==void 0){var r=n.call(t,e);if(en(r)!="object")return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(t)}var ys=function(e){var n=e.children,r=e.formattedGraphicalItems,i=e.legendWidth,a=e.legendContent,o=oe(n,Ut);if(!o)return null;var c=Ut.defaultProps,l=c!==void 0?wt(wt({},c),o.props):{},u;return o.props&&o.props.payload?u=o.props&&o.props.payload:a==="children"?u=(r||[]).reduce(function(s,f){var p=f.item,d=f.props,v=d.sectors||d.data||[];return s.concat(v.map(function(h){return{type:o.props.iconType||p.props.legendType,value:h.name,color:h.fill,payload:h}}))},[]):u=(r||[]).map(function(s){var f=s.item,p=f.type.defaultProps,d=p!==void 0?wt(wt({},p),f.props):{},v=d.dataKey,h=d.name,m=d.legendType,O=d.hide;return{inactive:O,dataKey:v,type:l.iconType||m||"square",color:Wo(f),value:h||v,payload:d}}),wt(wt(wt({},l),Ut.getWithHeight(o,i)),{},{payload:u,item:o})};function tn(t){"@babel/helpers - typeof";return tn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},tn(t)}function Vc(t){return Zh(t)||qh(t)||Yh(t)||Uh()}function Uh(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function Yh(t,e){if(t){if(typeof t=="string")return Ka(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);if(n==="Object"&&t.constructor&&(n=t.constructor.name),n==="Map"||n==="Set")return Array.from(t);if(n==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return Ka(t,e)}}function qh(t){if(typeof Symbol<"u"&&t[Symbol.iterator]!=null||t["@@iterator"]!=null)return Array.from(t)}function Zh(t){if(Array.isArray(t))return Ka(t)}function Ka(t,e){(e==null||e>t.length)&&(e=t.length);for(var n=0,r=new Array(e);n<e;n++)r[n]=t[n];return r}function Xc(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter(function(i){return Object.getOwnPropertyDescriptor(t,i).enumerable})),n.push.apply(n,r)}return n}function ie(t){for(var e=1;e<arguments.length;e++){var n=arguments[e]!=null?arguments[e]:{};e%2?Xc(Object(n),!0).forEach(function(r){qt(t,r,n[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):Xc(Object(n)).forEach(function(r){Object.defineProperty(t,r,Object.getOwnPropertyDescriptor(n,r))})}return t}function qt(t,e,n){return e=Jh(e),e in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function Jh(t){var e=Qh(t,"string");return tn(e)=="symbol"?e:e+""}function Qh(t,e){if(tn(t)!="object"||!t)return t;var n=t[Symbol.toPrimitive];if(n!==void 0){var r=n.call(t,e);if(tn(r)!="object")return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(t)}function Z(t,e,n){return G(t)||G(e)?n:pe(e)?he(t,e,n):K(e)?e(t):n}function Wr(t,e,n,r){var i=vp(t,function(c){return Z(c,e)});if(n==="number"){var a=i.filter(function(c){return R(c)||parseFloat(c)});return a.length?[jn(a),lt(a)]:[1/0,-1/0]}var o=r?i.filter(function(c){return!G(c)}):i;return o.map(function(c){return pe(c)||c instanceof Date?c:""})}var ey=function(e){var n,r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:[],i=arguments.length>2?arguments[2]:void 0,a=arguments.length>3?arguments[3]:void 0,o=-1,c=(n=r?.length)!==null&&n!==void 0?n:0;if(c<=1)return 0;if(a&&a.axisType==="angleAxis"&&Math.abs(Math.abs(a.range[1]-a.range[0])-360)<=1e-6)for(var l=a.range,u=0;u<c;u++){var s=u>0?i[u-1].coordinate:i[c-1].coordinate,f=i[u].coordinate,p=u>=c-1?i[0].coordinate:i[u+1].coordinate,d=void 0;if(ye(f-s)!==ye(p-f)){var v=[];if(ye(p-f)===ye(l[1]-l[0])){d=p;var h=f+l[1]-l[0];v[0]=Math.min(h,(h+s)/2),v[1]=Math.max(h,(h+s)/2)}else{d=s;var m=p+l[1]-l[0];v[0]=Math.min(f,(m+f)/2),v[1]=Math.max(f,(m+f)/2)}var O=[Math.min(f,(d+f)/2),Math.max(f,(d+f)/2)];if(e>O[0]&&e<=O[1]||e>=v[0]&&e<=v[1]){o=i[u].index;break}}else{var w=Math.min(s,p),x=Math.max(s,p);if(e>(w+f)/2&&e<=(x+f)/2){o=i[u].index;break}}}else for(var A=0;A<c;A++)if(A===0&&e<=(r[A].coordinate+r[A+1].coordinate)/2||A>0&&A<c-1&&e>(r[A].coordinate+r[A-1].coordinate)/2&&e<=(r[A].coordinate+r[A+1].coordinate)/2||A===c-1&&e>(r[A].coordinate+r[A-1].coordinate)/2){o=r[A].index;break}return o},Wo=function(e){var n,r=e,i=r.type.displayName,a=(n=e.type)!==null&&n!==void 0&&n.defaultProps?ie(ie({},e.type.defaultProps),e.props):e.props,o=a.stroke,c=a.fill,l;switch(i){case"Line":l=o;break;case"Area":case"Radar":l=o&&o!=="none"?o:c;break;default:l=c;break}return l},ty=function(e){var n=e.barSize,r=e.totalSize,i=e.stackGroups,a=i===void 0?{}:i;if(!a)return{};for(var o={},c=Object.keys(a),l=0,u=c.length;l<u;l++)for(var s=a[c[l]].stackGroups,f=Object.keys(s),p=0,d=f.length;p<d;p++){var v=s[f[p]],h=v.items,m=v.cateAxisId,O=h.filter(function(P){return Qe(P.type).indexOf("Bar")>=0});if(O&&O.length){var w=O[0].type.defaultProps,x=w!==void 0?ie(ie({},w),O[0].props):O[0].props,A=x.barSize,y=x[m];o[y]||(o[y]=[]);var g=G(A)?n:A;o[y].push({item:O[0],stackList:O.slice(1),barSize:G(g)?void 0:Pe(g,r,0)})}}return o},ry=function(e){var n=e.barGap,r=e.barCategoryGap,i=e.bandSize,a=e.sizeList,o=a===void 0?[]:a,c=e.maxBarSize,l=o.length;if(l<1)return null;var u=Pe(n,i,0,!0),s,f=[];if(o[0].barSize===+o[0].barSize){var p=!1,d=i/l,v=o.reduce(function(A,y){return A+y.barSize||0},0);v+=(l-1)*u,v>=i&&(v-=(l-1)*u,u=0),v>=i&&d>0&&(p=!0,d*=.9,v=l*d);var h=(i-v)/2>>0,m={offset:h-u,size:0};s=o.reduce(function(A,y){var g={item:y.item,position:{offset:m.offset+m.size+u,size:p?d:y.barSize}},P=[].concat(Vc(A),[g]);return m=P[P.length-1].position,y.stackList&&y.stackList.length&&y.stackList.forEach(function(S){P.push({item:S,position:m})}),P},f)}else{var O=Pe(r,i,0,!0);i-2*O-(l-1)*u<=0&&(u=0);var w=(i-2*O-(l-1)*u)/l;w>1&&(w>>=0);var x=c===+c?Math.min(w,c):w;s=o.reduce(function(A,y,g){var P=[].concat(Vc(A),[{item:y.item,position:{offset:O+(w+u)*g+(w-x)/2,size:x}}]);return y.stackList&&y.stackList.length&&y.stackList.forEach(function(S){P.push({item:S,position:P[P.length-1].position})}),P},f)}return s},ny=function(e,n,r,i){var a=r.children,o=r.width,c=r.margin,l=o-(c.left||0)-(c.right||0),u=ys({children:a,legendWidth:l});if(u){var s=i||{},f=s.width,p=s.height,d=u.align,v=u.verticalAlign,h=u.layout;if((h==="vertical"||h==="horizontal"&&v==="middle")&&d!=="center"&&R(e[d]))return ie(ie({},e),{},qt({},d,e[d]+(f||0)));if((h==="horizontal"||h==="vertical"&&d==="center")&&v!=="middle"&&R(e[v]))return ie(ie({},e),{},qt({},v,e[v]+(p||0)))}return e},iy=function(e,n,r){return G(n)?!0:e==="horizontal"?n==="yAxis":e==="vertical"||r==="x"?n==="xAxis":r==="y"?n==="yAxis":!0},ms=function(e,n,r,i,a){var o=n.props.children,c=ge(o,Tr).filter(function(u){return iy(i,a,u.props.direction)});if(c&&c.length){var l=c.map(function(u){return u.props.dataKey});return e.reduce(function(u,s){var f=Z(s,r);if(G(f))return u;var p=Array.isArray(f)?[jn(f),lt(f)]:[f,f],d=l.reduce(function(v,h){var m=Z(s,h,0),O=p[0]-Math.abs(Array.isArray(m)?m[0]:m),w=p[1]+Math.abs(Array.isArray(m)?m[1]:m);return[Math.min(O,v[0]),Math.max(w,v[1])]},[1/0,-1/0]);return[Math.min(d[0],u[0]),Math.max(d[1],u[1])]},[1/0,-1/0])}return null},ay=function(e,n,r,i,a){var o=n.map(function(c){return ms(e,c,r,a,i)}).filter(function(c){return!G(c)});return o&&o.length?o.reduce(function(c,l){return[Math.min(c[0],l[0]),Math.max(c[1],l[1])]},[1/0,-1/0]):null},gs=function(e,n,r,i,a){var o=n.map(function(l){var u=l.props.dataKey;return r==="number"&&u&&ms(e,l,u,i)||Wr(e,u,r,a)});if(r==="number")return o.reduce(function(l,u){return[Math.min(l[0],u[0]),Math.max(l[1],u[1])]},[1/0,-1/0]);var c={};return o.reduce(function(l,u){for(var s=0,f=u.length;s<f;s++)c[u[s]]||(c[u[s]]=!0,l.push(u[s]));return l},[])},bs=function(e,n){return e==="horizontal"&&n==="xAxis"||e==="vertical"&&n==="yAxis"||e==="centric"&&n==="angleAxis"||e==="radial"&&n==="radiusAxis"},Os=function(e,n,r,i){if(i)return e.map(function(l){return l.coordinate});var a,o,c=e.map(function(l){return l.coordinate===n&&(a=!0),l.coordinate===r&&(o=!0),l.coordinate});return a||c.push(n),o||c.push(r),c},Je=function(e,n,r){if(!e)return null;var i=e.scale,a=e.duplicateDomain,o=e.type,c=e.range,l=e.realScaleType==="scaleBand"?i.bandwidth()/2:2,u=(n||r)&&o==="category"&&i.bandwidth?i.bandwidth()/l:0;if(u=e.axisType==="angleAxis"&&c?.length>=2?ye(c[0]-c[1])*2*u:u,n&&(e.ticks||e.niceTicks)){var s=(e.ticks||e.niceTicks).map(function(f){var p=a?a.indexOf(f):f;return{coordinate:i(p)+u,value:f,offset:u}});return s.filter(function(f){return!vt(f.coordinate)})}return e.isCategorical&&e.categoricalDomain?e.categoricalDomain.map(function(f,p){return{coordinate:i(f)+u,value:f,index:p,offset:u}}):i.ticks&&!r?i.ticks(e.tickCount).map(function(f){return{coordinate:i(f)+u,value:f,offset:u}}):i.domain().map(function(f,p){return{coordinate:i(f)+u,value:a?a[f]:f,index:p,offset:u}})},ua=new WeakMap,Rn=function(e,n){if(typeof n!="function")return e;ua.has(e)||ua.set(e,new WeakMap);var r=ua.get(e);if(r.has(n))return r.get(n);var i=function(){e.apply(void 0,arguments),n.apply(void 0,arguments)};return r.set(n,i),i},xs=function(e,n,r){var i=e.scale,a=e.type,o=e.layout,c=e.axisType;if(i==="auto")return o==="radial"&&c==="radiusAxis"?{scale:ac(),realScaleType:"band"}:o==="radial"&&c==="angleAxis"?{scale:Oa(),realScaleType:"linear"}:a==="category"&&n&&(n.indexOf("LineChart")>=0||n.indexOf("AreaChart")>=0||n.indexOf("ComposedChart")>=0&&!r)?{scale:Kn(),realScaleType:"point"}:a==="category"?{scale:ac(),realScaleType:"band"}:{scale:Oa(),realScaleType:"linear"};if(pt(i)){var l="scale".concat(Ri(i));return{scale:(oc[l]||Kn)(),realScaleType:oc[l]?l:"point"}}return K(i)?{scale:i}:{scale:Kn(),realScaleType:"point"}},Gc=1e-4,ws=function(e){var n=e.domain();if(!(!n||n.length<=2)){var r=n.length,i=e.range(),a=Math.min(i[0],i[1])-Gc,o=Math.max(i[0],i[1])+Gc,c=e(n[0]),l=e(n[r-1]);(c<a||c>o||l<a||l>o)&&e.domain([n[0],n[r-1]])}},As=function(e,n){if(!e)return null;for(var r=0,i=e.length;r<i;r++)if(e[r].item===n)return e[r].position;return null},Ps=function(e,n){if(!n||n.length!==2||!R(n[0])||!R(n[1]))return e;var r=Math.min(n[0],n[1]),i=Math.max(n[0],n[1]),a=[e[0],e[1]];return(!R(e[0])||e[0]<r)&&(a[0]=r),(!R(e[1])||e[1]>i)&&(a[1]=i),a[0]>i&&(a[0]=i),a[1]<r&&(a[1]=r),a},oy=function(e){var n=e.length;if(!(n<=0))for(var r=0,i=e[0].length;r<i;++r)for(var a=0,o=0,c=0;c<n;++c){var l=vt(e[c][r][1])?e[c][r][0]:e[c][r][1];l>=0?(e[c][r][0]=a,e[c][r][1]=a+l,a=e[c][r][1]):(e[c][r][0]=o,e[c][r][1]=o+l,o=e[c][r][1])}},cy=function(e){var n=e.length;if(!(n<=0))for(var r=0,i=e[0].length;r<i;++r)for(var a=0,o=0;o<n;++o){var c=vt(e[o][r][1])?e[o][r][0]:e[o][r][1];c>=0?(e[o][r][0]=a,e[o][r][1]=a+c,a=e[o][r][1]):(e[o][r][0]=0,e[o][r][1]=0)}},ly={sign:oy,expand:dp,none:pp,silhouette:fp,wiggle:sp,positive:cy},uy=function(e,n,r){var i=n.map(function(c){return c.props.dataKey}),a=ly[r],o=lp().keys(i).value(function(c,l){return+Z(c,l,0)}).order(up).offset(a);return o(e)},sy=function(e,n,r,i,a,o){if(!e)return null;var c=o?n.reverse():n,l={},u=c.reduce(function(f,p){var d,v=(d=p.type)!==null&&d!==void 0&&d.defaultProps?ie(ie({},p.type.defaultProps),p.props):p.props,h=v.stackId,m=v.hide;if(m)return f;var O=v[r],w=f[O]||{hasStack:!1,stackGroups:{}};if(pe(h)){var x=w.stackGroups[h]||{numericAxisId:r,cateAxisId:i,items:[]};x.items.push(p),w.hasStack=!0,w.stackGroups[h]=x}else w.stackGroups[nt("_stackId_")]={numericAxisId:r,cateAxisId:i,items:[p]};return ie(ie({},f),{},qt({},O,w))},l),s={};return Object.keys(u).reduce(function(f,p){var d=u[p];if(d.hasStack){var v={};d.stackGroups=Object.keys(d.stackGroups).reduce(function(h,m){var O=d.stackGroups[m];return ie(ie({},h),{},qt({},m,{numericAxisId:r,cateAxisId:i,items:O.items,stackedData:uy(e,O.items,a)}))},v)}return ie(ie({},f),{},qt({},p,d))},s)},Ss=function(e,n){var r=n.realScaleType,i=n.type,a=n.tickCount,o=n.originalDomain,c=n.allowDecimals,l=r||n.scale;if(l!=="auto"&&l!=="linear")return null;if(a&&i==="number"&&o&&(o[0]==="auto"||o[1]==="auto")){var u=e.domain();if(!u.length)return null;var s=Eh(u,a,c);return e.domain([jn(s),lt(s)]),{niceTicks:s}}if(a&&i==="number"){var f=e.domain(),p=$h(f,a,c);return{niceTicks:p}}return null};function nr(t){var e=t.axis,n=t.ticks,r=t.bandSize,i=t.entry,a=t.index,o=t.dataKey;if(e.type==="category"){if(!e.allowDuplicatedCategory&&e.dataKey&&!G(i[e.dataKey])){var c=Xn(n,"value",i[e.dataKey]);if(c)return c.coordinate+r/2}return n[a]?n[a].coordinate+r/2:null}var l=Z(i,G(o)?e.dataKey:o);return G(l)?null:e.scale(l)}var ti=function(e){var n=e.axis,r=e.ticks,i=e.offset,a=e.bandSize,o=e.entry,c=e.index;if(n.type==="category")return r[c]?r[c].coordinate+i:null;var l=Z(o,n.dataKey,n.domain[c]);return G(l)?null:n.scale(l)-a/2+i},js=function(e){var n=e.numericAxis,r=n.scale.domain();if(n.type==="number"){var i=Math.min(r[0],r[1]),a=Math.max(r[0],r[1]);return i<=0&&a>=0?0:a<0?a:i}return r[0]},fy=function(e,n){var r,i=(r=e.type)!==null&&r!==void 0&&r.defaultProps?ie(ie({},e.type.defaultProps),e.props):e.props,a=i.stackId;if(pe(a)){var o=n[a];if(o){var c=o.items.indexOf(e);return c>=0?o.stackedData[c]:null}}return null},py=function(e){return e.reduce(function(n,r){return[jn(r.concat([n[0]]).filter(R)),lt(r.concat([n[1]]).filter(R))]},[1/0,-1/0])},Es=function(e,n,r){return Object.keys(e).reduce(function(i,a){var o=e[a],c=o.stackedData,l=c.reduce(function(u,s){var f=py(s.slice(n,r+1));return[Math.min(u[0],f[0]),Math.max(u[1],f[1])]},[1/0,-1/0]);return[Math.min(l[0],i[0]),Math.max(l[1],i[1])]},[1/0,-1/0]).map(function(i){return i===1/0||i===-1/0?0:i})},Hc=/^dataMin[\s]*-[\s]*([0-9]+([.]{1}[0-9]+){0,1})$/,Uc=/^dataMax[\s]*\+[\s]*([0-9]+([.]{1}[0-9]+){0,1})$/,Fa=function(e,n,r){if(K(e))return e(n,r);if(!Array.isArray(e))return n;var i=[];if(R(e[0]))i[0]=r?e[0]:Math.min(e[0],n[0]);else if(Hc.test(e[0])){var a=+Hc.exec(e[0])[1];i[0]=n[0]-a}else K(e[0])?i[0]=e[0](n[0]):i[0]=n[0];if(R(e[1]))i[1]=r?e[1]:Math.max(e[1],n[1]);else if(Uc.test(e[1])){var o=+Uc.exec(e[1])[1];i[1]=n[1]+o}else K(e[1])?i[1]=e[1](n[1]):i[1]=n[1];return i},ri=function(e,n,r){if(e&&e.scale&&e.scale.bandwidth){var i=e.scale.bandwidth();if(!r||i>0)return i}if(e&&n&&n.length>=2){for(var a=No(n,function(f){return f.coordinate}),o=1/0,c=1,l=a.length;c<l;c++){var u=a[c],s=a[c-1];o=Math.min((u.coordinate||0)-(s.coordinate||0),o)}return o===1/0?0:o}return r?void 0:0},Yc=function(e,n,r){return!e||!e.length||Le(e,he(r,"type.defaultProps.domain"))?n:e},zo=function(e,n){var r=e.type.defaultProps?ie(ie({},e.type.defaultProps),e.props):e.props,i=r.dataKey,a=r.name,o=r.unit,c=r.formatter,l=r.tooltipType,u=r.chartType,s=r.hide;return ie(ie({},L(e,!1)),{},{dataKey:i,unit:o,formatter:c,name:a||i,color:Wo(e),value:Z(n,i),type:l,payload:n,chartType:u,hide:s})};function rn(t){"@babel/helpers - typeof";return rn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},rn(t)}function qc(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter(function(i){return Object.getOwnPropertyDescriptor(t,i).enumerable})),n.push.apply(n,r)}return n}function qe(t){for(var e=1;e<arguments.length;e++){var n=arguments[e]!=null?arguments[e]:{};e%2?qc(Object(n),!0).forEach(function(r){$s(t,r,n[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):qc(Object(n)).forEach(function(r){Object.defineProperty(t,r,Object.getOwnPropertyDescriptor(n,r))})}return t}function $s(t,e,n){return e=dy(e),e in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function dy(t){var e=vy(t,"string");return rn(e)=="symbol"?e:e+""}function vy(t,e){if(rn(t)!="object"||!t)return t;var n=t[Symbol.toPrimitive];if(n!==void 0){var r=n.call(t,e);if(rn(r)!="object")return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(t)}function hy(t,e){return by(t)||gy(t,e)||my(t,e)||yy()}function yy(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function my(t,e){if(t){if(typeof t=="string")return Zc(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);if(n==="Object"&&t.constructor&&(n=t.constructor.name),n==="Map"||n==="Set")return Array.from(t);if(n==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return Zc(t,e)}}function Zc(t,e){(e==null||e>t.length)&&(e=t.length);for(var n=0,r=new Array(e);n<e;n++)r[n]=t[n];return r}function gy(t,e){var n=t==null?null:typeof Symbol<"u"&&t[Symbol.iterator]||t["@@iterator"];if(n!=null){var r,i,a,o,c=[],l=!0,u=!1;try{if(a=(n=n.call(t)).next,e!==0)for(;!(l=(r=a.call(n)).done)&&(c.push(r.value),c.length!==e);l=!0);}catch(s){u=!0,i=s}finally{try{if(!l&&n.return!=null&&(o=n.return(),Object(o)!==o))return}finally{if(u)throw i}}return c}}function by(t){if(Array.isArray(t))return t}var ni=Math.PI/180,Oy=function(e){return e*180/Math.PI},J=function(e,n,r,i){return{x:e+Math.cos(-ni*i)*r,y:n+Math.sin(-ni*i)*r}},_s=function(e,n){var r=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{top:0,right:0,bottom:0,left:0};return Math.min(Math.abs(e-(r.left||0)-(r.right||0)),Math.abs(n-(r.top||0)-(r.bottom||0)))/2},Ko=function(e,n,r,i,a){var o=e.width,c=e.height,l=e.startAngle,u=e.endAngle,s=Pe(e.cx,o,o/2),f=Pe(e.cy,c,c/2),p=_s(o,c,r),d=Pe(e.innerRadius,p,0),v=Pe(e.outerRadius,p,p*.8),h=Object.keys(n);return h.reduce(function(m,O){var w=n[O],x=w.domain,A=w.reversed,y;if(G(w.range))i==="angleAxis"?y=[l,u]:i==="radiusAxis"&&(y=[d,v]),A&&(y=[y[1],y[0]]);else{y=w.range;var g=y,P=hy(g,2);l=P[0],u=P[1]}var S=xs(w,a),j=S.realScaleType,E=S.scale;E.domain(x).range(y),ws(E);var $=Ss(E,qe(qe({},w),{},{realScaleType:j})),_=qe(qe(qe({},w),$),{},{range:y,radius:v,realScaleType:j,scale:E,cx:s,cy:f,innerRadius:d,outerRadius:v,startAngle:l,endAngle:u});return qe(qe({},m),{},$s({},O,_))},{})},xy=function(e,n){var r=e.x,i=e.y,a=n.x,o=n.y;return Math.sqrt(Math.pow(r-a,2)+Math.pow(i-o,2))},wy=function(e,n){var r=e.x,i=e.y,a=n.cx,o=n.cy,c=xy({x:r,y:i},{x:a,y:o});if(c<=0)return{radius:c};var l=(r-a)/c,u=Math.acos(l);return i>o&&(u=2*Math.PI-u),{radius:c,angle:Oy(u),angleInRadian:u}},Ay=function(e){var n=e.startAngle,r=e.endAngle,i=Math.floor(n/360),a=Math.floor(r/360),o=Math.min(i,a);return{startAngle:n-o*360,endAngle:r-o*360}},Py=function(e,n){var r=n.startAngle,i=n.endAngle,a=Math.floor(r/360),o=Math.floor(i/360),c=Math.min(a,o);return e+c*360},Jc=function(e,n){var r=e.x,i=e.y,a=wy({x:r,y:i},n),o=a.radius,c=a.angle,l=n.innerRadius,u=n.outerRadius;if(o<l||o>u)return!1;if(o===0)return!0;var s=Ay(n),f=s.startAngle,p=s.endAngle,d=c,v;if(f<=p){for(;d>p;)d-=360;for(;d<f;)d+=360;v=d>=f&&d<=p}else{for(;d>f;)d-=360;for(;d<p;)d+=360;v=d>=p&&d<=f}return v?qe(qe({},n),{},{radius:o,angle:Py(d,n)}):null},Ts=function(e){return!N.isValidElement(e)&&!K(e)&&typeof e!="boolean"?e.className:""};function nn(t){"@babel/helpers - typeof";return nn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},nn(t)}var Sy=["offset"];function jy(t){return Ty(t)||_y(t)||$y(t)||Ey()}function Ey(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function $y(t,e){if(t){if(typeof t=="string")return Va(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);if(n==="Object"&&t.constructor&&(n=t.constructor.name),n==="Map"||n==="Set")return Array.from(t);if(n==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return Va(t,e)}}function _y(t){if(typeof Symbol<"u"&&t[Symbol.iterator]!=null||t["@@iterator"]!=null)return Array.from(t)}function Ty(t){if(Array.isArray(t))return Va(t)}function Va(t,e){(e==null||e>t.length)&&(e=t.length);for(var n=0,r=new Array(e);n<e;n++)r[n]=t[n];return r}function Iy(t,e){if(t==null)return{};var n=ky(t,e),r,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(t);for(i=0;i<a.length;i++)r=a[i],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(n[r]=t[r])}return n}function ky(t,e){if(t==null)return{};var n={};for(var r in t)if(Object.prototype.hasOwnProperty.call(t,r)){if(e.indexOf(r)>=0)continue;n[r]=t[r]}return n}function Qc(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter(function(i){return Object.getOwnPropertyDescriptor(t,i).enumerable})),n.push.apply(n,r)}return n}function se(t){for(var e=1;e<arguments.length;e++){var n=arguments[e]!=null?arguments[e]:{};e%2?Qc(Object(n),!0).forEach(function(r){Dy(t,r,n[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):Qc(Object(n)).forEach(function(r){Object.defineProperty(t,r,Object.getOwnPropertyDescriptor(n,r))})}return t}function Dy(t,e,n){return e=Cy(e),e in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function Cy(t){var e=My(t,"string");return nn(e)=="symbol"?e:e+""}function My(t,e){if(nn(t)!="object"||!t)return t;var n=t[Symbol.toPrimitive];if(n!==void 0){var r=n.call(t,e);if(nn(r)!="object")return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(t)}function an(){return an=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var n=arguments[e];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(t[r]=n[r])}return t},an.apply(this,arguments)}var Ny=function(e){var n=e.value,r=e.formatter,i=G(e.children)?n:e.children;return K(r)?r(i):i},Ry=function(e,n){var r=ye(n-e),i=Math.min(Math.abs(n-e),360);return r*i},By=function(e,n,r){var i=e.position,a=e.viewBox,o=e.offset,c=e.className,l=a,u=l.cx,s=l.cy,f=l.innerRadius,p=l.outerRadius,d=l.startAngle,v=l.endAngle,h=l.clockWise,m=(f+p)/2,O=Ry(d,v),w=O>=0?1:-1,x,A;i==="insideStart"?(x=d+w*o,A=h):i==="insideEnd"?(x=v-w*o,A=!h):i==="end"&&(x=v+w*o,A=h),A=O<=0?A:!A;var y=J(u,s,m,x),g=J(u,s,m,x+(A?1:-1)*359),P="M".concat(y.x,",").concat(y.y,`
    A`).concat(m,",").concat(m,",0,1,").concat(A?0:1,`,
    `).concat(g.x,",").concat(g.y),S=G(e.id)?nt("recharts-radial-line-"):e.id;return b.createElement("text",an({},r,{dominantBaseline:"central",className:X("recharts-radial-bar-label",c)}),b.createElement("defs",null,b.createElement("path",{id:S,d:P})),b.createElement("textPath",{xlinkHref:"#".concat(S)},n))},Ly=function(e){var n=e.viewBox,r=e.offset,i=e.position,a=n,o=a.cx,c=a.cy,l=a.innerRadius,u=a.outerRadius,s=a.startAngle,f=a.endAngle,p=(s+f)/2;if(i==="outside"){var d=J(o,c,u+r,p),v=d.x,h=d.y;return{x:v,y:h,textAnchor:v>=o?"start":"end",verticalAnchor:"middle"}}if(i==="center")return{x:o,y:c,textAnchor:"middle",verticalAnchor:"middle"};if(i==="centerTop")return{x:o,y:c,textAnchor:"middle",verticalAnchor:"start"};if(i==="centerBottom")return{x:o,y:c,textAnchor:"middle",verticalAnchor:"end"};var m=(l+u)/2,O=J(o,c,m,p),w=O.x,x=O.y;return{x:w,y:x,textAnchor:"middle",verticalAnchor:"middle"}},Wy=function(e){var n=e.viewBox,r=e.parentViewBox,i=e.offset,a=e.position,o=n,c=o.x,l=o.y,u=o.width,s=o.height,f=s>=0?1:-1,p=f*i,d=f>0?"end":"start",v=f>0?"start":"end",h=u>=0?1:-1,m=h*i,O=h>0?"end":"start",w=h>0?"start":"end";if(a==="top"){var x={x:c+u/2,y:l-f*i,textAnchor:"middle",verticalAnchor:d};return se(se({},x),r?{height:Math.max(l-r.y,0),width:u}:{})}if(a==="bottom"){var A={x:c+u/2,y:l+s+p,textAnchor:"middle",verticalAnchor:v};return se(se({},A),r?{height:Math.max(r.y+r.height-(l+s),0),width:u}:{})}if(a==="left"){var y={x:c-m,y:l+s/2,textAnchor:O,verticalAnchor:"middle"};return se(se({},y),r?{width:Math.max(y.x-r.x,0),height:s}:{})}if(a==="right"){var g={x:c+u+m,y:l+s/2,textAnchor:w,verticalAnchor:"middle"};return se(se({},g),r?{width:Math.max(r.x+r.width-g.x,0),height:s}:{})}var P=r?{width:u,height:s}:{};return a==="insideLeft"?se({x:c+m,y:l+s/2,textAnchor:w,verticalAnchor:"middle"},P):a==="insideRight"?se({x:c+u-m,y:l+s/2,textAnchor:O,verticalAnchor:"middle"},P):a==="insideTop"?se({x:c+u/2,y:l+p,textAnchor:"middle",verticalAnchor:v},P):a==="insideBottom"?se({x:c+u/2,y:l+s-p,textAnchor:"middle",verticalAnchor:d},P):a==="insideTopLeft"?se({x:c+m,y:l+p,textAnchor:w,verticalAnchor:v},P):a==="insideTopRight"?se({x:c+u-m,y:l+p,textAnchor:O,verticalAnchor:v},P):a==="insideBottomLeft"?se({x:c+m,y:l+s-p,textAnchor:w,verticalAnchor:d},P):a==="insideBottomRight"?se({x:c+u-m,y:l+s-p,textAnchor:O,verticalAnchor:d},P):$r(a)&&(R(a.x)||Et(a.x))&&(R(a.y)||Et(a.y))?se({x:c+Pe(a.x,u),y:l+Pe(a.y,s),textAnchor:"end",verticalAnchor:"end"},P):se({x:c+u/2,y:l+s/2,textAnchor:"middle",verticalAnchor:"middle"},P)},zy=function(e){return"cx"in e&&R(e.cx)};function ve(t){var e=t.offset,n=e===void 0?5:e,r=Iy(t,Sy),i=se({offset:n},r),a=i.viewBox,o=i.position,c=i.value,l=i.children,u=i.content,s=i.className,f=s===void 0?"":s,p=i.textBreakAll;if(!a||G(c)&&G(l)&&!N.isValidElement(u)&&!K(u))return null;if(N.isValidElement(u))return N.cloneElement(u,i);var d;if(K(u)){if(d=N.createElement(u,i),N.isValidElement(d))return d}else d=Ny(i);var v=zy(a),h=L(i,!0);if(v&&(o==="insideStart"||o==="insideEnd"||o==="end"))return By(i,d,h);var m=v?Ly(i):Wy(i);return b.createElement(dt,an({className:X("recharts-label",f)},h,m,{breakAll:p}),d)}ve.displayName="Label";var Is=function(e){var n=e.cx,r=e.cy,i=e.angle,a=e.startAngle,o=e.endAngle,c=e.r,l=e.radius,u=e.innerRadius,s=e.outerRadius,f=e.x,p=e.y,d=e.top,v=e.left,h=e.width,m=e.height,O=e.clockWise,w=e.labelViewBox;if(w)return w;if(R(h)&&R(m)){if(R(f)&&R(p))return{x:f,y:p,width:h,height:m};if(R(d)&&R(v))return{x:d,y:v,width:h,height:m}}return R(f)&&R(p)?{x:f,y:p,width:0,height:0}:R(n)&&R(r)?{cx:n,cy:r,startAngle:a||i||0,endAngle:o||i||0,innerRadius:u||0,outerRadius:s||l||c||0,clockWise:O}:e.viewBox?e.viewBox:{}},Ky=function(e,n){return e?e===!0?b.createElement(ve,{key:"label-implicit",viewBox:n}):pe(e)?b.createElement(ve,{key:"label-implicit",viewBox:n,value:e}):N.isValidElement(e)?e.type===ve?N.cloneElement(e,{key:"label-implicit",viewBox:n}):b.createElement(ve,{key:"label-implicit",content:e,viewBox:n}):K(e)?b.createElement(ve,{key:"label-implicit",content:e,viewBox:n}):$r(e)?b.createElement(ve,an({viewBox:n},e,{key:"label-implicit"})):null:null},Fy=function(e,n){var r=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!0;if(!e||!e.children&&r&&!e.label)return null;var i=e.children,a=Is(e),o=ge(i,ve).map(function(l,u){return N.cloneElement(l,{viewBox:n||a,key:"label-".concat(u)})});if(!r)return o;var c=Ky(e.label,n||a);return[c].concat(jy(o))};ve.parseViewBox=Is;ve.renderCallByParent=Fy;function on(t){"@babel/helpers - typeof";return on=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},on(t)}var Vy=["valueAccessor"],Xy=["data","dataKey","clockWise","id","textBreakAll"];function Gy(t){return qy(t)||Yy(t)||Uy(t)||Hy()}function Hy(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function Uy(t,e){if(t){if(typeof t=="string")return Xa(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);if(n==="Object"&&t.constructor&&(n=t.constructor.name),n==="Map"||n==="Set")return Array.from(t);if(n==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return Xa(t,e)}}function Yy(t){if(typeof Symbol<"u"&&t[Symbol.iterator]!=null||t["@@iterator"]!=null)return Array.from(t)}function qy(t){if(Array.isArray(t))return Xa(t)}function Xa(t,e){(e==null||e>t.length)&&(e=t.length);for(var n=0,r=new Array(e);n<e;n++)r[n]=t[n];return r}function ii(){return ii=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var n=arguments[e];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(t[r]=n[r])}return t},ii.apply(this,arguments)}function el(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter(function(i){return Object.getOwnPropertyDescriptor(t,i).enumerable})),n.push.apply(n,r)}return n}function tl(t){for(var e=1;e<arguments.length;e++){var n=arguments[e]!=null?arguments[e]:{};e%2?el(Object(n),!0).forEach(function(r){Zy(t,r,n[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):el(Object(n)).forEach(function(r){Object.defineProperty(t,r,Object.getOwnPropertyDescriptor(n,r))})}return t}function Zy(t,e,n){return e=Jy(e),e in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function Jy(t){var e=Qy(t,"string");return on(e)=="symbol"?e:e+""}function Qy(t,e){if(on(t)!="object"||!t)return t;var n=t[Symbol.toPrimitive];if(n!==void 0){var r=n.call(t,e);if(on(r)!="object")return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(t)}function rl(t,e){if(t==null)return{};var n=em(t,e),r,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(t);for(i=0;i<a.length;i++)r=a[i],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(n[r]=t[r])}return n}function em(t,e){if(t==null)return{};var n={};for(var r in t)if(Object.prototype.hasOwnProperty.call(t,r)){if(e.indexOf(r)>=0)continue;n[r]=t[r]}return n}var tm=function(e){return Array.isArray(e.value)?Bu(e.value):e.value};function Ee(t){var e=t.valueAccessor,n=e===void 0?tm:e,r=rl(t,Vy),i=r.data,a=r.dataKey,o=r.clockWise,c=r.id,l=r.textBreakAll,u=rl(r,Xy);return!i||!i.length?null:b.createElement(F,{className:"recharts-label-list"},i.map(function(s,f){var p=G(a)?n(s,f):Z(s&&s.payload,a),d=G(c)?{}:{id:"".concat(c,"-").concat(f)};return b.createElement(ve,ii({},L(s,!0),u,d,{parentViewBox:s.parentViewBox,value:p,textBreakAll:l,viewBox:ve.parseViewBox(G(o)?s:tl(tl({},s),{},{clockWise:o})),key:"label-".concat(f),index:f}))}))}Ee.displayName="LabelList";function rm(t,e){return t?t===!0?b.createElement(Ee,{key:"labelList-implicit",data:e}):b.isValidElement(t)||K(t)?b.createElement(Ee,{key:"labelList-implicit",data:e,content:t}):$r(t)?b.createElement(Ee,ii({data:e},t,{key:"labelList-implicit"})):null:null}function nm(t,e){var n=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!0;if(!t||!t.children&&n&&!t.label)return null;var r=t.children,i=ge(r,Ee).map(function(o,c){return N.cloneElement(o,{data:e,key:"labelList-".concat(c)})});if(!n)return i;var a=rm(t.label,e);return[a].concat(Gy(i))}Ee.renderCallByParent=nm;var im=["component"];function Ga(t){"@babel/helpers - typeof";return Ga=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Ga(t)}function am(t,e){if(t==null)return{};var n=om(t,e),r,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(t);for(i=0;i<a.length;i++)r=a[i],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(n[r]=t[r])}return n}function om(t,e){if(t==null)return{};var n={};for(var r in t)if(Object.prototype.hasOwnProperty.call(t,r)){if(e.indexOf(r)>=0)continue;n[r]=t[r]}return n}function cm(t){var e=t.component,n=am(t,im),r;return N.isValidElement(e)?r=N.cloneElement(e,n):K(e)?r=N.createElement(e,n):Ne(!1,"Customized's props `component` must be React.element or Function, but got %s.",Ga(e)),b.createElement(F,{className:"recharts-customized-wrapper"},r)}cm.displayName="Customized";function cn(t){"@babel/helpers - typeof";return cn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},cn(t)}function Ha(){return Ha=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var n=arguments[e];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(t[r]=n[r])}return t},Ha.apply(this,arguments)}function nl(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter(function(i){return Object.getOwnPropertyDescriptor(t,i).enumerable})),n.push.apply(n,r)}return n}function il(t){for(var e=1;e<arguments.length;e++){var n=arguments[e]!=null?arguments[e]:{};e%2?nl(Object(n),!0).forEach(function(r){lm(t,r,n[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):nl(Object(n)).forEach(function(r){Object.defineProperty(t,r,Object.getOwnPropertyDescriptor(n,r))})}return t}function lm(t,e,n){return e=um(e),e in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function um(t){var e=sm(t,"string");return cn(e)=="symbol"?e:e+""}function sm(t,e){if(cn(t)!="object"||!t)return t;var n=t[Symbol.toPrimitive];if(n!==void 0){var r=n.call(t,e);if(cn(r)!="object")return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(t)}var fm=function(e,n){var r=ye(n-e),i=Math.min(Math.abs(n-e),359.999);return r*i},Bn=function(e){var n=e.cx,r=e.cy,i=e.radius,a=e.angle,o=e.sign,c=e.isExternal,l=e.cornerRadius,u=e.cornerIsExternal,s=l*(c?1:-1)+i,f=Math.asin(l/s)/ni,p=u?a:a+o*f,d=J(n,r,s,p),v=J(n,r,i,p),h=u?a-o*f:a,m=J(n,r,s*Math.cos(f*ni),h);return{center:d,circleTangency:v,lineTangency:m,theta:f}},ks=function(e){var n=e.cx,r=e.cy,i=e.innerRadius,a=e.outerRadius,o=e.startAngle,c=e.endAngle,l=fm(o,c),u=o+l,s=J(n,r,a,o),f=J(n,r,a,u),p="M ".concat(s.x,",").concat(s.y,`
    A `).concat(a,",").concat(a,`,0,
    `).concat(+(Math.abs(l)>180),",").concat(+(o>u),`,
    `).concat(f.x,",").concat(f.y,`
  `);if(i>0){var d=J(n,r,i,o),v=J(n,r,i,u);p+="L ".concat(v.x,",").concat(v.y,`
            A `).concat(i,",").concat(i,`,0,
            `).concat(+(Math.abs(l)>180),",").concat(+(o<=u),`,
            `).concat(d.x,",").concat(d.y," Z")}else p+="L ".concat(n,",").concat(r," Z");return p},pm=function(e){var n=e.cx,r=e.cy,i=e.innerRadius,a=e.outerRadius,o=e.cornerRadius,c=e.forceCornerRadius,l=e.cornerIsExternal,u=e.startAngle,s=e.endAngle,f=ye(s-u),p=Bn({cx:n,cy:r,radius:a,angle:u,sign:f,cornerRadius:o,cornerIsExternal:l}),d=p.circleTangency,v=p.lineTangency,h=p.theta,m=Bn({cx:n,cy:r,radius:a,angle:s,sign:-f,cornerRadius:o,cornerIsExternal:l}),O=m.circleTangency,w=m.lineTangency,x=m.theta,A=l?Math.abs(u-s):Math.abs(u-s)-h-x;if(A<0)return c?"M ".concat(v.x,",").concat(v.y,`
        a`).concat(o,",").concat(o,",0,0,1,").concat(o*2,`,0
        a`).concat(o,",").concat(o,",0,0,1,").concat(-o*2,`,0
      `):ks({cx:n,cy:r,innerRadius:i,outerRadius:a,startAngle:u,endAngle:s});var y="M ".concat(v.x,",").concat(v.y,`
    A`).concat(o,",").concat(o,",0,0,").concat(+(f<0),",").concat(d.x,",").concat(d.y,`
    A`).concat(a,",").concat(a,",0,").concat(+(A>180),",").concat(+(f<0),",").concat(O.x,",").concat(O.y,`
    A`).concat(o,",").concat(o,",0,0,").concat(+(f<0),",").concat(w.x,",").concat(w.y,`
  `);if(i>0){var g=Bn({cx:n,cy:r,radius:i,angle:u,sign:f,isExternal:!0,cornerRadius:o,cornerIsExternal:l}),P=g.circleTangency,S=g.lineTangency,j=g.theta,E=Bn({cx:n,cy:r,radius:i,angle:s,sign:-f,isExternal:!0,cornerRadius:o,cornerIsExternal:l}),$=E.circleTangency,_=E.lineTangency,k=E.theta,D=l?Math.abs(u-s):Math.abs(u-s)-j-k;if(D<0&&o===0)return"".concat(y,"L").concat(n,",").concat(r,"Z");y+="L".concat(_.x,",").concat(_.y,`
      A`).concat(o,",").concat(o,",0,0,").concat(+(f<0),",").concat($.x,",").concat($.y,`
      A`).concat(i,",").concat(i,",0,").concat(+(D>180),",").concat(+(f>0),",").concat(P.x,",").concat(P.y,`
      A`).concat(o,",").concat(o,",0,0,").concat(+(f<0),",").concat(S.x,",").concat(S.y,"Z")}else y+="L".concat(n,",").concat(r,"Z");return y},dm={cx:0,cy:0,innerRadius:0,outerRadius:0,startAngle:0,endAngle:0,cornerRadius:0,forceCornerRadius:!1,cornerIsExternal:!1},Fo=function(e){var n=il(il({},dm),e),r=n.cx,i=n.cy,a=n.innerRadius,o=n.outerRadius,c=n.cornerRadius,l=n.forceCornerRadius,u=n.cornerIsExternal,s=n.startAngle,f=n.endAngle,p=n.className;if(o<a||s===f)return null;var d=X("recharts-sector",p),v=o-a,h=Pe(c,v,0,!0),m;return h>0&&Math.abs(s-f)<360?m=pm({cx:r,cy:i,innerRadius:a,outerRadius:o,cornerRadius:Math.min(h,v/2),forceCornerRadius:l,cornerIsExternal:u,startAngle:s,endAngle:f}):m=ks({cx:r,cy:i,innerRadius:a,outerRadius:o,startAngle:s,endAngle:f}),b.createElement("path",Ha({},L(n,!0),{className:d,d:m,role:"img"}))};function ln(t){"@babel/helpers - typeof";return ln=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},ln(t)}function Ua(){return Ua=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var n=arguments[e];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(t[r]=n[r])}return t},Ua.apply(this,arguments)}function al(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter(function(i){return Object.getOwnPropertyDescriptor(t,i).enumerable})),n.push.apply(n,r)}return n}function ol(t){for(var e=1;e<arguments.length;e++){var n=arguments[e]!=null?arguments[e]:{};e%2?al(Object(n),!0).forEach(function(r){vm(t,r,n[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):al(Object(n)).forEach(function(r){Object.defineProperty(t,r,Object.getOwnPropertyDescriptor(n,r))})}return t}function vm(t,e,n){return e=hm(e),e in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function hm(t){var e=ym(t,"string");return ln(e)=="symbol"?e:e+""}function ym(t,e){if(ln(t)!="object"||!t)return t;var n=t[Symbol.toPrimitive];if(n!==void 0){var r=n.call(t,e);if(ln(r)!="object")return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(t)}var cl={curveBasisClosed:Ep,curveBasisOpen:jp,curveBasis:Sp,curveBumpX:Pp,curveBumpY:Ap,curveLinearClosed:wp,curveLinear:Lu,curveMonotoneX:xp,curveMonotoneY:Op,curveNatural:bp,curveStep:gp,curveStepAfter:mp,curveStepBefore:yp},Ln=function(e){return e.x===+e.x&&e.y===+e.y},Rr=function(e){return e.x},Br=function(e){return e.y},mm=function(e,n){if(K(e))return e;var r="curve".concat(Ri(e));return(r==="curveMonotone"||r==="curveBump")&&n?cl["".concat(r).concat(n==="vertical"?"Y":"X")]:cl[r]||Lu},gm=function(e){var n=e.type,r=n===void 0?"linear":n,i=e.points,a=i===void 0?[]:i,o=e.baseLine,c=e.layout,l=e.connectNulls,u=l===void 0?!1:l,s=mm(r,c),f=u?a.filter(function(h){return Ln(h)}):a,p;if(Array.isArray(o)){var d=u?o.filter(function(h){return Ln(h)}):o,v=f.map(function(h,m){return ol(ol({},h),{},{base:d[m]})});return c==="vertical"?p=Dn().y(Br).x1(Rr).x0(function(h){return h.base.x}):p=Dn().x(Rr).y1(Br).y0(function(h){return h.base.y}),p.defined(Ln).curve(s),p(v)}return c==="vertical"&&R(o)?p=Dn().y(Br).x1(Rr).x0(o):R(o)?p=Dn().x(Rr).y1(Br).y0(o):p=hp().x(Rr).y(Br),p.defined(Ln).curve(s),p(f)},ft=function(e){var n=e.className,r=e.points,i=e.path,a=e.pathRef;if((!r||!r.length)&&!i)return null;var o=r&&r.length?gm(e):i;return N.createElement("path",Ua({},L(e,!1),Gn(e),{className:X("recharts-curve",n),d:o,ref:a}))};function un(t){"@babel/helpers - typeof";return un=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},un(t)}function ai(){return ai=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var n=arguments[e];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(t[r]=n[r])}return t},ai.apply(this,arguments)}function bm(t,e){return Am(t)||wm(t,e)||xm(t,e)||Om()}function Om(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function xm(t,e){if(t){if(typeof t=="string")return ll(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);if(n==="Object"&&t.constructor&&(n=t.constructor.name),n==="Map"||n==="Set")return Array.from(t);if(n==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return ll(t,e)}}function ll(t,e){(e==null||e>t.length)&&(e=t.length);for(var n=0,r=new Array(e);n<e;n++)r[n]=t[n];return r}function wm(t,e){var n=t==null?null:typeof Symbol<"u"&&t[Symbol.iterator]||t["@@iterator"];if(n!=null){var r,i,a,o,c=[],l=!0,u=!1;try{if(a=(n=n.call(t)).next,e!==0)for(;!(l=(r=a.call(n)).done)&&(c.push(r.value),c.length!==e);l=!0);}catch(s){u=!0,i=s}finally{try{if(!l&&n.return!=null&&(o=n.return(),Object(o)!==o))return}finally{if(u)throw i}}return c}}function Am(t){if(Array.isArray(t))return t}function ul(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter(function(i){return Object.getOwnPropertyDescriptor(t,i).enumerable})),n.push.apply(n,r)}return n}function sl(t){for(var e=1;e<arguments.length;e++){var n=arguments[e]!=null?arguments[e]:{};e%2?ul(Object(n),!0).forEach(function(r){Pm(t,r,n[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):ul(Object(n)).forEach(function(r){Object.defineProperty(t,r,Object.getOwnPropertyDescriptor(n,r))})}return t}function Pm(t,e,n){return e=Sm(e),e in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function Sm(t){var e=jm(t,"string");return un(e)=="symbol"?e:e+""}function jm(t,e){if(un(t)!="object"||!t)return t;var n=t[Symbol.toPrimitive];if(n!==void 0){var r=n.call(t,e);if(un(r)!="object")return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(t)}var fl=function(e,n,r,i,a){var o=Math.min(Math.abs(r)/2,Math.abs(i)/2),c=i>=0?1:-1,l=r>=0?1:-1,u=i>=0&&r>=0||i<0&&r<0?1:0,s;if(o>0&&a instanceof Array){for(var f=[0,0,0,0],p=0,d=4;p<d;p++)f[p]=a[p]>o?o:a[p];s="M".concat(e,",").concat(n+c*f[0]),f[0]>0&&(s+="A ".concat(f[0],",").concat(f[0],",0,0,").concat(u,",").concat(e+l*f[0],",").concat(n)),s+="L ".concat(e+r-l*f[1],",").concat(n),f[1]>0&&(s+="A ".concat(f[1],",").concat(f[1],",0,0,").concat(u,`,
        `).concat(e+r,",").concat(n+c*f[1])),s+="L ".concat(e+r,",").concat(n+i-c*f[2]),f[2]>0&&(s+="A ".concat(f[2],",").concat(f[2],",0,0,").concat(u,`,
        `).concat(e+r-l*f[2],",").concat(n+i)),s+="L ".concat(e+l*f[3],",").concat(n+i),f[3]>0&&(s+="A ".concat(f[3],",").concat(f[3],",0,0,").concat(u,`,
        `).concat(e,",").concat(n+i-c*f[3])),s+="Z"}else if(o>0&&a===+a&&a>0){var v=Math.min(o,a);s="M ".concat(e,",").concat(n+c*v,`
            A `).concat(v,",").concat(v,",0,0,").concat(u,",").concat(e+l*v,",").concat(n,`
            L `).concat(e+r-l*v,",").concat(n,`
            A `).concat(v,",").concat(v,",0,0,").concat(u,",").concat(e+r,",").concat(n+c*v,`
            L `).concat(e+r,",").concat(n+i-c*v,`
            A `).concat(v,",").concat(v,",0,0,").concat(u,",").concat(e+r-l*v,",").concat(n+i,`
            L `).concat(e+l*v,",").concat(n+i,`
            A `).concat(v,",").concat(v,",0,0,").concat(u,",").concat(e,",").concat(n+i-c*v," Z")}else s="M ".concat(e,",").concat(n," h ").concat(r," v ").concat(i," h ").concat(-r," Z");return s},Em=function(e,n){if(!e||!n)return!1;var r=e.x,i=e.y,a=n.x,o=n.y,c=n.width,l=n.height;if(Math.abs(c)>0&&Math.abs(l)>0){var u=Math.min(a,a+c),s=Math.max(a,a+c),f=Math.min(o,o+l),p=Math.max(o,o+l);return r>=u&&r<=s&&i>=f&&i<=p}return!1},$m={x:0,y:0,width:0,height:0,radius:0,isAnimationActive:!1,isUpdateAnimationActive:!1,animationBegin:0,animationDuration:1500,animationEasing:"ease"},En=function(e){var n=sl(sl({},$m),e),r=N.useRef(),i=N.useState(-1),a=bm(i,2),o=a[0],c=a[1];N.useEffect(function(){if(r.current&&r.current.getTotalLength)try{var A=r.current.getTotalLength();A&&c(A)}catch{}},[]);var l=n.x,u=n.y,s=n.width,f=n.height,p=n.radius,d=n.className,v=n.animationEasing,h=n.animationDuration,m=n.animationBegin,O=n.isAnimationActive,w=n.isUpdateAnimationActive;if(l!==+l||u!==+u||s!==+s||f!==+f||s===0||f===0)return null;var x=X("recharts-rectangle",d);return w?b.createElement($e,{canBegin:o>0,from:{width:s,height:f,x:l,y:u},to:{width:s,height:f,x:l,y:u},duration:h,animationEasing:v,isActive:w},function(A){var y=A.width,g=A.height,P=A.x,S=A.y;return b.createElement($e,{canBegin:o>0,from:"0px ".concat(o===-1?1:o,"px"),to:"".concat(o,"px 0px"),attributeName:"strokeDasharray",begin:m,duration:h,isActive:O,easing:v},b.createElement("path",ai({},L(n,!0),{className:x,d:fl(P,S,y,g,p),ref:r})))}):b.createElement("path",ai({},L(n,!0),{className:x,d:fl(l,u,s,f,p)}))},_m=["points","className","baseLinePoints","connectNulls"];function Vt(){return Vt=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var n=arguments[e];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(t[r]=n[r])}return t},Vt.apply(this,arguments)}function Tm(t,e){if(t==null)return{};var n=Im(t,e),r,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(t);for(i=0;i<a.length;i++)r=a[i],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(n[r]=t[r])}return n}function Im(t,e){if(t==null)return{};var n={};for(var r in t)if(Object.prototype.hasOwnProperty.call(t,r)){if(e.indexOf(r)>=0)continue;n[r]=t[r]}return n}function pl(t){return Mm(t)||Cm(t)||Dm(t)||km()}function km(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function Dm(t,e){if(t){if(typeof t=="string")return Ya(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);if(n==="Object"&&t.constructor&&(n=t.constructor.name),n==="Map"||n==="Set")return Array.from(t);if(n==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return Ya(t,e)}}function Cm(t){if(typeof Symbol<"u"&&t[Symbol.iterator]!=null||t["@@iterator"]!=null)return Array.from(t)}function Mm(t){if(Array.isArray(t))return Ya(t)}function Ya(t,e){(e==null||e>t.length)&&(e=t.length);for(var n=0,r=new Array(e);n<e;n++)r[n]=t[n];return r}var dl=function(e){return e&&e.x===+e.x&&e.y===+e.y},Nm=function(){var e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:[],n=[[]];return e.forEach(function(r){dl(r)?n[n.length-1].push(r):n[n.length-1].length>0&&n.push([])}),dl(e[0])&&n[n.length-1].push(e[0]),n[n.length-1].length<=0&&(n=n.slice(0,-1)),n},zr=function(e,n){var r=Nm(e);n&&(r=[r.reduce(function(a,o){return[].concat(pl(a),pl(o))},[])]);var i=r.map(function(a){return a.reduce(function(o,c,l){return"".concat(o).concat(l===0?"M":"L").concat(c.x,",").concat(c.y)},"")}).join("");return r.length===1?"".concat(i,"Z"):i},Rm=function(e,n,r){var i=zr(e,r);return"".concat(i.slice(-1)==="Z"?i.slice(0,-1):i,"L").concat(zr(n.reverse(),r).slice(1))},Vo=function(e){var n=e.points,r=e.className,i=e.baseLinePoints,a=e.connectNulls,o=Tm(e,_m);if(!n||!n.length)return null;var c=X("recharts-polygon",r);if(i&&i.length){var l=o.stroke&&o.stroke!=="none",u=Rm(n,i,a);return b.createElement("g",{className:c},b.createElement("path",Vt({},L(o,!0),{fill:u.slice(-1)==="Z"?o.fill:"none",stroke:"none",d:u})),l?b.createElement("path",Vt({},L(o,!0),{fill:"none",d:zr(n,a)})):null,l?b.createElement("path",Vt({},L(o,!0),{fill:"none",d:zr(i,a)})):null)}var s=zr(n,a);return b.createElement("path",Vt({},L(o,!0),{fill:s.slice(-1)==="Z"?o.fill:"none",className:c,d:s}))};function qa(){return qa=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var n=arguments[e];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(t[r]=n[r])}return t},qa.apply(this,arguments)}var Ir=function(e){var n=e.cx,r=e.cy,i=e.r,a=e.className,o=X("recharts-dot",a);return n===+n&&r===+r&&i===+i?N.createElement("circle",qa({},L(e,!1),Gn(e),{className:o,cx:n,cy:r,r:i})):null};function sn(t){"@babel/helpers - typeof";return sn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},sn(t)}var Bm=["x","y","top","left","width","height","className"];function Za(){return Za=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var n=arguments[e];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(t[r]=n[r])}return t},Za.apply(this,arguments)}function vl(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter(function(i){return Object.getOwnPropertyDescriptor(t,i).enumerable})),n.push.apply(n,r)}return n}function Lm(t){for(var e=1;e<arguments.length;e++){var n=arguments[e]!=null?arguments[e]:{};e%2?vl(Object(n),!0).forEach(function(r){Wm(t,r,n[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):vl(Object(n)).forEach(function(r){Object.defineProperty(t,r,Object.getOwnPropertyDescriptor(n,r))})}return t}function Wm(t,e,n){return e=zm(e),e in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function zm(t){var e=Km(t,"string");return sn(e)=="symbol"?e:e+""}function Km(t,e){if(sn(t)!="object"||!t)return t;var n=t[Symbol.toPrimitive];if(n!==void 0){var r=n.call(t,e);if(sn(r)!="object")return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(t)}function Fm(t,e){if(t==null)return{};var n=Vm(t,e),r,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(t);for(i=0;i<a.length;i++)r=a[i],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(n[r]=t[r])}return n}function Vm(t,e){if(t==null)return{};var n={};for(var r in t)if(Object.prototype.hasOwnProperty.call(t,r)){if(e.indexOf(r)>=0)continue;n[r]=t[r]}return n}var Xm=function(e,n,r,i,a,o){return"M".concat(e,",").concat(a,"v").concat(i,"M").concat(o,",").concat(n,"h").concat(r)},Gm=function(e){var n=e.x,r=n===void 0?0:n,i=e.y,a=i===void 0?0:i,o=e.top,c=o===void 0?0:o,l=e.left,u=l===void 0?0:l,s=e.width,f=s===void 0?0:s,p=e.height,d=p===void 0?0:p,v=e.className,h=Fm(e,Bm),m=Lm({x:r,y:a,top:c,left:u,width:f,height:d},h);return!R(r)||!R(a)||!R(f)||!R(d)||!R(c)||!R(u)?null:b.createElement("path",Za({},L(m,!0),{className:X("recharts-cross",v),d:Xm(r,a,f,d,c,u)}))},Hm=["cx","cy","innerRadius","outerRadius","gridType","radialLines"];function fn(t){"@babel/helpers - typeof";return fn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},fn(t)}function Um(t,e){if(t==null)return{};var n=Ym(t,e),r,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(t);for(i=0;i<a.length;i++)r=a[i],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(n[r]=t[r])}return n}function Ym(t,e){if(t==null)return{};var n={};for(var r in t)if(Object.prototype.hasOwnProperty.call(t,r)){if(e.indexOf(r)>=0)continue;n[r]=t[r]}return n}function rt(){return rt=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var n=arguments[e];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(t[r]=n[r])}return t},rt.apply(this,arguments)}function hl(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter(function(i){return Object.getOwnPropertyDescriptor(t,i).enumerable})),n.push.apply(n,r)}return n}function pn(t){for(var e=1;e<arguments.length;e++){var n=arguments[e]!=null?arguments[e]:{};e%2?hl(Object(n),!0).forEach(function(r){qm(t,r,n[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):hl(Object(n)).forEach(function(r){Object.defineProperty(t,r,Object.getOwnPropertyDescriptor(n,r))})}return t}function qm(t,e,n){return e=Zm(e),e in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function Zm(t){var e=Jm(t,"string");return fn(e)=="symbol"?e:e+""}function Jm(t,e){if(fn(t)!="object"||!t)return t;var n=t[Symbol.toPrimitive];if(n!==void 0){var r=n.call(t,e);if(fn(r)!="object")return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(t)}var Qm=function(e,n,r,i){var a="";return i.forEach(function(o,c){var l=J(n,r,e,o);c?a+="L ".concat(l.x,",").concat(l.y):a+="M ".concat(l.x,",").concat(l.y)}),a+="Z",a},eg=function(e){var n=e.cx,r=e.cy,i=e.innerRadius,a=e.outerRadius,o=e.polarAngles,c=e.radialLines;if(!o||!o.length||!c)return null;var l=pn({stroke:"#ccc"},L(e,!1));return b.createElement("g",{className:"recharts-polar-grid-angle"},o.map(function(u){var s=J(n,r,i,u),f=J(n,r,a,u);return b.createElement("line",rt({},l,{key:"line-".concat(u),x1:s.x,y1:s.y,x2:f.x,y2:f.y}))}))},tg=function(e){var n=e.cx,r=e.cy,i=e.radius,a=e.index,o=pn(pn({stroke:"#ccc"},L(e,!1)),{},{fill:"none"});return b.createElement("circle",rt({},o,{className:X("recharts-polar-grid-concentric-circle",e.className),key:"circle-".concat(a),cx:n,cy:r,r:i}))},rg=function(e){var n=e.radius,r=e.index,i=pn(pn({stroke:"#ccc"},L(e,!1)),{},{fill:"none"});return b.createElement("path",rt({},i,{className:X("recharts-polar-grid-concentric-polygon",e.className),key:"path-".concat(r),d:Qm(n,e.cx,e.cy,e.polarAngles)}))},ng=function(e){var n=e.polarRadius,r=e.gridType;return!n||!n.length?null:b.createElement("g",{className:"recharts-polar-grid-concentric"},n.map(function(i,a){var o=a;return r==="circle"?b.createElement(tg,rt({key:o},e,{radius:i,index:a})):b.createElement(rg,rt({key:o},e,{radius:i,index:a}))}))},ig=function(e){var n=e.cx,r=n===void 0?0:n,i=e.cy,a=i===void 0?0:i,o=e.innerRadius,c=o===void 0?0:o,l=e.outerRadius,u=l===void 0?0:l,s=e.gridType,f=s===void 0?"polygon":s,p=e.radialLines,d=p===void 0?!0:p,v=Um(e,Hm);return u<=0?null:b.createElement("g",{className:"recharts-polar-grid"},b.createElement(eg,rt({cx:r,cy:a,innerRadius:c,outerRadius:u,gridType:f,radialLines:d},v)),b.createElement(ng,rt({cx:r,cy:a,innerRadius:c,outerRadius:u,gridType:f,radialLines:d},v)))};ig.displayName="PolarGrid";var ag=["cx","cy","angle","ticks","axisLine"],og=["ticks","tick","angle","tickFormatter","stroke"];function ir(t){"@babel/helpers - typeof";return ir=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},ir(t)}function Kr(){return Kr=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var n=arguments[e];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(t[r]=n[r])}return t},Kr.apply(this,arguments)}function yl(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter(function(i){return Object.getOwnPropertyDescriptor(t,i).enumerable})),n.push.apply(n,r)}return n}function At(t){for(var e=1;e<arguments.length;e++){var n=arguments[e]!=null?arguments[e]:{};e%2?yl(Object(n),!0).forEach(function(r){Fi(t,r,n[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):yl(Object(n)).forEach(function(r){Object.defineProperty(t,r,Object.getOwnPropertyDescriptor(n,r))})}return t}function ml(t,e){if(t==null)return{};var n=cg(t,e),r,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(t);for(i=0;i<a.length;i++)r=a[i],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(n[r]=t[r])}return n}function cg(t,e){if(t==null)return{};var n={};for(var r in t)if(Object.prototype.hasOwnProperty.call(t,r)){if(e.indexOf(r)>=0)continue;n[r]=t[r]}return n}function lg(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function gl(t,e){for(var n=0;n<e.length;n++){var r=e[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,Cs(r.key),r)}}function ug(t,e,n){return e&&gl(t.prototype,e),n&&gl(t,n),Object.defineProperty(t,"prototype",{writable:!1}),t}function sg(t,e,n){return e=oi(e),fg(t,Ds()?Reflect.construct(e,n||[],oi(t).constructor):e.apply(t,n))}function fg(t,e){if(e&&(ir(e)==="object"||typeof e=="function"))return e;if(e!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return pg(t)}function pg(t){if(t===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}function Ds(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(Ds=function(){return!!t})()}function oi(t){return oi=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(n){return n.__proto__||Object.getPrototypeOf(n)},oi(t)}function dg(t,e){if(typeof e!="function"&&e!==null)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&Ja(t,e)}function Ja(t,e){return Ja=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(r,i){return r.__proto__=i,r},Ja(t,e)}function Fi(t,e,n){return e=Cs(e),e in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function Cs(t){var e=vg(t,"string");return ir(e)=="symbol"?e:e+""}function vg(t,e){if(ir(t)!="object"||!t)return t;var n=t[Symbol.toPrimitive];if(n!==void 0){var r=n.call(t,e);if(ir(r)!="object")return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}var kr=function(t){function e(){return lg(this,e),sg(this,e,arguments)}return dg(e,t),ug(e,[{key:"getTickValueCoord",value:function(r){var i=r.coordinate,a=this.props,o=a.angle,c=a.cx,l=a.cy;return J(c,l,i,o)}},{key:"getTickTextAnchor",value:function(){var r=this.props.orientation,i;switch(r){case"left":i="end";break;case"right":i="start";break;default:i="middle";break}return i}},{key:"getViewBox",value:function(){var r=this.props,i=r.cx,a=r.cy,o=r.angle,c=r.ticks,l=Wu(c,function(s){return s.coordinate||0}),u=$p(c,function(s){return s.coordinate||0});return{cx:i,cy:a,startAngle:o,endAngle:o,innerRadius:u.coordinate||0,outerRadius:l.coordinate||0}}},{key:"renderAxisLine",value:function(){var r=this.props,i=r.cx,a=r.cy,o=r.angle,c=r.ticks,l=r.axisLine,u=ml(r,ag),s=c.reduce(function(v,h){return[Math.min(v[0],h.coordinate),Math.max(v[1],h.coordinate)]},[1/0,-1/0]),f=J(i,a,s[0],o),p=J(i,a,s[1],o),d=At(At(At({},L(u,!1)),{},{fill:"none"},L(l,!1)),{},{x1:f.x,y1:f.y,x2:p.x,y2:p.y});return b.createElement("line",Kr({className:"recharts-polar-radius-axis-line"},d))}},{key:"renderTicks",value:function(){var r=this,i=this.props,a=i.ticks,o=i.tick,c=i.angle,l=i.tickFormatter,u=i.stroke,s=ml(i,og),f=this.getTickTextAnchor(),p=L(s,!1),d=L(o,!1),v=a.map(function(h,m){var O=r.getTickValueCoord(h),w=At(At(At(At({textAnchor:f,transform:"rotate(".concat(90-c,", ").concat(O.x,", ").concat(O.y,")")},p),{},{stroke:"none",fill:u},d),{},{index:m},O),{},{payload:h});return b.createElement(F,Kr({className:X("recharts-polar-radius-axis-tick",Ts(o)),key:"tick-".concat(h.coordinate)},We(r.props,h,m)),e.renderTickItem(o,w,l?l(h.value,m):h.value))});return b.createElement(F,{className:"recharts-polar-radius-axis-ticks"},v)}},{key:"render",value:function(){var r=this.props,i=r.ticks,a=r.axisLine,o=r.tick;return!i||!i.length?null:b.createElement(F,{className:X("recharts-polar-radius-axis",this.props.className)},a&&this.renderAxisLine(),o&&this.renderTicks(),ve.renderCallByParent(this.props,this.getViewBox()))}}],[{key:"renderTickItem",value:function(r,i,a){var o;return b.isValidElement(r)?o=b.cloneElement(r,i):K(r)?o=r(i):o=b.createElement(dt,Kr({},i,{className:"recharts-polar-radius-axis-tick-value"}),a),o}}])}(N.PureComponent);Fi(kr,"displayName","PolarRadiusAxis");Fi(kr,"axisType","radiusAxis");Fi(kr,"defaultProps",{type:"number",radiusAxisId:0,cx:0,cy:0,angle:0,orientation:"right",stroke:"#ccc",axisLine:!0,tick:!0,tickCount:5,allowDataOverflow:!1,scale:"auto",allowDuplicatedCategory:!0});function ar(t){"@babel/helpers - typeof";return ar=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},ar(t)}function St(){return St=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var n=arguments[e];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(t[r]=n[r])}return t},St.apply(this,arguments)}function bl(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter(function(i){return Object.getOwnPropertyDescriptor(t,i).enumerable})),n.push.apply(n,r)}return n}function Pt(t){for(var e=1;e<arguments.length;e++){var n=arguments[e]!=null?arguments[e]:{};e%2?bl(Object(n),!0).forEach(function(r){Vi(t,r,n[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):bl(Object(n)).forEach(function(r){Object.defineProperty(t,r,Object.getOwnPropertyDescriptor(n,r))})}return t}function hg(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function Ol(t,e){for(var n=0;n<e.length;n++){var r=e[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,Ns(r.key),r)}}function yg(t,e,n){return e&&Ol(t.prototype,e),n&&Ol(t,n),Object.defineProperty(t,"prototype",{writable:!1}),t}function mg(t,e,n){return e=ci(e),gg(t,Ms()?Reflect.construct(e,n||[],ci(t).constructor):e.apply(t,n))}function gg(t,e){if(e&&(ar(e)==="object"||typeof e=="function"))return e;if(e!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return bg(t)}function bg(t){if(t===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}function Ms(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(Ms=function(){return!!t})()}function ci(t){return ci=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(n){return n.__proto__||Object.getPrototypeOf(n)},ci(t)}function Og(t,e){if(typeof e!="function"&&e!==null)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&Qa(t,e)}function Qa(t,e){return Qa=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(r,i){return r.__proto__=i,r},Qa(t,e)}function Vi(t,e,n){return e=Ns(e),e in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function Ns(t){var e=xg(t,"string");return ar(e)=="symbol"?e:e+""}function xg(t,e){if(ar(t)!="object"||!t)return t;var n=t[Symbol.toPrimitive];if(n!==void 0){var r=n.call(t,e);if(ar(r)!="object")return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}var wg=Math.PI/180,xl=1e-5,Dr=function(t){function e(){return hg(this,e),mg(this,e,arguments)}return Og(e,t),yg(e,[{key:"getTickLineCoord",value:function(r){var i=this.props,a=i.cx,o=i.cy,c=i.radius,l=i.orientation,u=i.tickSize,s=u||8,f=J(a,o,c,r.coordinate),p=J(a,o,c+(l==="inner"?-1:1)*s,r.coordinate);return{x1:f.x,y1:f.y,x2:p.x,y2:p.y}}},{key:"getTickTextAnchor",value:function(r){var i=this.props.orientation,a=Math.cos(-r.coordinate*wg),o;return a>xl?o=i==="outer"?"start":"end":a<-xl?o=i==="outer"?"end":"start":o="middle",o}},{key:"renderAxisLine",value:function(){var r=this.props,i=r.cx,a=r.cy,o=r.radius,c=r.axisLine,l=r.axisLineType,u=Pt(Pt({},L(this.props,!1)),{},{fill:"none"},L(c,!1));if(l==="circle")return b.createElement(Ir,St({className:"recharts-polar-angle-axis-line"},u,{cx:i,cy:a,r:o}));var s=this.props.ticks,f=s.map(function(p){return J(i,a,o,p.coordinate)});return b.createElement(Vo,St({className:"recharts-polar-angle-axis-line"},u,{points:f}))}},{key:"renderTicks",value:function(){var r=this,i=this.props,a=i.ticks,o=i.tick,c=i.tickLine,l=i.tickFormatter,u=i.stroke,s=L(this.props,!1),f=L(o,!1),p=Pt(Pt({},s),{},{fill:"none"},L(c,!1)),d=a.map(function(v,h){var m=r.getTickLineCoord(v),O=r.getTickTextAnchor(v),w=Pt(Pt(Pt({textAnchor:O},s),{},{stroke:"none",fill:u},f),{},{index:h,payload:v,x:m.x2,y:m.y2});return b.createElement(F,St({className:X("recharts-polar-angle-axis-tick",Ts(o)),key:"tick-".concat(v.coordinate)},We(r.props,v,h)),c&&b.createElement("line",St({className:"recharts-polar-angle-axis-tick-line"},p,m)),o&&e.renderTickItem(o,w,l?l(v.value,h):v.value))});return b.createElement(F,{className:"recharts-polar-angle-axis-ticks"},d)}},{key:"render",value:function(){var r=this.props,i=r.ticks,a=r.radius,o=r.axisLine;return a<=0||!i||!i.length?null:b.createElement(F,{className:X("recharts-polar-angle-axis",this.props.className)},o&&this.renderAxisLine(),this.renderTicks())}}],[{key:"renderTickItem",value:function(r,i,a){var o;return b.isValidElement(r)?o=b.cloneElement(r,i):K(r)?o=r(i):o=b.createElement(dt,St({},i,{className:"recharts-polar-angle-axis-tick-value"}),a),o}}])}(N.PureComponent);Vi(Dr,"displayName","PolarAngleAxis");Vi(Dr,"axisType","angleAxis");Vi(Dr,"defaultProps",{type:"category",angleAxisId:0,scale:"auto",cx:0,cy:0,orientation:"outer",axisLine:!0,tickLine:!0,tickSize:8,tick:!0,hide:!1,allowDuplicatedCategory:!0});function dn(t){"@babel/helpers - typeof";return dn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},dn(t)}function li(){return li=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var n=arguments[e];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(t[r]=n[r])}return t},li.apply(this,arguments)}function Ag(t,e){return Eg(t)||jg(t,e)||Sg(t,e)||Pg()}function Pg(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function Sg(t,e){if(t){if(typeof t=="string")return wl(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);if(n==="Object"&&t.constructor&&(n=t.constructor.name),n==="Map"||n==="Set")return Array.from(t);if(n==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return wl(t,e)}}function wl(t,e){(e==null||e>t.length)&&(e=t.length);for(var n=0,r=new Array(e);n<e;n++)r[n]=t[n];return r}function jg(t,e){var n=t==null?null:typeof Symbol<"u"&&t[Symbol.iterator]||t["@@iterator"];if(n!=null){var r,i,a,o,c=[],l=!0,u=!1;try{if(a=(n=n.call(t)).next,e!==0)for(;!(l=(r=a.call(n)).done)&&(c.push(r.value),c.length!==e);l=!0);}catch(s){u=!0,i=s}finally{try{if(!l&&n.return!=null&&(o=n.return(),Object(o)!==o))return}finally{if(u)throw i}}return c}}function Eg(t){if(Array.isArray(t))return t}function Al(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter(function(i){return Object.getOwnPropertyDescriptor(t,i).enumerable})),n.push.apply(n,r)}return n}function Pl(t){for(var e=1;e<arguments.length;e++){var n=arguments[e]!=null?arguments[e]:{};e%2?Al(Object(n),!0).forEach(function(r){$g(t,r,n[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):Al(Object(n)).forEach(function(r){Object.defineProperty(t,r,Object.getOwnPropertyDescriptor(n,r))})}return t}function $g(t,e,n){return e=_g(e),e in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function _g(t){var e=Tg(t,"string");return dn(e)=="symbol"?e:e+""}function Tg(t,e){if(dn(t)!="object"||!t)return t;var n=t[Symbol.toPrimitive];if(n!==void 0){var r=n.call(t,e);if(dn(r)!="object")return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(t)}var Sl=function(e,n,r,i,a){var o=r-i,c;return c="M ".concat(e,",").concat(n),c+="L ".concat(e+r,",").concat(n),c+="L ".concat(e+r-o/2,",").concat(n+a),c+="L ".concat(e+r-o/2-i,",").concat(n+a),c+="L ".concat(e,",").concat(n," Z"),c},Ig={x:0,y:0,upperWidth:0,lowerWidth:0,height:0,isUpdateAnimationActive:!1,animationBegin:0,animationDuration:1500,animationEasing:"ease"},kg=function(e){var n=Pl(Pl({},Ig),e),r=N.useRef(),i=N.useState(-1),a=Ag(i,2),o=a[0],c=a[1];N.useEffect(function(){if(r.current&&r.current.getTotalLength)try{var x=r.current.getTotalLength();x&&c(x)}catch{}},[]);var l=n.x,u=n.y,s=n.upperWidth,f=n.lowerWidth,p=n.height,d=n.className,v=n.animationEasing,h=n.animationDuration,m=n.animationBegin,O=n.isUpdateAnimationActive;if(l!==+l||u!==+u||s!==+s||f!==+f||p!==+p||s===0&&f===0||p===0)return null;var w=X("recharts-trapezoid",d);return O?b.createElement($e,{canBegin:o>0,from:{upperWidth:0,lowerWidth:0,height:p,x:l,y:u},to:{upperWidth:s,lowerWidth:f,height:p,x:l,y:u},duration:h,animationEasing:v,isActive:O},function(x){var A=x.upperWidth,y=x.lowerWidth,g=x.height,P=x.x,S=x.y;return b.createElement($e,{canBegin:o>0,from:"0px ".concat(o===-1?1:o,"px"),to:"".concat(o,"px 0px"),attributeName:"strokeDasharray",begin:m,duration:h,easing:v},b.createElement("path",li({},L(n,!0),{className:w,d:Sl(P,S,A,y,g),ref:r})))}):b.createElement("g",null,b.createElement("path",li({},L(n,!0),{className:w,d:Sl(l,u,s,f,p)})))},Dg=["option","shapeType","propTransformer","activeClassName","isActive"];function vn(t){"@babel/helpers - typeof";return vn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},vn(t)}function Cg(t,e){if(t==null)return{};var n=Mg(t,e),r,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(t);for(i=0;i<a.length;i++)r=a[i],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(n[r]=t[r])}return n}function Mg(t,e){if(t==null)return{};var n={};for(var r in t)if(Object.prototype.hasOwnProperty.call(t,r)){if(e.indexOf(r)>=0)continue;n[r]=t[r]}return n}function jl(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter(function(i){return Object.getOwnPropertyDescriptor(t,i).enumerable})),n.push.apply(n,r)}return n}function ui(t){for(var e=1;e<arguments.length;e++){var n=arguments[e]!=null?arguments[e]:{};e%2?jl(Object(n),!0).forEach(function(r){Ng(t,r,n[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):jl(Object(n)).forEach(function(r){Object.defineProperty(t,r,Object.getOwnPropertyDescriptor(n,r))})}return t}function Ng(t,e,n){return e=Rg(e),e in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function Rg(t){var e=Bg(t,"string");return vn(e)=="symbol"?e:e+""}function Bg(t,e){if(vn(t)!="object"||!t)return t;var n=t[Symbol.toPrimitive];if(n!==void 0){var r=n.call(t,e);if(vn(r)!="object")return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(t)}function Lg(t,e){return ui(ui({},e),t)}function Wg(t,e){return t==="symbols"}function El(t){var e=t.shapeType,n=t.elementProps;switch(e){case"rectangle":return b.createElement(En,n);case"trapezoid":return b.createElement(kg,n);case"sector":return b.createElement(Fo,n);case"symbols":if(Wg(e))return b.createElement(Li,n);break;default:return null}}function Rs(t){return N.isValidElement(t)?t.props:t}function or(t){var e=t.option,n=t.shapeType,r=t.propTransformer,i=r===void 0?Lg:r,a=t.activeClassName,o=a===void 0?"recharts-active-shape":a,c=t.isActive,l=Cg(t,Dg),u;if(N.isValidElement(e))u=N.cloneElement(e,ui(ui({},l),Rs(e)));else if(K(e))u=e(l);else if(_p(e)&&!Tp(e)){var s=i(e,l);u=b.createElement(El,{shapeType:n,elementProps:s})}else{var f=l;u=b.createElement(El,{shapeType:n,elementProps:f})}return c?b.createElement(F,{className:o},u):u}function Xi(t,e){return e!=null&&"trapezoids"in t.props}function Gi(t,e){return e!=null&&"sectors"in t.props}function hn(t,e){return e!=null&&"points"in t.props}function zg(t,e){var n,r,i=t.x===(e==null||(n=e.labelViewBox)===null||n===void 0?void 0:n.x)||t.x===e.x,a=t.y===(e==null||(r=e.labelViewBox)===null||r===void 0?void 0:r.y)||t.y===e.y;return i&&a}function Kg(t,e){var n=t.endAngle===e.endAngle,r=t.startAngle===e.startAngle;return n&&r}function Fg(t,e){var n=t.x===e.x,r=t.y===e.y,i=t.z===e.z;return n&&r&&i}function Vg(t,e){var n;return Xi(t,e)?n=zg:Gi(t,e)?n=Kg:hn(t,e)&&(n=Fg),n}function Xg(t,e){var n;return Xi(t,e)?n="trapezoids":Gi(t,e)?n="sectors":hn(t,e)&&(n="points"),n}function Gg(t,e){if(Xi(t,e)){var n;return(n=e.tooltipPayload)===null||n===void 0||(n=n[0])===null||n===void 0||(n=n.payload)===null||n===void 0?void 0:n.payload}if(Gi(t,e)){var r;return(r=e.tooltipPayload)===null||r===void 0||(r=r[0])===null||r===void 0||(r=r.payload)===null||r===void 0?void 0:r.payload}return hn(t,e)?e.payload:{}}function Hg(t){var e=t.activeTooltipItem,n=t.graphicalItem,r=t.itemData,i=Xg(n,e),a=Gg(n,e),o=r.filter(function(l,u){var s=Le(a,l),f=n.props[i].filter(function(v){var h=Vg(n,e);return h(v,e)}),p=n.props[i].indexOf(f[f.length-1]),d=u===p;return s&&d}),c=r.indexOf(o[o.length-1]);return c}var Fn;function cr(t){"@babel/helpers - typeof";return cr=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},cr(t)}function Xt(){return Xt=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var n=arguments[e];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(t[r]=n[r])}return t},Xt.apply(this,arguments)}function $l(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter(function(i){return Object.getOwnPropertyDescriptor(t,i).enumerable})),n.push.apply(n,r)}return n}function re(t){for(var e=1;e<arguments.length;e++){var n=arguments[e]!=null?arguments[e]:{};e%2?$l(Object(n),!0).forEach(function(r){Me(t,r,n[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):$l(Object(n)).forEach(function(r){Object.defineProperty(t,r,Object.getOwnPropertyDescriptor(n,r))})}return t}function Ug(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function _l(t,e){for(var n=0;n<e.length;n++){var r=e[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,Ls(r.key),r)}}function Yg(t,e,n){return e&&_l(t.prototype,e),n&&_l(t,n),Object.defineProperty(t,"prototype",{writable:!1}),t}function qg(t,e,n){return e=si(e),Zg(t,Bs()?Reflect.construct(e,n||[],si(t).constructor):e.apply(t,n))}function Zg(t,e){if(e&&(cr(e)==="object"||typeof e=="function"))return e;if(e!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return Jg(t)}function Jg(t){if(t===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}function Bs(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(Bs=function(){return!!t})()}function si(t){return si=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(n){return n.__proto__||Object.getPrototypeOf(n)},si(t)}function Qg(t,e){if(typeof e!="function"&&e!==null)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&eo(t,e)}function eo(t,e){return eo=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(r,i){return r.__proto__=i,r},eo(t,e)}function Me(t,e,n){return e=Ls(e),e in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function Ls(t){var e=eb(t,"string");return cr(e)=="symbol"?e:e+""}function eb(t,e){if(cr(t)!="object"||!t)return t;var n=t[Symbol.toPrimitive];if(n!==void 0){var r=n.call(t,e);if(cr(r)!="object")return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}var ht=function(t){function e(n){var r;return Ug(this,e),r=qg(this,e,[n]),Me(r,"pieRef",null),Me(r,"sectorRefs",[]),Me(r,"id",nt("recharts-pie-")),Me(r,"handleAnimationEnd",function(){var i=r.props.onAnimationEnd;r.setState({isAnimationFinished:!0}),K(i)&&i()}),Me(r,"handleAnimationStart",function(){var i=r.props.onAnimationStart;r.setState({isAnimationFinished:!1}),K(i)&&i()}),r.state={isAnimationFinished:!n.isAnimationActive,prevIsAnimationActive:n.isAnimationActive,prevAnimationId:n.animationId,sectorToFocus:0},r}return Qg(e,t),Yg(e,[{key:"isActiveIndex",value:function(r){var i=this.props.activeIndex;return Array.isArray(i)?i.indexOf(r)!==-1:r===i}},{key:"hasActiveIndex",value:function(){var r=this.props.activeIndex;return Array.isArray(r)?r.length!==0:r||r===0}},{key:"renderLabels",value:function(r){var i=this.props.isAnimationActive;if(i&&!this.state.isAnimationFinished)return null;var a=this.props,o=a.label,c=a.labelLine,l=a.dataKey,u=a.valueKey,s=L(this.props,!1),f=L(o,!1),p=L(c,!1),d=o&&o.offsetRadius||20,v=r.map(function(h,m){var O=(h.startAngle+h.endAngle)/2,w=J(h.cx,h.cy,h.outerRadius+d,O),x=re(re(re(re({},s),h),{},{stroke:"none"},f),{},{index:m,textAnchor:e.getTextAnchor(w.x,h.cx)},w),A=re(re(re(re({},s),h),{},{fill:"none",stroke:h.fill},p),{},{index:m,points:[J(h.cx,h.cy,h.outerRadius,O),w]}),y=l;return G(l)&&G(u)?y="value":G(l)&&(y=u),b.createElement(F,{key:"label-".concat(h.startAngle,"-").concat(h.endAngle,"-").concat(h.midAngle,"-").concat(m)},c&&e.renderLabelLineItem(c,A,"line"),e.renderLabelItem(o,x,Z(h,y)))});return b.createElement(F,{className:"recharts-pie-labels"},v)}},{key:"renderSectorsStatically",value:function(r){var i=this,a=this.props,o=a.activeShape,c=a.blendStroke,l=a.inactiveShape;return r.map(function(u,s){if(u?.startAngle===0&&u?.endAngle===0&&r.length!==1)return null;var f=i.isActiveIndex(s),p=l&&i.hasActiveIndex()?l:null,d=f?o:p,v=re(re({},u),{},{stroke:c?u.fill:u.stroke,tabIndex:-1});return b.createElement(F,Xt({ref:function(m){m&&!i.sectorRefs.includes(m)&&i.sectorRefs.push(m)},tabIndex:-1,className:"recharts-pie-sector"},We(i.props,u,s),{key:"sector-".concat(u?.startAngle,"-").concat(u?.endAngle,"-").concat(u.midAngle,"-").concat(s)}),b.createElement(or,Xt({option:d,isActive:f,shapeType:"sector"},v)))})}},{key:"renderSectorsWithAnimation",value:function(){var r=this,i=this.props,a=i.sectors,o=i.isAnimationActive,c=i.animationBegin,l=i.animationDuration,u=i.animationEasing,s=i.animationId,f=this.state,p=f.prevSectors,d=f.prevIsAnimationActive;return b.createElement($e,{begin:c,duration:l,isActive:o,easing:u,from:{t:0},to:{t:1},key:"pie-".concat(s,"-").concat(d),onAnimationStart:this.handleAnimationStart,onAnimationEnd:this.handleAnimationEnd},function(v){var h=v.t,m=[],O=a&&a[0],w=O.startAngle;return a.forEach(function(x,A){var y=p&&p[A],g=A>0?he(x,"paddingAngle",0):0;if(y){var P=q(y.endAngle-y.startAngle,x.endAngle-x.startAngle),S=re(re({},x),{},{startAngle:w+g,endAngle:w+P(h)+g});m.push(S),w=S.endAngle}else{var j=x.endAngle,E=x.startAngle,$=q(0,j-E),_=$(h),k=re(re({},x),{},{startAngle:w+g,endAngle:w+_+g});m.push(k),w=k.endAngle}}),b.createElement(F,null,r.renderSectorsStatically(m))})}},{key:"attachKeyboardHandlers",value:function(r){var i=this;r.onkeydown=function(a){if(!a.altKey)switch(a.key){case"ArrowLeft":{var o=++i.state.sectorToFocus%i.sectorRefs.length;i.sectorRefs[o].focus(),i.setState({sectorToFocus:o});break}case"ArrowRight":{var c=--i.state.sectorToFocus<0?i.sectorRefs.length-1:i.state.sectorToFocus%i.sectorRefs.length;i.sectorRefs[c].focus(),i.setState({sectorToFocus:c});break}case"Escape":{i.sectorRefs[i.state.sectorToFocus].blur(),i.setState({sectorToFocus:0});break}}}}},{key:"renderSectors",value:function(){var r=this.props,i=r.sectors,a=r.isAnimationActive,o=this.state.prevSectors;return a&&i&&i.length&&(!o||!Le(o,i))?this.renderSectorsWithAnimation():this.renderSectorsStatically(i)}},{key:"componentDidMount",value:function(){this.pieRef&&this.attachKeyboardHandlers(this.pieRef)}},{key:"render",value:function(){var r=this,i=this.props,a=i.hide,o=i.sectors,c=i.className,l=i.label,u=i.cx,s=i.cy,f=i.innerRadius,p=i.outerRadius,d=i.isAnimationActive,v=this.state.isAnimationFinished;if(a||!o||!o.length||!R(u)||!R(s)||!R(f)||!R(p))return null;var h=X("recharts-pie",c);return b.createElement(F,{tabIndex:this.props.rootTabIndex,className:h,ref:function(O){r.pieRef=O}},this.renderSectors(),l&&this.renderLabels(o),ve.renderCallByParent(this.props,null,!1),(!d||v)&&Ee.renderCallByParent(this.props,o,!1))}}],[{key:"getDerivedStateFromProps",value:function(r,i){return i.prevIsAnimationActive!==r.isAnimationActive?{prevIsAnimationActive:r.isAnimationActive,prevAnimationId:r.animationId,curSectors:r.sectors,prevSectors:[],isAnimationFinished:!0}:r.isAnimationActive&&r.animationId!==i.prevAnimationId?{prevAnimationId:r.animationId,curSectors:r.sectors,prevSectors:i.curSectors,isAnimationFinished:!0}:r.sectors!==i.curSectors?{curSectors:r.sectors,isAnimationFinished:!0}:null}},{key:"getTextAnchor",value:function(r,i){return r>i?"start":r<i?"end":"middle"}},{key:"renderLabelLineItem",value:function(r,i,a){if(b.isValidElement(r))return b.cloneElement(r,i);if(K(r))return r(i);var o=X("recharts-pie-label-line",typeof r!="boolean"?r.className:"");return b.createElement(ft,Xt({},i,{key:a,type:"linear",className:o}))}},{key:"renderLabelItem",value:function(r,i,a){if(b.isValidElement(r))return b.cloneElement(r,i);var o=a;if(K(r)&&(o=r(i),b.isValidElement(o)))return o;var c=X("recharts-pie-label-text",typeof r!="boolean"&&!K(r)?r.className:"");return b.createElement(dt,Xt({},i,{alignmentBaseline:"middle",className:c}),o)}}])}(N.PureComponent);Fn=ht;Me(ht,"displayName","Pie");Me(ht,"defaultProps",{stroke:"#fff",fill:"#808080",legendType:"rect",cx:"50%",cy:"50%",startAngle:0,endAngle:360,innerRadius:0,outerRadius:"80%",paddingAngle:0,labelLine:!0,hide:!1,minAngle:0,isAnimationActive:!be.isSsr,animationBegin:400,animationDuration:1500,animationEasing:"ease",nameKey:"name",blendStroke:!1,rootTabIndex:0});Me(ht,"parseDeltaAngle",function(t,e){var n=ye(e-t),r=Math.min(Math.abs(e-t),360);return n*r});Me(ht,"getRealPieData",function(t){var e=t.data,n=t.children,r=L(t,!1),i=ge(n,_r);return e&&e.length?e.map(function(a,o){return re(re(re({payload:a},r),a),i&&i[o]&&i[o].props)}):i&&i.length?i.map(function(a){return re(re({},r),a.props)}):[]});Me(ht,"parseCoordinateOfPie",function(t,e){var n=e.top,r=e.left,i=e.width,a=e.height,o=_s(i,a),c=r+Pe(t.cx,i,i/2),l=n+Pe(t.cy,a,a/2),u=Pe(t.innerRadius,o,0),s=Pe(t.outerRadius,o,o*.8),f=t.maxRadius||Math.sqrt(i*i+a*a)/2;return{cx:c,cy:l,innerRadius:u,outerRadius:s,maxRadius:f}});Me(ht,"getComposedData",function(t){var e=t.item,n=t.offset,r=e.type.defaultProps!==void 0?re(re({},e.type.defaultProps),e.props):e.props,i=Fn.getRealPieData(r);if(!i||!i.length)return null;var a=r.cornerRadius,o=r.startAngle,c=r.endAngle,l=r.paddingAngle,u=r.dataKey,s=r.nameKey,f=r.valueKey,p=r.tooltipType,d=Math.abs(r.minAngle),v=Fn.parseCoordinateOfPie(r,n),h=Fn.parseDeltaAngle(o,c),m=Math.abs(h),O=u;G(u)&&G(f)?(Ne(!1,`Use "dataKey" to specify the value of pie,
      the props "valueKey" will be deprecated in 1.1.0`),O="value"):G(u)&&(Ne(!1,`Use "dataKey" to specify the value of pie,
      the props "valueKey" will be deprecated in 1.1.0`),O=f);var w=i.filter(function(S){return Z(S,O,0)!==0}).length,x=(m>=360?w:w-1)*l,A=m-w*d-x,y=i.reduce(function(S,j){var E=Z(j,O,0);return S+(R(E)?E:0)},0),g;if(y>0){var P;g=i.map(function(S,j){var E=Z(S,O,0),$=Z(S,s,j),_=(R(E)?E:0)/y,k;j?k=P.endAngle+ye(h)*l*(E!==0?1:0):k=o;var D=k+ye(h)*((E!==0?d:0)+_*A),T=(k+D)/2,C=(v.innerRadius+v.outerRadius)/2,M=[{name:$,value:E,payload:S,dataKey:O,type:p}],B=J(v.cx,v.cy,C,T);return P=re(re(re({percent:_,cornerRadius:a,name:$,tooltipPayload:M,midAngle:T,middleRadius:C,tooltipPosition:B},S),v),{},{value:Z(S,O),startAngle:k,endAngle:D,payload:S,paddingAngle:ye(h)*l}),P})}return re(re({},v),{},{sectors:g,data:i})});var tb=["key"];function lr(t){"@babel/helpers - typeof";return lr=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},lr(t)}function rb(t,e){if(t==null)return{};var n=nb(t,e),r,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(t);for(i=0;i<a.length;i++)r=a[i],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(n[r]=t[r])}return n}function nb(t,e){if(t==null)return{};var n={};for(var r in t)if(Object.prototype.hasOwnProperty.call(t,r)){if(e.indexOf(r)>=0)continue;n[r]=t[r]}return n}function fi(){return fi=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var n=arguments[e];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(t[r]=n[r])}return t},fi.apply(this,arguments)}function Tl(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter(function(i){return Object.getOwnPropertyDescriptor(t,i).enumerable})),n.push.apply(n,r)}return n}function Ae(t){for(var e=1;e<arguments.length;e++){var n=arguments[e]!=null?arguments[e]:{};e%2?Tl(Object(n),!0).forEach(function(r){Ze(t,r,n[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):Tl(Object(n)).forEach(function(r){Object.defineProperty(t,r,Object.getOwnPropertyDescriptor(n,r))})}return t}function ib(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function Il(t,e){for(var n=0;n<e.length;n++){var r=e[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,zs(r.key),r)}}function ab(t,e,n){return e&&Il(t.prototype,e),n&&Il(t,n),Object.defineProperty(t,"prototype",{writable:!1}),t}function ob(t,e,n){return e=pi(e),cb(t,Ws()?Reflect.construct(e,n||[],pi(t).constructor):e.apply(t,n))}function cb(t,e){if(e&&(lr(e)==="object"||typeof e=="function"))return e;if(e!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return lb(t)}function lb(t){if(t===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}function Ws(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(Ws=function(){return!!t})()}function pi(t){return pi=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(n){return n.__proto__||Object.getPrototypeOf(n)},pi(t)}function ub(t,e){if(typeof e!="function"&&e!==null)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&to(t,e)}function to(t,e){return to=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(r,i){return r.__proto__=i,r},to(t,e)}function Ze(t,e,n){return e=zs(e),e in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function zs(t){var e=sb(t,"string");return lr(e)=="symbol"?e:e+""}function sb(t,e){if(lr(t)!="object"||!t)return t;var n=t[Symbol.toPrimitive];if(n!==void 0){var r=n.call(t,e);if(lr(r)!="object")return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}var Hi=function(t){function e(){var n;ib(this,e);for(var r=arguments.length,i=new Array(r),a=0;a<r;a++)i[a]=arguments[a];return n=ob(this,e,[].concat(i)),Ze(n,"state",{isAnimationFinished:!1}),Ze(n,"handleAnimationEnd",function(){var o=n.props.onAnimationEnd;n.setState({isAnimationFinished:!0}),K(o)&&o()}),Ze(n,"handleAnimationStart",function(){var o=n.props.onAnimationStart;n.setState({isAnimationFinished:!1}),K(o)&&o()}),Ze(n,"handleMouseEnter",function(o){var c=n.props.onMouseEnter;c&&c(n.props,o)}),Ze(n,"handleMouseLeave",function(o){var c=n.props.onMouseLeave;c&&c(n.props,o)}),n}return ub(e,t),ab(e,[{key:"renderDots",value:function(r){var i=this.props,a=i.dot,o=i.dataKey,c=L(this.props,!1),l=L(a,!0),u=r.map(function(s,f){var p=Ae(Ae(Ae({key:"dot-".concat(f),r:3},c),l),{},{dataKey:o,cx:s.x,cy:s.y,index:f,payload:s});return e.renderDotItem(a,p)});return b.createElement(F,{className:"recharts-radar-dots"},u)}},{key:"renderPolygonStatically",value:function(r){var i=this.props,a=i.shape,o=i.dot,c=i.isRange,l=i.baseLinePoints,u=i.connectNulls,s;return b.isValidElement(a)?s=b.cloneElement(a,Ae(Ae({},this.props),{},{points:r})):K(a)?s=a(Ae(Ae({},this.props),{},{points:r})):s=b.createElement(Vo,fi({},L(this.props,!0),{onMouseEnter:this.handleMouseEnter,onMouseLeave:this.handleMouseLeave,points:r,baseLinePoints:c?l:null,connectNulls:u})),b.createElement(F,{className:"recharts-radar-polygon"},s,o?this.renderDots(r):null)}},{key:"renderPolygonWithAnimation",value:function(){var r=this,i=this.props,a=i.points,o=i.isAnimationActive,c=i.animationBegin,l=i.animationDuration,u=i.animationEasing,s=i.animationId,f=this.state.prevPoints;return b.createElement($e,{begin:c,duration:l,isActive:o,easing:u,from:{t:0},to:{t:1},key:"radar-".concat(s),onAnimationEnd:this.handleAnimationEnd,onAnimationStart:this.handleAnimationStart},function(p){var d=p.t,v=f&&f.length/a.length,h=a.map(function(m,O){var w=f&&f[Math.floor(O*v)];if(w){var x=q(w.x,m.x),A=q(w.y,m.y);return Ae(Ae({},m),{},{x:x(d),y:A(d)})}var y=q(m.cx,m.x),g=q(m.cy,m.y);return Ae(Ae({},m),{},{x:y(d),y:g(d)})});return r.renderPolygonStatically(h)})}},{key:"renderPolygon",value:function(){var r=this.props,i=r.points,a=r.isAnimationActive,o=r.isRange,c=this.state.prevPoints;return a&&i&&i.length&&!o&&(!c||!Le(c,i))?this.renderPolygonWithAnimation():this.renderPolygonStatically(i)}},{key:"render",value:function(){var r=this.props,i=r.hide,a=r.className,o=r.points,c=r.isAnimationActive;if(i||!o||!o.length)return null;var l=this.state.isAnimationFinished,u=X("recharts-radar",a);return b.createElement(F,{className:u},this.renderPolygon(),(!c||l)&&Ee.renderCallByParent(this.props,o))}}],[{key:"getDerivedStateFromProps",value:function(r,i){return r.animationId!==i.prevAnimationId?{prevAnimationId:r.animationId,curPoints:r.points,prevPoints:i.curPoints}:r.points!==i.curPoints?{curPoints:r.points}:null}},{key:"renderDotItem",value:function(r,i){var a;if(b.isValidElement(r))a=b.cloneElement(r,i);else if(K(r))a=r(i);else{var o=i.key,c=rb(i,tb);a=b.createElement(Ir,fi({},c,{key:o,className:X("recharts-radar-dot",typeof r!="boolean"?r.className:"")}))}return a}}])}(N.PureComponent);Ze(Hi,"displayName","Radar");Ze(Hi,"defaultProps",{angleAxisId:0,radiusAxisId:0,hide:!1,activeDot:!0,dot:!1,legendType:"rect",isAnimationActive:!be.isSsr,animationBegin:0,animationDuration:1500,animationEasing:"ease"});Ze(Hi,"getComposedData",function(t){var e=t.radiusAxis,n=t.angleAxis,r=t.displayedData,i=t.dataKey,a=t.bandSize,o=n.cx,c=n.cy,l=!1,u=[],s=n.type!=="number"?a??0:0;r.forEach(function(p,d){var v=Z(p,n.dataKey,d),h=Z(p,i),m=n.scale(v)+s,O=Array.isArray(h)?Bu(h):h,w=G(O)?void 0:e.scale(O);Array.isArray(h)&&h.length>=2&&(l=!0),u.push(Ae(Ae({},J(o,c,w,m)),{},{name:v,value:h,cx:o,cy:c,radius:w,angle:m,payload:p}))});var f=[];return l&&u.forEach(function(p){if(Array.isArray(p.value)){var d=Ip(p.value),v=G(d)?void 0:e.scale(d);f.push(Ae(Ae({},p),{},{radius:v},J(o,c,v,p.angle)))}else f.push(p)}),{points:u,isRange:l,baseLinePoints:f}});function yn(t){"@babel/helpers - typeof";return yn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},yn(t)}function ro(){return ro=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var n=arguments[e];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(t[r]=n[r])}return t},ro.apply(this,arguments)}function kl(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter(function(i){return Object.getOwnPropertyDescriptor(t,i).enumerable})),n.push.apply(n,r)}return n}function sa(t){for(var e=1;e<arguments.length;e++){var n=arguments[e]!=null?arguments[e]:{};e%2?kl(Object(n),!0).forEach(function(r){fb(t,r,n[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):kl(Object(n)).forEach(function(r){Object.defineProperty(t,r,Object.getOwnPropertyDescriptor(n,r))})}return t}function fb(t,e,n){return e=pb(e),e in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function pb(t){var e=db(t,"string");return yn(e)=="symbol"?e:e+""}function db(t,e){if(yn(t)!="object"||!t)return t;var n=t[Symbol.toPrimitive];if(n!==void 0){var r=n.call(t,e);if(yn(r)!="object")return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(t)}function Dl(t){return typeof t=="string"?parseInt(t,10):t}function vb(t,e){var n="".concat(e.cx||t.cx),r=Number(n),i="".concat(e.cy||t.cy),a=Number(i);return sa(sa(sa({},e),t),{},{cx:r,cy:a})}function Cl(t){return b.createElement(or,ro({shapeType:"sector",propTransformer:vb},t))}var hb=["shape","activeShape","activeIndex","cornerRadius"],yb=["value","background"];function ur(t){"@babel/helpers - typeof";return ur=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},ur(t)}function di(){return di=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var n=arguments[e];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(t[r]=n[r])}return t},di.apply(this,arguments)}function Ml(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter(function(i){return Object.getOwnPropertyDescriptor(t,i).enumerable})),n.push.apply(n,r)}return n}function de(t){for(var e=1;e<arguments.length;e++){var n=arguments[e]!=null?arguments[e]:{};e%2?Ml(Object(n),!0).forEach(function(r){It(t,r,n[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):Ml(Object(n)).forEach(function(r){Object.defineProperty(t,r,Object.getOwnPropertyDescriptor(n,r))})}return t}function Nl(t,e){if(t==null)return{};var n=mb(t,e),r,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(t);for(i=0;i<a.length;i++)r=a[i],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(n[r]=t[r])}return n}function mb(t,e){if(t==null)return{};var n={};for(var r in t)if(Object.prototype.hasOwnProperty.call(t,r)){if(e.indexOf(r)>=0)continue;n[r]=t[r]}return n}function gb(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function Rl(t,e){for(var n=0;n<e.length;n++){var r=e[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,Fs(r.key),r)}}function bb(t,e,n){return e&&Rl(t.prototype,e),n&&Rl(t,n),Object.defineProperty(t,"prototype",{writable:!1}),t}function Ob(t,e,n){return e=vi(e),xb(t,Ks()?Reflect.construct(e,n||[],vi(t).constructor):e.apply(t,n))}function xb(t,e){if(e&&(ur(e)==="object"||typeof e=="function"))return e;if(e!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return wb(t)}function wb(t){if(t===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}function Ks(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(Ks=function(){return!!t})()}function vi(t){return vi=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(n){return n.__proto__||Object.getPrototypeOf(n)},vi(t)}function Ab(t,e){if(typeof e!="function"&&e!==null)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&no(t,e)}function no(t,e){return no=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(r,i){return r.__proto__=i,r},no(t,e)}function It(t,e,n){return e=Fs(e),e in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function Fs(t){var e=Pb(t,"string");return ur(e)=="symbol"?e:e+""}function Pb(t,e){if(ur(t)!="object"||!t)return t;var n=t[Symbol.toPrimitive];if(n!==void 0){var r=n.call(t,e);if(ur(r)!="object")return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}var Ui=function(t){function e(){var n;gb(this,e);for(var r=arguments.length,i=new Array(r),a=0;a<r;a++)i[a]=arguments[a];return n=Ob(this,e,[].concat(i)),It(n,"state",{isAnimationFinished:!1}),It(n,"handleAnimationEnd",function(){var o=n.props.onAnimationEnd;n.setState({isAnimationFinished:!0}),K(o)&&o()}),It(n,"handleAnimationStart",function(){var o=n.props.onAnimationStart;n.setState({isAnimationFinished:!1}),K(o)&&o()}),n}return Ab(e,t),bb(e,[{key:"getDeltaAngle",value:function(){var r=this.props,i=r.startAngle,a=r.endAngle,o=ye(a-i),c=Math.min(Math.abs(a-i),360);return o*c}},{key:"renderSectorsStatically",value:function(r){var i=this,a=this.props,o=a.shape,c=a.activeShape,l=a.activeIndex,u=a.cornerRadius,s=Nl(a,hb),f=L(s,!1);return r.map(function(p,d){var v=d===l,h=de(de(de(de({},f),{},{cornerRadius:Dl(u)},p),We(i.props,p,d)),{},{className:"recharts-radial-bar-sector ".concat(p.className),forceCornerRadius:s.forceCornerRadius,cornerIsExternal:s.cornerIsExternal,isActive:v,option:v?c:o});return b.createElement(Cl,di({},h,{key:"sector-".concat(d)}))})}},{key:"renderSectorsWithAnimation",value:function(){var r=this,i=this.props,a=i.data,o=i.isAnimationActive,c=i.animationBegin,l=i.animationDuration,u=i.animationEasing,s=i.animationId,f=this.state.prevData;return b.createElement($e,{begin:c,duration:l,isActive:o,easing:u,from:{t:0},to:{t:1},key:"radialBar-".concat(s),onAnimationStart:this.handleAnimationStart,onAnimationEnd:this.handleAnimationEnd},function(p){var d=p.t,v=a.map(function(h,m){var O=f&&f[m];if(O){var w=q(O.startAngle,h.startAngle),x=q(O.endAngle,h.endAngle);return de(de({},h),{},{startAngle:w(d),endAngle:x(d)})}var A=h.endAngle,y=h.startAngle,g=q(y,A);return de(de({},h),{},{endAngle:g(d)})});return b.createElement(F,null,r.renderSectorsStatically(v))})}},{key:"renderSectors",value:function(){var r=this.props,i=r.data,a=r.isAnimationActive,o=this.state.prevData;return a&&i&&i.length&&(!o||!Le(o,i))?this.renderSectorsWithAnimation():this.renderSectorsStatically(i)}},{key:"renderBackground",value:function(r){var i=this,a=this.props.cornerRadius,o=L(this.props.background,!1);return r.map(function(c,l){c.value;var u=c.background,s=Nl(c,yb);if(!u)return null;var f=de(de(de(de(de({cornerRadius:Dl(a)},s),{},{fill:"#eee"},u),o),We(i.props,c,l)),{},{index:l,className:X("recharts-radial-bar-background-sector",o?.className),option:u,isActive:!1});return b.createElement(Cl,di({},f,{key:"sector-".concat(l)}))})}},{key:"render",value:function(){var r=this.props,i=r.hide,a=r.data,o=r.className,c=r.background,l=r.isAnimationActive;if(i||!a||!a.length)return null;var u=this.state.isAnimationFinished,s=X("recharts-area",o);return b.createElement(F,{className:s},c&&b.createElement(F,{className:"recharts-radial-bar-background"},this.renderBackground(a)),b.createElement(F,{className:"recharts-radial-bar-sectors"},this.renderSectors()),(!l||u)&&Ee.renderCallByParent(de({},this.props),a))}}],[{key:"getDerivedStateFromProps",value:function(r,i){return r.animationId!==i.prevAnimationId?{prevAnimationId:r.animationId,curData:r.data,prevData:i.curData}:r.data!==i.curData?{curData:r.data}:null}}])}(N.PureComponent);It(Ui,"displayName","RadialBar");It(Ui,"defaultProps",{angleAxisId:0,radiusAxisId:0,minPointSize:0,hide:!1,legendType:"rect",data:[],isAnimationActive:!be.isSsr,animationBegin:0,animationDuration:1500,animationEasing:"ease",forceCornerRadius:!1,cornerIsExternal:!1});It(Ui,"getComposedData",function(t){var e=t.item,n=t.props,r=t.radiusAxis,i=t.radiusAxisTicks,a=t.angleAxis,o=t.angleAxisTicks,c=t.displayedData,l=t.dataKey,u=t.stackedData,s=t.barPosition,f=t.bandSize,p=t.dataStartIndex,d=As(s,e);if(!d)return null;var v=a.cx,h=a.cy,m=n.layout,O=e.props,w=O.children,x=O.minPointSize,A=m==="radial"?a:r,y=u?A.scale.domain():null,g=js({numericAxis:A}),P=ge(w,_r),S=c.map(function(j,E){var $,_,k,D,T,C;if(u?$=Ps(u[p+E],y):($=Z(j,l),Array.isArray($)||($=[g,$])),m==="radial"){_=ti({axis:r,ticks:i,bandSize:f,offset:d.offset,entry:j,index:E}),T=a.scale($[1]),D=a.scale($[0]),k=_+d.size;var M=T-D;if(Math.abs(x)>0&&Math.abs(M)<Math.abs(x)){var B=ye(M||x)*(Math.abs(x)-Math.abs(M));T+=B}C={background:{cx:v,cy:h,innerRadius:_,outerRadius:k,startAngle:n.startAngle,endAngle:n.endAngle}}}else{_=r.scale($[0]),k=r.scale($[1]),D=ti({axis:a,ticks:o,bandSize:f,offset:d.offset,entry:j,index:E}),T=D+d.size;var W=k-_;if(Math.abs(x)>0&&Math.abs(W)<Math.abs(x)){var V=ye(W||x)*(Math.abs(x)-Math.abs(W));k+=V}}return de(de(de(de({},j),C),{},{payload:j,value:u?$:$[1],cx:v,cy:h,innerRadius:_,outerRadius:k,startAngle:D,endAngle:T},P&&P[E]&&P[E].props),{},{tooltipPayload:[zo(e,j)],tooltipPosition:J(v,h,(_+k)/2,(D+T)/2)})});return{data:S,layout:m}});function mn(t){"@babel/helpers - typeof";return mn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},mn(t)}function Bl(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter(function(i){return Object.getOwnPropertyDescriptor(t,i).enumerable})),n.push.apply(n,r)}return n}function Ll(t){for(var e=1;e<arguments.length;e++){var n=arguments[e]!=null?arguments[e]:{};e%2?Bl(Object(n),!0).forEach(function(r){Vs(t,r,n[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):Bl(Object(n)).forEach(function(r){Object.defineProperty(t,r,Object.getOwnPropertyDescriptor(n,r))})}return t}function Vs(t,e,n){return e=Sb(e),e in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function Sb(t){var e=jb(t,"string");return mn(e)=="symbol"?e:e+""}function jb(t,e){if(mn(t)!="object"||!t)return t;var n=t[Symbol.toPrimitive];if(n!==void 0){var r=n.call(t,e);if(mn(r)!="object")return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(t)}var Eb=["Webkit","Moz","O","ms"],$b=function(e,n){var r=e.replace(/(\w)/,function(a){return a.toUpperCase()}),i=Eb.reduce(function(a,o){return Ll(Ll({},a),{},Vs({},o+r,n))},{});return i[e]=n,i};function sr(t){"@babel/helpers - typeof";return sr=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},sr(t)}function hi(){return hi=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var n=arguments[e];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(t[r]=n[r])}return t},hi.apply(this,arguments)}function Wl(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter(function(i){return Object.getOwnPropertyDescriptor(t,i).enumerable})),n.push.apply(n,r)}return n}function fa(t){for(var e=1;e<arguments.length;e++){var n=arguments[e]!=null?arguments[e]:{};e%2?Wl(Object(n),!0).forEach(function(r){Ie(t,r,n[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):Wl(Object(n)).forEach(function(r){Object.defineProperty(t,r,Object.getOwnPropertyDescriptor(n,r))})}return t}function _b(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function zl(t,e){for(var n=0;n<e.length;n++){var r=e[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,Gs(r.key),r)}}function Tb(t,e,n){return e&&zl(t.prototype,e),n&&zl(t,n),Object.defineProperty(t,"prototype",{writable:!1}),t}function Ib(t,e,n){return e=yi(e),kb(t,Xs()?Reflect.construct(e,n||[],yi(t).constructor):e.apply(t,n))}function kb(t,e){if(e&&(sr(e)==="object"||typeof e=="function"))return e;if(e!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return Db(t)}function Db(t){if(t===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}function Xs(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(Xs=function(){return!!t})()}function yi(t){return yi=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(n){return n.__proto__||Object.getPrototypeOf(n)},yi(t)}function Cb(t,e){if(typeof e!="function"&&e!==null)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&io(t,e)}function io(t,e){return io=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(r,i){return r.__proto__=i,r},io(t,e)}function Ie(t,e,n){return e=Gs(e),e in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function Gs(t){var e=Mb(t,"string");return sr(e)=="symbol"?e:e+""}function Mb(t,e){if(sr(t)!="object"||!t)return t;var n=t[Symbol.toPrimitive];if(n!==void 0){var r=n.call(t,e);if(sr(r)!="object")return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}var Nb=function(e){var n=e.data,r=e.startIndex,i=e.endIndex,a=e.x,o=e.width,c=e.travellerWidth;if(!n||!n.length)return{};var l=n.length,u=Kn().domain(Vn(0,l)).range([a,a+o-c]),s=u.domain().map(function(f){return u(f)});return{isTextActive:!1,isSlideMoving:!1,isTravellerMoving:!1,isTravellerFocused:!1,startX:u(r),endX:u(i),scale:u,scaleValues:s}},Kl=function(e){return e.changedTouches&&!!e.changedTouches.length},fr=function(t){function e(n){var r;return _b(this,e),r=Ib(this,e,[n]),Ie(r,"handleDrag",function(i){r.leaveTimer&&(clearTimeout(r.leaveTimer),r.leaveTimer=null),r.state.isTravellerMoving?r.handleTravellerMove(i):r.state.isSlideMoving&&r.handleSlideDrag(i)}),Ie(r,"handleTouchMove",function(i){i.changedTouches!=null&&i.changedTouches.length>0&&r.handleDrag(i.changedTouches[0])}),Ie(r,"handleDragEnd",function(){r.setState({isTravellerMoving:!1,isSlideMoving:!1},function(){var i=r.props,a=i.endIndex,o=i.onDragEnd,c=i.startIndex;o?.({endIndex:a,startIndex:c})}),r.detachDragEndListener()}),Ie(r,"handleLeaveWrapper",function(){(r.state.isTravellerMoving||r.state.isSlideMoving)&&(r.leaveTimer=window.setTimeout(r.handleDragEnd,r.props.leaveTimeOut))}),Ie(r,"handleEnterSlideOrTraveller",function(){r.setState({isTextActive:!0})}),Ie(r,"handleLeaveSlideOrTraveller",function(){r.setState({isTextActive:!1})}),Ie(r,"handleSlideDragStart",function(i){var a=Kl(i)?i.changedTouches[0]:i;r.setState({isTravellerMoving:!1,isSlideMoving:!0,slideMoveStartX:a.pageX}),r.attachDragEndListener()}),r.travellerDragStartHandlers={startX:r.handleTravellerDragStart.bind(r,"startX"),endX:r.handleTravellerDragStart.bind(r,"endX")},r.state={},r}return Cb(e,t),Tb(e,[{key:"componentWillUnmount",value:function(){this.leaveTimer&&(clearTimeout(this.leaveTimer),this.leaveTimer=null),this.detachDragEndListener()}},{key:"getIndex",value:function(r){var i=r.startX,a=r.endX,o=this.state.scaleValues,c=this.props,l=c.gap,u=c.data,s=u.length-1,f=Math.min(i,a),p=Math.max(i,a),d=e.getIndexInRange(o,f),v=e.getIndexInRange(o,p);return{startIndex:d-d%l,endIndex:v===s?s:v-v%l}}},{key:"getTextOfTick",value:function(r){var i=this.props,a=i.data,o=i.tickFormatter,c=i.dataKey,l=Z(a[r],c,r);return K(o)?o(l,r):l}},{key:"attachDragEndListener",value:function(){window.addEventListener("mouseup",this.handleDragEnd,!0),window.addEventListener("touchend",this.handleDragEnd,!0),window.addEventListener("mousemove",this.handleDrag,!0)}},{key:"detachDragEndListener",value:function(){window.removeEventListener("mouseup",this.handleDragEnd,!0),window.removeEventListener("touchend",this.handleDragEnd,!0),window.removeEventListener("mousemove",this.handleDrag,!0)}},{key:"handleSlideDrag",value:function(r){var i=this.state,a=i.slideMoveStartX,o=i.startX,c=i.endX,l=this.props,u=l.x,s=l.width,f=l.travellerWidth,p=l.startIndex,d=l.endIndex,v=l.onChange,h=r.pageX-a;h>0?h=Math.min(h,u+s-f-c,u+s-f-o):h<0&&(h=Math.max(h,u-o,u-c));var m=this.getIndex({startX:o+h,endX:c+h});(m.startIndex!==p||m.endIndex!==d)&&v&&v(m),this.setState({startX:o+h,endX:c+h,slideMoveStartX:r.pageX})}},{key:"handleTravellerDragStart",value:function(r,i){var a=Kl(i)?i.changedTouches[0]:i;this.setState({isSlideMoving:!1,isTravellerMoving:!0,movingTravellerId:r,brushMoveStartX:a.pageX}),this.attachDragEndListener()}},{key:"handleTravellerMove",value:function(r){var i=this.state,a=i.brushMoveStartX,o=i.movingTravellerId,c=i.endX,l=i.startX,u=this.state[o],s=this.props,f=s.x,p=s.width,d=s.travellerWidth,v=s.onChange,h=s.gap,m=s.data,O={startX:this.state.startX,endX:this.state.endX},w=r.pageX-a;w>0?w=Math.min(w,f+p-d-u):w<0&&(w=Math.max(w,f-u)),O[o]=u+w;var x=this.getIndex(O),A=x.startIndex,y=x.endIndex,g=function(){var S=m.length-1;return o==="startX"&&(c>l?A%h===0:y%h===0)||c<l&&y===S||o==="endX"&&(c>l?y%h===0:A%h===0)||c>l&&y===S};this.setState(Ie(Ie({},o,u+w),"brushMoveStartX",r.pageX),function(){v&&g()&&v(x)})}},{key:"handleTravellerMoveKeyboard",value:function(r,i){var a=this,o=this.state,c=o.scaleValues,l=o.startX,u=o.endX,s=this.state[i],f=c.indexOf(s);if(f!==-1){var p=f+r;if(!(p===-1||p>=c.length)){var d=c[p];i==="startX"&&d>=u||i==="endX"&&d<=l||this.setState(Ie({},i,d),function(){a.props.onChange(a.getIndex({startX:a.state.startX,endX:a.state.endX}))})}}}},{key:"renderBackground",value:function(){var r=this.props,i=r.x,a=r.y,o=r.width,c=r.height,l=r.fill,u=r.stroke;return b.createElement("rect",{stroke:u,fill:l,x:i,y:a,width:o,height:c})}},{key:"renderPanorama",value:function(){var r=this.props,i=r.x,a=r.y,o=r.width,c=r.height,l=r.data,u=r.children,s=r.padding,f=N.Children.only(u);return f?b.cloneElement(f,{x:i,y:a,width:o,height:c,margin:s,compact:!0,data:l}):null}},{key:"renderTravellerLayer",value:function(r,i){var a,o,c=this,l=this.props,u=l.y,s=l.travellerWidth,f=l.height,p=l.traveller,d=l.ariaLabel,v=l.data,h=l.startIndex,m=l.endIndex,O=Math.max(r,this.props.x),w=fa(fa({},L(this.props,!1)),{},{x:O,y:u,width:s,height:f}),x=d||"Min value: ".concat((a=v[h])===null||a===void 0?void 0:a.name,", Max value: ").concat((o=v[m])===null||o===void 0?void 0:o.name);return b.createElement(F,{tabIndex:0,role:"slider","aria-label":x,"aria-valuenow":r,className:"recharts-brush-traveller",onMouseEnter:this.handleEnterSlideOrTraveller,onMouseLeave:this.handleLeaveSlideOrTraveller,onMouseDown:this.travellerDragStartHandlers[i],onTouchStart:this.travellerDragStartHandlers[i],onKeyDown:function(y){["ArrowLeft","ArrowRight"].includes(y.key)&&(y.preventDefault(),y.stopPropagation(),c.handleTravellerMoveKeyboard(y.key==="ArrowRight"?1:-1,i))},onFocus:function(){c.setState({isTravellerFocused:!0})},onBlur:function(){c.setState({isTravellerFocused:!1})},style:{cursor:"col-resize"}},e.renderTraveller(p,w))}},{key:"renderSlide",value:function(r,i){var a=this.props,o=a.y,c=a.height,l=a.stroke,u=a.travellerWidth,s=Math.min(r,i)+u,f=Math.max(Math.abs(i-r)-u,0);return b.createElement("rect",{className:"recharts-brush-slide",onMouseEnter:this.handleEnterSlideOrTraveller,onMouseLeave:this.handleLeaveSlideOrTraveller,onMouseDown:this.handleSlideDragStart,onTouchStart:this.handleSlideDragStart,style:{cursor:"move"},stroke:"none",fill:l,fillOpacity:.2,x:s,y:o,width:f,height:c})}},{key:"renderText",value:function(){var r=this.props,i=r.startIndex,a=r.endIndex,o=r.y,c=r.height,l=r.travellerWidth,u=r.stroke,s=this.state,f=s.startX,p=s.endX,d=5,v={pointerEvents:"none",fill:u};return b.createElement(F,{className:"recharts-brush-texts"},b.createElement(dt,hi({textAnchor:"end",verticalAnchor:"middle",x:Math.min(f,p)-d,y:o+c/2},v),this.getTextOfTick(i)),b.createElement(dt,hi({textAnchor:"start",verticalAnchor:"middle",x:Math.max(f,p)+l+d,y:o+c/2},v),this.getTextOfTick(a)))}},{key:"render",value:function(){var r=this.props,i=r.data,a=r.className,o=r.children,c=r.x,l=r.y,u=r.width,s=r.height,f=r.alwaysShowText,p=this.state,d=p.startX,v=p.endX,h=p.isTextActive,m=p.isSlideMoving,O=p.isTravellerMoving,w=p.isTravellerFocused;if(!i||!i.length||!R(c)||!R(l)||!R(u)||!R(s)||u<=0||s<=0)return null;var x=X("recharts-brush",a),A=b.Children.count(o)===1,y=$b("userSelect","none");return b.createElement(F,{className:x,onMouseLeave:this.handleLeaveWrapper,onTouchMove:this.handleTouchMove,style:y},this.renderBackground(),A&&this.renderPanorama(),this.renderSlide(d,v),this.renderTravellerLayer(d,"startX"),this.renderTravellerLayer(v,"endX"),(h||m||O||w||f)&&this.renderText())}}],[{key:"renderDefaultTraveller",value:function(r){var i=r.x,a=r.y,o=r.width,c=r.height,l=r.stroke,u=Math.floor(a+c/2)-1;return b.createElement(b.Fragment,null,b.createElement("rect",{x:i,y:a,width:o,height:c,fill:l,stroke:"none"}),b.createElement("line",{x1:i+1,y1:u,x2:i+o-1,y2:u,fill:"none",stroke:"#fff"}),b.createElement("line",{x1:i+1,y1:u+2,x2:i+o-1,y2:u+2,fill:"none",stroke:"#fff"}))}},{key:"renderTraveller",value:function(r,i){var a;return b.isValidElement(r)?a=b.cloneElement(r,i):K(r)?a=r(i):a=e.renderDefaultTraveller(i),a}},{key:"getDerivedStateFromProps",value:function(r,i){var a=r.data,o=r.width,c=r.x,l=r.travellerWidth,u=r.updateId,s=r.startIndex,f=r.endIndex;if(a!==i.prevData||u!==i.prevUpdateId)return fa({prevData:a,prevTravellerWidth:l,prevUpdateId:u,prevX:c,prevWidth:o},a&&a.length?Nb({data:a,width:o,x:c,travellerWidth:l,startIndex:s,endIndex:f}):{scale:null,scaleValues:null});if(i.scale&&(o!==i.prevWidth||c!==i.prevX||l!==i.prevTravellerWidth)){i.scale.range([c,c+o-l]);var p=i.scale.domain().map(function(d){return i.scale(d)});return{prevData:a,prevTravellerWidth:l,prevUpdateId:u,prevX:c,prevWidth:o,startX:i.scale(r.startIndex),endX:i.scale(r.endIndex),scaleValues:p}}return null}},{key:"getIndexInRange",value:function(r,i){for(var a=r.length,o=0,c=a-1;c-o>1;){var l=Math.floor((o+c)/2);r[l]>i?c=l:o=l}return i>=r[c]?c:o}}])}(N.PureComponent);Ie(fr,"displayName","Brush");Ie(fr,"defaultProps",{height:40,travellerWidth:5,gap:1,fill:"#fff",stroke:"#666",padding:{top:1,right:1,bottom:1,left:1},leaveTimeOut:1e3,alwaysShowText:!1});var Ve=function(e,n){var r=e.alwaysShow,i=e.ifOverflow;return r&&(i="extendDomain"),i===n},Rb=["x","y"];function gn(t){"@babel/helpers - typeof";return gn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},gn(t)}function ao(){return ao=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var n=arguments[e];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(t[r]=n[r])}return t},ao.apply(this,arguments)}function Fl(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter(function(i){return Object.getOwnPropertyDescriptor(t,i).enumerable})),n.push.apply(n,r)}return n}function Lr(t){for(var e=1;e<arguments.length;e++){var n=arguments[e]!=null?arguments[e]:{};e%2?Fl(Object(n),!0).forEach(function(r){Bb(t,r,n[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):Fl(Object(n)).forEach(function(r){Object.defineProperty(t,r,Object.getOwnPropertyDescriptor(n,r))})}return t}function Bb(t,e,n){return e=Lb(e),e in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function Lb(t){var e=Wb(t,"string");return gn(e)=="symbol"?e:e+""}function Wb(t,e){if(gn(t)!="object"||!t)return t;var n=t[Symbol.toPrimitive];if(n!==void 0){var r=n.call(t,e);if(gn(r)!="object")return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(t)}function zb(t,e){if(t==null)return{};var n=Kb(t,e),r,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(t);for(i=0;i<a.length;i++)r=a[i],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(n[r]=t[r])}return n}function Kb(t,e){if(t==null)return{};var n={};for(var r in t)if(Object.prototype.hasOwnProperty.call(t,r)){if(e.indexOf(r)>=0)continue;n[r]=t[r]}return n}function Fb(t,e){var n=t.x,r=t.y,i=zb(t,Rb),a="".concat(n),o=parseInt(a,10),c="".concat(r),l=parseInt(c,10),u="".concat(e.height||i.height),s=parseInt(u,10),f="".concat(e.width||i.width),p=parseInt(f,10);return Lr(Lr(Lr(Lr(Lr({},e),i),o?{x:o}:{}),l?{y:l}:{}),{},{height:s,width:p,name:e.name,radius:e.radius})}function Vl(t){return b.createElement(or,ao({shapeType:"rectangle",propTransformer:Fb,activeClassName:"recharts-active-bar"},t))}var Vb=function(e){var n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0;return function(r,i){if(typeof e=="number")return e;var a=R(r)||Lp(r);return a?e(r,i):(a||kt(),n)}},Xb=["value","background"],Hs;function pr(t){"@babel/helpers - typeof";return pr=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},pr(t)}function Gb(t,e){if(t==null)return{};var n=Hb(t,e),r,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(t);for(i=0;i<a.length;i++)r=a[i],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(n[r]=t[r])}return n}function Hb(t,e){if(t==null)return{};var n={};for(var r in t)if(Object.prototype.hasOwnProperty.call(t,r)){if(e.indexOf(r)>=0)continue;n[r]=t[r]}return n}function mi(){return mi=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var n=arguments[e];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(t[r]=n[r])}return t},mi.apply(this,arguments)}function Xl(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter(function(i){return Object.getOwnPropertyDescriptor(t,i).enumerable})),n.push.apply(n,r)}return n}function ce(t){for(var e=1;e<arguments.length;e++){var n=arguments[e]!=null?arguments[e]:{};e%2?Xl(Object(n),!0).forEach(function(r){ut(t,r,n[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):Xl(Object(n)).forEach(function(r){Object.defineProperty(t,r,Object.getOwnPropertyDescriptor(n,r))})}return t}function Ub(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function Gl(t,e){for(var n=0;n<e.length;n++){var r=e[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,Ys(r.key),r)}}function Yb(t,e,n){return e&&Gl(t.prototype,e),n&&Gl(t,n),Object.defineProperty(t,"prototype",{writable:!1}),t}function qb(t,e,n){return e=gi(e),Zb(t,Us()?Reflect.construct(e,n||[],gi(t).constructor):e.apply(t,n))}function Zb(t,e){if(e&&(pr(e)==="object"||typeof e=="function"))return e;if(e!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return Jb(t)}function Jb(t){if(t===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}function Us(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(Us=function(){return!!t})()}function gi(t){return gi=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(n){return n.__proto__||Object.getPrototypeOf(n)},gi(t)}function Qb(t,e){if(typeof e!="function"&&e!==null)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&oo(t,e)}function oo(t,e){return oo=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(r,i){return r.__proto__=i,r},oo(t,e)}function ut(t,e,n){return e=Ys(e),e in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function Ys(t){var e=eO(t,"string");return pr(e)=="symbol"?e:e+""}function eO(t,e){if(pr(t)!="object"||!t)return t;var n=t[Symbol.toPrimitive];if(n!==void 0){var r=n.call(t,e);if(pr(r)!="object")return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}var Ct=function(t){function e(){var n;Ub(this,e);for(var r=arguments.length,i=new Array(r),a=0;a<r;a++)i[a]=arguments[a];return n=qb(this,e,[].concat(i)),ut(n,"state",{isAnimationFinished:!1}),ut(n,"id",nt("recharts-bar-")),ut(n,"handleAnimationEnd",function(){var o=n.props.onAnimationEnd;n.setState({isAnimationFinished:!0}),o&&o()}),ut(n,"handleAnimationStart",function(){var o=n.props.onAnimationStart;n.setState({isAnimationFinished:!1}),o&&o()}),n}return Qb(e,t),Yb(e,[{key:"renderRectanglesStatically",value:function(r){var i=this,a=this.props,o=a.shape,c=a.dataKey,l=a.activeIndex,u=a.activeBar,s=L(this.props,!1);return r&&r.map(function(f,p){var d=p===l,v=d?u:o,h=ce(ce(ce({},s),f),{},{isActive:d,option:v,index:p,dataKey:c,onAnimationStart:i.handleAnimationStart,onAnimationEnd:i.handleAnimationEnd});return b.createElement(F,mi({className:"recharts-bar-rectangle"},We(i.props,f,p),{key:"rectangle-".concat(f?.x,"-").concat(f?.y,"-").concat(f?.value,"-").concat(p)}),b.createElement(Vl,h))})}},{key:"renderRectanglesWithAnimation",value:function(){var r=this,i=this.props,a=i.data,o=i.layout,c=i.isAnimationActive,l=i.animationBegin,u=i.animationDuration,s=i.animationEasing,f=i.animationId,p=this.state.prevData;return b.createElement($e,{begin:l,duration:u,isActive:c,easing:s,from:{t:0},to:{t:1},key:"bar-".concat(f),onAnimationEnd:this.handleAnimationEnd,onAnimationStart:this.handleAnimationStart},function(d){var v=d.t,h=a.map(function(m,O){var w=p&&p[O];if(w){var x=q(w.x,m.x),A=q(w.y,m.y),y=q(w.width,m.width),g=q(w.height,m.height);return ce(ce({},m),{},{x:x(v),y:A(v),width:y(v),height:g(v)})}if(o==="horizontal"){var P=q(0,m.height),S=P(v);return ce(ce({},m),{},{y:m.y+m.height-S,height:S})}var j=q(0,m.width),E=j(v);return ce(ce({},m),{},{width:E})});return b.createElement(F,null,r.renderRectanglesStatically(h))})}},{key:"renderRectangles",value:function(){var r=this.props,i=r.data,a=r.isAnimationActive,o=this.state.prevData;return a&&i&&i.length&&(!o||!Le(o,i))?this.renderRectanglesWithAnimation():this.renderRectanglesStatically(i)}},{key:"renderBackground",value:function(){var r=this,i=this.props,a=i.data,o=i.dataKey,c=i.activeIndex,l=L(this.props.background,!1);return a.map(function(u,s){u.value;var f=u.background,p=Gb(u,Xb);if(!f)return null;var d=ce(ce(ce(ce(ce({},p),{},{fill:"#eee"},f),l),We(r.props,u,s)),{},{onAnimationStart:r.handleAnimationStart,onAnimationEnd:r.handleAnimationEnd,dataKey:o,index:s,className:"recharts-bar-background-rectangle"});return b.createElement(Vl,mi({key:"background-bar-".concat(s),option:r.props.background,isActive:s===c},d))})}},{key:"renderErrorBar",value:function(r,i){if(this.props.isAnimationActive&&!this.state.isAnimationFinished)return null;var a=this.props,o=a.data,c=a.xAxis,l=a.yAxis,u=a.layout,s=a.children,f=ge(s,Tr);if(!f)return null;var p=u==="vertical"?o[0].height/2:o[0].width/2,d=function(m,O){var w=Array.isArray(m.value)?m.value[1]:m.value;return{x:m.x,y:m.y,value:w,errorVal:Z(m,O)}},v={clipPath:r?"url(#clipPath-".concat(i,")"):null};return b.createElement(F,v,f.map(function(h){return b.cloneElement(h,{key:"error-bar-".concat(i,"-").concat(h.props.dataKey),data:o,xAxis:c,yAxis:l,layout:u,offset:p,dataPointFormatter:d})}))}},{key:"render",value:function(){var r=this.props,i=r.hide,a=r.data,o=r.className,c=r.xAxis,l=r.yAxis,u=r.left,s=r.top,f=r.width,p=r.height,d=r.isAnimationActive,v=r.background,h=r.id;if(i||!a||!a.length)return null;var m=this.state.isAnimationFinished,O=X("recharts-bar",o),w=c&&c.allowDataOverflow,x=l&&l.allowDataOverflow,A=w||x,y=G(h)?this.id:h;return b.createElement(F,{className:O},w||x?b.createElement("defs",null,b.createElement("clipPath",{id:"clipPath-".concat(y)},b.createElement("rect",{x:w?u:u-f/2,y:x?s:s-p/2,width:w?f:f*2,height:x?p:p*2}))):null,b.createElement(F,{className:"recharts-bar-rectangles",clipPath:A?"url(#clipPath-".concat(y,")"):null},v?this.renderBackground():null,this.renderRectangles()),this.renderErrorBar(A,y),(!d||m)&&Ee.renderCallByParent(this.props,a))}}],[{key:"getDerivedStateFromProps",value:function(r,i){return r.animationId!==i.prevAnimationId?{prevAnimationId:r.animationId,curData:r.data,prevData:i.curData}:r.data!==i.curData?{curData:r.data}:null}}])}(N.PureComponent);Hs=Ct;ut(Ct,"displayName","Bar");ut(Ct,"defaultProps",{xAxisId:0,yAxisId:0,legendType:"rect",minPointSize:0,hide:!1,data:[],layout:"vertical",activeBar:!1,isAnimationActive:!be.isSsr,animationBegin:0,animationDuration:400,animationEasing:"ease"});ut(Ct,"getComposedData",function(t){var e=t.props,n=t.item,r=t.barPosition,i=t.bandSize,a=t.xAxis,o=t.yAxis,c=t.xAxisTicks,l=t.yAxisTicks,u=t.stackedData,s=t.dataStartIndex,f=t.displayedData,p=t.offset,d=As(r,n);if(!d)return null;var v=e.layout,h=n.type.defaultProps,m=h!==void 0?ce(ce({},h),n.props):n.props,O=m.dataKey,w=m.children,x=m.minPointSize,A=v==="horizontal"?o:a,y=u?A.scale.domain():null,g=js({numericAxis:A}),P=ge(w,_r),S=f.map(function(j,E){var $,_,k,D,T,C;u?$=Ps(u[s+E],y):($=Z(j,O),Array.isArray($)||($=[g,$]));var M=Vb(x,Hs.defaultProps.minPointSize)($[1],E);if(v==="horizontal"){var B,W=[o.scale($[0]),o.scale($[1])],V=W[0],H=W[1];_=ti({axis:a,ticks:c,bandSize:i,offset:d.offset,entry:j,index:E}),k=(B=H??V)!==null&&B!==void 0?B:void 0,D=d.size;var z=V-H;if(T=Number.isNaN(z)?0:z,C={x:_,y:o.y,width:D,height:o.height},Math.abs(M)>0&&Math.abs(T)<Math.abs(M)){var Y=ye(T||M)*(Math.abs(M)-Math.abs(T));k-=Y,T+=Y}}else{var ee=[a.scale($[0]),a.scale($[1])],ae=ee[0],_e=ee[1];if(_=ae,k=ti({axis:o,ticks:l,bandSize:i,offset:d.offset,entry:j,index:E}),D=_e-ae,T=d.size,C={x:a.x,y:k,width:a.width,height:T},Math.abs(M)>0&&Math.abs(D)<Math.abs(M)){var mt=ye(D||M)*(Math.abs(M)-Math.abs(D));D+=mt}}return ce(ce(ce({},j),{},{x:_,y:k,width:D,height:T,value:u?$:$[1],payload:j,background:C},P&&P[E]&&P[E].props),{},{tooltipPayload:[zo(n,j)],tooltipPosition:{x:_+D/2,y:k+T/2}})});return ce({data:S,layout:v},p)});function bn(t){"@babel/helpers - typeof";return bn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},bn(t)}function tO(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function Hl(t,e){for(var n=0;n<e.length;n++){var r=e[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,qs(r.key),r)}}function rO(t,e,n){return e&&Hl(t.prototype,e),n&&Hl(t,n),Object.defineProperty(t,"prototype",{writable:!1}),t}function Ul(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter(function(i){return Object.getOwnPropertyDescriptor(t,i).enumerable})),n.push.apply(n,r)}return n}function Re(t){for(var e=1;e<arguments.length;e++){var n=arguments[e]!=null?arguments[e]:{};e%2?Ul(Object(n),!0).forEach(function(r){Yi(t,r,n[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):Ul(Object(n)).forEach(function(r){Object.defineProperty(t,r,Object.getOwnPropertyDescriptor(n,r))})}return t}function Yi(t,e,n){return e=qs(e),e in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function qs(t){var e=nO(t,"string");return bn(e)=="symbol"?e:e+""}function nO(t,e){if(bn(t)!="object"||!t)return t;var n=t[Symbol.toPrimitive];if(n!==void 0){var r=n.call(t,e);if(bn(r)!="object")return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(t)}var $n=function(e,n,r,i,a){var o=e.width,c=e.height,l=e.layout,u=e.children,s=Object.keys(n),f={left:r.left,leftMirror:r.left,right:o-r.right,rightMirror:o-r.right,top:r.top,topMirror:r.top,bottom:c-r.bottom,bottomMirror:c-r.bottom},p=!!oe(u,Ct);return s.reduce(function(d,v){var h=n[v],m=h.orientation,O=h.domain,w=h.padding,x=w===void 0?{}:w,A=h.mirror,y=h.reversed,g="".concat(m).concat(A?"Mirror":""),P,S,j,E,$;if(h.type==="number"&&(h.padding==="gap"||h.padding==="no-gap")){var _=O[1]-O[0],k=1/0,D=h.categoricalDomain.sort(Fp);if(D.forEach(function(ee,ae){ae>0&&(k=Math.min((ee||0)-(D[ae-1]||0),k))}),Number.isFinite(k)){var T=k/_,C=h.layout==="vertical"?r.height:r.width;if(h.padding==="gap"&&(P=T*C/2),h.padding==="no-gap"){var M=Pe(e.barCategoryGap,T*C),B=T*C/2;P=B-M-(B-M)/C*M}}}i==="xAxis"?S=[r.left+(x.left||0)+(P||0),r.left+r.width-(x.right||0)-(P||0)]:i==="yAxis"?S=l==="horizontal"?[r.top+r.height-(x.bottom||0),r.top+(x.top||0)]:[r.top+(x.top||0)+(P||0),r.top+r.height-(x.bottom||0)-(P||0)]:S=h.range,y&&(S=[S[1],S[0]]);var W=xs(h,a,p),V=W.scale,H=W.realScaleType;V.domain(O).range(S),ws(V);var z=Ss(V,Re(Re({},h),{},{realScaleType:H}));i==="xAxis"?($=m==="top"&&!A||m==="bottom"&&A,j=r.left,E=f[g]-$*h.height):i==="yAxis"&&($=m==="left"&&!A||m==="right"&&A,j=f[g]-$*h.width,E=r.top);var Y=Re(Re(Re({},h),z),{},{realScaleType:H,x:j,y:E,scale:V,width:i==="xAxis"?r.width:h.width,height:i==="yAxis"?r.height:h.height});return Y.bandSize=ri(Y,z),!h.hide&&i==="xAxis"?f[g]+=($?-1:1)*Y.height:h.hide||(f[g]+=($?-1:1)*Y.width),Re(Re({},d),{},Yi({},v,Y))},{})},Zs=function(e,n){var r=e.x,i=e.y,a=n.x,o=n.y;return{x:Math.min(r,a),y:Math.min(i,o),width:Math.abs(a-r),height:Math.abs(o-i)}},iO=function(e){var n=e.x1,r=e.y1,i=e.x2,a=e.y2;return Zs({x:n,y:r},{x:i,y:a})},Js=function(){function t(e){tO(this,t),this.scale=e}return rO(t,[{key:"domain",get:function(){return this.scale.domain}},{key:"range",get:function(){return this.scale.range}},{key:"rangeMin",get:function(){return this.range()[0]}},{key:"rangeMax",get:function(){return this.range()[1]}},{key:"bandwidth",get:function(){return this.scale.bandwidth}},{key:"apply",value:function(n){var r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},i=r.bandAware,a=r.position;if(n!==void 0){if(a)switch(a){case"start":return this.scale(n);case"middle":{var o=this.bandwidth?this.bandwidth()/2:0;return this.scale(n)+o}case"end":{var c=this.bandwidth?this.bandwidth():0;return this.scale(n)+c}default:return this.scale(n)}if(i){var l=this.bandwidth?this.bandwidth()/2:0;return this.scale(n)+l}return this.scale(n)}}},{key:"isInRange",value:function(n){var r=this.range(),i=r[0],a=r[r.length-1];return i<=a?n>=i&&n<=a:n>=a&&n<=i}}],[{key:"create",value:function(n){return new t(n)}}])}();Yi(Js,"EPS",1e-4);var Xo=function(e){var n=Object.keys(e).reduce(function(r,i){return Re(Re({},r),{},Yi({},i,Js.create(e[i])))},{});return Re(Re({},n),{},{apply:function(i){var a=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},o=a.bandAware,c=a.position;return kp(i,function(l,u){return n[u].apply(l,{bandAware:o,position:c})})},isInRange:function(i){return zu(i,function(a,o){return n[o].isInRange(a)})}})};function aO(t){return(t%180+180)%180}var oO=function(e){var n=e.width,r=e.height,i=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0,a=aO(i),o=a*Math.PI/180,c=Math.atan(r/n),l=o>c&&o<Math.PI-c?r/Math.sin(o):n/Math.cos(o);return Math.abs(l)},cO=Dp(function(t){return{x:t.left,y:t.top,width:t.width,height:t.height}},function(t){return["l",t.left,"t",t.top,"w",t.width,"h",t.height].join("")}),Go=N.createContext(void 0),Ho=N.createContext(void 0),Qs=N.createContext(void 0),ef=N.createContext({}),tf=N.createContext(void 0),rf=N.createContext(0),nf=N.createContext(0),Yl=function(e){var n=e.state,r=n.xAxisMap,i=n.yAxisMap,a=n.offset,o=e.clipPathId,c=e.children,l=e.width,u=e.height,s=cO(a);return b.createElement(Go.Provider,{value:r},b.createElement(Ho.Provider,{value:i},b.createElement(ef.Provider,{value:a},b.createElement(Qs.Provider,{value:s},b.createElement(tf.Provider,{value:o},b.createElement(rf.Provider,{value:u},b.createElement(nf.Provider,{value:l},c)))))))},lO=function(){return N.useContext(tf)},af=function(e){var n=N.useContext(Go);n==null&&kt();var r=n[e];return r==null&&kt(),r},uO=function(){var e=N.useContext(Go);return ct(e)},sO=function(){var e=N.useContext(Ho),n=Cp(e,function(r){return zu(r.domain,Number.isFinite)});return n||ct(e)},of=function(e){var n=N.useContext(Ho);n==null&&kt();var r=n[e];return r==null&&kt(),r},fO=function(){var e=N.useContext(Qs);return e},pO=function(){return N.useContext(ef)},Uo=function(){return N.useContext(nf)},Yo=function(){return N.useContext(rf)};function dr(t){"@babel/helpers - typeof";return dr=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},dr(t)}function dO(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function vO(t,e){for(var n=0;n<e.length;n++){var r=e[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,lf(r.key),r)}}function hO(t,e,n){return e&&vO(t.prototype,e),Object.defineProperty(t,"prototype",{writable:!1}),t}function yO(t,e,n){return e=bi(e),mO(t,cf()?Reflect.construct(e,n||[],bi(t).constructor):e.apply(t,n))}function mO(t,e){if(e&&(dr(e)==="object"||typeof e=="function"))return e;if(e!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return gO(t)}function gO(t){if(t===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}function cf(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(cf=function(){return!!t})()}function bi(t){return bi=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(n){return n.__proto__||Object.getPrototypeOf(n)},bi(t)}function bO(t,e){if(typeof e!="function"&&e!==null)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&co(t,e)}function co(t,e){return co=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(r,i){return r.__proto__=i,r},co(t,e)}function ql(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter(function(i){return Object.getOwnPropertyDescriptor(t,i).enumerable})),n.push.apply(n,r)}return n}function Zl(t){for(var e=1;e<arguments.length;e++){var n=arguments[e]!=null?arguments[e]:{};e%2?ql(Object(n),!0).forEach(function(r){qo(t,r,n[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):ql(Object(n)).forEach(function(r){Object.defineProperty(t,r,Object.getOwnPropertyDescriptor(n,r))})}return t}function qo(t,e,n){return e=lf(e),e in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function lf(t){var e=OO(t,"string");return dr(e)=="symbol"?e:e+""}function OO(t,e){if(dr(t)!="object"||!t)return t;var n=t[Symbol.toPrimitive];if(n!==void 0){var r=n.call(t,e);if(dr(r)!="object")return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}function xO(t,e){return SO(t)||PO(t,e)||AO(t,e)||wO()}function wO(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function AO(t,e){if(t){if(typeof t=="string")return Jl(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);if(n==="Object"&&t.constructor&&(n=t.constructor.name),n==="Map"||n==="Set")return Array.from(t);if(n==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return Jl(t,e)}}function Jl(t,e){(e==null||e>t.length)&&(e=t.length);for(var n=0,r=new Array(e);n<e;n++)r[n]=t[n];return r}function PO(t,e){var n=t==null?null:typeof Symbol<"u"&&t[Symbol.iterator]||t["@@iterator"];if(n!=null){var r,i,a,o,c=[],l=!0,u=!1;try{if(a=(n=n.call(t)).next,e!==0)for(;!(l=(r=a.call(n)).done)&&(c.push(r.value),c.length!==e);l=!0);}catch(s){u=!0,i=s}finally{try{if(!l&&n.return!=null&&(o=n.return(),Object(o)!==o))return}finally{if(u)throw i}}return c}}function SO(t){if(Array.isArray(t))return t}function lo(){return lo=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var n=arguments[e];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(t[r]=n[r])}return t},lo.apply(this,arguments)}var jO=function(e,n){var r;return b.isValidElement(e)?r=b.cloneElement(e,n):K(e)?r=e(n):r=b.createElement("line",lo({},n,{className:"recharts-reference-line-line"})),r},EO=function(e,n,r,i,a,o,c,l,u){var s=a.x,f=a.y,p=a.width,d=a.height;if(r){var v=u.y,h=e.y.apply(v,{position:o});if(Ve(u,"discard")&&!e.y.isInRange(h))return null;var m=[{x:s+p,y:h},{x:s,y:h}];return l==="left"?m.reverse():m}if(n){var O=u.x,w=e.x.apply(O,{position:o});if(Ve(u,"discard")&&!e.x.isInRange(w))return null;var x=[{x:w,y:f+d},{x:w,y:f}];return c==="top"?x.reverse():x}if(i){var A=u.segment,y=A.map(function(g){return e.apply(g,{position:o})});return Ve(u,"discard")&&Mp(y,function(g){return!e.isInRange(g)})?null:y}return null};function $O(t){var e=t.x,n=t.y,r=t.segment,i=t.xAxisId,a=t.yAxisId,o=t.shape,c=t.className,l=t.alwaysShow,u=lO(),s=af(i),f=of(a),p=fO();if(!u||!p)return null;Ne(l===void 0,'The alwaysShow prop is deprecated. Please use ifOverflow="extendDomain" instead.');var d=Xo({x:s.scale,y:f.scale}),v=pe(e),h=pe(n),m=r&&r.length===2,O=EO(d,v,h,m,p,t.position,s.orientation,f.orientation,t);if(!O)return null;var w=xO(O,2),x=w[0],A=x.x,y=x.y,g=w[1],P=g.x,S=g.y,j=Ve(t,"hidden")?"url(#".concat(u,")"):void 0,E=Zl(Zl({clipPath:j},L(t,!0)),{},{x1:A,y1:y,x2:P,y2:S});return b.createElement(F,{className:X("recharts-reference-line",c)},jO(o,E),ve.renderCallByParent(t,iO({x1:A,y1:y,x2:P,y2:S})))}var Zo=function(t){function e(){return dO(this,e),yO(this,e,arguments)}return bO(e,t),hO(e,[{key:"render",value:function(){return b.createElement($O,this.props)}}])}(b.Component);qo(Zo,"displayName","ReferenceLine");qo(Zo,"defaultProps",{isFront:!1,ifOverflow:"discard",xAxisId:0,yAxisId:0,fill:"none",stroke:"#ccc",fillOpacity:1,strokeWidth:1,position:"middle"});function uo(){return uo=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var n=arguments[e];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(t[r]=n[r])}return t},uo.apply(this,arguments)}function vr(t){"@babel/helpers - typeof";return vr=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},vr(t)}function Ql(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter(function(i){return Object.getOwnPropertyDescriptor(t,i).enumerable})),n.push.apply(n,r)}return n}function eu(t){for(var e=1;e<arguments.length;e++){var n=arguments[e]!=null?arguments[e]:{};e%2?Ql(Object(n),!0).forEach(function(r){qi(t,r,n[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):Ql(Object(n)).forEach(function(r){Object.defineProperty(t,r,Object.getOwnPropertyDescriptor(n,r))})}return t}function _O(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function TO(t,e){for(var n=0;n<e.length;n++){var r=e[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,sf(r.key),r)}}function IO(t,e,n){return e&&TO(t.prototype,e),Object.defineProperty(t,"prototype",{writable:!1}),t}function kO(t,e,n){return e=Oi(e),DO(t,uf()?Reflect.construct(e,n||[],Oi(t).constructor):e.apply(t,n))}function DO(t,e){if(e&&(vr(e)==="object"||typeof e=="function"))return e;if(e!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return CO(t)}function CO(t){if(t===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}function uf(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(uf=function(){return!!t})()}function Oi(t){return Oi=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(n){return n.__proto__||Object.getPrototypeOf(n)},Oi(t)}function MO(t,e){if(typeof e!="function"&&e!==null)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&so(t,e)}function so(t,e){return so=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(r,i){return r.__proto__=i,r},so(t,e)}function qi(t,e,n){return e=sf(e),e in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function sf(t){var e=NO(t,"string");return vr(e)=="symbol"?e:e+""}function NO(t,e){if(vr(t)!="object"||!t)return t;var n=t[Symbol.toPrimitive];if(n!==void 0){var r=n.call(t,e);if(vr(r)!="object")return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}var RO=function(e){var n=e.x,r=e.y,i=e.xAxis,a=e.yAxis,o=Xo({x:i.scale,y:a.scale}),c=o.apply({x:n,y:r},{bandAware:!0});return Ve(e,"discard")&&!o.isInRange(c)?null:c},Zi=function(t){function e(){return _O(this,e),kO(this,e,arguments)}return MO(e,t),IO(e,[{key:"render",value:function(){var r=this.props,i=r.x,a=r.y,o=r.r,c=r.alwaysShow,l=r.clipPathId,u=pe(i),s=pe(a);if(Ne(c===void 0,'The alwaysShow prop is deprecated. Please use ifOverflow="extendDomain" instead.'),!u||!s)return null;var f=RO(this.props);if(!f)return null;var p=f.x,d=f.y,v=this.props,h=v.shape,m=v.className,O=Ve(this.props,"hidden")?"url(#".concat(l,")"):void 0,w=eu(eu({clipPath:O},L(this.props,!0)),{},{cx:p,cy:d});return b.createElement(F,{className:X("recharts-reference-dot",m)},e.renderDot(h,w),ve.renderCallByParent(this.props,{x:p-o,y:d-o,width:2*o,height:2*o}))}}])}(b.Component);qi(Zi,"displayName","ReferenceDot");qi(Zi,"defaultProps",{isFront:!1,ifOverflow:"discard",xAxisId:0,yAxisId:0,r:10,fill:"#fff",stroke:"#ccc",fillOpacity:1,strokeWidth:1});qi(Zi,"renderDot",function(t,e){var n;return b.isValidElement(t)?n=b.cloneElement(t,e):K(t)?n=t(e):n=b.createElement(Ir,uo({},e,{cx:e.cx,cy:e.cy,className:"recharts-reference-dot-dot"})),n});function fo(){return fo=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var n=arguments[e];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(t[r]=n[r])}return t},fo.apply(this,arguments)}function hr(t){"@babel/helpers - typeof";return hr=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},hr(t)}function tu(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter(function(i){return Object.getOwnPropertyDescriptor(t,i).enumerable})),n.push.apply(n,r)}return n}function ru(t){for(var e=1;e<arguments.length;e++){var n=arguments[e]!=null?arguments[e]:{};e%2?tu(Object(n),!0).forEach(function(r){Ji(t,r,n[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):tu(Object(n)).forEach(function(r){Object.defineProperty(t,r,Object.getOwnPropertyDescriptor(n,r))})}return t}function BO(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function LO(t,e){for(var n=0;n<e.length;n++){var r=e[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,pf(r.key),r)}}function WO(t,e,n){return e&&LO(t.prototype,e),Object.defineProperty(t,"prototype",{writable:!1}),t}function zO(t,e,n){return e=xi(e),KO(t,ff()?Reflect.construct(e,n||[],xi(t).constructor):e.apply(t,n))}function KO(t,e){if(e&&(hr(e)==="object"||typeof e=="function"))return e;if(e!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return FO(t)}function FO(t){if(t===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}function ff(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(ff=function(){return!!t})()}function xi(t){return xi=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(n){return n.__proto__||Object.getPrototypeOf(n)},xi(t)}function VO(t,e){if(typeof e!="function"&&e!==null)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&po(t,e)}function po(t,e){return po=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(r,i){return r.__proto__=i,r},po(t,e)}function Ji(t,e,n){return e=pf(e),e in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function pf(t){var e=XO(t,"string");return hr(e)=="symbol"?e:e+""}function XO(t,e){if(hr(t)!="object"||!t)return t;var n=t[Symbol.toPrimitive];if(n!==void 0){var r=n.call(t,e);if(hr(r)!="object")return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}var GO=function(e,n,r,i,a){var o=a.x1,c=a.x2,l=a.y1,u=a.y2,s=a.xAxis,f=a.yAxis;if(!s||!f)return null;var p=Xo({x:s.scale,y:f.scale}),d={x:e?p.x.apply(o,{position:"start"}):p.x.rangeMin,y:r?p.y.apply(l,{position:"start"}):p.y.rangeMin},v={x:n?p.x.apply(c,{position:"end"}):p.x.rangeMax,y:i?p.y.apply(u,{position:"end"}):p.y.rangeMax};return Ve(a,"discard")&&(!p.isInRange(d)||!p.isInRange(v))?null:Zs(d,v)},Qi=function(t){function e(){return BO(this,e),zO(this,e,arguments)}return VO(e,t),WO(e,[{key:"render",value:function(){var r=this.props,i=r.x1,a=r.x2,o=r.y1,c=r.y2,l=r.className,u=r.alwaysShow,s=r.clipPathId;Ne(u===void 0,'The alwaysShow prop is deprecated. Please use ifOverflow="extendDomain" instead.');var f=pe(i),p=pe(a),d=pe(o),v=pe(c),h=this.props.shape;if(!f&&!p&&!d&&!v&&!h)return null;var m=GO(f,p,d,v,this.props);if(!m&&!h)return null;var O=Ve(this.props,"hidden")?"url(#".concat(s,")"):void 0;return b.createElement(F,{className:X("recharts-reference-area",l)},e.renderRect(h,ru(ru({clipPath:O},L(this.props,!0)),m)),ve.renderCallByParent(this.props,m))}}])}(b.Component);Ji(Qi,"displayName","ReferenceArea");Ji(Qi,"defaultProps",{isFront:!1,ifOverflow:"discard",xAxisId:0,yAxisId:0,r:10,fill:"#ccc",fillOpacity:.5,stroke:"none",strokeWidth:1});Ji(Qi,"renderRect",function(t,e){var n;return b.isValidElement(t)?n=b.cloneElement(t,e):K(t)?n=t(e):n=b.createElement(En,fo({},e,{className:"recharts-reference-area-rect"})),n});function df(t,e,n){if(e<1)return[];if(e===1&&n===void 0)return t;for(var r=[],i=0;i<t.length;i+=e)r.push(t[i]);return r}function HO(t,e,n){var r={width:t.width+e.width,height:t.height+e.height};return oO(r,n)}function UO(t,e,n){var r=n==="width",i=t.x,a=t.y,o=t.width,c=t.height;return e===1?{start:r?i:a,end:r?i+o:a+c}:{start:r?i+o:a+c,end:r?i:a}}function wi(t,e,n,r,i){if(t*e<t*r||t*e>t*i)return!1;var a=n();return t*(e-t*a/2-r)>=0&&t*(e+t*a/2-i)<=0}function YO(t,e){return df(t,e+1)}function qO(t,e,n,r,i){for(var a=(r||[]).slice(),o=e.start,c=e.end,l=0,u=1,s=o,f=function(){var v=r?.[l];if(v===void 0)return{v:df(r,u)};var h=l,m,O=function(){return m===void 0&&(m=n(v,h)),m},w=v.coordinate,x=l===0||wi(t,w,O,s,c);x||(l=0,s=o,u+=1),x&&(s=w+t*(O()/2+i),l+=u)},p;u<=a.length;)if(p=f(),p)return p.v;return[]}function On(t){"@babel/helpers - typeof";return On=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},On(t)}function nu(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter(function(i){return Object.getOwnPropertyDescriptor(t,i).enumerable})),n.push.apply(n,r)}return n}function xe(t){for(var e=1;e<arguments.length;e++){var n=arguments[e]!=null?arguments[e]:{};e%2?nu(Object(n),!0).forEach(function(r){ZO(t,r,n[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):nu(Object(n)).forEach(function(r){Object.defineProperty(t,r,Object.getOwnPropertyDescriptor(n,r))})}return t}function ZO(t,e,n){return e=JO(e),e in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function JO(t){var e=QO(t,"string");return On(e)=="symbol"?e:e+""}function QO(t,e){if(On(t)!="object"||!t)return t;var n=t[Symbol.toPrimitive];if(n!==void 0){var r=n.call(t,e);if(On(r)!="object")return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(t)}function ex(t,e,n,r,i){for(var a=(r||[]).slice(),o=a.length,c=e.start,l=e.end,u=function(p){var d=a[p],v,h=function(){return v===void 0&&(v=n(d,p)),v};if(p===o-1){var m=t*(d.coordinate+t*h()/2-l);a[p]=d=xe(xe({},d),{},{tickCoord:m>0?d.coordinate-m*t:d.coordinate})}else a[p]=d=xe(xe({},d),{},{tickCoord:d.coordinate});var O=wi(t,d.tickCoord,h,c,l);O&&(l=d.tickCoord-t*(h()/2+i),a[p]=xe(xe({},d),{},{isShow:!0}))},s=o-1;s>=0;s--)u(s);return a}function tx(t,e,n,r,i,a){var o=(r||[]).slice(),c=o.length,l=e.start,u=e.end;if(a){var s=r[c-1],f=n(s,c-1),p=t*(s.coordinate+t*f/2-u);o[c-1]=s=xe(xe({},s),{},{tickCoord:p>0?s.coordinate-p*t:s.coordinate});var d=wi(t,s.tickCoord,function(){return f},l,u);d&&(u=s.tickCoord-t*(f/2+i),o[c-1]=xe(xe({},s),{},{isShow:!0}))}for(var v=a?c-1:c,h=function(w){var x=o[w],A,y=function(){return A===void 0&&(A=n(x,w)),A};if(w===0){var g=t*(x.coordinate-t*y()/2-l);o[w]=x=xe(xe({},x),{},{tickCoord:g<0?x.coordinate-g*t:x.coordinate})}else o[w]=x=xe(xe({},x),{},{tickCoord:x.coordinate});var P=wi(t,x.tickCoord,y,l,u);P&&(l=x.tickCoord+t*(y()/2+i),o[w]=xe(xe({},x),{},{isShow:!0}))},m=0;m<v;m++)h(m);return o}function Jo(t,e,n){var r=t.tick,i=t.ticks,a=t.viewBox,o=t.minTickGap,c=t.orientation,l=t.interval,u=t.tickFormatter,s=t.unit,f=t.angle;if(!i||!i.length||!r)return[];if(R(l)||be.isSsr)return YO(i,typeof l=="number"&&R(l)?l:0);var p=[],d=c==="top"||c==="bottom"?"width":"height",v=s&&d==="width"?Yt(s,{fontSize:e,letterSpacing:n}):{width:0,height:0},h=function(x,A){var y=K(u)?u(x.value,A):x.value;return d==="width"?HO(Yt(y,{fontSize:e,letterSpacing:n}),v,f):Yt(y,{fontSize:e,letterSpacing:n})[d]},m=i.length>=2?ye(i[1].coordinate-i[0].coordinate):1,O=UO(a,m,d);return l==="equidistantPreserveStart"?qO(m,O,h,i,o):(l==="preserveStart"||l==="preserveStartEnd"?p=tx(m,O,h,i,o,l==="preserveStartEnd"):p=ex(m,O,h,i,o),p.filter(function(w){return w.isShow}))}var rx=["viewBox"],nx=["viewBox"],ix=["ticks"];function yr(t){"@babel/helpers - typeof";return yr=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},yr(t)}function Gt(){return Gt=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var n=arguments[e];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(t[r]=n[r])}return t},Gt.apply(this,arguments)}function iu(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter(function(i){return Object.getOwnPropertyDescriptor(t,i).enumerable})),n.push.apply(n,r)}return n}function ue(t){for(var e=1;e<arguments.length;e++){var n=arguments[e]!=null?arguments[e]:{};e%2?iu(Object(n),!0).forEach(function(r){Qo(t,r,n[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):iu(Object(n)).forEach(function(r){Object.defineProperty(t,r,Object.getOwnPropertyDescriptor(n,r))})}return t}function pa(t,e){if(t==null)return{};var n=ax(t,e),r,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(t);for(i=0;i<a.length;i++)r=a[i],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(n[r]=t[r])}return n}function ax(t,e){if(t==null)return{};var n={};for(var r in t)if(Object.prototype.hasOwnProperty.call(t,r)){if(e.indexOf(r)>=0)continue;n[r]=t[r]}return n}function ox(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function au(t,e){for(var n=0;n<e.length;n++){var r=e[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,hf(r.key),r)}}function cx(t,e,n){return e&&au(t.prototype,e),n&&au(t,n),Object.defineProperty(t,"prototype",{writable:!1}),t}function lx(t,e,n){return e=Ai(e),ux(t,vf()?Reflect.construct(e,n||[],Ai(t).constructor):e.apply(t,n))}function ux(t,e){if(e&&(yr(e)==="object"||typeof e=="function"))return e;if(e!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return sx(t)}function sx(t){if(t===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}function vf(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(vf=function(){return!!t})()}function Ai(t){return Ai=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(n){return n.__proto__||Object.getPrototypeOf(n)},Ai(t)}function fx(t,e){if(typeof e!="function"&&e!==null)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&vo(t,e)}function vo(t,e){return vo=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(r,i){return r.__proto__=i,r},vo(t,e)}function Qo(t,e,n){return e=hf(e),e in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function hf(t){var e=px(t,"string");return yr(e)=="symbol"?e:e+""}function px(t,e){if(yr(t)!="object"||!t)return t;var n=t[Symbol.toPrimitive];if(n!==void 0){var r=n.call(t,e);if(yr(r)!="object")return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}var Cr=function(t){function e(n){var r;return ox(this,e),r=lx(this,e,[n]),r.state={fontSize:"",letterSpacing:""},r}return fx(e,t),cx(e,[{key:"shouldComponentUpdate",value:function(r,i){var a=r.viewBox,o=pa(r,rx),c=this.props,l=c.viewBox,u=pa(c,nx);return!Tt(a,l)||!Tt(o,u)||!Tt(i,this.state)}},{key:"componentDidMount",value:function(){var r=this.layerReference;if(r){var i=r.getElementsByClassName("recharts-cartesian-axis-tick-value")[0];i&&this.setState({fontSize:window.getComputedStyle(i).fontSize,letterSpacing:window.getComputedStyle(i).letterSpacing})}}},{key:"getTickLineCoord",value:function(r){var i=this.props,a=i.x,o=i.y,c=i.width,l=i.height,u=i.orientation,s=i.tickSize,f=i.mirror,p=i.tickMargin,d,v,h,m,O,w,x=f?-1:1,A=r.tickSize||s,y=R(r.tickCoord)?r.tickCoord:r.coordinate;switch(u){case"top":d=v=r.coordinate,m=o+ +!f*l,h=m-x*A,w=h-x*p,O=y;break;case"left":h=m=r.coordinate,v=a+ +!f*c,d=v-x*A,O=d-x*p,w=y;break;case"right":h=m=r.coordinate,v=a+ +f*c,d=v+x*A,O=d+x*p,w=y;break;default:d=v=r.coordinate,m=o+ +f*l,h=m+x*A,w=h+x*p,O=y;break}return{line:{x1:d,y1:h,x2:v,y2:m},tick:{x:O,y:w}}}},{key:"getTickTextAnchor",value:function(){var r=this.props,i=r.orientation,a=r.mirror,o;switch(i){case"left":o=a?"start":"end";break;case"right":o=a?"end":"start";break;default:o="middle";break}return o}},{key:"getTickVerticalAnchor",value:function(){var r=this.props,i=r.orientation,a=r.mirror,o="end";switch(i){case"left":case"right":o="middle";break;case"top":o=a?"start":"end";break;default:o=a?"end":"start";break}return o}},{key:"renderAxisLine",value:function(){var r=this.props,i=r.x,a=r.y,o=r.width,c=r.height,l=r.orientation,u=r.mirror,s=r.axisLine,f=ue(ue(ue({},L(this.props,!1)),L(s,!1)),{},{fill:"none"});if(l==="top"||l==="bottom"){var p=+(l==="top"&&!u||l==="bottom"&&u);f=ue(ue({},f),{},{x1:i,y1:a+p*c,x2:i+o,y2:a+p*c})}else{var d=+(l==="left"&&!u||l==="right"&&u);f=ue(ue({},f),{},{x1:i+d*o,y1:a,x2:i+d*o,y2:a+c})}return b.createElement("line",Gt({},f,{className:X("recharts-cartesian-axis-line",he(s,"className"))}))}},{key:"renderTicks",value:function(r,i,a){var o=this,c=this.props,l=c.tickLine,u=c.stroke,s=c.tick,f=c.tickFormatter,p=c.unit,d=Jo(ue(ue({},this.props),{},{ticks:r}),i,a),v=this.getTickTextAnchor(),h=this.getTickVerticalAnchor(),m=L(this.props,!1),O=L(s,!1),w=ue(ue({},m),{},{fill:"none"},L(l,!1)),x=d.map(function(A,y){var g=o.getTickLineCoord(A),P=g.line,S=g.tick,j=ue(ue(ue(ue({textAnchor:v,verticalAnchor:h},m),{},{stroke:"none",fill:u},O),S),{},{index:y,payload:A,visibleTicksCount:d.length,tickFormatter:f});return b.createElement(F,Gt({className:"recharts-cartesian-axis-tick",key:"tick-".concat(A.value,"-").concat(A.coordinate,"-").concat(A.tickCoord)},We(o.props,A,y)),l&&b.createElement("line",Gt({},w,P,{className:X("recharts-cartesian-axis-tick-line",he(l,"className"))})),s&&e.renderTickItem(s,j,"".concat(K(f)?f(A.value,y):A.value).concat(p||"")))});return b.createElement("g",{className:"recharts-cartesian-axis-ticks"},x)}},{key:"render",value:function(){var r=this,i=this.props,a=i.axisLine,o=i.width,c=i.height,l=i.ticksGenerator,u=i.className,s=i.hide;if(s)return null;var f=this.props,p=f.ticks,d=pa(f,ix),v=p;return K(l)&&(v=p&&p.length>0?l(this.props):l(d)),o<=0||c<=0||!v||!v.length?null:b.createElement(F,{className:X("recharts-cartesian-axis",u),ref:function(m){r.layerReference=m}},a&&this.renderAxisLine(),this.renderTicks(v,this.state.fontSize,this.state.letterSpacing),ve.renderCallByParent(this.props))}}],[{key:"renderTickItem",value:function(r,i,a){var o,c=X(i.className,"recharts-cartesian-axis-tick-value");return b.isValidElement(r)?o=b.cloneElement(r,ue(ue({},i),{},{className:c})):K(r)?o=r(ue(ue({},i),{},{className:c})):o=b.createElement(dt,Gt({},i,{className:"recharts-cartesian-axis-tick-value"}),a),o}}])}(N.Component);Qo(Cr,"displayName","CartesianAxis");Qo(Cr,"defaultProps",{x:0,y:0,width:0,height:0,viewBox:{x:0,y:0,width:0,height:0},orientation:"bottom",ticks:[],stroke:"#666",tickLine:!0,axisLine:!0,tick:!0,mirror:!1,minTickGap:5,tickSize:6,tickMargin:2,interval:"preserveEnd"});var dx=["x1","y1","x2","y2","key"],vx=["offset"];function Dt(t){"@babel/helpers - typeof";return Dt=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Dt(t)}function ou(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter(function(i){return Object.getOwnPropertyDescriptor(t,i).enumerable})),n.push.apply(n,r)}return n}function we(t){for(var e=1;e<arguments.length;e++){var n=arguments[e]!=null?arguments[e]:{};e%2?ou(Object(n),!0).forEach(function(r){hx(t,r,n[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):ou(Object(n)).forEach(function(r){Object.defineProperty(t,r,Object.getOwnPropertyDescriptor(n,r))})}return t}function hx(t,e,n){return e=yx(e),e in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function yx(t){var e=mx(t,"string");return Dt(e)=="symbol"?e:e+""}function mx(t,e){if(Dt(t)!="object"||!t)return t;var n=t[Symbol.toPrimitive];if(n!==void 0){var r=n.call(t,e);if(Dt(r)!="object")return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(t)}function $t(){return $t=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var n=arguments[e];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(t[r]=n[r])}return t},$t.apply(this,arguments)}function cu(t,e){if(t==null)return{};var n=gx(t,e),r,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(t);for(i=0;i<a.length;i++)r=a[i],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(n[r]=t[r])}return n}function gx(t,e){if(t==null)return{};var n={};for(var r in t)if(Object.prototype.hasOwnProperty.call(t,r)){if(e.indexOf(r)>=0)continue;n[r]=t[r]}return n}var bx=function(e){var n=e.fill;if(!n||n==="none")return null;var r=e.fillOpacity,i=e.x,a=e.y,o=e.width,c=e.height,l=e.ry;return b.createElement("rect",{x:i,y:a,ry:l,width:o,height:c,stroke:"none",fill:n,fillOpacity:r,className:"recharts-cartesian-grid-bg"})};function yf(t,e){var n;if(b.isValidElement(t))n=b.cloneElement(t,e);else if(K(t))n=t(e);else{var r=e.x1,i=e.y1,a=e.x2,o=e.y2,c=e.key,l=cu(e,dx),u=L(l,!1);u.offset;var s=cu(u,vx);n=b.createElement("line",$t({},s,{x1:r,y1:i,x2:a,y2:o,fill:"none",key:c}))}return n}function Ox(t){var e=t.x,n=t.width,r=t.horizontal,i=r===void 0?!0:r,a=t.horizontalPoints;if(!i||!a||!a.length)return null;var o=a.map(function(c,l){var u=we(we({},t),{},{x1:e,y1:c,x2:e+n,y2:c,key:"line-".concat(l),index:l});return yf(i,u)});return b.createElement("g",{className:"recharts-cartesian-grid-horizontal"},o)}function xx(t){var e=t.y,n=t.height,r=t.vertical,i=r===void 0?!0:r,a=t.verticalPoints;if(!i||!a||!a.length)return null;var o=a.map(function(c,l){var u=we(we({},t),{},{x1:c,y1:e,x2:c,y2:e+n,key:"line-".concat(l),index:l});return yf(i,u)});return b.createElement("g",{className:"recharts-cartesian-grid-vertical"},o)}function wx(t){var e=t.horizontalFill,n=t.fillOpacity,r=t.x,i=t.y,a=t.width,o=t.height,c=t.horizontalPoints,l=t.horizontal,u=l===void 0?!0:l;if(!u||!e||!e.length)return null;var s=c.map(function(p){return Math.round(p+i-i)}).sort(function(p,d){return p-d});i!==s[0]&&s.unshift(0);var f=s.map(function(p,d){var v=!s[d+1],h=v?i+o-p:s[d+1]-p;if(h<=0)return null;var m=d%e.length;return b.createElement("rect",{key:"react-".concat(d),y:p,x:r,height:h,width:a,stroke:"none",fill:e[m],fillOpacity:n,className:"recharts-cartesian-grid-bg"})});return b.createElement("g",{className:"recharts-cartesian-gridstripes-horizontal"},f)}function Ax(t){var e=t.vertical,n=e===void 0?!0:e,r=t.verticalFill,i=t.fillOpacity,a=t.x,o=t.y,c=t.width,l=t.height,u=t.verticalPoints;if(!n||!r||!r.length)return null;var s=u.map(function(p){return Math.round(p+a-a)}).sort(function(p,d){return p-d});a!==s[0]&&s.unshift(0);var f=s.map(function(p,d){var v=!s[d+1],h=v?a+c-p:s[d+1]-p;if(h<=0)return null;var m=d%r.length;return b.createElement("rect",{key:"react-".concat(d),x:p,y:o,width:h,height:l,stroke:"none",fill:r[m],fillOpacity:i,className:"recharts-cartesian-grid-bg"})});return b.createElement("g",{className:"recharts-cartesian-gridstripes-vertical"},f)}var Px=function(e,n){var r=e.xAxis,i=e.width,a=e.height,o=e.offset;return Os(Jo(we(we(we({},Cr.defaultProps),r),{},{ticks:Je(r,!0),viewBox:{x:0,y:0,width:i,height:a}})),o.left,o.left+o.width,n)},Sx=function(e,n){var r=e.yAxis,i=e.width,a=e.height,o=e.offset;return Os(Jo(we(we(we({},Cr.defaultProps),r),{},{ticks:Je(r,!0),viewBox:{x:0,y:0,width:i,height:a}})),o.top,o.top+o.height,n)},zt={horizontal:!0,vertical:!0,stroke:"#ccc",fill:"none",verticalFill:[],horizontalFill:[]};function jx(t){var e,n,r,i,a,o,c=Uo(),l=Yo(),u=pO(),s=we(we({},t),{},{stroke:(e=t.stroke)!==null&&e!==void 0?e:zt.stroke,fill:(n=t.fill)!==null&&n!==void 0?n:zt.fill,horizontal:(r=t.horizontal)!==null&&r!==void 0?r:zt.horizontal,horizontalFill:(i=t.horizontalFill)!==null&&i!==void 0?i:zt.horizontalFill,vertical:(a=t.vertical)!==null&&a!==void 0?a:zt.vertical,verticalFill:(o=t.verticalFill)!==null&&o!==void 0?o:zt.verticalFill,x:R(t.x)?t.x:u.left,y:R(t.y)?t.y:u.top,width:R(t.width)?t.width:u.width,height:R(t.height)?t.height:u.height}),f=s.x,p=s.y,d=s.width,v=s.height,h=s.syncWithTicks,m=s.horizontalValues,O=s.verticalValues,w=uO(),x=sO();if(!R(d)||d<=0||!R(v)||v<=0||!R(f)||f!==+f||!R(p)||p!==+p)return null;var A=s.verticalCoordinatesGenerator||Px,y=s.horizontalCoordinatesGenerator||Sx,g=s.horizontalPoints,P=s.verticalPoints;if((!g||!g.length)&&K(y)){var S=m&&m.length,j=y({yAxis:x?we(we({},x),{},{ticks:S?m:x.ticks}):void 0,width:c,height:l,offset:u},S?!0:h);Ne(Array.isArray(j),"horizontalCoordinatesGenerator should return Array but instead it returned [".concat(Dt(j),"]")),Array.isArray(j)&&(g=j)}if((!P||!P.length)&&K(A)){var E=O&&O.length,$=A({xAxis:w?we(we({},w),{},{ticks:E?O:w.ticks}):void 0,width:c,height:l,offset:u},E?!0:h);Ne(Array.isArray($),"verticalCoordinatesGenerator should return Array but instead it returned [".concat(Dt($),"]")),Array.isArray($)&&(P=$)}return b.createElement("g",{className:"recharts-cartesian-grid"},b.createElement(bx,{fill:s.fill,fillOpacity:s.fillOpacity,x:s.x,y:s.y,width:s.width,height:s.height,ry:s.ry}),b.createElement(Ox,$t({},s,{offset:u,horizontalPoints:g,xAxis:w,yAxis:x})),b.createElement(xx,$t({},s,{offset:u,verticalPoints:P,xAxis:w,yAxis:x})),b.createElement(wx,$t({},s,{horizontalPoints:g})),b.createElement(Ax,$t({},s,{verticalPoints:P})))}jx.displayName="CartesianGrid";var Ex=["type","layout","connectNulls","ref"],$x=["key"];function mr(t){"@babel/helpers - typeof";return mr=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},mr(t)}function lu(t,e){if(t==null)return{};var n=_x(t,e),r,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(t);for(i=0;i<a.length;i++)r=a[i],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(n[r]=t[r])}return n}function _x(t,e){if(t==null)return{};var n={};for(var r in t)if(Object.prototype.hasOwnProperty.call(t,r)){if(e.indexOf(r)>=0)continue;n[r]=t[r]}return n}function Fr(){return Fr=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var n=arguments[e];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(t[r]=n[r])}return t},Fr.apply(this,arguments)}function uu(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter(function(i){return Object.getOwnPropertyDescriptor(t,i).enumerable})),n.push.apply(n,r)}return n}function Te(t){for(var e=1;e<arguments.length;e++){var n=arguments[e]!=null?arguments[e]:{};e%2?uu(Object(n),!0).forEach(function(r){Be(t,r,n[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):uu(Object(n)).forEach(function(r){Object.defineProperty(t,r,Object.getOwnPropertyDescriptor(n,r))})}return t}function Kt(t){return Dx(t)||kx(t)||Ix(t)||Tx()}function Tx(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function Ix(t,e){if(t){if(typeof t=="string")return ho(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);if(n==="Object"&&t.constructor&&(n=t.constructor.name),n==="Map"||n==="Set")return Array.from(t);if(n==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return ho(t,e)}}function kx(t){if(typeof Symbol<"u"&&t[Symbol.iterator]!=null||t["@@iterator"]!=null)return Array.from(t)}function Dx(t){if(Array.isArray(t))return ho(t)}function ho(t,e){(e==null||e>t.length)&&(e=t.length);for(var n=0,r=new Array(e);n<e;n++)r[n]=t[n];return r}function Cx(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function su(t,e){for(var n=0;n<e.length;n++){var r=e[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,gf(r.key),r)}}function Mx(t,e,n){return e&&su(t.prototype,e),n&&su(t,n),Object.defineProperty(t,"prototype",{writable:!1}),t}function Nx(t,e,n){return e=Pi(e),Rx(t,mf()?Reflect.construct(e,n||[],Pi(t).constructor):e.apply(t,n))}function Rx(t,e){if(e&&(mr(e)==="object"||typeof e=="function"))return e;if(e!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return Bx(t)}function Bx(t){if(t===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}function mf(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(mf=function(){return!!t})()}function Pi(t){return Pi=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(n){return n.__proto__||Object.getPrototypeOf(n)},Pi(t)}function Lx(t,e){if(typeof e!="function"&&e!==null)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&yo(t,e)}function yo(t,e){return yo=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(r,i){return r.__proto__=i,r},yo(t,e)}function Be(t,e,n){return e=gf(e),e in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function gf(t){var e=Wx(t,"string");return mr(e)=="symbol"?e:e+""}function Wx(t,e){if(mr(t)!="object"||!t)return t;var n=t[Symbol.toPrimitive];if(n!==void 0){var r=n.call(t,e);if(mr(r)!="object")return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}var _n=function(t){function e(){var n;Cx(this,e);for(var r=arguments.length,i=new Array(r),a=0;a<r;a++)i[a]=arguments[a];return n=Nx(this,e,[].concat(i)),Be(n,"state",{isAnimationFinished:!0,totalLength:0}),Be(n,"generateSimpleStrokeDasharray",function(o,c){return"".concat(c,"px ").concat(o-c,"px")}),Be(n,"getStrokeDasharray",function(o,c,l){var u=l.reduce(function(O,w){return O+w});if(!u)return n.generateSimpleStrokeDasharray(c,o);for(var s=Math.floor(o/u),f=o%u,p=c-o,d=[],v=0,h=0;v<l.length;h+=l[v],++v)if(h+l[v]>f){d=[].concat(Kt(l.slice(0,v)),[f-h]);break}var m=d.length%2===0?[0,p]:[p];return[].concat(Kt(e.repeat(l,s)),Kt(d),m).map(function(O){return"".concat(O,"px")}).join(", ")}),Be(n,"id",nt("recharts-line-")),Be(n,"pathRef",function(o){n.mainCurve=o}),Be(n,"handleAnimationEnd",function(){n.setState({isAnimationFinished:!0}),n.props.onAnimationEnd&&n.props.onAnimationEnd()}),Be(n,"handleAnimationStart",function(){n.setState({isAnimationFinished:!1}),n.props.onAnimationStart&&n.props.onAnimationStart()}),n}return Lx(e,t),Mx(e,[{key:"componentDidMount",value:function(){if(this.props.isAnimationActive){var r=this.getTotalLength();this.setState({totalLength:r})}}},{key:"componentDidUpdate",value:function(){if(this.props.isAnimationActive){var r=this.getTotalLength();r!==this.state.totalLength&&this.setState({totalLength:r})}}},{key:"getTotalLength",value:function(){var r=this.mainCurve;try{return r&&r.getTotalLength&&r.getTotalLength()||0}catch{return 0}}},{key:"renderErrorBar",value:function(r,i){if(this.props.isAnimationActive&&!this.state.isAnimationFinished)return null;var a=this.props,o=a.points,c=a.xAxis,l=a.yAxis,u=a.layout,s=a.children,f=ge(s,Tr);if(!f)return null;var p=function(h,m){return{x:h.x,y:h.y,value:h.value,errorVal:Z(h.payload,m)}},d={clipPath:r?"url(#clipPath-".concat(i,")"):null};return b.createElement(F,d,f.map(function(v){return b.cloneElement(v,{key:"bar-".concat(v.props.dataKey),data:o,xAxis:c,yAxis:l,layout:u,dataPointFormatter:p})}))}},{key:"renderDots",value:function(r,i,a){var o=this.props.isAnimationActive;if(o&&!this.state.isAnimationFinished)return null;var c=this.props,l=c.dot,u=c.points,s=c.dataKey,f=L(this.props,!1),p=L(l,!0),d=u.map(function(h,m){var O=Te(Te(Te({key:"dot-".concat(m),r:3},f),p),{},{index:m,cx:h.x,cy:h.y,value:h.value,dataKey:s,payload:h.payload,points:u});return e.renderDotItem(l,O)}),v={clipPath:r?"url(#clipPath-".concat(i?"":"dots-").concat(a,")"):null};return b.createElement(F,Fr({className:"recharts-line-dots",key:"dots"},v),d)}},{key:"renderCurveStatically",value:function(r,i,a,o){var c=this.props,l=c.type,u=c.layout,s=c.connectNulls;c.ref;var f=lu(c,Ex),p=Te(Te(Te({},L(f,!0)),{},{fill:"none",className:"recharts-line-curve",clipPath:i?"url(#clipPath-".concat(a,")"):null,points:r},o),{},{type:l,layout:u,connectNulls:s});return b.createElement(ft,Fr({},p,{pathRef:this.pathRef}))}},{key:"renderCurveWithAnimation",value:function(r,i){var a=this,o=this.props,c=o.points,l=o.strokeDasharray,u=o.isAnimationActive,s=o.animationBegin,f=o.animationDuration,p=o.animationEasing,d=o.animationId,v=o.animateNewValues,h=o.width,m=o.height,O=this.state,w=O.prevPoints,x=O.totalLength;return b.createElement($e,{begin:s,duration:f,isActive:u,easing:p,from:{t:0},to:{t:1},key:"line-".concat(d),onAnimationEnd:this.handleAnimationEnd,onAnimationStart:this.handleAnimationStart},function(A){var y=A.t;if(w){var g=w.length/c.length,P=c.map(function(_,k){var D=Math.floor(k*g);if(w[D]){var T=w[D],C=q(T.x,_.x),M=q(T.y,_.y);return Te(Te({},_),{},{x:C(y),y:M(y)})}if(v){var B=q(h*2,_.x),W=q(m/2,_.y);return Te(Te({},_),{},{x:B(y),y:W(y)})}return Te(Te({},_),{},{x:_.x,y:_.y})});return a.renderCurveStatically(P,r,i)}var S=q(0,x),j=S(y),E;if(l){var $="".concat(l).split(/[,\s]+/gim).map(function(_){return parseFloat(_)});E=a.getStrokeDasharray(j,x,$)}else E=a.generateSimpleStrokeDasharray(x,j);return a.renderCurveStatically(c,r,i,{strokeDasharray:E})})}},{key:"renderCurve",value:function(r,i){var a=this.props,o=a.points,c=a.isAnimationActive,l=this.state,u=l.prevPoints,s=l.totalLength;return c&&o&&o.length&&(!u&&s>0||!Le(u,o))?this.renderCurveWithAnimation(r,i):this.renderCurveStatically(o,r,i)}},{key:"render",value:function(){var r,i=this.props,a=i.hide,o=i.dot,c=i.points,l=i.className,u=i.xAxis,s=i.yAxis,f=i.top,p=i.left,d=i.width,v=i.height,h=i.isAnimationActive,m=i.id;if(a||!c||!c.length)return null;var O=this.state.isAnimationFinished,w=c.length===1,x=X("recharts-line",l),A=u&&u.allowDataOverflow,y=s&&s.allowDataOverflow,g=A||y,P=G(m)?this.id:m,S=(r=L(o,!1))!==null&&r!==void 0?r:{r:3,strokeWidth:2},j=S.r,E=j===void 0?3:j,$=S.strokeWidth,_=$===void 0?2:$,k=Vu(o)?o:{},D=k.clipDot,T=D===void 0?!0:D,C=E*2+_;return b.createElement(F,{className:x},A||y?b.createElement("defs",null,b.createElement("clipPath",{id:"clipPath-".concat(P)},b.createElement("rect",{x:A?p:p-d/2,y:y?f:f-v/2,width:A?d:d*2,height:y?v:v*2})),!T&&b.createElement("clipPath",{id:"clipPath-dots-".concat(P)},b.createElement("rect",{x:p-C/2,y:f-C/2,width:d+C,height:v+C}))):null,!w&&this.renderCurve(g,P),this.renderErrorBar(g,P),(w||o)&&this.renderDots(g,T,P),(!h||O)&&Ee.renderCallByParent(this.props,c))}}],[{key:"getDerivedStateFromProps",value:function(r,i){return r.animationId!==i.prevAnimationId?{prevAnimationId:r.animationId,curPoints:r.points,prevPoints:i.curPoints}:r.points!==i.curPoints?{curPoints:r.points}:null}},{key:"repeat",value:function(r,i){for(var a=r.length%2!==0?[].concat(Kt(r),[0]):r,o=[],c=0;c<i;++c)o=[].concat(Kt(o),Kt(a));return o}},{key:"renderDotItem",value:function(r,i){var a;if(b.isValidElement(r))a=b.cloneElement(r,i);else if(K(r))a=r(i);else{var o=i.key,c=lu(i,$x),l=X("recharts-line-dot",typeof r!="boolean"?r.className:"");a=b.createElement(Ir,Fr({key:o},c,{className:l}))}return a}}])}(N.PureComponent);Be(_n,"displayName","Line");Be(_n,"defaultProps",{xAxisId:0,yAxisId:0,connectNulls:!1,activeDot:!0,dot:!0,legendType:"line",stroke:"#3182bd",strokeWidth:1,fill:"#fff",points:[],isAnimationActive:!be.isSsr,animateNewValues:!0,animationBegin:0,animationDuration:1500,animationEasing:"ease",hide:!1,label:!1});Be(_n,"getComposedData",function(t){var e=t.props,n=t.xAxis,r=t.yAxis,i=t.xAxisTicks,a=t.yAxisTicks,o=t.dataKey,c=t.bandSize,l=t.displayedData,u=t.offset,s=e.layout,f=l.map(function(p,d){var v=Z(p,o);return s==="horizontal"?{x:nr({axis:n,ticks:i,bandSize:c,entry:p,index:d}),y:G(v)?null:r.scale(v),value:v,payload:p}:{x:G(v)?null:n.scale(v),y:nr({axis:r,ticks:a,bandSize:c,entry:p,index:d}),value:v,payload:p}});return Te({points:f,layout:s},u)});var zx=["layout","type","stroke","connectNulls","isRange","ref"],Kx=["key"],bf;function gr(t){"@babel/helpers - typeof";return gr=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},gr(t)}function Of(t,e){if(t==null)return{};var n=Fx(t,e),r,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(t);for(i=0;i<a.length;i++)r=a[i],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(n[r]=t[r])}return n}function Fx(t,e){if(t==null)return{};var n={};for(var r in t)if(Object.prototype.hasOwnProperty.call(t,r)){if(e.indexOf(r)>=0)continue;n[r]=t[r]}return n}function _t(){return _t=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var n=arguments[e];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(t[r]=n[r])}return t},_t.apply(this,arguments)}function fu(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter(function(i){return Object.getOwnPropertyDescriptor(t,i).enumerable})),n.push.apply(n,r)}return n}function ot(t){for(var e=1;e<arguments.length;e++){var n=arguments[e]!=null?arguments[e]:{};e%2?fu(Object(n),!0).forEach(function(r){Fe(t,r,n[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):fu(Object(n)).forEach(function(r){Object.defineProperty(t,r,Object.getOwnPropertyDescriptor(n,r))})}return t}function Vx(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function pu(t,e){for(var n=0;n<e.length;n++){var r=e[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,wf(r.key),r)}}function Xx(t,e,n){return e&&pu(t.prototype,e),n&&pu(t,n),Object.defineProperty(t,"prototype",{writable:!1}),t}function Gx(t,e,n){return e=Si(e),Hx(t,xf()?Reflect.construct(e,n||[],Si(t).constructor):e.apply(t,n))}function Hx(t,e){if(e&&(gr(e)==="object"||typeof e=="function"))return e;if(e!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return Ux(t)}function Ux(t){if(t===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}function xf(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(xf=function(){return!!t})()}function Si(t){return Si=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(n){return n.__proto__||Object.getPrototypeOf(n)},Si(t)}function Yx(t,e){if(typeof e!="function"&&e!==null)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&mo(t,e)}function mo(t,e){return mo=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(r,i){return r.__proto__=i,r},mo(t,e)}function Fe(t,e,n){return e=wf(e),e in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function wf(t){var e=qx(t,"string");return gr(e)=="symbol"?e:e+""}function qx(t,e){if(gr(t)!="object"||!t)return t;var n=t[Symbol.toPrimitive];if(n!==void 0){var r=n.call(t,e);if(gr(r)!="object")return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}var yt=function(t){function e(){var n;Vx(this,e);for(var r=arguments.length,i=new Array(r),a=0;a<r;a++)i[a]=arguments[a];return n=Gx(this,e,[].concat(i)),Fe(n,"state",{isAnimationFinished:!0}),Fe(n,"id",nt("recharts-area-")),Fe(n,"handleAnimationEnd",function(){var o=n.props.onAnimationEnd;n.setState({isAnimationFinished:!0}),K(o)&&o()}),Fe(n,"handleAnimationStart",function(){var o=n.props.onAnimationStart;n.setState({isAnimationFinished:!1}),K(o)&&o()}),n}return Yx(e,t),Xx(e,[{key:"renderDots",value:function(r,i,a){var o=this.props.isAnimationActive,c=this.state.isAnimationFinished;if(o&&!c)return null;var l=this.props,u=l.dot,s=l.points,f=l.dataKey,p=L(this.props,!1),d=L(u,!0),v=s.map(function(m,O){var w=ot(ot(ot({key:"dot-".concat(O),r:3},p),d),{},{index:O,cx:m.x,cy:m.y,dataKey:f,value:m.value,payload:m.payload,points:s});return e.renderDotItem(u,w)}),h={clipPath:r?"url(#clipPath-".concat(i?"":"dots-").concat(a,")"):null};return b.createElement(F,_t({className:"recharts-area-dots"},h),v)}},{key:"renderHorizontalRect",value:function(r){var i=this.props,a=i.baseLine,o=i.points,c=i.strokeWidth,l=o[0].x,u=o[o.length-1].x,s=r*Math.abs(l-u),f=lt(o.map(function(p){return p.y||0}));return R(a)&&typeof a=="number"?f=Math.max(a,f):a&&Array.isArray(a)&&a.length&&(f=Math.max(lt(a.map(function(p){return p.y||0})),f)),R(f)?b.createElement("rect",{x:l<u?l:l-s,y:0,width:s,height:Math.floor(f+(c?parseInt("".concat(c),10):1))}):null}},{key:"renderVerticalRect",value:function(r){var i=this.props,a=i.baseLine,o=i.points,c=i.strokeWidth,l=o[0].y,u=o[o.length-1].y,s=r*Math.abs(l-u),f=lt(o.map(function(p){return p.x||0}));return R(a)&&typeof a=="number"?f=Math.max(a,f):a&&Array.isArray(a)&&a.length&&(f=Math.max(lt(a.map(function(p){return p.x||0})),f)),R(f)?b.createElement("rect",{x:0,y:l<u?l:l-s,width:f+(c?parseInt("".concat(c),10):1),height:Math.floor(s)}):null}},{key:"renderClipRect",value:function(r){var i=this.props.layout;return i==="vertical"?this.renderVerticalRect(r):this.renderHorizontalRect(r)}},{key:"renderAreaStatically",value:function(r,i,a,o){var c=this.props,l=c.layout,u=c.type,s=c.stroke,f=c.connectNulls,p=c.isRange;c.ref;var d=Of(c,zx);return b.createElement(F,{clipPath:a?"url(#clipPath-".concat(o,")"):null},b.createElement(ft,_t({},L(d,!0),{points:r,connectNulls:f,type:u,baseLine:i,layout:l,stroke:"none",className:"recharts-area-area"})),s!=="none"&&b.createElement(ft,_t({},L(this.props,!1),{className:"recharts-area-curve",layout:l,type:u,connectNulls:f,fill:"none",points:r})),s!=="none"&&p&&b.createElement(ft,_t({},L(this.props,!1),{className:"recharts-area-curve",layout:l,type:u,connectNulls:f,fill:"none",points:i})))}},{key:"renderAreaWithAnimation",value:function(r,i){var a=this,o=this.props,c=o.points,l=o.baseLine,u=o.isAnimationActive,s=o.animationBegin,f=o.animationDuration,p=o.animationEasing,d=o.animationId,v=this.state,h=v.prevPoints,m=v.prevBaseLine;return b.createElement($e,{begin:s,duration:f,isActive:u,easing:p,from:{t:0},to:{t:1},key:"area-".concat(d),onAnimationEnd:this.handleAnimationEnd,onAnimationStart:this.handleAnimationStart},function(O){var w=O.t;if(h){var x=h.length/c.length,A=c.map(function(S,j){var E=Math.floor(j*x);if(h[E]){var $=h[E],_=q($.x,S.x),k=q($.y,S.y);return ot(ot({},S),{},{x:_(w),y:k(w)})}return S}),y;if(R(l)&&typeof l=="number"){var g=q(m,l);y=g(w)}else if(G(l)||vt(l)){var P=q(m,0);y=P(w)}else y=l.map(function(S,j){var E=Math.floor(j*x);if(m[E]){var $=m[E],_=q($.x,S.x),k=q($.y,S.y);return ot(ot({},S),{},{x:_(w),y:k(w)})}return S});return a.renderAreaStatically(A,y,r,i)}return b.createElement(F,null,b.createElement("defs",null,b.createElement("clipPath",{id:"animationClipPath-".concat(i)},a.renderClipRect(w))),b.createElement(F,{clipPath:"url(#animationClipPath-".concat(i,")")},a.renderAreaStatically(c,l,r,i)))})}},{key:"renderArea",value:function(r,i){var a=this.props,o=a.points,c=a.baseLine,l=a.isAnimationActive,u=this.state,s=u.prevPoints,f=u.prevBaseLine,p=u.totalLength;return l&&o&&o.length&&(!s&&p>0||!Le(s,o)||!Le(f,c))?this.renderAreaWithAnimation(r,i):this.renderAreaStatically(o,c,r,i)}},{key:"render",value:function(){var r,i=this.props,a=i.hide,o=i.dot,c=i.points,l=i.className,u=i.top,s=i.left,f=i.xAxis,p=i.yAxis,d=i.width,v=i.height,h=i.isAnimationActive,m=i.id;if(a||!c||!c.length)return null;var O=this.state.isAnimationFinished,w=c.length===1,x=X("recharts-area",l),A=f&&f.allowDataOverflow,y=p&&p.allowDataOverflow,g=A||y,P=G(m)?this.id:m,S=(r=L(o,!1))!==null&&r!==void 0?r:{r:3,strokeWidth:2},j=S.r,E=j===void 0?3:j,$=S.strokeWidth,_=$===void 0?2:$,k=Vu(o)?o:{},D=k.clipDot,T=D===void 0?!0:D,C=E*2+_;return b.createElement(F,{className:x},A||y?b.createElement("defs",null,b.createElement("clipPath",{id:"clipPath-".concat(P)},b.createElement("rect",{x:A?s:s-d/2,y:y?u:u-v/2,width:A?d:d*2,height:y?v:v*2})),!T&&b.createElement("clipPath",{id:"clipPath-dots-".concat(P)},b.createElement("rect",{x:s-C/2,y:u-C/2,width:d+C,height:v+C}))):null,w?null:this.renderArea(g,P),(o||w)&&this.renderDots(g,T,P),(!h||O)&&Ee.renderCallByParent(this.props,c))}}],[{key:"getDerivedStateFromProps",value:function(r,i){return r.animationId!==i.prevAnimationId?{prevAnimationId:r.animationId,curPoints:r.points,curBaseLine:r.baseLine,prevPoints:i.curPoints,prevBaseLine:i.curBaseLine}:r.points!==i.curPoints||r.baseLine!==i.curBaseLine?{curPoints:r.points,curBaseLine:r.baseLine}:null}}])}(N.PureComponent);bf=yt;Fe(yt,"displayName","Area");Fe(yt,"defaultProps",{stroke:"#3182bd",fill:"#3182bd",fillOpacity:.6,xAxisId:0,yAxisId:0,legendType:"line",connectNulls:!1,points:[],dot:!1,activeDot:!0,hide:!1,isAnimationActive:!be.isSsr,animationBegin:0,animationDuration:1500,animationEasing:"ease"});Fe(yt,"getBaseValue",function(t,e,n,r){var i=t.layout,a=t.baseValue,o=e.props.baseValue,c=o??a;if(R(c)&&typeof c=="number")return c;var l=i==="horizontal"?r:n,u=l.scale.domain();if(l.type==="number"){var s=Math.max(u[0],u[1]),f=Math.min(u[0],u[1]);return c==="dataMin"?f:c==="dataMax"||s<0?s:Math.max(Math.min(u[0],u[1]),0)}return c==="dataMin"?u[0]:c==="dataMax"?u[1]:u[0]});Fe(yt,"getComposedData",function(t){var e=t.props,n=t.item,r=t.xAxis,i=t.yAxis,a=t.xAxisTicks,o=t.yAxisTicks,c=t.bandSize,l=t.dataKey,u=t.stackedData,s=t.dataStartIndex,f=t.displayedData,p=t.offset,d=e.layout,v=u&&u.length,h=bf.getBaseValue(e,n,r,i),m=d==="horizontal",O=!1,w=f.map(function(A,y){var g;v?g=u[s+y]:(g=Z(A,l),Array.isArray(g)?O=!0:g=[h,g]);var P=g[1]==null||v&&Z(A,l)==null;return m?{x:nr({axis:r,ticks:a,bandSize:c,entry:A,index:y}),y:P?null:i.scale(g[1]),value:g,payload:A}:{x:P?null:r.scale(g[1]),y:nr({axis:i,ticks:o,bandSize:c,entry:A,index:y}),value:g,payload:A}}),x;return v||O?x=w.map(function(A){var y=Array.isArray(A.value)?A.value[0]:null;return m?{x:A.x,y:y!=null&&A.y!=null?i.scale(y):null}:{x:y!=null?r.scale(y):null,y:A.y}}):x=m?i.scale(h):r.scale(h),ot({points:w,baseLine:x,layout:d,isRange:O},p)});Fe(yt,"renderDotItem",function(t,e){var n;if(b.isValidElement(t))n=b.cloneElement(t,e);else if(K(t))n=t(e);else{var r=X("recharts-area-dot",typeof t!="boolean"?t.className:""),i=e.key,a=Of(e,Kx);n=b.createElement(Ir,_t({},a,{key:i,className:r}))}return n});function br(t){"@babel/helpers - typeof";return br=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},br(t)}function Zx(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function Jx(t,e){for(var n=0;n<e.length;n++){var r=e[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,Sf(r.key),r)}}function Qx(t,e,n){return e&&Jx(t.prototype,e),Object.defineProperty(t,"prototype",{writable:!1}),t}function ew(t,e,n){return e=ji(e),tw(t,Af()?Reflect.construct(e,n||[],ji(t).constructor):e.apply(t,n))}function tw(t,e){if(e&&(br(e)==="object"||typeof e=="function"))return e;if(e!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return rw(t)}function rw(t){if(t===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}function Af(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(Af=function(){return!!t})()}function ji(t){return ji=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(n){return n.__proto__||Object.getPrototypeOf(n)},ji(t)}function nw(t,e){if(typeof e!="function"&&e!==null)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&go(t,e)}function go(t,e){return go=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(r,i){return r.__proto__=i,r},go(t,e)}function Pf(t,e,n){return e=Sf(e),e in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function Sf(t){var e=iw(t,"string");return br(e)=="symbol"?e:e+""}function iw(t,e){if(br(t)!="object"||!t)return t;var n=t[Symbol.toPrimitive];if(n!==void 0){var r=n.call(t,e);if(br(r)!="object")return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}var Tn=function(t){function e(){return Zx(this,e),ew(this,e,arguments)}return nw(e,t),Qx(e,[{key:"render",value:function(){return null}}])}(N.Component);Pf(Tn,"displayName","ZAxis");Pf(Tn,"defaultProps",{zAxisId:0,range:[64,64],scale:"auto",type:"number"});var aw=["option","isActive"];function Vr(){return Vr=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var n=arguments[e];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(t[r]=n[r])}return t},Vr.apply(this,arguments)}function ow(t,e){if(t==null)return{};var n=cw(t,e),r,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(t);for(i=0;i<a.length;i++)r=a[i],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(n[r]=t[r])}return n}function cw(t,e){if(t==null)return{};var n={};for(var r in t)if(Object.prototype.hasOwnProperty.call(t,r)){if(e.indexOf(r)>=0)continue;n[r]=t[r]}return n}function lw(t){var e=t.option,n=t.isActive,r=ow(t,aw);return typeof e=="string"?N.createElement(or,Vr({option:N.createElement(Li,Vr({type:e},r)),isActive:n,shapeType:"symbols"},r)):N.createElement(or,Vr({option:e,isActive:n,shapeType:"symbols"},r))}function Or(t){"@babel/helpers - typeof";return Or=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Or(t)}function Xr(){return Xr=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var n=arguments[e];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(t[r]=n[r])}return t},Xr.apply(this,arguments)}function du(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter(function(i){return Object.getOwnPropertyDescriptor(t,i).enumerable})),n.push.apply(n,r)}return n}function Ce(t){for(var e=1;e<arguments.length;e++){var n=arguments[e]!=null?arguments[e]:{};e%2?du(Object(n),!0).forEach(function(r){st(t,r,n[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):du(Object(n)).forEach(function(r){Object.defineProperty(t,r,Object.getOwnPropertyDescriptor(n,r))})}return t}function uw(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function vu(t,e){for(var n=0;n<e.length;n++){var r=e[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,Ef(r.key),r)}}function sw(t,e,n){return e&&vu(t.prototype,e),n&&vu(t,n),Object.defineProperty(t,"prototype",{writable:!1}),t}function fw(t,e,n){return e=Ei(e),pw(t,jf()?Reflect.construct(e,n||[],Ei(t).constructor):e.apply(t,n))}function pw(t,e){if(e&&(Or(e)==="object"||typeof e=="function"))return e;if(e!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return dw(t)}function dw(t){if(t===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}function jf(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(jf=function(){return!!t})()}function Ei(t){return Ei=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(n){return n.__proto__||Object.getPrototypeOf(n)},Ei(t)}function vw(t,e){if(typeof e!="function"&&e!==null)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&bo(t,e)}function bo(t,e){return bo=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(r,i){return r.__proto__=i,r},bo(t,e)}function st(t,e,n){return e=Ef(e),e in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function Ef(t){var e=hw(t,"string");return Or(e)=="symbol"?e:e+""}function hw(t,e){if(Or(t)!="object"||!t)return t;var n=t[Symbol.toPrimitive];if(n!==void 0){var r=n.call(t,e);if(Or(r)!="object")return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}var In=function(t){function e(){var n;uw(this,e);for(var r=arguments.length,i=new Array(r),a=0;a<r;a++)i[a]=arguments[a];return n=fw(this,e,[].concat(i)),st(n,"state",{isAnimationFinished:!1}),st(n,"handleAnimationEnd",function(){n.setState({isAnimationFinished:!0})}),st(n,"handleAnimationStart",function(){n.setState({isAnimationFinished:!1})}),st(n,"id",nt("recharts-scatter-")),n}return vw(e,t),sw(e,[{key:"renderSymbolsStatically",value:function(r){var i=this,a=this.props,o=a.shape,c=a.activeShape,l=a.activeIndex,u=L(this.props,!1);return r.map(function(s,f){var p=l===f,d=p?c:o,v=Ce(Ce({},u),s);return b.createElement(F,Xr({className:"recharts-scatter-symbol",key:"symbol-".concat(s?.cx,"-").concat(s?.cy,"-").concat(s?.size,"-").concat(f)},We(i.props,s,f),{role:"img"}),b.createElement(lw,Xr({option:d,isActive:p,key:"symbol-".concat(f)},v)))})}},{key:"renderSymbolsWithAnimation",value:function(){var r=this,i=this.props,a=i.points,o=i.isAnimationActive,c=i.animationBegin,l=i.animationDuration,u=i.animationEasing,s=i.animationId,f=this.state.prevPoints;return b.createElement($e,{begin:c,duration:l,isActive:o,easing:u,from:{t:0},to:{t:1},key:"pie-".concat(s),onAnimationEnd:this.handleAnimationEnd,onAnimationStart:this.handleAnimationStart},function(p){var d=p.t,v=a.map(function(h,m){var O=f&&f[m];if(O){var w=q(O.cx,h.cx),x=q(O.cy,h.cy),A=q(O.size,h.size);return Ce(Ce({},h),{},{cx:w(d),cy:x(d),size:A(d)})}var y=q(0,h.size);return Ce(Ce({},h),{},{size:y(d)})});return b.createElement(F,null,r.renderSymbolsStatically(v))})}},{key:"renderSymbols",value:function(){var r=this.props,i=r.points,a=r.isAnimationActive,o=this.state.prevPoints;return a&&i&&i.length&&(!o||!Le(o,i))?this.renderSymbolsWithAnimation():this.renderSymbolsStatically(i)}},{key:"renderErrorBar",value:function(){var r=this.props.isAnimationActive;if(r&&!this.state.isAnimationFinished)return null;var i=this.props,a=i.points,o=i.xAxis,c=i.yAxis,l=i.children,u=ge(l,Tr);return u?u.map(function(s,f){var p=s.props,d=p.direction,v=p.dataKey;return b.cloneElement(s,{key:"".concat(d,"-").concat(v,"-").concat(a[f]),data:a,xAxis:o,yAxis:c,layout:d==="x"?"vertical":"horizontal",dataPointFormatter:function(m,O){return{x:m.cx,y:m.cy,value:d==="x"?+m.node.x:+m.node.y,errorVal:Z(m,O)}}})}):null}},{key:"renderLine",value:function(){var r=this.props,i=r.points,a=r.line,o=r.lineType,c=r.lineJointType,l=L(this.props,!1),u=L(a,!1),s,f;if(o==="joint")s=i.map(function(x){return{x:x.cx,y:x.cy}});else if(o==="fitting"){var p=Kp(i),d=p.xmin,v=p.xmax,h=p.a,m=p.b,O=function(A){return h*A+m};s=[{x:d,y:O(d)},{x:v,y:O(v)}]}var w=Ce(Ce(Ce({},l),{},{fill:"none",stroke:l&&l.fill},u),{},{points:s});return b.isValidElement(a)?f=b.cloneElement(a,w):K(a)?f=a(w):f=b.createElement(ft,Xr({},w,{type:c})),b.createElement(F,{className:"recharts-scatter-line",key:"recharts-scatter-line"},f)}},{key:"render",value:function(){var r=this.props,i=r.hide,a=r.points,o=r.line,c=r.className,l=r.xAxis,u=r.yAxis,s=r.left,f=r.top,p=r.width,d=r.height,v=r.id,h=r.isAnimationActive;if(i||!a||!a.length)return null;var m=this.state.isAnimationFinished,O=X("recharts-scatter",c),w=l&&l.allowDataOverflow,x=u&&u.allowDataOverflow,A=w||x,y=G(v)?this.id:v;return b.createElement(F,{className:O,clipPath:A?"url(#clipPath-".concat(y,")"):null},w||x?b.createElement("defs",null,b.createElement("clipPath",{id:"clipPath-".concat(y)},b.createElement("rect",{x:w?s:s-p/2,y:x?f:f-d/2,width:w?p:p*2,height:x?d:d*2}))):null,o&&this.renderLine(),this.renderErrorBar(),b.createElement(F,{key:"recharts-scatter-symbols"},this.renderSymbols()),(!h||m)&&Ee.renderCallByParent(this.props,a))}}],[{key:"getDerivedStateFromProps",value:function(r,i){return r.animationId!==i.prevAnimationId?{prevAnimationId:r.animationId,curPoints:r.points,prevPoints:i.curPoints}:r.points!==i.curPoints?{curPoints:r.points}:null}}])}(N.PureComponent);st(In,"displayName","Scatter");st(In,"defaultProps",{xAxisId:0,yAxisId:0,zAxisId:0,legendType:"circle",lineType:"joint",lineJointType:"linear",data:[],shape:"circle",hide:!1,isAnimationActive:!be.isSsr,animationBegin:0,animationDuration:400,animationEasing:"linear"});st(In,"getComposedData",function(t){var e=t.xAxis,n=t.yAxis,r=t.zAxis,i=t.item,a=t.displayedData,o=t.xAxisTicks,c=t.yAxisTicks,l=t.offset,u=i.props.tooltipType,s=ge(i.props.children,_r),f=G(e.dataKey)?i.props.dataKey:e.dataKey,p=G(n.dataKey)?i.props.dataKey:n.dataKey,d=r&&r.dataKey,v=r?r.range:Tn.defaultProps.range,h=v&&v[0],m=e.scale.bandwidth?e.scale.bandwidth():0,O=n.scale.bandwidth?n.scale.bandwidth():0,w=a.map(function(x,A){var y=Z(x,f),g=Z(x,p),P=!G(d)&&Z(x,d)||"-",S=[{name:G(e.dataKey)?i.props.name:e.name||e.dataKey,unit:e.unit||"",value:y,payload:x,dataKey:f,type:u},{name:G(n.dataKey)?i.props.name:n.name||n.dataKey,unit:n.unit||"",value:g,payload:x,dataKey:p,type:u}];P!=="-"&&S.push({name:r.name||r.dataKey,unit:r.unit||"",value:P,payload:x,dataKey:d,type:u});var j=nr({axis:e,ticks:o,bandSize:m,entry:x,index:A,dataKey:f}),E=nr({axis:n,ticks:c,bandSize:O,entry:x,index:A,dataKey:p}),$=P!=="-"?r.scale(P):h,_=Math.sqrt(Math.max($,0)/Math.PI);return Ce(Ce({},x),{},{cx:j,cy:E,x:j-_,y:E-_,xAxis:e,yAxis:n,zAxis:r,width:2*_,height:2*_,size:$,node:{x:y,y:g,z:P},tooltipPayload:S,tooltipPosition:{x:j,y:E},payload:x},s&&s[A]&&s[A].props)});return Ce({points:w},l)});function xr(t){"@babel/helpers - typeof";return xr=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},xr(t)}function yw(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function mw(t,e){for(var n=0;n<e.length;n++){var r=e[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,Tf(r.key),r)}}function gw(t,e,n){return e&&mw(t.prototype,e),Object.defineProperty(t,"prototype",{writable:!1}),t}function bw(t,e,n){return e=$i(e),Ow(t,$f()?Reflect.construct(e,n||[],$i(t).constructor):e.apply(t,n))}function Ow(t,e){if(e&&(xr(e)==="object"||typeof e=="function"))return e;if(e!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return xw(t)}function xw(t){if(t===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}function $f(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return($f=function(){return!!t})()}function $i(t){return $i=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(n){return n.__proto__||Object.getPrototypeOf(n)},$i(t)}function ww(t,e){if(typeof e!="function"&&e!==null)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&Oo(t,e)}function Oo(t,e){return Oo=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(r,i){return r.__proto__=i,r},Oo(t,e)}function _f(t,e,n){return e=Tf(e),e in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function Tf(t){var e=Aw(t,"string");return xr(e)=="symbol"?e:e+""}function Aw(t,e){if(xr(t)!="object"||!t)return t;var n=t[Symbol.toPrimitive];if(n!==void 0){var r=n.call(t,e);if(xr(r)!="object")return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}function xo(){return xo=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var n=arguments[e];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(t[r]=n[r])}return t},xo.apply(this,arguments)}function Pw(t){var e=t.xAxisId,n=Uo(),r=Yo(),i=af(e);return i==null?null:N.createElement(Cr,xo({},i,{className:X("recharts-".concat(i.axisType," ").concat(i.axisType),i.className),viewBox:{x:0,y:0,width:n,height:r},ticksGenerator:function(o){return Je(o,!0)}}))}var Mt=function(t){function e(){return yw(this,e),bw(this,e,arguments)}return ww(e,t),gw(e,[{key:"render",value:function(){return N.createElement(Pw,this.props)}}])}(N.Component);_f(Mt,"displayName","XAxis");_f(Mt,"defaultProps",{allowDecimals:!0,hide:!1,orientation:"bottom",width:0,height:30,mirror:!1,xAxisId:0,tickCount:5,type:"category",padding:{left:0,right:0},allowDataOverflow:!1,scale:"auto",reversed:!1,allowDuplicatedCategory:!0});function wr(t){"@babel/helpers - typeof";return wr=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},wr(t)}function Sw(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function jw(t,e){for(var n=0;n<e.length;n++){var r=e[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,Df(r.key),r)}}function Ew(t,e,n){return e&&jw(t.prototype,e),Object.defineProperty(t,"prototype",{writable:!1}),t}function $w(t,e,n){return e=_i(e),_w(t,If()?Reflect.construct(e,n||[],_i(t).constructor):e.apply(t,n))}function _w(t,e){if(e&&(wr(e)==="object"||typeof e=="function"))return e;if(e!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return Tw(t)}function Tw(t){if(t===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}function If(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(If=function(){return!!t})()}function _i(t){return _i=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(n){return n.__proto__||Object.getPrototypeOf(n)},_i(t)}function Iw(t,e){if(typeof e!="function"&&e!==null)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&wo(t,e)}function wo(t,e){return wo=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(r,i){return r.__proto__=i,r},wo(t,e)}function kf(t,e,n){return e=Df(e),e in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function Df(t){var e=kw(t,"string");return wr(e)=="symbol"?e:e+""}function kw(t,e){if(wr(t)!="object"||!t)return t;var n=t[Symbol.toPrimitive];if(n!==void 0){var r=n.call(t,e);if(wr(r)!="object")return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}function Ao(){return Ao=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var n=arguments[e];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(t[r]=n[r])}return t},Ao.apply(this,arguments)}var Dw=function(e){var n=e.yAxisId,r=Uo(),i=Yo(),a=of(n);return a==null?null:N.createElement(Cr,Ao({},a,{className:X("recharts-".concat(a.axisType," ").concat(a.axisType),a.className),viewBox:{x:0,y:0,width:r,height:i},ticksGenerator:function(c){return Je(c,!0)}}))},Nt=function(t){function e(){return Sw(this,e),$w(this,e,arguments)}return Iw(e,t),Ew(e,[{key:"render",value:function(){return N.createElement(Dw,this.props)}}])}(N.Component);kf(Nt,"displayName","YAxis");kf(Nt,"defaultProps",{allowDuplicatedCategory:!0,allowDecimals:!0,hide:!1,orientation:"left",width:60,height:0,mirror:!1,yAxisId:0,tickCount:5,type:"number",padding:{top:0,bottom:0},allowDataOverflow:!1,scale:"auto",reversed:!1});function hu(t){return Rw(t)||Nw(t)||Mw(t)||Cw()}function Cw(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function Mw(t,e){if(t){if(typeof t=="string")return Po(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);if(n==="Object"&&t.constructor&&(n=t.constructor.name),n==="Map"||n==="Set")return Array.from(t);if(n==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return Po(t,e)}}function Nw(t){if(typeof Symbol<"u"&&t[Symbol.iterator]!=null||t["@@iterator"]!=null)return Array.from(t)}function Rw(t){if(Array.isArray(t))return Po(t)}function Po(t,e){(e==null||e>t.length)&&(e=t.length);for(var n=0,r=new Array(e);n<e;n++)r[n]=t[n];return r}var So=function(e,n,r,i,a){var o=ge(e,Zo),c=ge(e,Zi),l=[].concat(hu(o),hu(c)),u=ge(e,Qi),s="".concat(i,"Id"),f=i[0],p=n;if(l.length&&(p=l.reduce(function(h,m){if(m.props[s]===r&&Ve(m.props,"extendDomain")&&R(m.props[f])){var O=m.props[f];return[Math.min(h[0],O),Math.max(h[1],O)]}return h},p)),u.length){var d="".concat(f,"1"),v="".concat(f,"2");p=u.reduce(function(h,m){if(m.props[s]===r&&Ve(m.props,"extendDomain")&&R(m.props[d])&&R(m.props[v])){var O=m.props[d],w=m.props[v];return[Math.min(h[0],O,w),Math.max(h[1],O,w)]}return h},p)}return a&&a.length&&(p=a.reduce(function(h,m){return R(m)?[Math.min(h[0],m),Math.max(h[1],m)]:h},p)),p},da=new Np,va="recharts.syncMouseEvents";function xn(t){"@babel/helpers - typeof";return xn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},xn(t)}function Bw(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function Lw(t,e){for(var n=0;n<e.length;n++){var r=e[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,Cf(r.key),r)}}function Ww(t,e,n){return e&&Lw(t.prototype,e),Object.defineProperty(t,"prototype",{writable:!1}),t}function ha(t,e,n){return e=Cf(e),e in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function Cf(t){var e=zw(t,"string");return xn(e)=="symbol"?e:e+""}function zw(t,e){if(xn(t)!="object"||!t)return t;var n=t[Symbol.toPrimitive];if(n!==void 0){var r=n.call(t,e);if(xn(r)!="object")return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}var Kw=function(){function t(){Bw(this,t),ha(this,"activeIndex",0),ha(this,"coordinateList",[]),ha(this,"layout","horizontal")}return Ww(t,[{key:"setDetails",value:function(n){var r,i=n.coordinateList,a=i===void 0?null:i,o=n.container,c=o===void 0?null:o,l=n.layout,u=l===void 0?null:l,s=n.offset,f=s===void 0?null:s,p=n.mouseHandlerCallback,d=p===void 0?null:p;this.coordinateList=(r=a??this.coordinateList)!==null&&r!==void 0?r:[],this.container=c??this.container,this.layout=u??this.layout,this.offset=f??this.offset,this.mouseHandlerCallback=d??this.mouseHandlerCallback,this.activeIndex=Math.min(Math.max(this.activeIndex,0),this.coordinateList.length-1)}},{key:"focus",value:function(){this.spoofMouse()}},{key:"keyboardEvent",value:function(n){if(this.coordinateList.length!==0)switch(n.key){case"ArrowRight":{if(this.layout!=="horizontal")return;this.activeIndex=Math.min(this.activeIndex+1,this.coordinateList.length-1),this.spoofMouse();break}case"ArrowLeft":{if(this.layout!=="horizontal")return;this.activeIndex=Math.max(this.activeIndex-1,0),this.spoofMouse();break}}}},{key:"setIndex",value:function(n){this.activeIndex=n}},{key:"spoofMouse",value:function(){var n,r;if(this.layout==="horizontal"&&this.coordinateList.length!==0){var i=this.container.getBoundingClientRect(),a=i.x,o=i.y,c=i.height,l=this.coordinateList[this.activeIndex].coordinate,u=((n=window)===null||n===void 0?void 0:n.scrollX)||0,s=((r=window)===null||r===void 0?void 0:r.scrollY)||0,f=a+l+u,p=o+this.offset.top+c/2+s;this.mouseHandlerCallback({pageX:f,pageY:p})}}}])}();function Fw(t,e,n){if(n==="number"&&e===!0&&Array.isArray(t)){var r=t?.[0],i=t?.[1];if(r&&i&&R(r)&&R(i))return!0}return!1}function Vw(t,e,n,r){var i=r/2;return{stroke:"none",fill:"#ccc",x:t==="horizontal"?e.x-i:n.left+.5,y:t==="horizontal"?n.top+.5:e.y-i,width:t==="horizontal"?r:n.width-1,height:t==="horizontal"?n.height-1:r}}function Mf(t){var e=t.cx,n=t.cy,r=t.radius,i=t.startAngle,a=t.endAngle,o=J(e,n,r,i),c=J(e,n,r,a);return{points:[o,c],cx:e,cy:n,radius:r,startAngle:i,endAngle:a}}function Xw(t,e,n){var r,i,a,o;if(t==="horizontal")r=e.x,a=r,i=n.top,o=n.top+n.height;else if(t==="vertical")i=e.y,o=i,r=n.left,a=n.left+n.width;else if(e.cx!=null&&e.cy!=null)if(t==="centric"){var c=e.cx,l=e.cy,u=e.innerRadius,s=e.outerRadius,f=e.angle,p=J(c,l,u,f),d=J(c,l,s,f);r=p.x,i=p.y,a=d.x,o=d.y}else return Mf(e);return[{x:r,y:i},{x:a,y:o}]}function wn(t){"@babel/helpers - typeof";return wn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},wn(t)}function yu(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter(function(i){return Object.getOwnPropertyDescriptor(t,i).enumerable})),n.push.apply(n,r)}return n}function Wn(t){for(var e=1;e<arguments.length;e++){var n=arguments[e]!=null?arguments[e]:{};e%2?yu(Object(n),!0).forEach(function(r){Gw(t,r,n[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):yu(Object(n)).forEach(function(r){Object.defineProperty(t,r,Object.getOwnPropertyDescriptor(n,r))})}return t}function Gw(t,e,n){return e=Hw(e),e in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function Hw(t){var e=Uw(t,"string");return wn(e)=="symbol"?e:e+""}function Uw(t,e){if(wn(t)!="object"||!t)return t;var n=t[Symbol.toPrimitive];if(n!==void 0){var r=n.call(t,e);if(wn(r)!="object")return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(t)}function Yw(t){var e,n,r=t.element,i=t.tooltipEventType,a=t.isActive,o=t.activeCoordinate,c=t.activePayload,l=t.offset,u=t.activeTooltipIndex,s=t.tooltipAxisBandSize,f=t.layout,p=t.chartName,d=(e=r.props.cursor)!==null&&e!==void 0?e:(n=r.type.defaultProps)===null||n===void 0?void 0:n.cursor;if(!r||!d||!a||!o||p!=="ScatterChart"&&i!=="axis")return null;var v,h=ft;if(p==="ScatterChart")v=o,h=Gm;else if(p==="BarChart")v=Vw(f,o,l,s),h=En;else if(f==="radial"){var m=Mf(o),O=m.cx,w=m.cy,x=m.radius,A=m.startAngle,y=m.endAngle;v={cx:O,cy:w,startAngle:A,endAngle:y,innerRadius:x,outerRadius:x},h=Fo}else v={points:Xw(f,o,l)},h=ft;var g=Wn(Wn(Wn(Wn({stroke:"#ccc",pointerEvents:"none"},l),v),L(d,!1)),{},{payload:c,payloadIndex:u,className:X("recharts-tooltip-cursor",d.className)});return N.isValidElement(d)?N.cloneElement(d,g):N.createElement(h,g)}var qw=["item"],Zw=["children","className","width","height","style","compact","title","desc"];function Ar(t){"@babel/helpers - typeof";return Ar=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Ar(t)}function Ht(){return Ht=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var n=arguments[e];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(t[r]=n[r])}return t},Ht.apply(this,arguments)}function mu(t,e){return eA(t)||Qw(t,e)||Rf(t,e)||Jw()}function Jw(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function Qw(t,e){var n=t==null?null:typeof Symbol<"u"&&t[Symbol.iterator]||t["@@iterator"];if(n!=null){var r,i,a,o,c=[],l=!0,u=!1;try{if(a=(n=n.call(t)).next,e!==0)for(;!(l=(r=a.call(n)).done)&&(c.push(r.value),c.length!==e);l=!0);}catch(s){u=!0,i=s}finally{try{if(!l&&n.return!=null&&(o=n.return(),Object(o)!==o))return}finally{if(u)throw i}}return c}}function eA(t){if(Array.isArray(t))return t}function gu(t,e){if(t==null)return{};var n=tA(t,e),r,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(t);for(i=0;i<a.length;i++)r=a[i],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(n[r]=t[r])}return n}function tA(t,e){if(t==null)return{};var n={};for(var r in t)if(Object.prototype.hasOwnProperty.call(t,r)){if(e.indexOf(r)>=0)continue;n[r]=t[r]}return n}function rA(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function nA(t,e){for(var n=0;n<e.length;n++){var r=e[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,Bf(r.key),r)}}function iA(t,e,n){return e&&nA(t.prototype,e),Object.defineProperty(t,"prototype",{writable:!1}),t}function aA(t,e,n){return e=Ti(e),oA(t,Nf()?Reflect.construct(e,n||[],Ti(t).constructor):e.apply(t,n))}function oA(t,e){if(e&&(Ar(e)==="object"||typeof e=="function"))return e;if(e!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return cA(t)}function cA(t){if(t===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}function Nf(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(Nf=function(){return!!t})()}function Ti(t){return Ti=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(n){return n.__proto__||Object.getPrototypeOf(n)},Ti(t)}function lA(t,e){if(typeof e!="function"&&e!==null)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&jo(t,e)}function jo(t,e){return jo=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(r,i){return r.__proto__=i,r},jo(t,e)}function Pr(t){return fA(t)||sA(t)||Rf(t)||uA()}function uA(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function Rf(t,e){if(t){if(typeof t=="string")return Eo(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);if(n==="Object"&&t.constructor&&(n=t.constructor.name),n==="Map"||n==="Set")return Array.from(t);if(n==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return Eo(t,e)}}function sA(t){if(typeof Symbol<"u"&&t[Symbol.iterator]!=null||t["@@iterator"]!=null)return Array.from(t)}function fA(t){if(Array.isArray(t))return Eo(t)}function Eo(t,e){(e==null||e>t.length)&&(e=t.length);for(var n=0,r=new Array(e);n<e;n++)r[n]=t[n];return r}function bu(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter(function(i){return Object.getOwnPropertyDescriptor(t,i).enumerable})),n.push.apply(n,r)}return n}function I(t){for(var e=1;e<arguments.length;e++){var n=arguments[e]!=null?arguments[e]:{};e%2?bu(Object(n),!0).forEach(function(r){U(t,r,n[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):bu(Object(n)).forEach(function(r){Object.defineProperty(t,r,Object.getOwnPropertyDescriptor(n,r))})}return t}function U(t,e,n){return e=Bf(e),e in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function Bf(t){var e=pA(t,"string");return Ar(e)=="symbol"?e:e+""}function pA(t,e){if(Ar(t)!="object"||!t)return t;var n=t[Symbol.toPrimitive];if(n!==void 0){var r=n.call(t,e);if(Ar(r)!="object")return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(t)}var dA={xAxis:["bottom","top"],yAxis:["left","right"]},vA={width:"100%",height:"100%"},Lf={x:0,y:0};function zn(t){return t}var hA=function(e,n){return n==="horizontal"?e.x:n==="vertical"?e.y:n==="centric"?e.angle:e.radius},yA=function(e,n,r,i){var a=n.find(function(s){return s&&s.index===r});if(a){if(e==="horizontal")return{x:a.coordinate,y:i.y};if(e==="vertical")return{x:i.x,y:a.coordinate};if(e==="centric"){var o=a.coordinate,c=i.radius;return I(I(I({},i),J(i.cx,i.cy,c,o)),{},{angle:o,radius:c})}var l=a.coordinate,u=i.angle;return I(I(I({},i),J(i.cx,i.cy,l,u)),{},{angle:u,radius:l})}return Lf},ea=function(e,n){var r=n.graphicalItems,i=n.dataStartIndex,a=n.dataEndIndex,o=(r??[]).reduce(function(c,l){var u=l.props.data;return u&&u.length?[].concat(Pr(c),Pr(u)):c},[]);return o.length>0?o:e&&e.length&&R(i)&&R(a)?e.slice(i,a+1):[]};function Wf(t){return t==="number"?[0,"auto"]:void 0}var $o=function(e,n,r,i){var a=e.graphicalItems,o=e.tooltipAxis,c=ea(n,e);return r<0||!a||!a.length||r>=c.length?null:a.reduce(function(l,u){var s,f=(s=u.props.data)!==null&&s!==void 0?s:n;f&&e.dataStartIndex+e.dataEndIndex!==0&&e.dataEndIndex-e.dataStartIndex>=r&&(f=f.slice(e.dataStartIndex,e.dataEndIndex+1));var p;if(o.dataKey&&!o.allowDuplicatedCategory){var d=f===void 0?c:f;p=Xn(d,o.dataKey,i)}else p=f&&f[r]||c[r];return p?[].concat(Pr(l),[zo(u,p)]):l},[])},Ou=function(e,n,r,i){var a=i||{x:e.chartX,y:e.chartY},o=hA(a,r),c=e.orderedTooltipTicks,l=e.tooltipAxis,u=e.tooltipTicks,s=ey(o,c,u,l);if(s>=0&&u){var f=u[s]&&u[s].value,p=$o(e,n,s,f),d=yA(r,c,s,a);return{activeTooltipIndex:s,activeLabel:f,activePayload:p,activeCoordinate:d}}return null},mA=function(e,n){var r=n.axes,i=n.graphicalItems,a=n.axisType,o=n.axisIdKey,c=n.stackGroups,l=n.dataStartIndex,u=n.dataEndIndex,s=e.layout,f=e.children,p=e.stackOffset,d=bs(s,a);return r.reduce(function(v,h){var m,O=h.type.defaultProps!==void 0?I(I({},h.type.defaultProps),h.props):h.props,w=O.type,x=O.dataKey,A=O.allowDataOverflow,y=O.allowDuplicatedCategory,g=O.scale,P=O.ticks,S=O.includeHidden,j=O[o];if(v[j])return v;var E=ea(e.data,{graphicalItems:i.filter(function(z){var Y,ee=o in z.props?z.props[o]:(Y=z.type.defaultProps)===null||Y===void 0?void 0:Y[o];return ee===j}),dataStartIndex:l,dataEndIndex:u}),$=E.length,_,k,D;Fw(O.domain,A,w)&&(_=Fa(O.domain,null,A),d&&(w==="number"||g!=="auto")&&(D=Wr(E,x,"category")));var T=Wf(w);if(!_||_.length===0){var C,M=(C=O.domain)!==null&&C!==void 0?C:T;if(x){if(_=Wr(E,x,w),w==="category"&&d){var B=zp(_);y&&B?(k=_,_=Vn(0,$)):y||(_=Yc(M,_,h).reduce(function(z,Y){return z.indexOf(Y)>=0?z:[].concat(Pr(z),[Y])},[]))}else if(w==="category")y?_=_.filter(function(z){return z!==""&&!G(z)}):_=Yc(M,_,h).reduce(function(z,Y){return z.indexOf(Y)>=0||Y===""||G(Y)?z:[].concat(Pr(z),[Y])},[]);else if(w==="number"){var W=ay(E,i.filter(function(z){var Y,ee,ae=o in z.props?z.props[o]:(Y=z.type.defaultProps)===null||Y===void 0?void 0:Y[o],_e="hide"in z.props?z.props.hide:(ee=z.type.defaultProps)===null||ee===void 0?void 0:ee.hide;return ae===j&&(S||!_e)}),x,a,s);W&&(_=W)}d&&(w==="number"||g!=="auto")&&(D=Wr(E,x,"category"))}else d?_=Vn(0,$):c&&c[j]&&c[j].hasStack&&w==="number"?_=p==="expand"?[0,1]:Es(c[j].stackGroups,l,u):_=gs(E,i.filter(function(z){var Y=o in z.props?z.props[o]:z.type.defaultProps[o],ee="hide"in z.props?z.props.hide:z.type.defaultProps.hide;return Y===j&&(S||!ee)}),w,s,!0);if(w==="number")_=So(f,_,j,a,P),M&&(_=Fa(M,_,A));else if(w==="category"&&M){var V=M,H=_.every(function(z){return V.indexOf(z)>=0});H&&(_=V)}}return I(I({},v),{},U({},j,I(I({},O),{},{axisType:a,domain:_,categoricalDomain:D,duplicateDomain:k,originalDomain:(m=O.domain)!==null&&m!==void 0?m:T,isCategorical:d,layout:s})))},{})},gA=function(e,n){var r=n.graphicalItems,i=n.Axis,a=n.axisType,o=n.axisIdKey,c=n.stackGroups,l=n.dataStartIndex,u=n.dataEndIndex,s=e.layout,f=e.children,p=ea(e.data,{graphicalItems:r,dataStartIndex:l,dataEndIndex:u}),d=p.length,v=bs(s,a),h=-1;return r.reduce(function(m,O){var w=O.type.defaultProps!==void 0?I(I({},O.type.defaultProps),O.props):O.props,x=w[o],A=Wf("number");if(!m[x]){h++;var y;return v?y=Vn(0,d):c&&c[x]&&c[x].hasStack?(y=Es(c[x].stackGroups,l,u),y=So(f,y,x,a)):(y=Fa(A,gs(p,r.filter(function(g){var P,S,j=o in g.props?g.props[o]:(P=g.type.defaultProps)===null||P===void 0?void 0:P[o],E="hide"in g.props?g.props.hide:(S=g.type.defaultProps)===null||S===void 0?void 0:S.hide;return j===x&&!E}),"number",s),i.defaultProps.allowDataOverflow),y=So(f,y,x,a)),I(I({},m),{},U({},x,I(I({axisType:a},i.defaultProps),{},{hide:!0,orientation:he(dA,"".concat(a,".").concat(h%2),null),domain:y,originalDomain:A,isCategorical:v,layout:s})))}return m},{})},bA=function(e,n){var r=n.axisType,i=r===void 0?"xAxis":r,a=n.AxisComp,o=n.graphicalItems,c=n.stackGroups,l=n.dataStartIndex,u=n.dataEndIndex,s=e.children,f="".concat(i,"Id"),p=ge(s,a),d={};return p&&p.length?d=mA(e,{axes:p,graphicalItems:o,axisType:i,axisIdKey:f,stackGroups:c,dataStartIndex:l,dataEndIndex:u}):o&&o.length&&(d=gA(e,{Axis:a,graphicalItems:o,axisType:i,axisIdKey:f,stackGroups:c,dataStartIndex:l,dataEndIndex:u})),d},OA=function(e){var n=ct(e),r=Je(n,!1,!0);return{tooltipTicks:r,orderedTooltipTicks:No(r,function(i){return i.coordinate}),tooltipAxis:n,tooltipAxisBandSize:ri(n,r)}},xu=function(e){var n=e.children,r=e.defaultShowTooltip,i=oe(n,fr),a=0,o=0;return e.data&&e.data.length!==0&&(o=e.data.length-1),i&&i.props&&(i.props.startIndex>=0&&(a=i.props.startIndex),i.props.endIndex>=0&&(o=i.props.endIndex)),{chartX:0,chartY:0,dataStartIndex:a,dataEndIndex:o,activeTooltipIndex:-1,isTooltipActive:!!r}},xA=function(e){return!e||!e.length?!1:e.some(function(n){var r=Qe(n&&n.type);return r&&r.indexOf("Bar")>=0})},wu=function(e){return e==="horizontal"?{numericAxisName:"yAxis",cateAxisName:"xAxis"}:e==="vertical"?{numericAxisName:"xAxis",cateAxisName:"yAxis"}:e==="centric"?{numericAxisName:"radiusAxis",cateAxisName:"angleAxis"}:{numericAxisName:"angleAxis",cateAxisName:"radiusAxis"}},wA=function(e,n){var r=e.props,i=e.graphicalItems,a=e.xAxisMap,o=a===void 0?{}:a,c=e.yAxisMap,l=c===void 0?{}:c,u=r.width,s=r.height,f=r.children,p=r.margin||{},d=oe(f,fr),v=oe(f,Ut),h=Object.keys(l).reduce(function(y,g){var P=l[g],S=P.orientation;return!P.mirror&&!P.hide?I(I({},y),{},U({},S,y[S]+P.width)):y},{left:p.left||0,right:p.right||0}),m=Object.keys(o).reduce(function(y,g){var P=o[g],S=P.orientation;return!P.mirror&&!P.hide?I(I({},y),{},U({},S,he(y,"".concat(S))+P.height)):y},{top:p.top||0,bottom:p.bottom||0}),O=I(I({},m),h),w=O.bottom;d&&(O.bottom+=d.props.height||fr.defaultProps.height),v&&n&&(O=ny(O,i,r,n));var x=u-O.left-O.right,A=s-O.top-O.bottom;return I(I({brushBottom:w},O),{},{width:Math.max(x,0),height:Math.max(A,0)})},AA=function(e,n){if(n==="xAxis")return e[n].width;if(n==="yAxis")return e[n].height},it=function(e){var n=e.chartName,r=e.GraphicalChild,i=e.defaultTooltipEventType,a=i===void 0?"axis":i,o=e.validateTooltipEventTypes,c=o===void 0?["axis"]:o,l=e.axisComponents,u=e.legendContent,s=e.formatAxisMap,f=e.defaultProps,p=function(O,w){var x=w.graphicalItems,A=w.stackGroups,y=w.offset,g=w.updateId,P=w.dataStartIndex,S=w.dataEndIndex,j=O.barSize,E=O.layout,$=O.barGap,_=O.barCategoryGap,k=O.maxBarSize,D=wu(E),T=D.numericAxisName,C=D.cateAxisName,M=xA(x),B=[];return x.forEach(function(W,V){var H=ea(O.data,{graphicalItems:[W],dataStartIndex:P,dataEndIndex:S}),z=W.type.defaultProps!==void 0?I(I({},W.type.defaultProps),W.props):W.props,Y=z.dataKey,ee=z.maxBarSize,ae=z["".concat(T,"Id")],_e=z["".concat(C,"Id")],mt={},Se=l.reduce(function(He,Ue){var Bt=w["".concat(Ue.axisType,"Map")],je=z["".concat(Ue.axisType,"Id")];Bt&&Bt[je]||Ue.axisType==="zAxis"||kt();var Lt=Bt[je];return I(I({},He),{},U(U({},Ue.axisType,Lt),"".concat(Ue.axisType,"Ticks"),Je(Lt)))},mt),ze=Se[C],gt=Se["".concat(C,"Ticks")],at=A&&A[ae]&&A[ae].hasStack&&fy(W,A[ae].stackGroups),bt=Qe(W.type).indexOf("Bar")>=0,Xe=ri(ze,gt),Ge=[],Ot=M&&ty({barSize:j,stackGroups:A,totalSize:AA(Se,C)});if(bt){var xt,le,Oe=G(ee)?k:ee,ke=(xt=(le=ri(ze,gt,!0))!==null&&le!==void 0?le:Oe)!==null&&xt!==void 0?xt:0;Ge=ry({barGap:$,barCategoryGap:_,bandSize:ke!==Xe?ke:Xe,sizeList:Ot[_e],maxBarSize:Oe}),ke!==Xe&&(Ge=Ge.map(function(He){return I(I({},He),{},{position:I(I({},He.position),{},{offset:He.position.offset-ke/2})})}))}var Ke=W&&W.type&&W.type.getComposedData;Ke&&B.push({props:I(I({},Ke(I(I({},Se),{},{displayedData:H,props:O,dataKey:Y,item:W,bandSize:Xe,barPosition:Ge,offset:y,stackedData:at,layout:E,dataStartIndex:P,dataEndIndex:S}))),{},U(U(U({key:W.key||"item-".concat(V)},T,Se[T]),C,Se[C]),"animationId",g)),childIndex:Qp(W,O.children),item:W})}),B},d=function(O,w){var x=O.props,A=O.dataStartIndex,y=O.dataEndIndex,g=O.updateId;if(!Hn({props:x}))return null;var P=x.children,S=x.layout,j=x.stackOffset,E=x.data,$=x.reverseStackOrder,_=wu(S),k=_.numericAxisName,D=_.cateAxisName,T=ge(P,r),C=sy(E,T,"".concat(k,"Id"),"".concat(D,"Id"),j,$),M=l.reduce(function(z,Y){var ee="".concat(Y.axisType,"Map");return I(I({},z),{},U({},ee,bA(x,I(I({},Y),{},{graphicalItems:T,stackGroups:Y.axisType===k&&C,dataStartIndex:A,dataEndIndex:y}))))},{}),B=wA(I(I({},M),{},{props:x,graphicalItems:T}),w?.legendBBox);Object.keys(M).forEach(function(z){M[z]=s(x,M[z],B,z.replace("Map",""),n)});var W=M["".concat(D,"Map")],V=OA(W),H=p(x,I(I({},M),{},{dataStartIndex:A,dataEndIndex:y,updateId:g,graphicalItems:T,stackGroups:C,offset:B}));return I(I({formattedGraphicalItems:H,graphicalItems:T,offset:B,stackGroups:C},V),M)},v=function(m){function O(w){var x,A,y;return rA(this,O),y=aA(this,O,[w]),U(y,"eventEmitterSymbol",Symbol("rechartsEventEmitter")),U(y,"accessibilityManager",new Kw),U(y,"handleLegendBBoxUpdate",function(g){if(g){var P=y.state,S=P.dataStartIndex,j=P.dataEndIndex,E=P.updateId;y.setState(I({legendBBox:g},d({props:y.props,dataStartIndex:S,dataEndIndex:j,updateId:E},I(I({},y.state),{},{legendBBox:g}))))}}),U(y,"handleReceiveSyncEvent",function(g,P,S){if(y.props.syncId===g){if(S===y.eventEmitterSymbol&&typeof y.props.syncMethod!="function")return;y.applySyncEvent(P)}}),U(y,"handleBrushChange",function(g){var P=g.startIndex,S=g.endIndex;if(P!==y.state.dataStartIndex||S!==y.state.dataEndIndex){var j=y.state.updateId;y.setState(function(){return I({dataStartIndex:P,dataEndIndex:S},d({props:y.props,dataStartIndex:P,dataEndIndex:S,updateId:j},y.state))}),y.triggerSyncEvent({dataStartIndex:P,dataEndIndex:S})}}),U(y,"handleMouseEnter",function(g){var P=y.getMouseInfo(g);if(P){var S=I(I({},P),{},{isTooltipActive:!0});y.setState(S),y.triggerSyncEvent(S);var j=y.props.onMouseEnter;K(j)&&j(S,g)}}),U(y,"triggeredAfterMouseMove",function(g){var P=y.getMouseInfo(g),S=P?I(I({},P),{},{isTooltipActive:!0}):{isTooltipActive:!1};y.setState(S),y.triggerSyncEvent(S);var j=y.props.onMouseMove;K(j)&&j(S,g)}),U(y,"handleItemMouseEnter",function(g){y.setState(function(){return{isTooltipActive:!0,activeItem:g,activePayload:g.tooltipPayload,activeCoordinate:g.tooltipPosition||{x:g.cx,y:g.cy}}})}),U(y,"handleItemMouseLeave",function(){y.setState(function(){return{isTooltipActive:!1}})}),U(y,"handleMouseMove",function(g){g.persist(),y.throttleTriggeredAfterMouseMove(g)}),U(y,"handleMouseLeave",function(g){y.throttleTriggeredAfterMouseMove.cancel();var P={isTooltipActive:!1};y.setState(P),y.triggerSyncEvent(P);var S=y.props.onMouseLeave;K(S)&&S(P,g)}),U(y,"handleOuterEvent",function(g){var P=Jp(g),S=he(y.props,"".concat(P));if(P&&K(S)){var j,E;/.*touch.*/i.test(P)?E=y.getMouseInfo(g.changedTouches[0]):E=y.getMouseInfo(g),S((j=E)!==null&&j!==void 0?j:{},g)}}),U(y,"handleClick",function(g){var P=y.getMouseInfo(g);if(P){var S=I(I({},P),{},{isTooltipActive:!0});y.setState(S),y.triggerSyncEvent(S);var j=y.props.onClick;K(j)&&j(S,g)}}),U(y,"handleMouseDown",function(g){var P=y.props.onMouseDown;if(K(P)){var S=y.getMouseInfo(g);P(S,g)}}),U(y,"handleMouseUp",function(g){var P=y.props.onMouseUp;if(K(P)){var S=y.getMouseInfo(g);P(S,g)}}),U(y,"handleTouchMove",function(g){g.changedTouches!=null&&g.changedTouches.length>0&&y.throttleTriggeredAfterMouseMove(g.changedTouches[0])}),U(y,"handleTouchStart",function(g){g.changedTouches!=null&&g.changedTouches.length>0&&y.handleMouseDown(g.changedTouches[0])}),U(y,"handleTouchEnd",function(g){g.changedTouches!=null&&g.changedTouches.length>0&&y.handleMouseUp(g.changedTouches[0])}),U(y,"handleDoubleClick",function(g){var P=y.props.onDoubleClick;if(K(P)){var S=y.getMouseInfo(g);P(S,g)}}),U(y,"handleContextMenu",function(g){var P=y.props.onContextMenu;if(K(P)){var S=y.getMouseInfo(g);P(S,g)}}),U(y,"triggerSyncEvent",function(g){y.props.syncId!==void 0&&da.emit(va,y.props.syncId,g,y.eventEmitterSymbol)}),U(y,"applySyncEvent",function(g){var P=y.props,S=P.layout,j=P.syncMethod,E=y.state.updateId,$=g.dataStartIndex,_=g.dataEndIndex;if(g.dataStartIndex!==void 0||g.dataEndIndex!==void 0)y.setState(I({dataStartIndex:$,dataEndIndex:_},d({props:y.props,dataStartIndex:$,dataEndIndex:_,updateId:E},y.state)));else if(g.activeTooltipIndex!==void 0){var k=g.chartX,D=g.chartY,T=g.activeTooltipIndex,C=y.state,M=C.offset,B=C.tooltipTicks;if(!M)return;if(typeof j=="function")T=j(B,g);else if(j==="value"){T=-1;for(var W=0;W<B.length;W++)if(B[W].value===g.activeLabel){T=W;break}}var V=I(I({},M),{},{x:M.left,y:M.top}),H=Math.min(k,V.x+V.width),z=Math.min(D,V.y+V.height),Y=B[T]&&B[T].value,ee=$o(y.state,y.props.data,T),ae=B[T]?{x:S==="horizontal"?B[T].coordinate:H,y:S==="horizontal"?z:B[T].coordinate}:Lf;y.setState(I(I({},g),{},{activeLabel:Y,activeCoordinate:ae,activePayload:ee,activeTooltipIndex:T}))}else y.setState(g)}),U(y,"renderCursor",function(g){var P,S=y.state,j=S.isTooltipActive,E=S.activeCoordinate,$=S.activePayload,_=S.offset,k=S.activeTooltipIndex,D=S.tooltipAxisBandSize,T=y.getTooltipEventType(),C=(P=g.props.active)!==null&&P!==void 0?P:j,M=y.props.layout,B=g.key||"_recharts-cursor";return b.createElement(Yw,{key:B,activeCoordinate:E,activePayload:$,activeTooltipIndex:k,chartName:n,element:g,isActive:C,layout:M,offset:_,tooltipAxisBandSize:D,tooltipEventType:T})}),U(y,"renderPolarAxis",function(g,P,S){var j=he(g,"type.axisType"),E=he(y.state,"".concat(j,"Map")),$=g.type.defaultProps,_=$!==void 0?I(I({},$),g.props):g.props,k=E&&E[_["".concat(j,"Id")]];return N.cloneElement(g,I(I({},k),{},{className:X(j,k.className),key:g.key||"".concat(P,"-").concat(S),ticks:Je(k,!0)}))}),U(y,"renderPolarGrid",function(g){var P=g.props,S=P.radialLines,j=P.polarAngles,E=P.polarRadius,$=y.state,_=$.radiusAxisMap,k=$.angleAxisMap,D=ct(_),T=ct(k),C=T.cx,M=T.cy,B=T.innerRadius,W=T.outerRadius;return N.cloneElement(g,{polarAngles:Array.isArray(j)?j:Je(T,!0).map(function(V){return V.coordinate}),polarRadius:Array.isArray(E)?E:Je(D,!0).map(function(V){return V.coordinate}),cx:C,cy:M,innerRadius:B,outerRadius:W,key:g.key||"polar-grid",radialLines:S})}),U(y,"renderLegend",function(){var g=y.state.formattedGraphicalItems,P=y.props,S=P.children,j=P.width,E=P.height,$=y.props.margin||{},_=j-($.left||0)-($.right||0),k=ys({children:S,formattedGraphicalItems:g,legendWidth:_,legendContent:u});if(!k)return null;var D=k.item,T=gu(k,qw);return N.cloneElement(D,I(I({},T),{},{chartWidth:j,chartHeight:E,margin:$,onBBoxUpdate:y.handleLegendBBoxUpdate}))}),U(y,"renderTooltip",function(){var g,P=y.props,S=P.children,j=P.accessibilityLayer,E=oe(S,me);if(!E)return null;var $=y.state,_=$.isTooltipActive,k=$.activeCoordinate,D=$.activePayload,T=$.activeLabel,C=$.offset,M=(g=E.props.active)!==null&&g!==void 0?g:_;return N.cloneElement(E,{viewBox:I(I({},C),{},{x:C.left,y:C.top}),active:M,label:T,payload:M?D:[],coordinate:k,accessibilityLayer:j})}),U(y,"renderBrush",function(g){var P=y.props,S=P.margin,j=P.data,E=y.state,$=E.offset,_=E.dataStartIndex,k=E.dataEndIndex,D=E.updateId;return N.cloneElement(g,{key:g.key||"_recharts-brush",onChange:Rn(y.handleBrushChange,g.props.onChange),data:j,x:R(g.props.x)?g.props.x:$.left,y:R(g.props.y)?g.props.y:$.top+$.height+$.brushBottom-(S.bottom||0),width:R(g.props.width)?g.props.width:$.width,startIndex:_,endIndex:k,updateId:"brush-".concat(D)})}),U(y,"renderReferenceElement",function(g,P,S){if(!g)return null;var j=y,E=j.clipPathId,$=y.state,_=$.xAxisMap,k=$.yAxisMap,D=$.offset,T=g.type.defaultProps||{},C=g.props,M=C.xAxisId,B=M===void 0?T.xAxisId:M,W=C.yAxisId,V=W===void 0?T.yAxisId:W;return N.cloneElement(g,{key:g.key||"".concat(P,"-").concat(S),xAxis:_[B],yAxis:k[V],viewBox:{x:D.left,y:D.top,width:D.width,height:D.height},clipPathId:E})}),U(y,"renderActivePoints",function(g){var P=g.item,S=g.activePoint,j=g.basePoint,E=g.childIndex,$=g.isRange,_=[],k=P.props.key,D=P.item.type.defaultProps!==void 0?I(I({},P.item.type.defaultProps),P.item.props):P.item.props,T=D.activeDot,C=D.dataKey,M=I(I({index:E,dataKey:C,cx:S.x,cy:S.y,r:4,fill:Wo(P.item),strokeWidth:2,stroke:"#fff",payload:S.payload,value:S.value},L(T,!1)),Gn(T));return _.push(O.renderActiveDot(T,M,"".concat(k,"-activePoint-").concat(E))),j?_.push(O.renderActiveDot(T,I(I({},M),{},{cx:j.x,cy:j.y}),"".concat(k,"-basePoint-").concat(E))):$&&_.push(null),_}),U(y,"renderGraphicChild",function(g,P,S){var j=y.filterFormatItem(g,P,S);if(!j)return null;var E=y.getTooltipEventType(),$=y.state,_=$.isTooltipActive,k=$.tooltipAxis,D=$.activeTooltipIndex,T=$.activeLabel,C=y.props.children,M=oe(C,me),B=j.props,W=B.points,V=B.isRange,H=B.baseLine,z=j.item.type.defaultProps!==void 0?I(I({},j.item.type.defaultProps),j.item.props):j.item.props,Y=z.activeDot,ee=z.hide,ae=z.activeBar,_e=z.activeShape,mt=!!(!ee&&_&&M&&(Y||ae||_e)),Se={};E!=="axis"&&M&&M.props.trigger==="click"?Se={onClick:Rn(y.handleItemMouseEnter,g.props.onClick)}:E!=="axis"&&(Se={onMouseLeave:Rn(y.handleItemMouseLeave,g.props.onMouseLeave),onMouseEnter:Rn(y.handleItemMouseEnter,g.props.onMouseEnter)});var ze=N.cloneElement(g,I(I({},j.props),Se));function gt(Ue){return typeof k.dataKey=="function"?k.dataKey(Ue.payload):null}if(mt)if(D>=0){var at,bt;if(k.dataKey&&!k.allowDuplicatedCategory){var Xe=typeof k.dataKey=="function"?gt:"payload.".concat(k.dataKey.toString());at=Xn(W,Xe,T),bt=V&&H&&Xn(H,Xe,T)}else at=W?.[D],bt=V&&H&&H[D];if(_e||ae){var Ge=g.props.activeIndex!==void 0?g.props.activeIndex:D;return[N.cloneElement(g,I(I(I({},j.props),Se),{},{activeIndex:Ge})),null,null]}if(!G(at))return[ze].concat(Pr(y.renderActivePoints({item:j,activePoint:at,basePoint:bt,childIndex:D,isRange:V})))}else{var Ot,xt=(Ot=y.getItemByXY(y.state.activeCoordinate))!==null&&Ot!==void 0?Ot:{graphicalItem:ze},le=xt.graphicalItem,Oe=le.item,ke=Oe===void 0?g:Oe,Ke=le.childIndex,He=I(I(I({},j.props),Se),{},{activeIndex:Ke});return[N.cloneElement(ke,He),null,null]}return V?[ze,null,null]:[ze,null]}),U(y,"renderCustomized",function(g,P,S){return N.cloneElement(g,I(I({key:"recharts-customized-".concat(S)},y.props),y.state))}),U(y,"renderMap",{CartesianGrid:{handler:zn,once:!0},ReferenceArea:{handler:y.renderReferenceElement},ReferenceLine:{handler:zn},ReferenceDot:{handler:y.renderReferenceElement},XAxis:{handler:zn},YAxis:{handler:zn},Brush:{handler:y.renderBrush,once:!0},Bar:{handler:y.renderGraphicChild},Line:{handler:y.renderGraphicChild},Area:{handler:y.renderGraphicChild},Radar:{handler:y.renderGraphicChild},RadialBar:{handler:y.renderGraphicChild},Scatter:{handler:y.renderGraphicChild},Pie:{handler:y.renderGraphicChild},Funnel:{handler:y.renderGraphicChild},Tooltip:{handler:y.renderCursor,once:!0},PolarGrid:{handler:y.renderPolarGrid,once:!0},PolarAngleAxis:{handler:y.renderPolarAxis},PolarRadiusAxis:{handler:y.renderPolarAxis},Customized:{handler:y.renderCustomized}}),y.clipPathId="".concat((x=w.id)!==null&&x!==void 0?x:nt("recharts"),"-clip"),y.throttleTriggeredAfterMouseMove=Ru(y.triggeredAfterMouseMove,(A=w.throttleDelay)!==null&&A!==void 0?A:1e3/60),y.state={},y}return lA(O,m),iA(O,[{key:"componentDidMount",value:function(){var x,A;this.addListener(),this.accessibilityManager.setDetails({container:this.container,offset:{left:(x=this.props.margin.left)!==null&&x!==void 0?x:0,top:(A=this.props.margin.top)!==null&&A!==void 0?A:0},coordinateList:this.state.tooltipTicks,mouseHandlerCallback:this.triggeredAfterMouseMove,layout:this.props.layout}),this.displayDefaultTooltip()}},{key:"displayDefaultTooltip",value:function(){var x=this.props,A=x.children,y=x.data,g=x.height,P=x.layout,S=oe(A,me);if(S){var j=S.props.defaultIndex;if(!(typeof j!="number"||j<0||j>this.state.tooltipTicks.length-1)){var E=this.state.tooltipTicks[j]&&this.state.tooltipTicks[j].value,$=$o(this.state,y,j,E),_=this.state.tooltipTicks[j].coordinate,k=(this.state.offset.top+g)/2,D=P==="horizontal",T=D?{x:_,y:k}:{y:_,x:k},C=this.state.formattedGraphicalItems.find(function(B){var W=B.item;return W.type.name==="Scatter"});C&&(T=I(I({},T),C.props.points[j].tooltipPosition),$=C.props.points[j].tooltipPayload);var M={activeTooltipIndex:j,isTooltipActive:!0,activeLabel:E,activePayload:$,activeCoordinate:T};this.setState(M),this.renderCursor(S),this.accessibilityManager.setIndex(j)}}}},{key:"getSnapshotBeforeUpdate",value:function(x,A){if(!this.props.accessibilityLayer)return null;if(this.state.tooltipTicks!==A.tooltipTicks&&this.accessibilityManager.setDetails({coordinateList:this.state.tooltipTicks}),this.props.layout!==x.layout&&this.accessibilityManager.setDetails({layout:this.props.layout}),this.props.margin!==x.margin){var y,g;this.accessibilityManager.setDetails({offset:{left:(y=this.props.margin.left)!==null&&y!==void 0?y:0,top:(g=this.props.margin.top)!==null&&g!==void 0?g:0}})}return null}},{key:"componentDidUpdate",value:function(x){Aa([oe(x.children,me)],[oe(this.props.children,me)])||this.displayDefaultTooltip()}},{key:"componentWillUnmount",value:function(){this.removeListener(),this.throttleTriggeredAfterMouseMove.cancel()}},{key:"getTooltipEventType",value:function(){var x=oe(this.props.children,me);if(x&&typeof x.props.shared=="boolean"){var A=x.props.shared?"axis":"item";return c.indexOf(A)>=0?A:a}return a}},{key:"getMouseInfo",value:function(x){if(!this.container)return null;var A=this.container,y=A.getBoundingClientRect(),g=Dv(y),P={chartX:Math.round(x.pageX-g.left),chartY:Math.round(x.pageY-g.top)},S=y.width/A.offsetWidth||1,j=this.inRange(P.chartX,P.chartY,S);if(!j)return null;var E=this.state,$=E.xAxisMap,_=E.yAxisMap,k=this.getTooltipEventType(),D=Ou(this.state,this.props.data,this.props.layout,j);if(k!=="axis"&&$&&_){var T=ct($).scale,C=ct(_).scale,M=T&&T.invert?T.invert(P.chartX):null,B=C&&C.invert?C.invert(P.chartY):null;return I(I({},P),{},{xValue:M,yValue:B},D)}return D?I(I({},P),D):null}},{key:"inRange",value:function(x,A){var y=arguments.length>2&&arguments[2]!==void 0?arguments[2]:1,g=this.props.layout,P=x/y,S=A/y;if(g==="horizontal"||g==="vertical"){var j=this.state.offset,E=P>=j.left&&P<=j.left+j.width&&S>=j.top&&S<=j.top+j.height;return E?{x:P,y:S}:null}var $=this.state,_=$.angleAxisMap,k=$.radiusAxisMap;if(_&&k){var D=ct(_);return Jc({x:P,y:S},D)}return null}},{key:"parseEventsOfWrapper",value:function(){var x=this.props.children,A=this.getTooltipEventType(),y=oe(x,me),g={};y&&A==="axis"&&(y.props.trigger==="click"?g={onClick:this.handleClick}:g={onMouseEnter:this.handleMouseEnter,onDoubleClick:this.handleDoubleClick,onMouseMove:this.handleMouseMove,onMouseLeave:this.handleMouseLeave,onTouchMove:this.handleTouchMove,onTouchStart:this.handleTouchStart,onTouchEnd:this.handleTouchEnd,onContextMenu:this.handleContextMenu});var P=Gn(this.props,this.handleOuterEvent);return I(I({},P),g)}},{key:"addListener",value:function(){da.on(va,this.handleReceiveSyncEvent)}},{key:"removeListener",value:function(){da.removeListener(va,this.handleReceiveSyncEvent)}},{key:"filterFormatItem",value:function(x,A,y){for(var g=this.state.formattedGraphicalItems,P=0,S=g.length;P<S;P++){var j=g[P];if(j.item===x||j.props.key===x.key||A===Qe(j.item.type)&&y===j.childIndex)return j}return null}},{key:"renderClipPath",value:function(){var x=this.clipPathId,A=this.state.offset,y=A.left,g=A.top,P=A.height,S=A.width;return b.createElement("defs",null,b.createElement("clipPath",{id:x},b.createElement("rect",{x:y,y:g,height:P,width:S})))}},{key:"getXScales",value:function(){var x=this.state.xAxisMap;return x?Object.entries(x).reduce(function(A,y){var g=mu(y,2),P=g[0],S=g[1];return I(I({},A),{},U({},P,S.scale))},{}):null}},{key:"getYScales",value:function(){var x=this.state.yAxisMap;return x?Object.entries(x).reduce(function(A,y){var g=mu(y,2),P=g[0],S=g[1];return I(I({},A),{},U({},P,S.scale))},{}):null}},{key:"getXScaleByAxisId",value:function(x){var A;return(A=this.state.xAxisMap)===null||A===void 0||(A=A[x])===null||A===void 0?void 0:A.scale}},{key:"getYScaleByAxisId",value:function(x){var A;return(A=this.state.yAxisMap)===null||A===void 0||(A=A[x])===null||A===void 0?void 0:A.scale}},{key:"getItemByXY",value:function(x){var A=this.state,y=A.formattedGraphicalItems,g=A.activeItem;if(y&&y.length)for(var P=0,S=y.length;P<S;P++){var j=y[P],E=j.props,$=j.item,_=$.type.defaultProps!==void 0?I(I({},$.type.defaultProps),$.props):$.props,k=Qe($.type);if(k==="Bar"){var D=(E.data||[]).find(function(B){return Em(x,B)});if(D)return{graphicalItem:j,payload:D}}else if(k==="RadialBar"){var T=(E.data||[]).find(function(B){return Jc(x,B)});if(T)return{graphicalItem:j,payload:T}}else if(Xi(j,g)||Gi(j,g)||hn(j,g)){var C=Hg({graphicalItem:j,activeTooltipItem:g,itemData:_.data}),M=_.activeIndex===void 0?C:_.activeIndex;return{graphicalItem:I(I({},j),{},{childIndex:M}),payload:hn(j,g)?_.data[C]:j.props.data[C]}}}return null}},{key:"render",value:function(){var x=this;if(!Hn(this))return null;var A=this.props,y=A.children,g=A.className,P=A.width,S=A.height,j=A.style,E=A.compact,$=A.title,_=A.desc,k=gu(A,Zw),D=L(k,!1);if(E)return b.createElement(Yl,{state:this.state,width:this.props.width,height:this.props.height,clipPathId:this.clipPathId},b.createElement(Zt,Ht({},D,{width:P,height:S,title:$,desc:_}),this.renderClipPath(),pc(y,this.renderMap)));if(this.props.accessibilityLayer){var T,C;D.tabIndex=(T=this.props.tabIndex)!==null&&T!==void 0?T:0,D.role=(C=this.props.role)!==null&&C!==void 0?C:"application",D.onKeyDown=function(B){x.accessibilityManager.keyboardEvent(B)},D.onFocus=function(){x.accessibilityManager.focus()}}var M=this.parseEventsOfWrapper();return b.createElement(Yl,{state:this.state,width:this.props.width,height:this.props.height,clipPathId:this.clipPathId},b.createElement("div",Ht({className:X("recharts-wrapper",g),style:I({position:"relative",cursor:"default",width:P,height:S},j)},M,{ref:function(W){x.container=W}}),b.createElement(Zt,Ht({},D,{width:P,height:S,title:$,desc:_,style:vA}),this.renderClipPath(),pc(y,this.renderMap)),this.renderLegend(),this.renderTooltip()))}}])}(N.Component);U(v,"displayName",n),U(v,"defaultProps",I({layout:"horizontal",stackOffset:"none",barCategoryGap:"10%",barGap:4,margin:{top:5,right:5,bottom:5,left:5},reverseStackOrder:!1,syncMethod:"index"},f)),U(v,"getDerivedStateFromProps",function(m,O){var w=m.dataKey,x=m.data,A=m.children,y=m.width,g=m.height,P=m.layout,S=m.stackOffset,j=m.margin,E=O.dataStartIndex,$=O.dataEndIndex;if(O.updateId===void 0){var _=xu(m);return I(I(I({},_),{},{updateId:0},d(I(I({props:m},_),{},{updateId:0}),O)),{},{prevDataKey:w,prevData:x,prevWidth:y,prevHeight:g,prevLayout:P,prevStackOffset:S,prevMargin:j,prevChildren:A})}if(w!==O.prevDataKey||x!==O.prevData||y!==O.prevWidth||g!==O.prevHeight||P!==O.prevLayout||S!==O.prevStackOffset||!Tt(j,O.prevMargin)){var k=xu(m),D={chartX:O.chartX,chartY:O.chartY,isTooltipActive:O.isTooltipActive},T=I(I({},Ou(O,x,P)),{},{updateId:O.updateId+1}),C=I(I(I({},k),D),T);return I(I(I({},C),d(I({props:m},C),O)),{},{prevDataKey:w,prevData:x,prevWidth:y,prevHeight:g,prevLayout:P,prevStackOffset:S,prevMargin:j,prevChildren:A})}if(!Aa(A,O.prevChildren)){var M,B,W,V,H=oe(A,fr),z=H&&(M=(B=H.props)===null||B===void 0?void 0:B.startIndex)!==null&&M!==void 0?M:E,Y=H&&(W=(V=H.props)===null||V===void 0?void 0:V.endIndex)!==null&&W!==void 0?W:$,ee=z!==E||Y!==$,ae=!G(x),_e=ae&&!ee?O.updateId:O.updateId+1;return I(I({updateId:_e},d(I(I({props:m},O),{},{updateId:_e,dataStartIndex:z,dataEndIndex:Y}),O)),{},{prevChildren:A,dataStartIndex:z,dataEndIndex:Y})}return null}),U(v,"renderActiveDot",function(m,O,w){var x;return N.isValidElement(m)?x=N.cloneElement(m,O):K(m)?x=m(O):x=b.createElement(Ir,O),b.createElement(F,{className:"recharts-active-dot",key:w},x)});var h=N.forwardRef(function(O,w){return b.createElement(v,Ht({},O,{ref:w}))});return h.displayName=v.displayName,h},zP=it({chartName:"LineChart",GraphicalChild:_n,axisComponents:[{axisType:"xAxis",AxisComp:Mt},{axisType:"yAxis",AxisComp:Nt}],formatAxisMap:$n}),KP=it({chartName:"BarChart",GraphicalChild:Ct,defaultTooltipEventType:"axis",validateTooltipEventTypes:["axis","item"],axisComponents:[{axisType:"xAxis",AxisComp:Mt},{axisType:"yAxis",AxisComp:Nt}],formatAxisMap:$n}),FP=it({chartName:"PieChart",GraphicalChild:ht,validateTooltipEventTypes:["item"],defaultTooltipEventType:"item",legendContent:"children",axisComponents:[{axisType:"angleAxis",AxisComp:Dr},{axisType:"radiusAxis",AxisComp:kr}],formatAxisMap:Ko,defaultProps:{layout:"centric",startAngle:0,endAngle:360,cx:"50%",cy:"50%",innerRadius:0,outerRadius:"80%"}}),PA=["#1890FF","#66B5FF","#41D9C7","#2FC25B","#6EDB8F","#9AE65C","#FACC14","#E6965C","#57AD71","#223273","#738AE6","#7564CC","#8543E0","#A877ED","#5C8EE6","#13C2C2","#70E0E0","#5CA3E6","#3436C7","#8082FF","#DD81E6","#F04864","#FA7D92","#D598D9"],SA=["width","height","className","style","children","type"];function Sr(t){"@babel/helpers - typeof";return Sr=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Sr(t)}function Ii(){return Ii=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var n=arguments[e];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(t[r]=n[r])}return t},Ii.apply(this,arguments)}function jA(t,e){if(t==null)return{};var n=EA(t,e),r,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(t);for(i=0;i<a.length;i++)r=a[i],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(n[r]=t[r])}return n}function EA(t,e){if(t==null)return{};var n={};for(var r in t)if(Object.prototype.hasOwnProperty.call(t,r)){if(e.indexOf(r)>=0)continue;n[r]=t[r]}return n}function $A(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function Au(t,e){for(var n=0;n<e.length;n++){var r=e[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,Kf(r.key),r)}}function _A(t,e,n){return e&&Au(t.prototype,e),n&&Au(t,n),Object.defineProperty(t,"prototype",{writable:!1}),t}function TA(t,e,n){return e=ki(e),IA(t,zf()?Reflect.construct(e,n||[],ki(t).constructor):e.apply(t,n))}function IA(t,e){if(e&&(Sr(e)==="object"||typeof e=="function"))return e;if(e!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return kA(t)}function kA(t){if(t===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}function zf(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(zf=function(){return!!t})()}function ki(t){return ki=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(n){return n.__proto__||Object.getPrototypeOf(n)},ki(t)}function DA(t,e){if(typeof e!="function"&&e!==null)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&_o(t,e)}function _o(t,e){return _o=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(r,i){return r.__proto__=i,r},_o(t,e)}function Pu(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter(function(i){return Object.getOwnPropertyDescriptor(t,i).enumerable})),n.push.apply(n,r)}return n}function te(t){for(var e=1;e<arguments.length;e++){var n=arguments[e]!=null?arguments[e]:{};e%2?Pu(Object(n),!0).forEach(function(r){et(t,r,n[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):Pu(Object(n)).forEach(function(r){Object.defineProperty(t,r,Object.getOwnPropertyDescriptor(n,r))})}return t}function et(t,e,n){return e=Kf(e),e in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function Kf(t){var e=CA(t,"string");return Sr(e)=="symbol"?e:e+""}function CA(t,e){if(Sr(t)!="object"||!t)return t;var n=t[Symbol.toPrimitive];if(n!==void 0){var r=n.call(t,e);if(Sr(r)!="object")return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}var An="value",ya=function t(e){var n=e.depth,r=e.node,i=e.index,a=e.valueKey,o=r.children,c=n+1,l=o&&o.length?o.map(function(s,f){return t({depth:c,node:s,index:f,valueKey:a})}):null,u;return o&&o.length?u=l.reduce(function(s,f){return s+f[An]},0):u=vt(r[a])||r[a]<=0?0:r[a],te(te({},r),{},et(et(et({children:l},An,u),"depth",n),"index",i))},MA=function(e){return{x:e.x,y:e.y,width:e.width,height:e.height}},NA=function(e,n){var r=n<0?0:n;return e.map(function(i){var a=i[An]*r;return te(te({},i),{},{area:vt(a)||a<=0?0:a})})},RA=function(e,n,r){var i=n*n,a=e.area*e.area,o=e.reduce(function(u,s){return{min:Math.min(u.min,s.area),max:Math.max(u.max,s.area)}},{min:1/0,max:0}),c=o.min,l=o.max;return a?Math.max(i*l*r/a,a/(i*c*r)):1/0},BA=function(e,n,r,i){var a=n?Math.round(e.area/n):0;(i||a>r.height)&&(a=r.height);for(var o=r.x,c,l=0,u=e.length;l<u;l++)c=e[l],c.x=o,c.y=r.y,c.height=a,c.width=Math.min(a?Math.round(c.area/a):0,r.x+r.width-o),o+=c.width;return c.width+=r.x+r.width-o,te(te({},r),{},{y:r.y+a,height:r.height-a})},LA=function(e,n,r,i){var a=n?Math.round(e.area/n):0;(i||a>r.width)&&(a=r.width);for(var o=r.y,c,l=0,u=e.length;l<u;l++)c=e[l],c.x=r.x,c.y=o,c.width=a,c.height=Math.min(a?Math.round(c.area/a):0,r.y+r.height-o),o+=c.height;return c&&(c.height+=r.y+r.height-o),te(te({},r),{},{x:r.x+a,width:r.width-a})},Su=function(e,n,r,i){return n===r.width?BA(e,n,r,i):LA(e,n,r,i)},ma=function t(e,n){var r=e.children;if(r&&r.length){var i=MA(e),a=[],o=1/0,c,l,u=Math.min(i.width,i.height),s=NA(r,i.width*i.height/e[An]),f=s.slice();for(a.area=0;f.length>0;)a.push(c=f[0]),a.area+=c.area,l=RA(a,u,n),l<=o?(f.shift(),o=l):(a.area-=a.pop().area,i=Su(a,u,i,!1),u=Math.min(i.width,i.height),a.length=a.area=0,o=1/0);return a.length&&(i=Su(a,u,i,!0),a.length=a.area=0),te(te({},e),{},{children:s.map(function(p){return t(p,n)})})}return e},WA={isTooltipActive:!1,isAnimationFinished:!1,activeNode:null,formatRoot:null,currentRoot:null,nestIndex:[]},Ff=function(t){function e(){var n;$A(this,e);for(var r=arguments.length,i=new Array(r),a=0;a<r;a++)i[a]=arguments[a];return n=TA(this,e,[].concat(i)),et(n,"state",te({},WA)),et(n,"handleAnimationEnd",function(){var o=n.props.onAnimationEnd;n.setState({isAnimationFinished:!0}),K(o)&&o()}),et(n,"handleAnimationStart",function(){var o=n.props.onAnimationStart;n.setState({isAnimationFinished:!1}),K(o)&&o()}),n}return DA(e,t),_A(e,[{key:"handleMouseEnter",value:function(r,i){i.persist();var a=this.props,o=a.onMouseEnter,c=a.children,l=oe(c,me);l?this.setState({isTooltipActive:!0,activeNode:r},function(){o&&o(r,i)}):o&&o(r,i)}},{key:"handleMouseLeave",value:function(r,i){i.persist();var a=this.props,o=a.onMouseLeave,c=a.children,l=oe(c,me);l?this.setState({isTooltipActive:!1,activeNode:null},function(){o&&o(r,i)}):o&&o(r,i)}},{key:"handleClick",value:function(r){var i=this.props,a=i.onClick,o=i.type;if(o==="nest"&&r.children){var c=this.props,l=c.width,u=c.height,s=c.dataKey,f=c.aspectRatio,p=ya({depth:0,node:te(te({},r),{},{x:0,y:0,width:l,height:u}),index:0,valueKey:s}),d=ma(p,f),v=this.state.nestIndex;v.push(r),this.setState({formatRoot:d,currentRoot:p,nestIndex:v})}a&&a(r)}},{key:"handleNestIndex",value:function(r,i){var a=this.state.nestIndex,o=this.props,c=o.width,l=o.height,u=o.dataKey,s=o.aspectRatio,f=ya({depth:0,node:te(te({},r),{},{x:0,y:0,width:c,height:l}),index:0,valueKey:u}),p=ma(f,s);a=a.slice(0,i+1),this.setState({formatRoot:p,currentRoot:r,nestIndex:a})}},{key:"renderItem",value:function(r,i,a){var o=this,c=this.props,l=c.isAnimationActive,u=c.animationBegin,s=c.animationDuration,f=c.animationEasing,p=c.isUpdateAnimationActive,d=c.type,v=c.animationId,h=c.colorPanel,m=this.state.isAnimationFinished,O=i.width,w=i.height,x=i.x,A=i.y,y=i.depth,g=parseInt("".concat((Math.random()*2-1)*O),10),P={};return(a||d==="nest")&&(P={onMouseEnter:this.handleMouseEnter.bind(this,i),onMouseLeave:this.handleMouseLeave.bind(this,i),onClick:this.handleClick.bind(this,i)}),l?b.createElement($e,{begin:u,duration:s,isActive:l,easing:f,key:"treemap-".concat(v),from:{x,y:A,width:O,height:w},to:{x,y:A,width:O,height:w},onAnimationStart:this.handleAnimationStart,onAnimationEnd:this.handleAnimationEnd},function(S){var j=S.x,E=S.y,$=S.width,_=S.height;return b.createElement($e,{from:"translate(".concat(g,"px, ").concat(g,"px)"),to:"translate(0, 0)",attributeName:"transform",begin:u,easing:f,isActive:l,duration:s},b.createElement(F,P,function(){return y>2&&!m?null:o.constructor.renderContentItem(r,te(te({},i),{},{isAnimationActive:l,isUpdateAnimationActive:!p,width:$,height:_,x:j,y:E}),d,h)}()))}):b.createElement(F,P,this.constructor.renderContentItem(r,te(te({},i),{},{isAnimationActive:!1,isUpdateAnimationActive:!1,width:O,height:w,x,y:A}),d,h))}},{key:"renderNode",value:function(r,i){var a=this,o=this.props,c=o.content,l=o.type,u=te(te(te({},L(this.props,!1)),i),{},{root:r}),s=!i.children||!i.children.length,f=this.state.currentRoot,p=(f.children||[]).filter(function(d){return d.depth===i.depth&&d.name===i.name});return!p.length&&r.depth&&l==="nest"?null:b.createElement(F,{key:"recharts-treemap-node-".concat(u.x,"-").concat(u.y,"-").concat(u.name),className:"recharts-treemap-depth-".concat(i.depth)},this.renderItem(c,u,s),i.children&&i.children.length?i.children.map(function(d){return a.renderNode(i,d)}):null)}},{key:"renderAllNodes",value:function(){var r=this.state.formatRoot;return r?this.renderNode(r,r):null}},{key:"renderTooltip",value:function(){var r=this.props,i=r.children,a=r.nameKey,o=oe(i,me);if(!o)return null;var c=this.props,l=c.width,u=c.height,s=this.state,f=s.isTooltipActive,p=s.activeNode,d={x:0,y:0,width:l,height:u},v=p?{x:p.x+p.width/2,y:p.y+p.height/2}:null,h=f&&p?[{payload:p,name:Z(p,a,""),value:Z(p,An)}]:[];return b.cloneElement(o,{viewBox:d,active:f,coordinate:v,label:"",payload:h})}},{key:"renderNestIndex",value:function(){var r=this,i=this.props,a=i.nameKey,o=i.nestIndexContent,c=this.state.nestIndex;return b.createElement("div",{className:"recharts-treemap-nest-index-wrapper",style:{marginTop:"8px",textAlign:"center"}},c.map(function(l,u){var s=he(l,a,"root"),f=null;return b.isValidElement(o)&&(f=b.cloneElement(o,l,u)),K(o)?f=o(l,u):f=s,b.createElement("div",{onClick:r.handleNestIndex.bind(r,l,u),key:"nest-index-".concat(nt()),className:"recharts-treemap-nest-index-box",style:{cursor:"pointer",display:"inline-block",padding:"0 7px",background:"#000",color:"#fff",marginRight:"3px"}},f)}))}},{key:"render",value:function(){if(!Hn(this))return null;var r=this.props,i=r.width,a=r.height,o=r.className,c=r.style,l=r.children,u=r.type,s=jA(r,SA),f=L(s,!1);return b.createElement("div",{className:X("recharts-wrapper",o),style:te(te({},c),{},{position:"relative",cursor:"default",width:i,height:a}),role:"region"},b.createElement(Zt,Ii({},f,{width:i,height:u==="nest"?a-30:a}),this.renderAllNodes(),Xu(l)),this.renderTooltip(),u==="nest"&&this.renderNestIndex())}}],[{key:"getDerivedStateFromProps",value:function(r,i){if(r.data!==i.prevData||r.type!==i.prevType||r.width!==i.prevWidth||r.height!==i.prevHeight||r.dataKey!==i.prevDataKey||r.aspectRatio!==i.prevAspectRatio){var a=ya({depth:0,node:{children:r.data,x:0,y:0,width:r.width,height:r.height},index:0,valueKey:r.dataKey}),o=ma(a,r.aspectRatio);return te(te({},i),{},{formatRoot:o,currentRoot:a,nestIndex:[a],prevAspectRatio:r.aspectRatio,prevData:r.data,prevWidth:r.width,prevHeight:r.height,prevDataKey:r.dataKey,prevType:r.type})}return null}},{key:"renderContentItem",value:function(r,i,a,o){if(b.isValidElement(r))return b.cloneElement(r,i);if(K(r))return r(i);var c=i.x,l=i.y,u=i.width,s=i.height,f=i.index,p=null;u>10&&s>10&&i.children&&a==="nest"&&(p=b.createElement(Vo,{points:[{x:c+2,y:l+s/2},{x:c+6,y:l+s/2+3},{x:c+2,y:l+s/2+6}]}));var d=null,v=Yt(i.name);u>20&&s>20&&v.width<u&&v.height<s&&(d=b.createElement("text",{x:c+8,y:l+s/2+7,fontSize:14},i.name));var h=o||PA;return b.createElement("g",null,b.createElement(En,Ii({fill:i.depth<2?h[f%h.length]:"rgba(255,255,255,0)",stroke:"#fff"},Ku(i,"children"),{role:"img"})),p,d)}}])}(N.PureComponent);et(Ff,"displayName","Treemap");et(Ff,"defaultProps",{aspectRatio:.5*(1+Math.sqrt(5)),dataKey:"value",type:"flat",isAnimationActive:!be.isSsr,isUpdateAnimationActive:!be.isSsr,animationBegin:0,animationDuration:1500,animationEasing:"linear"});var zA=["width","height","className","style","children"],KA=["sourceX","sourceY","sourceControlX","targetX","targetY","targetControlX","linkWidth"];function jr(t){"@babel/helpers - typeof";return jr=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},jr(t)}function ju(t,e){if(t==null)return{};var n=FA(t,e),r,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(t);for(i=0;i<a.length;i++)r=a[i],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(n[r]=t[r])}return n}function FA(t,e){if(t==null)return{};var n={};for(var r in t)if(Object.prototype.hasOwnProperty.call(t,r)){if(e.indexOf(r)>=0)continue;n[r]=t[r]}return n}function jt(){return jt=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var n=arguments[e];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(t[r]=n[r])}return t},jt.apply(this,arguments)}function VA(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function Eu(t,e){for(var n=0;n<e.length;n++){var r=e[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,Xf(r.key),r)}}function XA(t,e,n){return e&&Eu(t.prototype,e),n&&Eu(t,n),Object.defineProperty(t,"prototype",{writable:!1}),t}function GA(t,e,n){return e=Di(e),HA(t,Vf()?Reflect.construct(e,n||[],Di(t).constructor):e.apply(t,n))}function HA(t,e){if(e&&(jr(e)==="object"||typeof e=="function"))return e;if(e!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return UA(t)}function UA(t){if(t===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}function Vf(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(Vf=function(){return!!t})()}function Di(t){return Di=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(n){return n.__proto__||Object.getPrototypeOf(n)},Di(t)}function YA(t,e){if(typeof e!="function"&&e!==null)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&To(t,e)}function To(t,e){return To=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(r,i){return r.__proto__=i,r},To(t,e)}function $u(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter(function(i){return Object.getOwnPropertyDescriptor(t,i).enumerable})),n.push.apply(n,r)}return n}function ne(t){for(var e=1;e<arguments.length;e++){var n=arguments[e]!=null?arguments[e]:{};e%2?$u(Object(n),!0).forEach(function(r){ta(t,r,n[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):$u(Object(n)).forEach(function(r){Object.defineProperty(t,r,Object.getOwnPropertyDescriptor(n,r))})}return t}function ta(t,e,n){return e=Xf(e),e in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function Xf(t){var e=qA(t,"string");return jr(e)=="symbol"?e:e+""}function qA(t,e){if(jr(t)!="object"||!t)return t;var n=t[Symbol.toPrimitive];if(n!==void 0){var r=n.call(t,e);if(jr(r)!="object")return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}var ZA={x:0,y:0},JA=function(e,n){var r=+e,i=n-r;return function(a){return r+i*a}},ra=function(e){return e.y+e.dy/2},Pn=function(e){return e&&e.value||0},Ci=function(e,n){return n.reduce(function(r,i){return r+Pn(e[i])},0)},QA=function(e,n,r){return r.reduce(function(i,a){var o=n[a],c=e[o.source];return i+ra(c)*Pn(n[a])},0)},eP=function(e,n,r){return r.reduce(function(i,a){var o=n[a],c=e[o.target];return i+ra(c)*Pn(n[a])},0)},tP=function(e,n){return e.y-n.y},rP=function(e,n){for(var r=[],i=[],a=[],o=[],c=0,l=e.length;c<l;c++){var u=e[c];u.source===n&&(a.push(u.target),o.push(c)),u.target===n&&(r.push(u.source),i.push(c))}return{sourceNodes:r,sourceLinks:i,targetLinks:o,targetNodes:a}},nP=function t(e,n){for(var r=n.targetNodes,i=0,a=r.length;i<a;i++){var o=e[r[i]];o&&(o.depth=Math.max(n.depth+1,o.depth),t(e,o))}},iP=function(e,n,r){for(var i=e.nodes,a=e.links,o=i.map(function(h,m){var O=rP(a,m);return ne(ne(ne({},h),O),{},{value:Math.max(Ci(a,O.sourceLinks),Ci(a,O.targetLinks)),depth:0})}),c=0,l=o.length;c<l;c++){var u=o[c];u.sourceNodes.length||nP(o,u)}var s=Wu(o,function(h){return h.depth}).depth;if(s>=1)for(var f=(n-r)/s,p=0,d=o.length;p<d;p++){var v=o[p];v.targetNodes.length||(v.depth=s),v.x=v.depth*f,v.dx=r}return{tree:o,maxDepth:s}},aP=function(e){for(var n=[],r=0,i=e.length;r<i;r++){var a=e[r];n[a.depth]||(n[a.depth]=[]),n[a.depth].push(a)}return n},oP=function(e,n,r,i){for(var a=jn(e.map(function(f){return(n-(f.length-1)*r)/Rp(f,Pn)})),o=0,c=e.length;o<c;o++)for(var l=0,u=e[o].length;l<u;l++){var s=e[o][l];s.y=l,s.dy=s.value*a}return i.map(function(f){return ne(ne({},f),{},{dy:Pn(f)*a})})},ga=function(e,n,r){for(var i=arguments.length>3&&arguments[3]!==void 0?arguments[3]:!0,a=0,o=e.length;a<o;a++){var c=e[a],l=c.length;i&&c.sort(tP);for(var u=0,s=0;s<l;s++){var f=c[s],p=u-f.y;p>0&&(f.y+=p),u=f.y+f.dy+r}u=n+r;for(var d=l-1;d>=0;d--){var v=c[d],h=v.y+v.dy+r-u;if(h>0)v.y-=h,u=v.y;else break}}},cP=function(e,n,r,i){for(var a=0,o=n.length;a<o;a++)for(var c=n[a],l=0,u=c.length;l<u;l++){var s=c[l];if(s.sourceLinks.length){var f=Ci(r,s.sourceLinks),p=QA(e,r,s.sourceLinks),d=p/f;s.y+=(d-ra(s))*i}}},lP=function(e,n,r,i){for(var a=n.length-1;a>=0;a--)for(var o=n[a],c=0,l=o.length;c<l;c++){var u=o[c];if(u.targetLinks.length){var s=Ci(r,u.targetLinks),f=eP(e,r,u.targetLinks),p=f/s;u.y+=(p-ra(u))*i}}},uP=function(e,n){for(var r=0,i=e.length;r<i;r++){var a=e[r],o=0,c=0;a.targetLinks.sort(function(v,h){return e[n[v].target].y-e[n[h].target].y}),a.sourceLinks.sort(function(v,h){return e[n[v].source].y-e[n[h].source].y});for(var l=0,u=a.targetLinks.length;l<u;l++){var s=n[a.targetLinks[l]];s&&(s.sy=o,o+=s.dy)}for(var f=0,p=a.sourceLinks.length;f<p;f++){var d=n[a.sourceLinks[f]];d&&(d.ty=c,c+=d.dy)}}},sP=function(e){var n=e.data,r=e.width,i=e.height,a=e.iterations,o=e.nodeWidth,c=e.nodePadding,l=e.sort,u=n.links,s=iP(n,r,o),f=s.tree,p=aP(f),d=oP(p,i,c,u);ga(p,i,c,l);for(var v=1,h=1;h<=a;h++)lP(f,p,d,v*=.99),ga(p,i,c,l),cP(f,p,d,v),ga(p,i,c,l);return uP(f,d),{nodes:f,links:d}},fP=function(e,n){return n==="node"?{x:e.x+e.width/2,y:e.y+e.height/2}:{x:(e.sourceX+e.targetX)/2,y:(e.sourceY+e.targetY)/2}},pP=function(e,n,r){var i=e.payload;if(n==="node")return[{payload:e,name:Z(i,r,""),value:Z(i,"value")}];if(i.source&&i.target){var a=Z(i.source,r,""),o=Z(i.target,r,"");return[{payload:e,name:"".concat(a," - ").concat(o),value:Z(i,"value")}]}return[]},Gf=function(t){function e(){var n;VA(this,e);for(var r=arguments.length,i=new Array(r),a=0;a<r;a++)i[a]=arguments[a];return n=GA(this,e,[].concat(i)),ta(n,"state",{activeElement:null,activeElementType:null,isTooltipActive:!1,nodes:[],links:[]}),n}return YA(e,t),XA(e,[{key:"handleMouseEnter",value:function(r,i,a){var o=this.props,c=o.onMouseEnter,l=o.children,u=oe(l,me);u?this.setState(function(s){return u.props.trigger==="hover"?ne(ne({},s),{},{activeElement:r,activeElementType:i,isTooltipActive:!0}):s},function(){c&&c(r,i,a)}):c&&c(r,i,a)}},{key:"handleMouseLeave",value:function(r,i,a){var o=this.props,c=o.onMouseLeave,l=o.children,u=oe(l,me);u?this.setState(function(s){return u.props.trigger==="hover"?ne(ne({},s),{},{activeElement:void 0,activeElementType:void 0,isTooltipActive:!1}):s},function(){c&&c(r,i,a)}):c&&c(r,i,a)}},{key:"handleClick",value:function(r,i,a){var o=this.props,c=o.onClick,l=o.children,u=oe(l,me);u&&u.props.trigger==="click"&&(this.state.isTooltipActive?this.setState(function(s){return ne(ne({},s),{},{activeElement:void 0,activeElementType:void 0,isTooltipActive:!1})}):this.setState(function(s){return ne(ne({},s),{},{activeElement:r,activeElementType:i,isTooltipActive:!0})})),c&&c(r,i,a)}},{key:"renderLinks",value:function(r,i){var a=this,o=this.props,c=o.linkCurvature,l=o.link,u=o.margin,s=he(u,"top")||0,f=he(u,"left")||0;return b.createElement(F,{className:"recharts-sankey-links",key:"recharts-sankey-links"},r.map(function(p,d){var v=p.sy,h=p.ty,m=p.dy,O=i[p.source],w=i[p.target],x=O.x+O.dx+f,A=w.x+f,y=JA(x,A),g=y(c),P=y(1-c),S=O.y+v+m/2+s,j=w.y+h+m/2+s,E=ne({sourceX:x,targetX:A,sourceY:S,targetY:j,sourceControlX:g,targetControlX:P,sourceRelativeY:v,targetRelativeY:h,linkWidth:m,index:d,payload:ne(ne({},p),{},{source:O,target:w})},L(l,!1)),$={onMouseEnter:a.handleMouseEnter.bind(a,E,"link"),onMouseLeave:a.handleMouseLeave.bind(a,E,"link"),onClick:a.handleClick.bind(a,E,"link")};return b.createElement(F,jt({key:"link-".concat(p.source,"-").concat(p.target,"-").concat(p.value)},$),a.constructor.renderLinkItem(l,E))}))}},{key:"renderNodes",value:function(r){var i=this,a=this.props,o=a.node,c=a.margin,l=he(c,"top")||0,u=he(c,"left")||0;return b.createElement(F,{className:"recharts-sankey-nodes",key:"recharts-sankey-nodes"},r.map(function(s,f){var p=s.x,d=s.y,v=s.dx,h=s.dy,m=ne(ne({},L(o,!1)),{},{x:p+u,y:d+l,width:v,height:h,index:f,payload:s}),O={onMouseEnter:i.handleMouseEnter.bind(i,m,"node"),onMouseLeave:i.handleMouseLeave.bind(i,m,"node"),onClick:i.handleClick.bind(i,m,"node")};return b.createElement(F,jt({key:"node-".concat(s.x,"-").concat(s.y,"-").concat(s.value)},O),i.constructor.renderNodeItem(o,m))}))}},{key:"renderTooltip",value:function(){var r=this.props,i=r.children,a=r.width,o=r.height,c=r.nameKey,l=oe(i,me);if(!l)return null;var u=this.state,s=u.isTooltipActive,f=u.activeElement,p=u.activeElementType,d={x:0,y:0,width:a,height:o},v=f?fP(f,p):ZA,h=f?pP(f,p,c):[];return b.cloneElement(l,{viewBox:d,active:s,coordinate:v,label:"",payload:h})}},{key:"render",value:function(){if(!Hn(this))return null;var r=this.props,i=r.width,a=r.height,o=r.className,c=r.style,l=r.children,u=ju(r,zA),s=this.state,f=s.links,p=s.nodes,d=L(u,!1);return b.createElement("div",{className:X("recharts-wrapper",o),style:ne(ne({},c),{},{position:"relative",cursor:"default",width:i,height:a}),role:"region"},b.createElement(Zt,jt({},d,{width:i,height:a}),Xu(l),this.renderLinks(f,p),this.renderNodes(p)),this.renderTooltip())}}],[{key:"getDerivedStateFromProps",value:function(r,i){var a=r.data,o=r.width,c=r.height,l=r.margin,u=r.iterations,s=r.nodeWidth,f=r.nodePadding,p=r.sort;if(a!==i.prevData||o!==i.prevWidth||c!==i.prevHeight||!Tt(l,i.prevMargin)||u!==i.prevIterations||s!==i.prevNodeWidth||f!==i.prevNodePadding||p!==i.sort){var d=o-(l&&l.left||0)-(l&&l.right||0),v=c-(l&&l.top||0)-(l&&l.bottom||0),h=sP({data:a,width:d,height:v,iterations:u,nodeWidth:s,nodePadding:f,sort:p}),m=h.links,O=h.nodes;return ne(ne({},i),{},{nodes:O,links:m,prevData:a,prevWidth:u,prevHeight:c,prevMargin:l,prevNodePadding:f,prevNodeWidth:s,prevIterations:u,prevSort:p})}return null}},{key:"renderLinkItem",value:function(r,i){if(b.isValidElement(r))return b.cloneElement(r,i);if(K(r))return r(i);var a=i.sourceX,o=i.sourceY,c=i.sourceControlX,l=i.targetX,u=i.targetY,s=i.targetControlX,f=i.linkWidth,p=ju(i,KA);return b.createElement("path",jt({className:"recharts-sankey-link",d:`
          M`.concat(a,",").concat(o,`
          C`).concat(c,",").concat(o," ").concat(s,",").concat(u," ").concat(l,",").concat(u,`
        `),fill:"none",stroke:"#333",strokeWidth:f,strokeOpacity:"0.2"},L(p,!1)))}},{key:"renderNodeItem",value:function(r,i){return b.isValidElement(r)?b.cloneElement(r,i):K(r)?r(i):b.createElement(En,jt({className:"recharts-sankey-node",fill:"#0088fe",fillOpacity:"0.8"},L(i,!1),{role:"img"}))}}])}(N.PureComponent);ta(Gf,"displayName","Sankey");ta(Gf,"defaultProps",{nameKey:"name",dataKey:"value",nodePadding:10,nodeWidth:10,linkCurvature:.5,iterations:32,margin:{top:5,right:5,bottom:5,left:5},sort:!0});var VP=it({chartName:"RadarChart",GraphicalChild:Hi,axisComponents:[{axisType:"angleAxis",AxisComp:Dr},{axisType:"radiusAxis",AxisComp:kr}],formatAxisMap:Ko,defaultProps:{layout:"centric",startAngle:90,endAngle:-270,cx:"50%",cy:"50%",innerRadius:0,outerRadius:"80%"}}),XP=it({chartName:"ScatterChart",GraphicalChild:In,defaultTooltipEventType:"item",validateTooltipEventTypes:["item"],axisComponents:[{axisType:"xAxis",AxisComp:Mt},{axisType:"yAxis",AxisComp:Nt},{axisType:"zAxis",AxisComp:Tn}],formatAxisMap:$n}),GP=it({chartName:"AreaChart",GraphicalChild:yt,axisComponents:[{axisType:"xAxis",AxisComp:Mt},{axisType:"yAxis",AxisComp:Nt}],formatAxisMap:$n}),HP=it({chartName:"RadialBarChart",GraphicalChild:Ui,legendContent:"children",defaultTooltipEventType:"axis",validateTooltipEventTypes:["axis","item"],axisComponents:[{axisType:"angleAxis",AxisComp:Dr},{axisType:"radiusAxis",AxisComp:kr}],formatAxisMap:Ko,defaultProps:{layout:"radial",startAngle:0,endAngle:360,cx:"50%",cy:"50%",innerRadius:0,outerRadius:"80%"}}),UP=it({chartName:"ComposedChart",GraphicalChild:[_n,yt,Ct,In],axisComponents:[{axisType:"xAxis",AxisComp:Mt},{axisType:"yAxis",AxisComp:Nt},{axisType:"zAxis",AxisComp:Tn}],formatAxisMap:$n});function Io(){return Io=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var n=arguments[e];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(t[r]=n[r])}return t},Io.apply(this,arguments)}function _u(t,e){return hP(t)||vP(t,e)||Hf(t,e)||dP()}function dP(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function vP(t,e){var n=t==null?null:typeof Symbol<"u"&&t[Symbol.iterator]||t["@@iterator"];if(n!=null){var r,i,a,o,c=[],l=!0,u=!1;try{if(a=(n=n.call(t)).next,e!==0)for(;!(l=(r=a.call(n)).done)&&(c.push(r.value),c.length!==e);l=!0);}catch(s){u=!0,i=s}finally{try{if(!l&&n.return!=null&&(o=n.return(),Object(o)!==o))return}finally{if(u)throw i}}return c}}function hP(t){if(Array.isArray(t))return t}function yP(t){return bP(t)||gP(t)||Hf(t)||mP()}function mP(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function Hf(t,e){if(t){if(typeof t=="string")return ko(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);if(n==="Object"&&t.constructor&&(n=t.constructor.name),n==="Map"||n==="Set")return Array.from(t);if(n==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return ko(t,e)}}function gP(t){if(typeof Symbol<"u"&&t[Symbol.iterator]!=null||t["@@iterator"]!=null)return Array.from(t)}function bP(t){if(Array.isArray(t))return ko(t)}function ko(t,e){(e==null||e>t.length)&&(e=t.length);for(var n=0,r=new Array(e);n<e;n++)r[n]=t[n];return r}var OP={fontWeight:"bold",paintOrder:"stroke fill",fontSize:".75rem",stroke:"#FFF",fill:"black",pointerEvents:"none"};function Uf(t){if(!t.children||t.children.length===0)return 1;var e=t.children.map(function(n){return Uf(n)});return 1+Math.max.apply(Math,yP(e))}var YP=function(e){var n=e.className,r=e.data,i=e.children,a=e.width,o=e.height,c=e.padding,l=c===void 0?2:c,u=e.dataKey,s=u===void 0?"value":u,f=e.ringPadding,p=f===void 0?2:f,d=e.innerRadius,v=d===void 0?50:d,h=e.fill,m=h===void 0?"#333":h,O=e.stroke,w=O===void 0?"#FFF":O,x=e.textOptions,A=x===void 0?OP:x,y=e.outerRadius,g=y===void 0?Math.min(a,o)/2:y,P=e.cx,S=P===void 0?a/2:P,j=e.cy,E=j===void 0?o/2:j,$=e.startAngle,_=$===void 0?0:$,k=e.endAngle,D=k===void 0?360:k,T=e.onClick,C=e.onMouseEnter,M=e.onMouseLeave,B=N.useState(!1),W=_u(B,2),V=W[0],H=W[1],z=N.useState(null),Y=_u(z,2),ee=Y[0],ae=Y[1],_e=Oa([0,r[s]],[0,D]),mt=Uf(r),Se=(g-v)/mt,ze=[],gt=new Map([]);function at(le,Oe){C&&C(le,Oe),ae(le),H(!0)}function bt(le,Oe){M&&M(le,Oe),ae(null),H(!1)}function Xe(le){T&&T(le)}function Ge(le,Oe){var ke=Oe.radius,Ke=Oe.innerR,He=Oe.initialAngle,Ue=Oe.childColor,Bt=He;le&&le.forEach(function(je){var Lt,na,kn=_e(je[s]),Mr=Bt,ec=(Lt=(na=je?.fill)!==null&&na!==void 0?na:Ue)!==null&&Lt!==void 0?Lt:m,tc=J(0,0,Ke+ke/2,-(Mr+kn-kn/2)),Zf=tc.x,Jf=tc.y;Bt+=kn,ze.push(b.createElement("g",{"aria-label":je.name,tabIndex:0},b.createElement(Fo,{onClick:function(){return Xe(je)},onMouseEnter:function(ia){return at(je,ia)},onMouseLeave:function(ia){return bt(je,ia)},fill:ec,stroke:w,strokeWidth:l,startAngle:Mr,endAngle:Mr+kn,innerRadius:Ke,outerRadius:Ke+ke,cx:S,cy:E}),b.createElement(dt,Io({},A,{alignmentBaseline:"middle",textAnchor:"middle",x:Zf+S,y:E-Jf}),je[s])));var rc=J(S,E,Ke+ke/2,Mr),Qf=rc.x,ep=rc.y;return gt.set(je.name,{x:Qf,y:ep}),Ge(je.children,{radius:ke,innerR:Ke+ke+p,initialAngle:Mr,childColor:ec})})}Ge(r.children,{radius:Se,innerR:v,initialAngle:_});var Ot=X("recharts-sunburst",n);function xt(){var le=oe([i],me);if(!le||!ee)return null;var Oe={x:0,y:0,width:a,height:o};return b.cloneElement(le,{viewBox:Oe,coordinate:gt.get(ee.name),payload:[ee],active:V})}return b.createElement("div",{className:X("recharts-wrapper",n),style:{position:"relative",width:a,height:o},role:"region"},b.createElement(Zt,{width:a,height:o},i,b.createElement(F,{className:Ot},ze)),xt())};function Sn(t){"@babel/helpers - typeof";return Sn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Sn(t)}function Do(){return Do=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var n=arguments[e];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(t[r]=n[r])}return t},Do.apply(this,arguments)}function Tu(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter(function(i){return Object.getOwnPropertyDescriptor(t,i).enumerable})),n.push.apply(n,r)}return n}function ba(t){for(var e=1;e<arguments.length;e++){var n=arguments[e]!=null?arguments[e]:{};e%2?Tu(Object(n),!0).forEach(function(r){xP(t,r,n[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):Tu(Object(n)).forEach(function(r){Object.defineProperty(t,r,Object.getOwnPropertyDescriptor(n,r))})}return t}function xP(t,e,n){return e=wP(e),e in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function wP(t){var e=AP(t,"string");return Sn(e)=="symbol"?e:e+""}function AP(t,e){if(Sn(t)!="object"||!t)return t;var n=t[Symbol.toPrimitive];if(n!==void 0){var r=n.call(t,e);if(Sn(r)!="object")return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(t)}function PP(t,e){var n="".concat(e.x||t.x),r=parseInt(n,10),i="".concat(e.y||t.y),a=parseInt(i,10),o="".concat(e?.height||t?.height),c=parseInt(o,10);return ba(ba(ba({},e),Rs(t)),{},{height:c,x:r,y:a})}function SP(t){return b.createElement(or,Do({shapeType:"trapezoid",propTransformer:PP},t))}var Co;function Iu(t,e){return _P(t)||$P(t,e)||EP(t,e)||jP()}function jP(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function EP(t,e){if(t){if(typeof t=="string")return ku(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);if(n==="Object"&&t.constructor&&(n=t.constructor.name),n==="Map"||n==="Set")return Array.from(t);if(n==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return ku(t,e)}}function ku(t,e){(e==null||e>t.length)&&(e=t.length);for(var n=0,r=new Array(e);n<e;n++)r[n]=t[n];return r}function $P(t,e){var n=t==null?null:typeof Symbol<"u"&&t[Symbol.iterator]||t["@@iterator"];if(n!=null){var r,i,a,o,c=[],l=!0,u=!1;try{if(a=(n=n.call(t)).next,e===0){if(Object(n)!==n)return;l=!1}else for(;!(l=(r=a.call(n)).done)&&(c.push(r.value),c.length!==e);l=!0);}catch(s){u=!0,i=s}finally{try{if(!l&&n.return!=null&&(o=n.return(),Object(o)!==o))return}finally{if(u)throw i}}return c}}function _P(t){if(Array.isArray(t))return t}function Er(t){"@babel/helpers - typeof";return Er=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Er(t)}function Mi(){return Mi=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var n=arguments[e];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(t[r]=n[r])}return t},Mi.apply(this,arguments)}function Du(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter(function(i){return Object.getOwnPropertyDescriptor(t,i).enumerable})),n.push.apply(n,r)}return n}function fe(t){for(var e=1;e<arguments.length;e++){var n=arguments[e]!=null?arguments[e]:{};e%2?Du(Object(n),!0).forEach(function(r){tt(t,r,n[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):Du(Object(n)).forEach(function(r){Object.defineProperty(t,r,Object.getOwnPropertyDescriptor(n,r))})}return t}function TP(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function Cu(t,e){for(var n=0;n<e.length;n++){var r=e[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,qf(r.key),r)}}function IP(t,e,n){return e&&Cu(t.prototype,e),n&&Cu(t,n),Object.defineProperty(t,"prototype",{writable:!1}),t}function kP(t,e,n){return e=Ni(e),DP(t,Yf()?Reflect.construct(e,n||[],Ni(t).constructor):e.apply(t,n))}function DP(t,e){if(e&&(Er(e)==="object"||typeof e=="function"))return e;if(e!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return CP(t)}function CP(t){if(t===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}function Yf(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(Yf=function(){return!!t})()}function Ni(t){return Ni=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(n){return n.__proto__||Object.getPrototypeOf(n)},Ni(t)}function MP(t,e){if(typeof e!="function"&&e!==null)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&Mo(t,e)}function Mo(t,e){return Mo=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(r,i){return r.__proto__=i,r},Mo(t,e)}function tt(t,e,n){return e=qf(e),e in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function qf(t){var e=NP(t,"string");return Er(e)=="symbol"?e:e+""}function NP(t,e){if(Er(t)!="object"||!t)return t;var n=t[Symbol.toPrimitive];if(n!==void 0){var r=n.call(t,e);if(Er(r)!="object")return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}var Rt=function(t){function e(){var n;TP(this,e);for(var r=arguments.length,i=new Array(r),a=0;a<r;a++)i[a]=arguments[a];return n=kP(this,e,[].concat(i)),tt(n,"state",{isAnimationFinished:!1}),tt(n,"handleAnimationEnd",function(){var o=n.props.onAnimationEnd;n.setState({isAnimationFinished:!0}),K(o)&&o()}),tt(n,"handleAnimationStart",function(){var o=n.props.onAnimationStart;n.setState({isAnimationFinished:!1}),K(o)&&o()}),n}return MP(e,t),IP(e,[{key:"isActiveIndex",value:function(r){var i=this.props.activeIndex;return Array.isArray(i)?i.indexOf(r)!==-1:r===i}},{key:"renderTrapezoidsStatically",value:function(r){var i=this,a=this.props,o=a.shape,c=a.activeShape;return r.map(function(l,u){var s=i.isActiveIndex(u)?c:o,f=fe(fe({},l),{},{isActive:i.isActiveIndex(u),stroke:l.stroke});return b.createElement(F,Mi({className:"recharts-funnel-trapezoid"},We(i.props,l,u),{key:"trapezoid-".concat(l?.x,"-").concat(l?.y,"-").concat(l?.name,"-").concat(l?.value),role:"img"}),b.createElement(SP,Mi({option:s},f)))})}},{key:"renderTrapezoidsWithAnimation",value:function(){var r=this,i=this.props,a=i.trapezoids,o=i.isAnimationActive,c=i.animationBegin,l=i.animationDuration,u=i.animationEasing,s=i.animationId,f=this.state.prevTrapezoids;return b.createElement($e,{begin:c,duration:l,isActive:o,easing:u,from:{t:0},to:{t:1},key:"funnel-".concat(s),onAnimationStart:this.handleAnimationStart,onAnimationEnd:this.handleAnimationEnd},function(p){var d=p.t,v=a.map(function(h,m){var O=f&&f[m];if(O){var w=q(O.x,h.x),x=q(O.y,h.y),A=q(O.upperWidth,h.upperWidth),y=q(O.lowerWidth,h.lowerWidth),g=q(O.height,h.height);return fe(fe({},h),{},{x:w(d),y:x(d),upperWidth:A(d),lowerWidth:y(d),height:g(d)})}var P=q(h.x+h.upperWidth/2,h.x),S=q(h.y+h.height/2,h.y),j=q(0,h.upperWidth),E=q(0,h.lowerWidth),$=q(0,h.height);return fe(fe({},h),{},{x:P(d),y:S(d),upperWidth:j(d),lowerWidth:E(d),height:$(d)})});return b.createElement(F,null,r.renderTrapezoidsStatically(v))})}},{key:"renderTrapezoids",value:function(){var r=this.props,i=r.trapezoids,a=r.isAnimationActive,o=this.state.prevTrapezoids;return a&&i&&i.length&&(!o||!Le(o,i))?this.renderTrapezoidsWithAnimation():this.renderTrapezoidsStatically(i)}},{key:"render",value:function(){var r=this.props,i=r.hide,a=r.trapezoids,o=r.className,c=r.isAnimationActive,l=this.state.isAnimationFinished;if(i||!a||!a.length)return null;var u=X("recharts-trapezoids",o);return b.createElement(F,{className:u},this.renderTrapezoids(),(!c||l)&&Ee.renderCallByParent(this.props,a))}}],[{key:"getDerivedStateFromProps",value:function(r,i){return r.animationId!==i.prevAnimationId?{prevAnimationId:r.animationId,curTrapezoids:r.trapezoids,prevTrapezoids:i.curTrapezoids}:r.trapezoids!==i.curTrapezoids?{curTrapezoids:r.trapezoids}:null}}])}(N.PureComponent);Co=Rt;tt(Rt,"displayName","Funnel");tt(Rt,"defaultProps",{stroke:"#fff",fill:"#808080",legendType:"rect",labelLine:!0,hide:!1,isAnimationActive:!be.isSsr,animationBegin:400,animationDuration:1500,animationEasing:"ease",nameKey:"name",lastShapeType:"triangle"});tt(Rt,"getRealFunnelData",function(t){var e=t.props,n=e.data,r=e.children,i=L(t.props,!1),a=ge(r,_r);return n&&n.length?n.map(function(o,c){return fe(fe(fe({payload:o},i),o),a&&a[c]&&a[c].props)}):a&&a.length?a.map(function(o){return fe(fe({},i),o.props)}):[]});tt(Rt,"getRealWidthHeight",function(t,e){var n=t.props.width,r=e.width,i=e.height,a=e.left,o=e.right,c=e.top,l=e.bottom,u=i,s=r;return Mu(n)?s=n:pt(n)&&(s=s*parseFloat(n)/100),{realWidth:s-a-o-50,realHeight:u-l-c,offsetX:(r-s)/2,offsetY:(i-u)/2}});tt(Rt,"getComposedData",function(t){var e=t.item,n=t.offset,r=Co.getRealFunnelData(e),i=e.props,a=i.dataKey,o=i.nameKey,c=i.tooltipType,l=i.lastShapeType,u=i.reversed,s=n.left,f=n.top,p=Co.getRealWidthHeight(e,n),d=p.realHeight,v=p.realWidth,h=p.offsetX,m=p.offsetY,O=Math.max.apply(null,r.map(function(g){return Z(g,a,0)})),w=r.length,x=d/w,A={x:n.left,y:n.top,width:n.width,height:n.height},y=r.map(function(g,P){var S=Z(g,a,0),j=Z(g,o,P),E=S,$;if(P!==w-1){if($=Z(r[P+1],a,0),$ instanceof Array){var _=$,k=Iu(_,1);$=k[0]}}else if(S instanceof Array&&S.length===2){var D=Iu(S,2);E=D[0],$=D[1]}else l==="rectangle"?$=E:$=0;var T=(O-E)*v/(2*O)+f+25+h,C=x*P+s+m,M=E/O*v,B=$/O*v,W=[{name:j,value:E,payload:g,dataKey:a,type:c}],V={x:T+M/2,y:C+x/2};return fe(fe({x:T,y:C,width:Math.max(M,B),upperWidth:M,lowerWidth:B,height:x,name:j,val:E,tooltipPayload:W,tooltipPosition:V},Ku(g,"width")),{},{payload:g,parentViewBox:A,labelViewBox:{x:T+(M-B)/4,y:C,width:Math.abs(M-B)/2+Math.min(M,B),height:x}})});return u&&(y=y.map(function(g,P){var S=g.y-P*x+(w-1-P)*x;return fe(fe({},g),{},{upperWidth:g.lowerWidth,lowerWidth:g.upperWidth,x:g.x-(g.lowerWidth-g.upperWidth)/2,y:g.y-P*x+(w-1-P)*x,tooltipPosition:fe(fe({},g.tooltipPosition),{},{y:S+x/2}),labelViewBox:fe(fe({},g.labelViewBox),{},{y:S})})})),{trapezoids:y,data:r}});var qP=it({chartName:"FunnelChart",GraphicalChild:Rt,validateTooltipEventTypes:["item"],defaultTooltipEventType:"item",axisComponents:[],defaultProps:{layout:"centric"}});export{yt as Area,GP as AreaChart,Ct as Bar,KP as BarChart,fr as Brush,Cr as CartesianAxis,jx as CartesianGrid,_r as Cell,UP as ComposedChart,Gm as Cross,ft as Curve,cm as Customized,Bo as DefaultLegendContent,Gd as DefaultTooltipContent,Ir as Dot,Tr as ErrorBar,Rt as Funnel,qP as FunnelChart,be as Global,ve as Label,Ee as LabelList,F as Layer,Ut as Legend,_n as Line,zP as LineChart,ht as Pie,FP as PieChart,Dr as PolarAngleAxis,ig as PolarGrid,kr as PolarRadiusAxis,Vo as Polygon,Hi as Radar,VP as RadarChart,Ui as RadialBar,HP as RadialBarChart,En as Rectangle,Qi as ReferenceArea,Zi as ReferenceDot,Zo as ReferenceLine,WP as ResponsiveContainer,Gf as Sankey,In as Scatter,XP as ScatterChart,Fo as Sector,YP as SunburstChart,Zt as Surface,Li as Symbols,dt as Text,me as Tooltip,kg as Trapezoid,Ff as Treemap,Mt as XAxis,Nt as YAxis,Tn as ZAxis};
