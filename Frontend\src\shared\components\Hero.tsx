import { Link } from "react-router-dom";
import { ArrowRight, Play, ChevronDown } from "lucide-react";

import { useAutoScroll } from "@/shared/hooks/use-auto-scroll";
import { useLanguage } from "@/core/providers/LanguageContext";
import { cn } from "@/core/utils/utils";
import { useEffect, useRef, useState } from "react";
import { getServices } from "@/shared/services/service.service";
import { Service } from "@/core/types";
import { useAuth } from "@/core/providers/AuthContext";
import ParticleSystem from "@/shared/components/ParticleSystem";
import FloatingElements from "@/shared/components/FloatingElements";
import AnimatedBackground from "@/shared/components/AnimatedBackground";
import MorphingBlob from "@/shared/components/MorphingBlob";
import TextReveal, { GradientTextReveal } from "@/shared/components/TextReveal";
import { MagneticElement } from "@/shared/components/MagneticCursor";
import { motion } from "framer-motion";

const Hero = () => {
  const { scrollToSection } = useAutoScroll();
  const { t, isRTL } = useLanguage();
  const { user, isAuthenticated, logout } = useAuth();

  // Service dropdown state
  const [services, setServices] = useState<Service[]>([]);
  const [dropdownOpen, setDropdownOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    // Fetch services only once
    getServices({ limit: 100 }).then(res => setServices(res.data)).catch(() => setServices([]));
  }, []);

  // Close dropdown on outside click
  useEffect(() => {
    function handleClick(e: MouseEvent) {
      if (dropdownRef.current && !dropdownRef.current.contains(e.target as Node)) {
        setDropdownOpen(false);
      }
    }
    if (dropdownOpen) {
      document.addEventListener("mousedown", handleClick);
    } else {
      document.removeEventListener("mousedown", handleClick);
    }
    return () => document.removeEventListener("mousedown", handleClick);
  }, [dropdownOpen]);

  return (
    <section className="relative min-h-screen flex items-center justify-center overflow-hidden">


      {/* Content */}
      <div className="relative z-10 container-custom text-center">
        <div className="max-w-4xl mx-auto">
          {/* Badge */}
          <div className="inline-flex items-center px-4 py-2 bg-brand-grey-900/50 border border-brand-grey-700 rounded-full mb-8 animate-fade-in">
            <span className="text-brand-grey-300 text-sm font-medium">
              {t("hero.badge")}
            </span>
          </div>

          {/* Main Headline */}
          <h1
            className={cn(
              "text-3xl sm:text-4xl md:text-6xl lg:text-7xl font-bold text-brand-white mb-4 sm:mb-6 leading-tight animate-slide-in-left px-2 sm:px-0",
              isRTL && "text-right",
            )}
            style={{ animationDelay: "0.2s" }}
          >
            {t("hero.title")}
            <br />
            <span className="gradient-text">{t("hero.titleHighlight")}</span>
          </h1>

          {/* Subtitle */}
          <p
            className={cn(
              "text-lg sm:text-xl md:text-2xl text-brand-grey-300 mb-8 sm:mb-10 max-w-3xl mx-auto leading-relaxed animate-slide-in-right px-4 sm:px-0",
              isRTL && "text-right",
            )}
            style={{ animationDelay: "0.4s" }}
          >
            {t("hero.subtitle")}
          </p>

          {/* CTA Buttons + Services Dropdown */}
          <div
            className={cn(
              "flex flex-col sm:flex-row gap-4 justify-center items-center animate-fade-in relative",
              isRTL && "sm:flex-row-reverse",
            )}
            style={{ animationDelay: "0.6s" }}
          >
            {/* Services Dropdown Trigger */}
            <div className="relative" ref={dropdownRef}>
              <button
                className={cn(
                  "btn-secondary group text-base sm:text-lg px-6 sm:px-8 py-3 sm:py-4 flex items-center hover:shadow-xl hover:shadow-brand-grey-500/20 transition-all duration-300 w-full sm:w-auto justify-center",
                  isRTL && "flex-row-reverse",
                )}
                onMouseEnter={() => setDropdownOpen(true)}
                onFocus={() => setDropdownOpen(true)}
                onMouseLeave={() => setDropdownOpen(false)}
                aria-haspopup="listbox"
                aria-expanded={dropdownOpen}
                tabIndex={0}
              >
                {t("hero.servicesDropdown") || "Our Services"}
                <ChevronDown className={cn("ml-2 h-4 w-4 sm:h-5 sm:w-5 transition-transform", dropdownOpen && "rotate-180")}/>
              </button>
              {/* Dropdown Menu */}
              {dropdownOpen && (
                <div
                  className="absolute left-0 right-0 mt-2 bg-white border border-brand-grey-200 rounded-xl shadow-2xl z-50 min-w-[220px] text-left animate-fade-in"
                  onMouseEnter={() => setDropdownOpen(true)}
                  onMouseLeave={() => setDropdownOpen(false)}
                  role="listbox"
                >
                  {services.length === 0 ? (
                    <div className="px-4 py-3 text-brand-grey-400">No services found.</div>
                  ) : (
                    services.map((service) => (
                      <Link
                        key={service.id}
                        to={`/services/${service.id}`}
                        className="block px-5 py-3 text-brand-black hover:bg-brand-grey-100 transition-colors rounded-xl"
                        tabIndex={0}
                        role="option"
                      >
                        {service.title}
                      </Link>
                    ))
                  )}
                </div>
              )}
            </div>

            {/* Contact Button */}
            <Link
              to="/contact"
              className={cn(
                "btn-primary group text-base sm:text-lg px-6 sm:px-8 py-3 sm:py-4 hover:shadow-xl hover:shadow-brand-white/20 transition-all duration-300 flex items-center w-full sm:w-auto justify-center",
                isRTL && "flex-row-reverse",
              )}
            >
              {t("hero.ctaPrimary")}
              <ArrowRight
                className={cn(
                  "h-4 w-4 sm:h-5 sm:w-5 group-hover:transition-transform",
                  isRTL
                    ? "mr-2 group-hover:-translate-x-1 rotate-180"
                    : "ml-2 group-hover:translate-x-1",
                )}
              />
            </Link>

            <button
              onClick={() => scrollToSection("about-section")}
              className={cn(
                "btn-secondary group text-base sm:text-lg px-6 sm:px-8 py-3 sm:py-4 hover:shadow-xl hover:shadow-brand-grey-500/20 transition-all duration-300 flex items-center w-full sm:w-auto justify-center",
                isRTL && "flex-row-reverse",
              )}
            >
              <Play
                className={cn(
                  "h-4 w-4 sm:h-5 sm:w-5 group-hover:scale-110 transition-transform",
                  isRTL ? "ml-2" : "mr-2",
                )}
              />
              {t("hero.ctaSecondary")}
            </button>
          </div>

          {/* Stats */}
          <div
            className="mt-12 sm:mt-16 grid grid-cols-1 sm:grid-cols-3 gap-6 sm:gap-8 max-w-2xl mx-auto animate-fade-in px-4 sm:px-0"
            style={{ animationDelay: "0.8s" }}
          >
          
          </div>
        </div>
      </div>

      {/* Scroll Indicator */}
      <button
        onClick={() => scrollToSection("about-section")}
        className="absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce hover:scale-110 transition-transform duration-300 group cursor-pointer"
        aria-label="Scroll to about section"
      >
        <div className="w-6 h-10 border-2 border-brand-grey-500 group-hover:border-brand-grey-300 rounded-full flex justify-center transition-colors">
          <div className="w-1 h-3 bg-brand-grey-500 group-hover:bg-brand-grey-300 rounded-full mt-2 transition-colors"></div>
        </div>
      </button>
    </section>
  );
};

export default Hero;
