import { memo, useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { ArrowRight, Star, Zap } from 'lucide-react';
import { Link } from 'react-router-dom';

interface EnhancedServiceCardProps {
  title: string;
  description: string;
  image?: string;
  href: string;
  features?: string[];
  price?: string;
  popular?: boolean;
  delay?: number;
}

const EnhancedServiceCard = memo(({
  title,
  description,
  image,
  href,
  features = [],
  price,
  popular = false,
  delay = 0
}: EnhancedServiceCardProps) => {
  const [isHovered, setIsHovered] = useState(false);

  return (
    <motion.div
      initial={{ opacity: 0, y: 50 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ 
        duration: 0.6, 
        delay: delay * 0.1,
        ease: "easeOut"
      }}
      className="relative group"
    >
      <Link to={href}>
        <div
          className="relative h-96 rounded-2xl overflow-hidden cursor-pointer"
          onMouseEnter={() => setIsHovered(true)}
          onMouseLeave={() => setIsHovered(false)}
        >
          {/* Background Image */}
          <div className="absolute inset-0">
            {image ? (
              <img
                src={image}
                alt={title}
                className="w-full h-full object-cover transition-transform duration-700 group-hover:scale-110"
              />
            ) : (
              <div className="w-full h-full bg-gradient-to-br from-brand-purple-600 to-brand-purple-800" />
            )}
          </div>

          {/* Gradient Overlays */}
          <div className="absolute inset-0 bg-gradient-to-t from-brand-black via-brand-black/60 to-transparent" />
          <div className="absolute inset-0 bg-gradient-to-br from-brand-purple-900/40 via-transparent to-brand-purple-600/30 opacity-80" />

          {/* Popular Badge */}
          {popular && (
            <motion.div
              initial={{ scale: 0, rotate: -10 }}
              animate={{ scale: 1, rotate: 0 }}
              transition={{ delay: delay * 0.1 + 0.3 }}
              className="absolute top-4 right-4 z-20"
            >
              <div className="bg-gradient-to-r from-yellow-400 to-orange-500 text-black px-3 py-1 rounded-full text-xs font-bold flex items-center gap-1">
                <Star className="w-3 h-3" />
                Popular
              </div>
            </motion.div>
          )}

          {/* Content - Always Visible */}
          <div className="absolute bottom-0 left-0 right-0 p-6 z-10">
            <motion.div
              initial={{ opacity: 1 }}
              animate={{ opacity: isHovered ? 0 : 1 }}
              transition={{ duration: 0.3 }}
            >
              <h3 className="text-2xl font-bold text-white mb-2 group-hover:text-brand-purple-200 transition-colors duration-300">
                {title}
              </h3>
              <p className="text-brand-grey-300 text-sm line-clamp-2">
                {description}
              </p>
            </motion.div>
          </div>

          {/* Hover Content - Revealed on Hover */}
          <AnimatePresence>
            {isHovered && (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: 20 }}
                transition={{ duration: 0.3 }}
                className="absolute inset-0 p-6 z-20 flex flex-col justify-between"
              >
                {/* Enhanced Background */}
                <div className="absolute inset-0 bg-gradient-to-br from-brand-purple-900/95 via-brand-purple-800/90 to-brand-black/95 backdrop-blur-sm" />
                
                <div className="relative z-10">
                  {/* Header */}
                  <div className="flex items-start justify-between mb-4">
                    <div>
                      <h3 className="text-2xl font-bold text-white mb-2">
                        {title}
                      </h3>
                      {price && (
                        <div className="text-brand-purple-200 font-semibold">
                          {price}
                        </div>
                      )}
                    </div>
                    <div className="bg-brand-purple-600 p-2 rounded-lg">
                      <Zap className="w-5 h-5 text-white" />
                    </div>
                  </div>

                  {/* Description */}
                  <p className="text-brand-grey-200 mb-6 leading-relaxed">
                    {description}
                  </p>

                  {/* Features */}
                  {features.length > 0 && (
                    <div className="mb-6">
                      <h4 className="text-white font-semibold mb-3">Key Features:</h4>
                      <ul className="space-y-2">
                        {features.slice(0, 3).map((feature, index) => (
                          <motion.li
                            key={index}
                            initial={{ opacity: 0, x: -10 }}
                            animate={{ opacity: 1, x: 0 }}
                            transition={{ delay: index * 0.1 }}
                            className="flex items-center text-brand-purple-200 text-sm"
                          >
                            <div className="w-1.5 h-1.5 bg-brand-purple-400 rounded-full mr-3" />
                            {feature}
                          </motion.li>
                        ))}
                      </ul>
                    </div>
                  )}
                </div>

                {/* CTA Button */}
                <motion.div
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.2 }}
                  className="relative z-10"
                >
                  <div className="bg-gradient-to-r from-brand-purple-600 to-brand-purple-700 hover:from-brand-purple-700 hover:to-brand-purple-800 text-white px-6 py-3 rounded-lg font-semibold flex items-center justify-between group/btn transition-all duration-300 shadow-lg hover:shadow-xl">
                    <span>Learn More</span>
                    <ArrowRight className="w-5 h-5 group-hover/btn:translate-x-1 transition-transform duration-300" />
                  </div>
                </motion.div>
              </motion.div>
            )}
          </AnimatePresence>

          {/* Glow Effect */}
          <div className="absolute inset-0 rounded-2xl ring-2 ring-brand-purple-500/0 group-hover:ring-brand-purple-500/50 transition-all duration-500" />
          
          {/* Shimmer Effect */}
          <div className="absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity duration-500">
            <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent shimmer-effect" />
          </div>
        </div>
      </Link>
    </motion.div>
  );
});

EnhancedServiceCard.displayName = 'EnhancedServiceCard';

export default EnhancedServiceCard;
