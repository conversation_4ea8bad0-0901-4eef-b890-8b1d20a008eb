import jwt from 'jsonwebtoken';
import asyncHandler from 'express-async-handler';
import User from '../../components/user/userModel.js';
import Admin from '../../components/auth/authModel.js';
import { JWT_SECRET, ROLES } from '../config/constants.js';

// Protect routes
const protect = asyncHandler(async (req, res, next) => {
  let token;

  // Check for token in Authorization header
  if (req.headers.authorization && req.headers.authorization.startsWith('Bearer')) {
    // Get token from header
    token = req.headers.authorization.split(' ')[1];
  }
  // Check for token in cookies
  else if (req.cookies && req.cookies.token) {
    token = req.cookies.token;
  }

  // Debug logging for authentication issues
  if (!token) {
    console.log('[AUTH DEBUG] No token found:', {
      hasAuthHeader: !!req.headers.authorization,
      authHeader: req.headers.authorization?.substring(0, 20) + '...',
      hasCookies: !!req.cookies,
      cookieKeys: req.cookies ? Object.keys(req.cookies) : [],
      hasTokenCookie: !!(req.cookies && req.cookies.token),
      userAgent: req.headers['user-agent']?.substring(0, 50) + '...'
    });
    return res.status(401).json({
      success: false,
      message: 'Not authorized, no token',
      debug: process.env.NODE_ENV === 'development' ? {
        hasAuthHeader: !!req.headers.authorization,
        hasCookies: !!req.cookies,
        cookieKeys: req.cookies ? Object.keys(req.cookies) : []
      } : undefined
    });
  }

  try {
    // Verify token
    const decoded = jwt.verify(token, process.env.JWT_SECRET);

    // Get user from both collections
    let user = await User.findById(decoded.id).select('-password');

    if (!user) {
      user = await Admin.findById(decoded.id).select('-password');
    }

    if (!user) {
      return res.status(401).json({ success: false, message: 'Not authorized, user not found' });
    }

    req.user = user;

    next();
  } catch (error) {
    console.error('Authentication error:', error);
    return res.status(401).json({ success: false, message: 'Not authorized, invalid token' });
  }
});

// Admin middleware
const admin = (req, res, next) => {
  if (req.user && req.user.role === ROLES.ADMIN) {
    next();
  } else {
    res.status(403);
    throw new Error('Not authorized as an admin');
  }
};

// Marketing middleware
const marketing = (req, res, next) => {
  if (req.user && req.user.role === ROLES.MARKETING) {
    next();
  } else {
    res.status(403);
    throw new Error('Not authorized as marketing personnel');
  }
};

// Admin or Marketing middleware
const adminOrMarketing = (req, res, next) => {
  if (req.user && (req.user.role === ROLES.ADMIN || req.user.role === ROLES.MARKETING)) {
    next();
  } else {
    res.status(403);
    throw new Error('Not authorized to access this resource');
  }
};

// Role-based access control
const authorize = (...roles) => {
  return (req, res, next) => {
    if (!req.user || !roles.includes(req.user.role)) {
      res.status(403);
      throw new Error(`User role ${req.user?.role} is not authorized to access this route`);
    }
    next();
  };
};

// Optional auth - sets req.user if authenticated, but doesn't require auth
const optionalAuth = asyncHandler(async (req, res, next) => {
  let token;

  // Check for token in Authorization header
  if (req.headers.authorization && req.headers.authorization.startsWith('Bearer')) {
    token = req.headers.authorization.split(' ')[1];
  }
  // Check for token in cookies
  else if (req.cookies && req.cookies.token) {
    token = req.cookies.token;
  }

  // If no token, continue without setting user
  if (!token) {
    req.user = null;
    return next();
  }

  try {
    // Verify token
    const decoded = jwt.verify(token, process.env.JWT_SECRET);

    // Get user from both collections
    let user = await User.findById(decoded.id).select('-password');
    if (!user) {
      user = await Admin.findById(decoded.id).select('-password');
    }

    if (user) {
      req.user = user;
    } else {
      req.user = null;
    }
  } catch (error) {
    // If token is invalid, continue without setting user
    req.user = null;
  }

  next();
});

export { protect, admin, marketing, adminOrMarketing, authorize, optionalAuth };
