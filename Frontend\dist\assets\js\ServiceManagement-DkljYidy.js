import{r as t,o as f,j as e,bp as g,bq as N,br as y}from"./react-vendor-Dq0qSR31.js";import{L as v,M as w}from"./index-CasGuY6o.js";import"./vendor-OXu-rwpf.js";import"./utils-vendor-DSNVchvY.js";import"./state-vendor-DU4y5LsH.js";const S=()=>e.jsxs("div",{className:"animate-pulse bg-brand-grey-900 rounded-xl p-4 flex flex-col gap-4 shadow-md min-h-[160px]",children:[e.jsx("div",{className:"h-6 bg-brand-grey-800 rounded w-2/3 mb-2"}),e.jsx("div",{className:"h-4 bg-brand-grey-800 rounded w-1/2 mb-2"}),e.jsx("div",{className:"h-3 bg-brand-grey-800 rounded w-1/3"}),e.jsxs("div",{className:"flex gap-2 mt-4",children:[e.jsx("div",{className:"h-8 w-8 bg-brand-grey-800 rounded-full"}),e.jsx("div",{className:"h-8 w-8 bg-brand-grey-800 rounded-full"})]})]}),C=()=>{const[l,b]=t.useState([]),[h,d]=t.useState(!1),[i,r]=t.useState(null),[n,c]=t.useState(null),o=f();t.useEffect(()=>{m()},[]);const m=async()=>{d(!0),r(null);try{const s=await v();b(s.data)}catch{r("Failed to fetch services")}finally{d(!1)}},x=()=>{o("/dashboard/services/new")},p=s=>{o(`/dashboard/services/${s.id}/edit`)},u=async s=>{if(window.confirm("Are you sure you want to delete this service?")){c(s);try{await w(s),m()}catch{r("Failed to delete service")}finally{c(null)}}};return e.jsxs("div",{className:"p-6 max-w-7xl mx-auto",children:[e.jsxs("div",{className:"flex flex-col sm:flex-row justify-between items-center mb-8 gap-4",children:[e.jsx("h2",{className:"text-3xl font-bold text-brand-white tracking-tight",children:"Service Management"}),e.jsxs("button",{className:"flex items-center gap-2 px-4 py-2 rounded-lg bg-brand-primary hover:bg-brand-primary-dark text-white font-semibold shadow transition",onClick:x,children:[e.jsx(g,{className:"text-lg"})," Add Service"]})]}),i&&e.jsx("div",{className:"text-red-500 mb-4",children:i}),h?e.jsx("div",{className:"grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-6",children:[...Array(6)].map((s,a)=>e.jsx(S,{},a))}):l.length===0?e.jsxs("div",{className:"flex flex-col items-center justify-center py-20 text-center text-brand-grey-400",children:[e.jsx("img",{src:"/placeholder.svg",alt:"No services",className:"w-32 mb-6 opacity-60"}),e.jsx("h3",{className:"text-xl font-semibold mb-2",children:"No services found"}),e.jsx("p",{className:"mb-4",children:"Start by adding your first service!"}),e.jsxs("button",{className:"flex items-center gap-2 px-4 py-2 rounded-lg bg-brand-primary hover:bg-brand-primary-dark text-white font-semibold shadow transition",onClick:x,children:[e.jsx(g,{className:"text-lg"})," Add Service"]})]}):e.jsx("div",{className:"grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-6",children:l.map(s=>e.jsxs("div",{className:"bg-brand-grey-900 rounded-xl shadow-md p-5 flex flex-col gap-3 hover:shadow-lg transition group border border-brand-grey-800",children:[e.jsx("div",{className:"flex items-center gap-2 mb-2",children:s.image&&e.jsx("img",{src:s.image,alt:s.title,className:"w-12 h-12 object-cover rounded"})}),e.jsx("h3",{className:"text-lg font-bold text-brand-white group-hover:text-brand-primary transition line-clamp-2",children:s.title}),e.jsx("p",{className:"text-brand-grey-300 text-sm line-clamp-3 mb-2",children:s.description}),s.subServices&&s.subServices.length>0&&e.jsxs("details",{className:"mb-2",children:[e.jsxs("summary",{className:"cursor-pointer text-brand-primary font-semibold mb-2",children:[s.subServices.length," Sub-Service",s.subServices.length>1?"s":""]}),e.jsx("div",{className:"flex flex-col gap-3 mt-2",children:s.subServices.map((a,j)=>e.jsxs("div",{className:"bg-brand-grey-800 border border-brand-grey-700 rounded-lg p-3 flex items-center gap-4",children:[a.image&&e.jsx("img",{src:a.image,alt:a.title,className:"w-10 h-10 object-cover rounded border border-brand-grey-700"}),e.jsxs("div",{className:"flex-1",children:[e.jsx("div",{className:"font-semibold text-brand-white",children:a.title}),e.jsx("div",{className:"text-brand-grey-300 text-xs",children:a.description})]})]},j))})]}),e.jsxs("div",{className:"flex items-center justify-between mt-auto pt-2",children:[e.jsx("span",{className:"text-xs text-brand-grey-400",children:s.createdAt?new Date(s.createdAt).toLocaleDateString():""}),e.jsxs("div",{className:"flex gap-2",children:[e.jsx("button",{className:"p-2 rounded-full hover:bg-brand-grey-800 transition",title:"Edit",onClick:()=>p(s),children:e.jsx(N,{className:"text-brand-primary text-lg"})}),e.jsx("button",{className:`p-2 rounded-full hover:bg-red-600/20 transition ${n===s.id?"opacity-50 pointer-events-none":""}`,title:"Delete",onClick:()=>u(s.id),disabled:n===s.id,children:e.jsx(y,{className:"text-red-500 text-lg"})})]})]})]},s.id))})]})};export{C as default};
