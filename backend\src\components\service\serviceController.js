import asyncHandler from 'express-async-handler';
import Service from './serviceModel.js';
import { StatusCodes } from 'http-status-codes';
import path from 'path';
import ErrorResponse from '../../shared/utils/errorResponse.js';
import mongoose from 'mongoose';

// @desc    Get all services
// @route   GET /api/services
// @access  Public
const getServices = asyncHandler(async (req, res) => {
  // Copy req.query
  const reqQuery = { ...req.query };

  // Fields to exclude
  const removeFields = ['select', 'sort', 'page', 'limit'];

  // Loop over removeFields and delete them from reqQuery
  removeFields.forEach(param => delete reqQuery[param]);

  // Create query string
  let queryStr = JSON.stringify(reqQuery);

  // Create operators ($gt, $gte, etc)
  queryStr = queryStr.replace(/\b(gt|gte|lt|lte|in)\b/g, match => `$${match}`);

  // Finding resource
  let query = Service.find(JSON.parse(queryStr));

  // Select Fields
  if (req.query.select) {
    const fields = req.query.select.split(',').join(' ');
    query = query.select(fields);
  }

  // Sort
  if (req.query.sort) {
    const sortBy = req.query.sort.split(',').join(' ');
    query = query.sort(sortBy);
  } else {
    query = query.sort('-createdAt');
  }

  // Pagination
  const page = parseInt(req.query.page, 10) || 1;
  const limit = parseInt(req.query.limit, 10) || 10;
  const startIndex = (page - 1) * limit;
  const endIndex = page * limit;
  const total = await Service.countDocuments(JSON.parse(queryStr));

  query = query.skip(startIndex).limit(limit);

  // Executing query
  const services = await query;

  // Pagination result
  const pagination = {};

  if (endIndex < total) {
    pagination.next = {
      page: page + 1,
      limit,
    };
  }

  if (startIndex > 0) {
    pagination.prev = {
      page: page - 1,
      limit,
    };
  }

  res.status(StatusCodes.OK).json({
    success: true,
    count: services.length,
    pagination,
    data: services,
  });
});

// @desc    Get single service
// @route   GET /api/services/:id
// @access  Public
const getService = asyncHandler(async (req, res) => {
  let service = null;
  // Try to find by ObjectId if valid
  if (mongoose.Types.ObjectId.isValid(req.params.id)) {
    service = await Service.findById(req.params.id);
  }
  // If not found, try by slug
  if (!service) {
    service = await Service.findOne({ slug: req.params.id });
  }
  if (!service) {
    res.status(StatusCodes.NOT_FOUND);
    throw new Error('Service not found');
  }
  res.status(StatusCodes.OK).json({
    success: true,
    data: service,
  });
});

// @desc    Create new service
// @route   POST /api/services
// @access  Private/Admin
const createService = asyncHandler(async (req, res) => {
  const service = await Service.create(req.body);

  res.status(StatusCodes.CREATED).json({
    success: true,
    data: service,
  });
});

// @desc    Update service
// @route   PUT /api/services/:id
// @access  Private/Admin
const updateService = asyncHandler(async (req, res) => {
  let service = await Service.findById(req.params.id);

  if (!service) {
    res.status(StatusCodes.NOT_FOUND);
    throw new Error('Service not found');
  }

  // Update fields, including subServices
  service.title = req.body.title ?? service.title;
  service.description = req.body.description ?? service.description;
  service.image = req.body.image ?? service.image;
  service.subServices = req.body.subServices ?? service.subServices;

  await service.save();

  res.status(StatusCodes.OK).json({
    success: true,
    data: service,
  });
});

// @desc    Delete service
// @route   DELETE /api/services/:id
// @access  Private/Admin
const deleteService = asyncHandler(async (req, res) => {
  const service = await Service.findById(req.params.id);

  if (!service) {
    res.status(StatusCodes.NOT_FOUND);
    throw new Error('Service not found');
  }

  await Service.deleteOne({ _id: service._id });

  res.status(StatusCodes.OK).json({
    success: true,
    data: {},
  });
});

// @desc    Upload photo for service
// @route   PUT /api/services/:id/photo
// @access  Private/Admin
const servicePhotoUpload = asyncHandler(async (req, res) => {
  const service = await Service.findById(req.params.id);

  if (!service) {
    res.status(StatusCodes.NOT_FOUND);
    throw new Error(`Service not found with id of ${req.params.id}`);
  }

  if (!req.files) {
    res.status(StatusCodes.BAD_REQUEST);
    throw new Error('Please upload a file');
  }

  const file = req.files.file;

  // Make sure the image is a photo
  if (!file.mimetype.startsWith('image')) {
    res.status(StatusCodes.BAD_REQUEST);
    throw new Error('Please upload an image file');
  }

  // Check filesize
  if (file.size > process.env.MAX_FILE_UPLOAD) {
    res.status(StatusCodes.BAD_REQUEST);
    throw new Error(`Please upload an image less than ${process.env.MAX_FILE_UPLOAD}`);
  }

  // Create custom filename
  file.name = `photo_${service._id}${path.parse(file.name).ext}`;

  file.mv(`${process.env.FILE_UPLOAD_PATH}/${file.name}`, async err => {
    if (err) {
      res.status(StatusCodes.INTERNAL_SERVER_ERROR);
      throw new Error('Problem with file upload');
    }

    await Service.findByIdAndUpdate(req.params.id, { image: file.name });

    res.status(StatusCodes.OK).json({
      success: true,
      data: file.name,
    });
  });
});

export {
  getServices,
  getService,
  createService,
  updateService,
  deleteService,
  servicePhotoUpload,
};
