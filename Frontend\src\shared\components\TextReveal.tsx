import { motion, useInView } from 'framer-motion';
import { useRef } from 'react';

interface TextRevealProps {
  text: string;
  className?: string;
  delay?: number;
  duration?: number;
  type?: 'words' | 'characters' | 'lines';
  staggerDelay?: number;
}

const TextReveal = ({
  text,
  className = '',
  delay = 0,
  duration = 0.6,
  type = 'words',
  staggerDelay = 0.1
}: TextRevealProps) => {
  const ref = useRef(null);
  const isInView = useInView(ref, { once: true, margin: "-10%" });

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: staggerDelay,
        delayChildren: delay,
      },
    },
  };

  const itemVariants = {
    hidden: { 
      opacity: 0, 
      y: 20,
      rotateX: -90,
    },
    visible: {
      opacity: 1,
      y: 0,
      rotateX: 0,
      transition: {
        duration,
        ease: "easeOut",
      },
    },
  };

  const splitText = () => {
    switch (type) {
      case 'characters':
        return text.split('').map((char, index) => (
          <motion.span
            key={index}
            variants={itemVariants}
            className="inline-block"
            style={{ transformOrigin: 'bottom' }}
          >
            {char === ' ' ? '\u00A0' : char}
          </motion.span>
        ));
      
      case 'words':
        return text.split(' ').map((word, index) => (
          <motion.span
            key={index}
            variants={itemVariants}
            className="inline-block mr-2"
            style={{ transformOrigin: 'bottom' }}
          >
            {word}
          </motion.span>
        ));
      
      case 'lines':
        return text.split('\n').map((line, index) => (
          <motion.div
            key={index}
            variants={itemVariants}
            className="block"
            style={{ transformOrigin: 'bottom' }}
          >
            {line}
          </motion.div>
        ));
      
      default:
        return text;
    }
  };

  return (
    <motion.div
      ref={ref}
      className={className}
      variants={containerVariants}
      initial="hidden"
      animate={isInView ? "visible" : "hidden"}
    >
      {splitText()}
    </motion.div>
  );
};

// Typewriter effect component
interface TypewriterProps {
  text: string;
  className?: string;
  delay?: number;
  speed?: number;
  cursor?: boolean;
  cursorColor?: string;
}

export const Typewriter = ({
  text,
  className = '',
  delay = 0,
  speed = 50,
  cursor = true,
  cursorColor = 'currentColor'
}: TypewriterProps) => {
  const ref = useRef(null);
  const isInView = useInView(ref, { once: true });

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        delay,
        staggerChildren: speed / 1000,
      },
    },
  };

  const letterVariants = {
    hidden: { opacity: 0 },
    visible: { opacity: 1 },
  };

  return (
    <motion.div
      ref={ref}
      className={`relative ${className}`}
      variants={containerVariants}
      initial="hidden"
      animate={isInView ? "visible" : "hidden"}
    >
      {text.split('').map((char, index) => (
        <motion.span
          key={index}
          variants={letterVariants}
          className="inline-block"
        >
          {char === ' ' ? '\u00A0' : char}
        </motion.span>
      ))}
      
      {cursor && (
        <motion.span
          className="inline-block w-0.5 h-6 ml-1"
          style={{ backgroundColor: cursorColor }}
          animate={{ opacity: [1, 0] }}
          transition={{
            duration: 0.8,
            repeat: Infinity,
            repeatType: "reverse",
            delay: delay + (text.length * speed) / 1000,
          }}
        />
      )}
    </motion.div>
  );
};

// Gradient text reveal
interface GradientTextRevealProps {
  text: string;
  className?: string;
  gradientColors?: string[];
  delay?: number;
}

export const GradientTextReveal = ({
  text,
  className = '',
  gradientColors = ['#9333ea', '#a855f7', '#c084fc'],
  delay = 0
}: GradientTextRevealProps) => {
  const ref = useRef(null);
  const isInView = useInView(ref, { once: true });

  return (
    <motion.div
      ref={ref}
      className={className}
      initial={{ opacity: 0, scale: 0.8 }}
      animate={isInView ? { opacity: 1, scale: 1 } : { opacity: 0, scale: 0.8 }}
      transition={{ duration: 0.8, delay }}
    >
      <motion.span
        className="bg-gradient-to-r bg-clip-text text-transparent"
        style={{
          backgroundImage: `linear-gradient(45deg, ${gradientColors.join(', ')})`
        }}
        animate={isInView ? {
          backgroundPosition: ['0% 50%', '100% 50%', '0% 50%'],
        } : {}}
        transition={{
          duration: 3,
          repeat: Infinity,
          ease: "easeInOut",
          delay: delay + 0.5,
        }}
        style={{
          backgroundSize: '200% 200%',
        }}
      >
        {text}
      </motion.span>
    </motion.div>
  );
};

export default TextReveal;
