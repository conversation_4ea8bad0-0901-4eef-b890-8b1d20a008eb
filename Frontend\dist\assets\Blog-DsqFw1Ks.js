var g=(d,i,a)=>new Promise((o,n)=>{var m=r=>{try{l(a.next(r))}catch(s){n(s)}},x=r=>{try{l(a.throw(r))}catch(s){n(s)}},l=r=>r.done?o(r.value):Promise.resolve(r.value).then(m,x);l((a=a.apply(d,i)).next())});import{c as y,u as j,a as v,b as N,r as c,g as w,j as e,m as u,B as k,L as h,A as B}from"./index-hEW_vQ3f.js";/**
 * @license lucide-react v0.336.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const f=y("BookOpen",[["path",{d:"M2 3h6a4 4 0 0 1 4 4v14a3 3 0 0 0-3-3H2z",key:"vv98re"}],["path",{d:"M22 3h-6a4 4 0 0 0-4 4v14a3 3 0 0 1 3-3h7z",key:"1cyq3y"}]]),S=()=>{const{trackClick:d}=j(),{language:i,t:a}=v(),{scrollToTop:o}=N(),[n,m]=c.useState([]),[x,l]=c.useState(!1),[r,s]=c.useState(null);c.useEffect(()=>{o(),b()},[i,o]);const b=()=>g(null,null,function*(){l(!0),s(null);try{const t=yield w({language:i});m(t.data)}catch(t){s("Failed to fetch blogs")}finally{l(!1)}});return e.jsx("div",{className:"pt-20 min-h-screen relative",children:e.jsx("div",{className:"section-padding",children:e.jsxs("div",{className:"container-custom",children:[e.jsxs(u.div,{initial:{opacity:0,y:30},animate:{opacity:1,y:0},transition:{duration:.8},className:"text-center max-w-4xl mx-auto mb-16",children:[e.jsx("div",{className:"inline-flex items-center justify-center w-20 h-20 bg-gradient-to-br from-brand-purple-600 to-brand-purple-800 rounded-3xl mb-8 glow-effect",children:e.jsx(f,{className:"w-10 h-10 text-white"})}),e.jsx("h1",{className:"text-4xl md:text-6xl font-bold gradient-text mb-6",children:a("blog.title")}),e.jsx("p",{className:"text-xl text-brand-purple-200 mb-10 leading-relaxed",children:a("blog.subtitle")}),e.jsx("div",{className:"w-32 h-1 bg-gradient-to-r from-brand-purple-600 to-brand-purple-800 mx-auto rounded-full"})]}),x?e.jsxs("div",{className:"text-center text-brand-purple-300 py-12",children:[e.jsx("div",{className:"animate-spin w-8 h-8 border-4 border-brand-purple-600 border-t-transparent rounded-full mx-auto mb-4"}),a("common.loading")]}):r?e.jsxs("div",{className:"text-center text-red-500 py-12 bg-red-500/10 rounded-2xl border border-red-500/20",children:[e.jsx("p",{className:"mb-4",children:r}),e.jsx("button",{onClick:b,className:"btn-primary",children:"Try Again"})]}):n.length===0?e.jsxs("div",{className:"text-center text-brand-purple-300 py-12",children:[e.jsx(f,{className:"w-16 h-16 mx-auto mb-4 text-brand-purple-500"}),e.jsx("p",{children:"No blog posts found."})]}):e.jsx("div",{className:"space-y-16",children:n.map((t,p)=>e.jsx(u.div,{initial:{opacity:0,y:50},animate:{opacity:1,y:0},transition:{duration:.6,delay:p*.1},className:`flex flex-col ${p%2===0?"lg:flex-row":"lg:flex-row-reverse"} gap-8 items-center`,children:e.jsx(k,{title:t.title,excerpt:t.excerpt,image:t.image||"/public/placeholder.svg",date:t.publishedAt?new Date(t.publishedAt).toLocaleDateString():new Date(t.createdAt).toLocaleDateString(),readTime:"5 min read",category:t.category||"General",href:`/blog/${t.slug||t.id}`,url:t.url,onClick:()=>d({pageName:"Blog",postTitle:t.title}),index:p})},t.id))}),e.jsxs(u.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.6,delay:.3},className:"flex flex-col sm:flex-row gap-4 justify-center mt-16",children:[e.jsxs(h,{to:"/",className:"bg-gradient-to-r from-brand-purple-600 to-brand-purple-700 hover:from-brand-purple-700 hover:to-brand-purple-800 text-white px-8 py-4 rounded-lg font-semibold flex items-center justify-center transition-all duration-300 glow-effect",children:[a("nav.home"),e.jsx(B,{className:"ml-2 h-5 w-5"})]}),e.jsx(h,{to:"/contact",className:"bg-transparent border-2 border-brand-purple-500 text-brand-purple-300 hover:bg-brand-purple-600 hover:text-white hover:border-brand-purple-400 px-8 py-4 rounded-lg font-semibold transition-all duration-300",children:a("nav.contact")})]})]})})})};export{S as default};
