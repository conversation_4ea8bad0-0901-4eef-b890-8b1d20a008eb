import{aP as L,aQ as B,aR as P,aS as U,aT as X,aU as W,aV as z,aW as k,aX as F,aY as Q,aZ as V}from"./vendor-OXu-rwpf.js";var G=typeof window<"u"&&window.__REDUX_DEVTOOLS_EXTENSION_COMPOSE__?window.__REDUX_DEVTOOLS_EXTENSION_COMPOSE__:function(){if(arguments.length!==0)return typeof arguments[0]=="object"?k:k.apply(null,arguments)};function A(e,r){function c(...t){if(r){let n=r(...t);if(!n)throw new Error(C(0));return{type:e,payload:n.payload,..."meta"in n&&{meta:n.meta},..."error"in n&&{error:n.error}}}return{type:e,payload:t[0]}}return c.toString=()=>`${e}`,c.type=e,c.match=t=>L(t)&&t.type===e,c}var I=class D extends Array{constructor(...r){super(...r),Object.setPrototypeOf(this,D.prototype)}static get[Symbol.species](){return D}concat(...r){return super.concat.apply(this,r)}prepend(...r){return r.length===1&&Array.isArray(r[0])?new D(...r[0].concat(this)):new D(...r.concat(this))}};function x(e){return P(e)?B(e,()=>{}):e}function R(e,r,c){return e.has(r)?e.get(r):e.set(r,c(r)).get(r)}function H(e){return typeof e=="boolean"}var K=()=>function(r){const{thunk:c=!0,immutableCheck:t=!0,serializableCheck:n=!0,actionCreatorCheck:a=!0}=r??{};let u=new I;return c&&(H(c)?u.push(Q):u.push(V(c.extraArgument))),u},Y="RTK_autoBatch",v=e=>r=>{setTimeout(r,e)},Z=(e={type:"raf"})=>r=>(...c)=>{const t=r(...c);let n=!0,a=!1,u=!1;const o=new Set,s=e.type==="tick"?queueMicrotask:e.type==="raf"?typeof window<"u"&&window.requestAnimationFrame?window.requestAnimationFrame:v(10):e.type==="callback"?e.queueNotification:v(e.timeout),l=()=>{u=!1,a&&(a=!1,o.forEach(i=>i()))};return Object.assign({},t,{subscribe(i){const h=()=>n&&i(),w=t.subscribe(h);return o.add(i),()=>{w(),o.delete(i)}},dispatch(i){try{return n=!i?.meta?.[Y],a=!n,a&&(u||(u=!0,s(l))),t.dispatch(i)}finally{n=!0}}})},J=e=>function(c){const{autoBatch:t=!0}=c??{};let n=new I(e);return t&&n.push(Z(typeof t=="object"?t:void 0)),n};function le(e){const r=K(),{reducer:c=void 0,middleware:t,devTools:n=!0,preloadedState:a=void 0,enhancers:u=void 0}=e||{};let o;if(typeof c=="function")o=c;else if(X(c))o=W(c);else throw new Error(C(1));let s;typeof t=="function"?s=t(r):s=r();let l=k;n&&(l=G({trace:!1,...typeof n=="object"&&n}));const i=z(...s),h=J(i);let w=typeof u=="function"?u(h):h();const T=l(...w);return F(o,a,T)}function N(e){const r={},c=[];let t;const n={addCase(a,u){const o=typeof a=="string"?a:a.type;if(!o)throw new Error(C(28));if(o in r)throw new Error(C(29));return r[o]=u,n},addMatcher(a,u){return c.push({matcher:a,reducer:u}),n},addDefaultCase(a){return t=a,n}};return e(n),[r,c,t]}function ee(e){return typeof e=="function"}function re(e,r){let[c,t,n]=N(r),a;if(ee(e))a=()=>x(e());else{const o=x(e);a=()=>o}function u(o=a(),s){let l=[c[s.type],...t.filter(({matcher:i})=>i(s)).map(({reducer:i})=>i)];return l.filter(i=>!!i).length===0&&(l=[n]),l.reduce((i,h)=>{if(h)if(U(i)){const T=h(i,s);return T===void 0?i:T}else{if(P(i))return B(i,w=>h(w,s));{const w=h(i,s);if(w===void 0){if(i===null)return i;throw Error("A case reducer on a non-draftable value must not return undefined")}return w}}return i},o)}return u.getInitialState=a,u}var te=Symbol.for("rtk-slice-createasyncthunk");function ne(e,r){return`${e}/${r}`}function ce({creators:e}={}){const r=e?.asyncThunk?.[te];return function(t){const{name:n,reducerPath:a=n}=t;if(!n)throw new Error(C(11));const u=(typeof t.reducers=="function"?t.reducers(ie()):t.reducers)||{},o=Object.keys(u),s={sliceCaseReducersByName:{},sliceCaseReducersByType:{},actionCreators:{},sliceMatchers:[]},l={addCase(f,d){const p=typeof f=="string"?f:f.type;if(!p)throw new Error(C(12));if(p in s.sliceCaseReducersByType)throw new Error(C(13));return s.sliceCaseReducersByType[p]=d,l},addMatcher(f,d){return s.sliceMatchers.push({matcher:f,reducer:d}),l},exposeAction(f,d){return s.actionCreators[f]=d,l},exposeCaseReducer(f,d){return s.sliceCaseReducersByName[f]=d,l}};o.forEach(f=>{const d=u[f],p={reducerName:f,type:ne(n,f),createNotation:typeof t.reducers=="function"};oe(d)?fe(p,d,l,r):ue(p,d,l)});function i(){const[f={},d=[],p=void 0]=typeof t.extraReducers=="function"?N(t.extraReducers):[t.extraReducers],g={...f,...s.sliceCaseReducersByType};return re(t.initialState,m=>{for(let y in g)m.addCase(y,g[y]);for(let y of s.sliceMatchers)m.addMatcher(y.matcher,y.reducer);for(let y of d)m.addMatcher(y.matcher,y.reducer);p&&m.addDefaultCase(p)})}const h=f=>f,w=new Map,T=new WeakMap;let E;function S(f,d){return E||(E=i()),E(f,d)}function M(){return E||(E=i()),E.getInitialState()}function _(f,d=!1){function p(m){let y=m[f];return typeof y>"u"&&d&&(y=R(T,p,M)),y}function g(m=h){const y=R(w,d,()=>new WeakMap);return R(y,m,()=>{const j={};for(const[$,q]of Object.entries(t.selectors??{}))j[$]=ae(q,m,()=>R(T,m,M),d);return j})}return{reducerPath:f,getSelectors:g,get selectors(){return g(p)},selectSlice:p}}const O={name:n,reducer:S,actions:s.actionCreators,caseReducers:s.sliceCaseReducersByName,getInitialState:M,..._(a),injectInto(f,{reducerPath:d,...p}={}){const g=d??a;return f.inject({reducerPath:g,reducer:S},p),{...O,..._(g,!0)}}};return O}}function ae(e,r,c,t){function n(a,...u){let o=r(a);return typeof o>"u"&&t&&(o=c()),e(o,...u)}return n.unwrapped=e,n}var he=ce();function ie(){function e(r,c){return{_reducerDefinitionType:"asyncThunk",payloadCreator:r,...c}}return e.withTypes=()=>e,{reducer(r){return Object.assign({[r.name](...c){return r(...c)}}[r.name],{_reducerDefinitionType:"reducer"})},preparedReducer(r,c){return{_reducerDefinitionType:"reducerWithPrepare",prepare:r,reducer:c}},asyncThunk:e}}function ue({type:e,reducerName:r,createNotation:c},t,n){let a,u;if("reducer"in t){if(c&&!se(t))throw new Error(C(17));a=t.reducer,u=t.prepare}else a=t;n.addCase(e,a).exposeCaseReducer(r,a).exposeAction(r,u?A(e,u):A(e))}function oe(e){return e._reducerDefinitionType==="asyncThunk"}function se(e){return e._reducerDefinitionType==="reducerWithPrepare"}function fe({type:e,reducerName:r},c,t,n){if(!n)throw new Error(C(18));const{payloadCreator:a,fulfilled:u,pending:o,rejected:s,settled:l,options:i}=c,h=n(e,a,i);t.exposeAction(r,h),u&&t.addCase(h.fulfilled,u),o&&t.addCase(h.pending,o),s&&t.addCase(h.rejected,s),l&&t.addMatcher(h.settled,l),t.exposeCaseReducer(r,{fulfilled:u||b,pending:o||b,rejected:s||b,settled:l||b})}function b(){}function C(e){return`Minified Redux Toolkit error #${e}; visit https://redux-toolkit.js.org/Errors?code=${e} for the full message or use the non-minified dev environment for full errors. `}export{le as a,he as c};
