import { useState } from "react";
import { useAuth } from "@/core/providers/AuthContext";
import { useAppDispatch } from "@/store/hooks";
import { loginSuccess, logout as reduxLogout } from "@/store/authSlice";
import api from '@/shared/services/api/axios';
import { Mail, Lock, User, Building, Target, Briefcase, Eye, EyeOff, Sparkles } from "lucide-react";

const API_URL = "/api/auth";

// Business domain options
const BUSINESS_DOMAINS = [
  { value: 'technology', label: 'Technology' },
  { value: 'healthcare', label: 'Healthcare' },
  { value: 'finance', label: 'Finance' },
  { value: 'education', label: 'Education' },
  { value: 'retail', label: 'Retail' },
  { value: 'manufacturing', label: 'Manufacturing' },
  { value: 'consulting', label: 'Consulting' },
  { value: 'real_estate', label: 'Real Estate' },
  { value: 'hospitality', label: 'Hospitality' },
  { value: 'transportation', label: 'Transportation' },
  { value: 'media', label: 'Media & Entertainment' },
  { value: 'non_profit', label: 'Non-Profit' },
  { value: 'government', label: 'Government' },
  { value: 'other', label: 'Other' }
];

// Client segment options
const CLIENT_SEGMENTS = [
  { value: 'startup', label: 'Startup' },
  { value: 'small_business', label: 'Small Business' },
  { value: 'medium_business', label: 'Medium Business' },
  { value: 'enterprise', label: 'Enterprise' },
  { value: 'freelancer', label: 'Freelancer' },
  { value: 'agency', label: 'Agency' },
  { value: 'consultant', label: 'Consultant' },
  { value: 'non_profit', label: 'Non-Profit' },
  { value: 'government', label: 'Government' },
  { value: 'other', label: 'Other' }
];

export default function AuthUserForm({ onSuccess }: { onSuccess?: () => void }) {
  const [isRegister, setIsRegister] = useState(false);
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [name, setName] = useState("");
  const [businessDomain, setBusinessDomain] = useState("");
  const [clientSegment, setClientSegment] = useState("");
  const [companyName, setCompanyName] = useState("");
  const [error, setError] = useState("");
  const [success, setSuccess] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const { login, register, refetchUser } = useAuth();
  const dispatch = useAppDispatch();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError("");
    setSuccess("");
    setIsLoading(true);
    try {
      if (isRegister) {
        await register({
          name,
          email,
          password,
          role: "user",
          businessDomain,
          clientSegment,
          companyName: companyName || undefined
        });
        setSuccess("Account created! Please check your email to verify your account.");
      } else {
        // Get the current sessionId to match analytics tracking
        const getSessionId = () => {
          const now = Date.now();
          let sessionId = sessionStorage.getItem('sessionId');
          let sessionTimestamp = sessionStorage.getItem('sessionTimestamp');
          if (!sessionId || !sessionTimestamp) {
            sessionId = Math.random().toString(36).substring(2) + now.toString(36);
            sessionStorage.setItem('sessionId', sessionId);
          }
          sessionStorage.setItem('sessionTimestamp', now.toString());
          return sessionId;
        };

        // Include sessionId in login request for visitor linking
        const loginData = {
          email,
          password,
          sessionId: getSessionId()
        };

        // Use direct API call to get user and token for Redux
        const { data } = await api.post("/auth/login", loginData, { withCredentials: true });
        if (data && data.data) {
          dispatch(loginSuccess({ user: data.data, token: data.token || "" }));
        }

        // Force refetch user data to update AuthContext
        await refetchUser();

        // Force a small delay and trigger re-render
        setTimeout(() => {
          setSuccess("");
          if (onSuccess) onSuccess();

          // Force re-render by dispatching a custom event
          window.dispatchEvent(new CustomEvent('userLoggedIn'));
        }, 200);
      }
    } catch (err: any) {
      setError(
        err?.response?.data?.message ||
        (isRegister ? "Registration failed." : "Login failed.")
      );
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="w-full touch-manipulation">
      {/* Enhanced Tab Switcher with Purple Theme */}
      <div className="relative mb-4 sm:mb-6 lg:mb-8">
        <div className="flex bg-brand-grey-100 rounded-2xl p-1 shadow-inner">
          <button
            className={`flex-1 py-3 px-4 sm:px-6 rounded-xl font-bold text-sm sm:text-base transition-all duration-300 relative overflow-hidden ${
              !isRegister
                ? 'bg-gradient-to-r from-brand-purple-600 to-brand-purple-700 text-white shadow-lg transform scale-105 shadow-brand-purple-500/25'
                : 'text-brand-grey-600 hover:text-brand-grey-800 hover:bg-brand-grey-50'
            }`}
            onClick={() => setIsRegister(false)}
          >
            {!isRegister && (
              <div className="absolute inset-0 bg-gradient-to-r from-brand-purple-500/20 to-brand-purple-600/20 animate-pulse"></div>
            )}
            <span className="relative flex items-center justify-center gap-2">
              <Sparkles className="w-4 h-4" />
              Sign In
            </span>
          </button>
          <button
            className={`flex-1 py-3 px-4 sm:px-6 rounded-xl font-bold text-sm sm:text-base transition-all duration-300 relative overflow-hidden ${
              isRegister
                ? 'bg-gradient-to-r from-brand-purple-600 to-brand-purple-700 text-white shadow-lg transform scale-105 shadow-brand-purple-500/25'
                : 'text-brand-grey-600 hover:text-brand-grey-800 hover:bg-brand-grey-50'
            }`}
            onClick={() => setIsRegister(true)}
          >
            {isRegister && (
              <div className="absolute inset-0 bg-gradient-to-r from-brand-purple-500/20 to-brand-purple-600/20 animate-pulse"></div>
            )}
            <span className="relative flex items-center justify-center gap-2">
              <User className="w-4 h-4" />
              Create Account
            </span>
          </button>
        </div>
      </div>
      <form className="space-y-3 sm:space-y-4 lg:space-y-6" onSubmit={handleSubmit}>
        {isRegister && (
          <>
            {/* Name Field */}
            <div className="space-y-2">
              <label htmlFor="name" className="block text-sm font-bold text-brand-grey-800">
                Full Name <span className="text-brand-purple-600">*</span>
              </label>
              <div className="relative group">
                <User className="absolute left-3 top-1/2 transform -translate-y-1/2 text-brand-grey-400 group-focus-within:text-brand-purple-600 h-5 w-5 transition-colors duration-300" />
                <input
                  type="text"
                  id="name"
                  name="name"
                  required
                  className="w-full pl-12 pr-4 py-3 sm:py-4 rounded-xl border-2 border-brand-grey-200 focus:border-brand-purple-600 focus:ring-4 focus:ring-brand-purple-100 transition-all duration-300 text-brand-grey-800 placeholder-brand-grey-400 bg-brand-grey-50 focus:bg-white shadow-sm hover:shadow-md focus:shadow-lg"
                  placeholder="Enter your full name"
                  value={name}
                  onChange={e => setName(e.target.value)}
                />
                <div className="absolute inset-0 rounded-xl bg-gradient-to-r from-brand-purple-600/5 to-brand-purple-700/5 opacity-0 group-focus-within:opacity-100 transition-opacity duration-300 pointer-events-none"></div>
              </div>
            </div>

            {/* Business Domain Field */}
            <div className="space-y-2">
              <label htmlFor="businessDomain" className="block text-sm font-bold text-brand-grey-800">
                Business Domain <span className="text-brand-purple-600">*</span>
              </label>
              <div className="relative group">
                <Building className="absolute left-3 top-1/2 transform -translate-y-1/2 text-brand-grey-400 group-focus-within:text-brand-purple-600 h-5 w-5 transition-colors duration-300 z-10" />
                <select
                  id="businessDomain"
                  name="businessDomain"
                  required
                  className="w-full pl-12 pr-10 py-3 sm:py-4 rounded-xl border-2 border-brand-grey-200 focus:border-brand-purple-600 focus:ring-4 focus:ring-brand-purple-100 transition-all duration-300 text-brand-grey-800 bg-brand-grey-50 focus:bg-white appearance-none shadow-sm hover:shadow-md focus:shadow-lg cursor-pointer"
                  value={businessDomain}
                  onChange={e => setBusinessDomain(e.target.value)}
                >
                  <option value="">Select your business domain</option>
                  {BUSINESS_DOMAINS.map((domain) => (
                    <option key={domain.value} value={domain.value}>
                      {domain.label}
                    </option>
                  ))}
                </select>
                <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                  <svg className="w-5 h-5 text-brand-grey-400 group-focus-within:text-brand-purple-600 transition-colors duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                  </svg>
                </div>
                <div className="absolute inset-0 rounded-xl bg-gradient-to-r from-brand-purple-600/5 to-brand-purple-700/5 opacity-0 group-focus-within:opacity-100 transition-opacity duration-300 pointer-events-none"></div>
              </div>
            </div>

            {/* Client Segment Field */}
            <div className="space-y-2">
              <label htmlFor="clientSegment" className="block text-sm font-bold text-brand-grey-800">
                Client Segment <span className="text-brand-purple-600">*</span>
              </label>
              <div className="relative group">
                <Target className="absolute left-3 top-1/2 transform -translate-y-1/2 text-brand-grey-400 group-focus-within:text-brand-purple-600 h-5 w-5 transition-colors duration-300 z-10" />
                <select
                  id="clientSegment"
                  name="clientSegment"
                  required
                  className="w-full pl-12 pr-10 py-3 sm:py-4 rounded-xl border-2 border-brand-grey-200 focus:border-brand-purple-600 focus:ring-4 focus:ring-brand-purple-100 transition-all duration-300 text-brand-grey-800 bg-brand-grey-50 focus:bg-white appearance-none shadow-sm hover:shadow-md focus:shadow-lg cursor-pointer"
                  value={clientSegment}
                  onChange={e => setClientSegment(e.target.value)}
                >
                  <option value="">Select your client segment</option>
                  {CLIENT_SEGMENTS.map((segment) => (
                    <option key={segment.value} value={segment.value}>
                      {segment.label}
                    </option>
                  ))}
                </select>
                <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                  <svg className="w-5 h-5 text-brand-grey-400 group-focus-within:text-brand-purple-600 transition-colors duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                  </svg>
                </div>
                <div className="absolute inset-0 rounded-xl bg-gradient-to-r from-brand-purple-600/5 to-brand-purple-700/5 opacity-0 group-focus-within:opacity-100 transition-opacity duration-300 pointer-events-none"></div>
              </div>
            </div>

            {/* Company Name Field */}
            <div className="space-y-2">
              <label htmlFor="companyName" className="block text-sm font-bold text-brand-grey-800">
                Company Name <span className="text-brand-grey-400 font-normal">(Optional)</span>
              </label>
              <div className="relative group">
                <Briefcase className="absolute left-3 top-1/2 transform -translate-y-1/2 text-brand-grey-400 group-focus-within:text-brand-purple-600 h-5 w-5 transition-colors duration-300" />
                <input
                  type="text"
                  id="companyName"
                  name="companyName"
                  className="w-full pl-12 pr-4 py-3 sm:py-4 rounded-xl border-2 border-brand-grey-200 focus:border-brand-purple-600 focus:ring-4 focus:ring-brand-purple-100 transition-all duration-300 text-brand-grey-800 placeholder-brand-grey-400 bg-brand-grey-50 focus:bg-white shadow-sm hover:shadow-md focus:shadow-lg"
                  placeholder="Enter your company name"
                  value={companyName}
                  onChange={e => setCompanyName(e.target.value)}
                />
                <div className="absolute inset-0 rounded-xl bg-gradient-to-r from-brand-purple-600/5 to-brand-purple-700/5 opacity-0 group-focus-within:opacity-100 transition-opacity duration-300 pointer-events-none"></div>
              </div>
            </div>
          </>
        )}
        {/* Email Field */}
        <div className="space-y-2">
          <label htmlFor="email" className="block text-sm font-bold text-brand-grey-800">
            Email Address <span className="text-brand-purple-600">*</span>
          </label>
          <div className="relative group">
            <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 text-brand-grey-400 group-focus-within:text-brand-purple-600 h-5 w-5 transition-colors duration-300" />
            <input
              type="email"
              id="email"
              name="email"
              required
              className="w-full pl-12 pr-4 py-3 sm:py-4 rounded-xl border-2 border-brand-grey-200 focus:border-brand-purple-600 focus:ring-4 focus:ring-brand-purple-100 transition-all duration-300 text-brand-grey-800 placeholder-brand-grey-400 bg-brand-grey-50 focus:bg-white shadow-sm hover:shadow-md focus:shadow-lg"
              placeholder="Enter your email address"
              value={email}
              onChange={e => setEmail(e.target.value)}
            />
            <div className="absolute inset-0 rounded-xl bg-gradient-to-r from-brand-purple-600/5 to-brand-purple-700/5 opacity-0 group-focus-within:opacity-100 transition-opacity duration-300 pointer-events-none"></div>
          </div>
        </div>

        {/* Password Field */}
        <div className="space-y-2">
          <label htmlFor="password" className="block text-sm font-bold text-brand-grey-800">
            Password <span className="text-brand-purple-600">*</span>
          </label>
          <div className="relative group">
            <Lock className="absolute left-3 top-1/2 transform -translate-y-1/2 text-brand-grey-400 group-focus-within:text-brand-purple-600 h-5 w-5 transition-colors duration-300" />
            <input
              type={showPassword ? "text" : "password"}
              id="password"
              name="password"
              required
              className="w-full pl-12 pr-12 py-3 sm:py-4 rounded-xl border-2 border-brand-grey-200 focus:border-brand-purple-600 focus:ring-4 focus:ring-brand-purple-100 transition-all duration-300 text-brand-grey-800 placeholder-brand-grey-400 bg-brand-grey-50 focus:bg-white shadow-sm hover:shadow-md focus:shadow-lg"
              placeholder="Enter your password"
              value={password}
              onChange={e => setPassword(e.target.value)}
            />
            <button
              type="button"
              className="absolute right-3 top-1/2 transform -translate-y-1/2 text-brand-grey-400 hover:text-brand-purple-600 transition-colors duration-300"
              onClick={() => setShowPassword(!showPassword)}
            >
              {showPassword ? <EyeOff className="h-5 w-5" /> : <Eye className="h-5 w-5" />}
            </button>
            <div className="absolute inset-0 rounded-xl bg-gradient-to-r from-brand-purple-600/5 to-brand-purple-700/5 opacity-0 group-focus-within:opacity-100 transition-opacity duration-300 pointer-events-none"></div>
          </div>
        </div>

        {/* Error and Success Messages */}
        {error && (
          <div className="bg-red-50 border-2 border-red-200 text-red-700 px-4 py-3 rounded-xl text-center font-medium shadow-sm animate-fade-in">
            <div className="flex items-center justify-center gap-2">
              <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
              </svg>
              {error}
            </div>
          </div>
        )}
        {success && (
          <div className="bg-green-50 border-2 border-green-200 text-green-700 px-4 py-3 rounded-xl text-center font-medium shadow-sm animate-fade-in">
            <div className="flex items-center justify-center gap-2">
              <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
              </svg>
              {success}
            </div>
          </div>
        )}

        {/* Submit Button */}
        <button
          type="submit"
          className="w-full bg-gradient-to-r from-brand-purple-600 to-brand-purple-700 hover:from-brand-purple-700 hover:to-brand-purple-800 text-white font-bold py-4 px-6 rounded-xl shadow-lg hover:shadow-xl hover:shadow-brand-purple-500/25 transform hover:scale-105 transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none relative overflow-hidden group"
          disabled={isLoading}
        >
          <div className="absolute inset-0 bg-gradient-to-r from-brand-purple-500/20 to-brand-purple-600/20 translate-x-[-100%] group-hover:translate-x-[100%] transition-transform duration-700"></div>
          {isLoading ? (
            <div className="flex items-center justify-center relative z-10">
              <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"></div>
              {isRegister ? "Creating Account..." : "Signing In..."}
            </div>
          ) : (
            <span className="relative z-10 flex items-center justify-center gap-2">
              <Sparkles className="w-5 h-5" />
              {isRegister ? "Create Account" : "Sign In"}
            </span>
          )}
        </button>
      </form>
    </div>
  );
}
