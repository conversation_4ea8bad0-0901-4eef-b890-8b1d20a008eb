import { Router } from 'express';
import authRoutes from '../components/auth/authRoutes.js';
import userRoutes from '../components/user/userRoutes.js';
import blogRoutes from '../components/blog/blogRoutes.js';
import serviceRoutes from '../components/service/serviceRoutes.js';
import cartRoutes from '../components/cart/cartRoutes.js';
import campaignRoutes from '../components/campaign/campaignRoutes.js';
import leadRoutes from '../components/lead/leadRoutes.js';
import dashboardRoutes from '../components/dashboard/dashboardRoutes.js';
import calendarRoutes from '../components/calendar/calendarRoutes.js';
import analyticsRoutes from '../components/analytics/analyticsRoutes.js';
import financialMetricsRoutes from '../components/analytics/financialMetricsRoutes.js';
import visitorRoutes from '../components/visitor/visitorRoutes.js';

const router = Router();

// Health check endpoint for Railway
router.get('/health', (req, res) => {
  res.status(200).json({
    status: 'OK',
    message: 'ClickForYou Backend is running',
    timestamp: new Date().toISOString(),
    environment: process.env.NODE_ENV || 'development'
  });
});

// Mount component routes
router.use('/auth', authRoutes);
router.use('/users', userRoutes);
router.use('/blogs', blogRoutes);
router.use('/services', serviceRoutes);
router.use('/cart', cartRoutes);
router.use('/campaigns', campaignRoutes);
router.use('/leads', leadRoutes);
router.use('/dashboard', dashboardRoutes);
router.use('/calendar', calendarRoutes);
router.use('/analytics', analyticsRoutes);
router.use('/marketing', analyticsRoutes);
router.use('/financial-metrics', financialMetricsRoutes);
router.use('/visitor', visitorRoutes);

// Health check route
router.get('/health', (req, res) => {
  res.status(200).json({
    success: true,
    message: 'Server is running',
    timestamp: new Date().toISOString()
  });
});

export default router;