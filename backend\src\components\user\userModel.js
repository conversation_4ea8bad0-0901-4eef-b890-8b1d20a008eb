import mongoose from 'mongoose';
import bcrypt from 'bcryptjs';
import jwt from 'jsonwebtoken';
import crypto from 'crypto';
import { ROLES } from '../../shared/config/constants.js';

const userSchema = new mongoose.Schema(
  {
    name: {
      type: String,
      required: [true, 'Please add a name'],
      trim: true,
      maxlength: [50, 'Name cannot be more than 50 characters'],
    },
    email: {
      type: String,
      required: [true, 'Please add an email'],
      unique: true,
      match: [
        /^\w+([\.-]?\w+)*@\w+([\.-]?\w+)*(\.\w{2,3})+$/,
        'Please add a valid email',
      ],
    },
    password: {
      type: String,
      required: [true, 'Please add a password'],
      minlength: [6, 'Password must be at least 6 characters'],
      select: false,
    },
    role: {
      type: String,
      enum: ['user'], // Only regular users in this collection
      default: 'user',
    },
    resetPasswordToken: String,
    resetPasswordExpire: Date,
    isActive: {
      type: Boolean,
      default: true,
    },
    // Business information fields
    businessDomain: {
      type: String,
      required: [true, 'Please specify your business domain'],
      enum: [
        'technology',
        'healthcare',
        'finance',
        'education',
        'retail',
        'manufacturing',
        'consulting',
        'real_estate',
        'hospitality',
        'transportation',
        'media',
        'non_profit',
        'government',
        'other'
      ],
    },
    clientSegment: {
      type: String,
      required: [true, 'Please specify your client segment'],
      enum: [
        'startup',
        'small_business',
        'medium_enterprise',
        'large_enterprise',
        'individual',
        'freelancer',
        'agency',
        'non_profit',
        'government',
        'other'
      ],
    },
    // Company information
    companyName: {
      type: String,
      trim: true,
      maxlength: [100, 'Company name cannot be more than 100 characters'],
    },
    companySize: {
      type: String,
      enum: ['1-10', '11-50', '51-200', '201-1000', '1000+'],
    },
    // Registration tracking
    registrationIP: {
      type: String,
    },
    isVerified: {
      type: Boolean,
      default: false,
    },
    verificationToken: String,
    verificationTokenExpires: Date,
  },
  {
    timestamps: true,
    toJSON: { virtuals: true },
    toObject: { virtuals: true },
  }
);

// Encrypt password using bcrypt
userSchema.pre('save', async function (next) {
  if (!this.isModified('password')) {
    next();
  }

  const salt = await bcrypt.genSalt(10);
  this.password = await bcrypt.hash(this.password, salt);
});

// Sign JWT and return
userSchema.methods.getSignedJwtToken = function () {
  return jwt.sign(
    { id: this._id, role: this.role },
    process.env.JWT_SECRET,
    {
      expiresIn: process.env.JWT_EXPIRE,
    }
  );
};

// Match user entered password to hashed password in database
userSchema.methods.matchPassword = async function (enteredPassword) {
  return await bcrypt.compare(enteredPassword, this.password);
};

// Check if user is admin
userSchema.methods.isAdmin = function () {
  return this.role === ROLES.ADMIN;
};

// Check if user has marketing role
userSchema.methods.isMarketing = function () {
  return this.role === ROLES.MARKETING;
};

// Check if user has admin or marketing role
userSchema.methods.hasDashboardAccess = function () {
  return [ROLES.ADMIN, ROLES.MARKETING].includes(this.role);
};

// Get business domain display name
userSchema.methods.getBusinessDomainDisplay = function () {
  const domainMap = {
    'technology': 'Technology',
    'healthcare': 'Healthcare',
    'finance': 'Finance',
    'education': 'Education',
    'retail': 'Retail',
    'manufacturing': 'Manufacturing',
    'consulting': 'Consulting',
    'real_estate': 'Real Estate',
    'hospitality': 'Hospitality',
    'transportation': 'Transportation',
    'media': 'Media',
    'non_profit': 'Non-Profit',
    'government': 'Government',
    'other': 'Other'
  };
  return domainMap[this.businessDomain] || this.businessDomain;
};

// Get client segment display name
userSchema.methods.getClientSegmentDisplay = function () {
  const segmentMap = {
    'startup': 'Startup',
    'small_business': 'Small Business',
    'medium_enterprise': 'Medium Enterprise',
    'large_enterprise': 'Large Enterprise',
    'individual': 'Individual',
    'freelancer': 'Freelancer',
    'agency': 'Agency',
    'non_profit': 'Non-Profit',
    'government': 'Government',
    'other': 'Other'
  };
  return segmentMap[this.clientSegment] || this.clientSegment;
};

// Generate and hash password reset token
userSchema.methods.getResetPasswordToken = function () {
  // Generate token
  const resetToken = crypto.randomBytes(20).toString('hex');
  // Hash token and set to resetPasswordToken field
  this.resetPasswordToken = crypto.createHash('sha256').update(resetToken).digest('hex');
  // Set expire
  this.resetPasswordExpire = Date.now() + 60 * 60 * 1000; // 1 hour
  return resetToken;
};

const User = mongoose.models.User || mongoose.model('User', userSchema);

export default User;
