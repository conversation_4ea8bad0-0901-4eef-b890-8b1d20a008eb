import React, { useState, useEffect } from 'react';
import axios from 'axios';

interface VisitPage {
  path: string;
  timestamp: string;
  duration: number;
  pageName?: string;
}



interface Visitor {
  _id: string;
  ipAddress: string;
  visitorName?: string;
  visitorEmail?: string;
  totalVisits: number;
  totalPageViews: number;
  totalClicks: number;
  totalQuoteRequests: number;
  totalCartActions: number;
  totalTimeSpent: number;
  leadScore: number;
  leadStatus: 'new' | 'in_progress' | 'qualified' | 'converted' | 'lost';
  businessDomain?: string;
  clientSegment?: string;
  location: {
    country?: string;
    region?: string;
    city?: string;
  };
  lastVisit: string;
  pageVisits: PageVisit[];
  clicks: Click[];
  quoteRequests: QuoteRequest[];
  cartActions: CartAction[];
  userId?: {
    _id: string;
    name: string;
    email: string;
    role: string;
  };
  visitorType?: string;
}

interface PageVisit {
  path: string;
  pageName?: string;
  timestamp: string;
  duration: number;
  sessionId: string;
}

interface Click {
  buttonLabel?: string;
  component?: string;
  path?: string;
  timestamp: string;
  sessionId: string;
}

interface QuoteRequest {
  serviceName: string;
  subServiceName: string;
  subServiceDescription?: string;
  timestamp: string;
  sessionId: string;
  path?: string;
  userEmail?: string;
  userName?: string;
}

interface CartAction {
  action: 'add' | 'remove';
  serviceName: string;
  subServiceName: string;
  subServiceDescription?: string;
  timestamp: string;
  sessionId: string;
  path?: string;
  userEmail?: string;
  userName?: string;
}

interface VisitorAnalyticsTableProps {
  refreshTrigger?: number;
}

const VisitorAnalyticsTable: React.FC<VisitorAnalyticsTableProps> = ({ refreshTrigger }) => {
  const [visitors, setVisitors] = useState<Visitor[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [sortBy, setSortBy] = useState('lastVisit');
  const [sortOrder, setSortOrder] = useState('desc');
  const [filterStatus, setFilterStatus] = useState('');
  const [filterDomain, setFilterDomain] = useState('');
  const [selectedVisitor, setSelectedVisitor] = useState<Visitor | null>(null);
  const [showPagesModal, setShowPagesModal] = useState(false);
  const [showQuotesModal, setShowQuotesModal] = useState(false);
  const [showCartModal, setShowCartModal] = useState(false);

  const fetchVisitors = async () => {
    try {
      setLoading(true);
      const params = new URLSearchParams({
        page: currentPage.toString(),
        limit: '20',
        sortBy,
        sortOrder,
        ...(filterStatus && { leadStatus: filterStatus }),
        ...(filterDomain && { businessDomain: filterDomain }),
      });

      const response = await axios.get(`/api/visitor/list?${params}`);

      // Debug logging
      console.log('Visitor API Response:', response.data);
      console.log('Visitors array:', response.data.visitors);
      console.log('First visitor:', response.data.visitors?.[0]);

      setVisitors(response.data.visitors || []);
      setTotalPages(response.data.totalPages || 1);
      setError(null);
    } catch (err) {
      setError('Failed to fetch visitor data');
      console.error('Visitor fetch error:', err);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchVisitors();
  }, [currentPage, sortBy, sortOrder, filterStatus, filterDomain, refreshTrigger]);

  const handleSort = (field: string) => {
    if (sortBy === field) {
      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');
    } else {
      setSortBy(field);
      setSortOrder('desc');
    }
  };

  const getStatusBadge = (status: string) => {
    const statusColors = {
      new: 'bg-blue-100 text-blue-800',
      in_progress: 'bg-yellow-100 text-yellow-800',
      qualified: 'bg-green-100 text-green-800',
      converted: 'bg-purple-100 text-purple-800',
      lost: 'bg-red-100 text-red-800',
    };
    return statusColors[status as keyof typeof statusColors] || 'bg-gray-100 text-gray-800';
  };

  const getVisitorTypeBadge = (type: string) => {
    const typeColors = {
      customer: 'bg-purple-100 text-purple-800',
      hot_lead: 'bg-red-100 text-red-800',
      warm_lead: 'bg-orange-100 text-orange-800',
      returning_visitor: 'bg-blue-100 text-blue-800',
      new_visitor: 'bg-gray-100 text-gray-800',
    };
    return typeColors[type as keyof typeof typeColors] || 'bg-gray-100 text-gray-800';
  };

  const formatDuration = (milliseconds: number) => {
    const minutes = Math.floor(milliseconds / 60000);
    const seconds = Math.floor((milliseconds % 60000) / 1000);
    return `${minutes}m ${seconds}s`;
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const getLastPageVisited = (visitor: Visitor) => {
    if (!visitor.pageVisits || visitor.pageVisits.length === 0) return 'N/A';

    const lastPage = visitor.pageVisits[visitor.pageVisits.length - 1];
    return lastPage.pageName || lastPage.path || 'Unknown';
  };

  const getAllVisitedPages = (visitor: Visitor) => {
    if (!visitor.pageVisits) return [];

    return visitor.pageVisits
      .map(page => ({
        path: page.path,
        pageName: page.pageName || 'Unknown Page',
        timestamp: page.timestamp,
        duration: page.duration,
        sessionId: page.sessionId
      }))
      .sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());
  };

  const handleShowPages = (visitor: Visitor) => {
    setSelectedVisitor(visitor);
    setShowPagesModal(true);
  };

  if (loading) {
    return (
      <div className="bg-brand-grey-900 border border-brand-grey-700 rounded-xl p-6">
        <div className="flex items-center justify-center h-32">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-brand-grey-900 border border-brand-grey-700 rounded-xl p-6">
        <div className="text-red-500 text-center">{error}</div>
      </div>
    );
  }

  return (
    <div className="bg-brand-grey-900 border border-brand-grey-700 rounded-xl p-6">
      <div className="flex justify-between items-center mb-6">
        <h3 className="text-lg font-bold text-brand-white">Visitor Analytics</h3>
        
        {/* Filters */}
        <div className="flex gap-4">
          <select
            value={filterStatus}
            onChange={(e) => setFilterStatus(e.target.value)}
            className="bg-brand-grey-800 text-brand-white border border-brand-grey-600 rounded px-3 py-1 text-sm"
          >
            <option value="">All Statuses</option>
            <option value="new">New</option>
            <option value="in_progress">In Progress</option>
            <option value="qualified">Qualified</option>
            <option value="converted">Converted</option>
            <option value="lost">Lost</option>
          </select>
          
          <select
            value={filterDomain}
            onChange={(e) => setFilterDomain(e.target.value)}
            className="bg-brand-grey-800 text-brand-white border border-brand-grey-600 rounded px-3 py-1 text-sm"
          >
            <option value="">All Domains</option>
            <option value="technology">Technology</option>
            <option value="healthcare">Healthcare</option>
            <option value="finance">Finance</option>
            <option value="education">Education</option>
            <option value="retail">Retail</option>
            <option value="other">Other</option>
          </select>
        </div>
      </div>

      <div className="overflow-x-auto bg-brand-grey-900 rounded-xl border border-brand-grey-700">
        <table className="min-w-full table-auto">
          <thead className="bg-brand-grey-800 sticky top-0 z-10">
            <tr className="text-brand-white text-left border-b-2 border-brand-purple-500">
              <th className="py-4 px-6 font-semibold text-sm">
                <div className="text-brand-purple-300">Visitor Identity</div>
                <div className="text-xs text-brand-grey-400 font-normal">IP & Location</div>
              </th>
              <th className="py-4 px-6 font-semibold text-sm">
                <div className="text-brand-blue-300">Visit Information</div>
                <div className="text-xs text-brand-grey-400 font-normal">Time & Pages</div>
              </th>
              <th
                className="py-4 px-6 font-semibold text-sm cursor-pointer hover:bg-brand-grey-700 transition-colors rounded"
                onClick={() => handleSort('leadScore')}
              >
                <div className="text-brand-yellow-300">Lead Score {sortBy === 'leadScore' && (sortOrder === 'asc' ? '↑' : '↓')}</div>
                <div className="text-xs text-brand-grey-400 font-normal">Quality Rating</div>
              </th>
              <th className="py-4 px-6 font-semibold text-sm">
                <div className="text-brand-green-300">Lead Status</div>
                <div className="text-xs text-brand-grey-400 font-normal">Classification</div>
              </th>
              <th className="py-4 px-6 font-semibold text-sm">
                <div className="text-brand-orange-300">Business Profile</div>
                <div className="text-xs text-brand-grey-400 font-normal">Domain & Segment</div>
              </th>
              <th
                className="py-4 px-6 font-semibold text-sm cursor-pointer hover:bg-brand-grey-700 transition-colors rounded"
                onClick={() => handleSort('totalVisits')}
              >
                <div className="text-brand-cyan-300">Activity Stats {sortBy === 'totalVisits' && (sortOrder === 'asc' ? '↑' : '↓')}</div>
                <div className="text-xs text-brand-grey-400 font-normal">Visits & Engagement</div>
              </th>
              <th className="py-4 px-6 font-semibold text-sm">
                <div className="text-brand-pink-300">User Account</div>
                <div className="text-xs text-brand-grey-400 font-normal">Registration Status</div>
              </th>
            </tr>
          </thead>
          <tbody>
            {(!visitors || visitors.length === 0) ? (
              <tr>
                <td colSpan={7} className="py-12 text-center text-brand-grey-400">
                  <div className="space-y-2">
                    <div className="text-lg">{loading ? 'Loading visitors...' : 'No visitors found'}</div>
                    {!loading && (
                      <div className="text-sm">Visitor data will appear here once users start browsing your site.</div>
                    )}
                  </div>
                </td>
              </tr>
            ) : (
              visitors.map((visitor) => {
                // Safety check for visitor object
                if (!visitor || typeof visitor !== 'object') {
                  console.error('Invalid visitor object:', visitor);
                  return null;
                }

                return (
                <tr key={visitor._id} className="border-b border-brand-grey-700 hover:bg-brand-grey-800/50 transition-colors">
                  {/* Visitor Identity */}
                  <td className="py-4 px-6">
                    <div className="space-y-1">
                      <div className="text-brand-white font-mono text-sm">{visitor.ipAddress}</div>
                      <div className="text-brand-grey-400 text-xs">
                        {visitor.location.region && visitor.location.country
                          ? `${visitor.location.region}, ${visitor.location.country}`
                          : visitor.location.country || 'Unknown Location'
                        }
                      </div>
                    </div>
                  </td>

                  {/* Visit Information */}
                  <td className="py-4 px-6">
                    <div className="space-y-2">
                      <div className="text-brand-white text-sm">{formatDate(visitor.lastVisit)}</div>
                      <div className="text-brand-grey-400 text-xs">Last: {getLastPageVisited(visitor)}</div>
                      <button
                        onClick={() => handleShowPages(visitor)}
                        className="bg-brand-blue-600 hover:bg-brand-blue-700 text-white px-3 py-1 rounded text-xs font-medium transition-colors"
                      >
                        View Pages ({visitor.totalPageViews || 0})
                      </button>
                    </div>
                  </td>
                  {/* Lead Score */}
                  <td className="py-4 px-6">
                    <div className="space-y-2">
                      <div className="flex items-center space-x-3">
                        <div className="w-20 bg-brand-grey-700 rounded-full h-3">
                          <div
                            className={`h-3 rounded-full transition-all duration-300 ${
                              visitor.leadScore >= 80 ? 'bg-green-500' :
                              visitor.leadScore >= 60 ? 'bg-yellow-500' :
                              visitor.leadScore >= 40 ? 'bg-orange-500' : 'bg-red-500'
                            }`}
                            style={{ width: `${Math.min(visitor.leadScore, 100)}%` }}
                          ></div>
                        </div>
                        <span className="text-brand-white text-lg font-bold">{visitor.leadScore}</span>
                      </div>
                      <div className="text-xs text-brand-grey-400">
                        {visitor.leadScore >= 80 ? 'Excellent' :
                         visitor.leadScore >= 60 ? 'Good' :
                         visitor.leadScore >= 40 ? 'Fair' : 'Low'}
                      </div>
                    </div>
                  </td>
                  {/* Lead Status */}
                  <td className="py-4 px-6">
                    <div className="space-y-2">
                      <span className={`px-3 py-1 rounded-full text-sm font-medium ${getStatusBadge(visitor.leadStatus)}`}>
                        {visitor.leadStatus.replace('_', ' ')}
                      </span>
                      <div className="text-xs">
                        <span className={`px-2 py-1 rounded text-xs ${getVisitorTypeBadge(visitor.visitorType)}`}>
                          {visitor.visitorType.replace('_', ' ')}
                        </span>
                      </div>
                    </div>
                  </td>

                  {/* Business Profile */}
                  <td className="py-4 px-6">
                    <div className="space-y-2">
                      <div className="text-brand-white text-sm font-medium">
                        <span className="px-3 py-1 bg-brand-cyan-900 text-brand-cyan-300 rounded-lg text-xs">
                          {visitor.businessDomain || 'Not specified'}
                        </span>
                      </div>
                      <div className="text-brand-grey-400 text-xs">
                        <span className="px-3 py-1 bg-brand-emerald-900 text-brand-emerald-300 rounded-lg text-xs">
                          {visitor.clientSegment || 'Not specified'}
                        </span>
                      </div>
                    </div>
                  </td>
                  {/* Activity Stats */}
                  <td className="py-4 px-6">
                    <div className="space-y-3">
                      <div className="flex items-center space-x-4">
                        <div className="text-center">
                          <div className="text-brand-white text-lg font-bold">{visitor.totalVisits}</div>
                          <div className="text-xs text-brand-grey-400">Visits</div>
                        </div>
                        <div className="text-center">
                          <div className="text-brand-blue-300 text-sm font-medium">{visitor.totalPageViews || 0}</div>
                          <div className="text-xs text-brand-grey-400">Pages</div>
                        </div>
                        <div className="text-center">
                          <div className="text-brand-grey-300 text-sm">{visitor.totalClicks || 0}</div>
                          <div className="text-xs text-brand-grey-400">Clicks</div>
                        </div>
                      </div>

                      <div className="flex items-center space-x-4">
                        <div
                          className="text-center cursor-pointer hover:bg-brand-purple-900/20 p-2 rounded transition-colors"
                          onClick={() => {
                            setSelectedVisitor(visitor);
                            setShowQuotesModal(true);
                          }}
                          title="Click to view quote requests"
                        >
                          <div className="text-brand-purple-300 text-sm font-bold">{visitor.totalQuoteRequests || 0}</div>
                          <div className="text-xs text-brand-purple-400">Quotes</div>
                        </div>
                        <div
                          className="text-center cursor-pointer hover:bg-brand-green-900/20 p-2 rounded transition-colors"
                          onClick={() => {
                            setSelectedVisitor(visitor);
                            setShowCartModal(true);
                          }}
                          title="Click to view cart actions"
                        >
                          <div className="text-brand-green-300 text-sm font-bold">{visitor.totalCartActions || 0}</div>
                          <div className="text-xs text-brand-green-400">Cart</div>
                        </div>
                      </div>

                      <div className="text-xs text-brand-grey-400 text-center">
                        Avg: {formatDuration(visitor.totalTimeSpent / (visitor.totalVisits || 1))}
                      </div>
                    </div>
                  </td>

                  {/* User Account */}
                  <td className="py-4 px-6">
                    <div className="text-center">
                      {visitor.visitorName && visitor.visitorEmail ? (
                        <div className="space-y-1">
                          <div className="text-brand-white font-medium text-sm">{visitor.visitorName}</div>
                          <div className="text-brand-grey-400 text-xs">{visitor.visitorEmail}</div>
                          <div className="inline-block px-2 py-1 bg-brand-green-900 text-brand-green-300 rounded-full text-xs">
                            Registered
                          </div>
                        </div>
                      ) : (
                        <div className="space-y-1">
                          <div className="text-brand-grey-500 text-sm">Anonymous</div>
                          <div className="inline-block px-2 py-1 bg-brand-grey-700 text-brand-grey-400 rounded-full text-xs">
                            Guest
                          </div>
                        </div>
                      )}
                    </div>
                  </td>
              </tr>
              );
            }))}
          </tbody>
        </table>
      </div>

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="flex justify-center items-center mt-6 space-x-2">
          <button
            onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
            disabled={currentPage === 1}
            className="px-3 py-1 bg-brand-grey-800 text-brand-white rounded disabled:opacity-50"
          >
            Previous
          </button>
          <span className="text-brand-grey-300">
            Page {currentPage} of {totalPages}
          </span>
          <button
            onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
            disabled={currentPage === totalPages}
            className="px-3 py-1 bg-brand-grey-800 text-brand-white rounded disabled:opacity-50"
          >
            Next
          </button>
        </div>
      )}

      {/* Pages Modal */}
      {showPagesModal && selectedVisitor && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-brand-grey-900 border border-brand-grey-700 rounded-xl p-6 max-w-4xl w-full mx-4 max-h-[80vh] overflow-hidden">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-lg font-bold text-brand-white">
                Pages Visited - {selectedVisitor.visitorName || selectedVisitor.ipAddress}
              </h3>
              <button
                onClick={() => setShowPagesModal(false)}
                className="text-brand-grey-400 hover:text-brand-white text-xl"
              >
                ×
              </button>
            </div>

            <div className="mb-4 text-brand-grey-300 text-sm">
              <div>Total Visits: {selectedVisitor.totalVisits}</div>
              <div>Total Pages Viewed: {selectedVisitor.totalPageViews || 0}</div>
              <div>User: {selectedVisitor.userId ? `${selectedVisitor.userId.name} (${selectedVisitor.userId.email})` : 'Anonymous'}</div>
            </div>

            <div className="overflow-y-auto max-h-96">
              <table className="min-w-full">
                <thead className="sticky top-0 bg-brand-grey-900">
                  <tr className="text-brand-white text-left border-b border-brand-grey-700">
                    <th className="py-2 px-3">Date & Time</th>
                    <th className="py-2 px-3">Page</th>
                    <th className="py-2 px-3">Path</th>
                    <th className="py-2 px-3">Duration</th>
                  </tr>
                </thead>
                <tbody>
                  {getAllVisitedPages(selectedVisitor).map((page, index) => (
                    <tr key={index} className="border-b border-brand-grey-700 hover:bg-brand-grey-800">
                      <td className="py-2 px-3 text-brand-grey-300 text-sm">
                        {formatDate(page.timestamp)}
                      </td>
                      <td className="py-2 px-3 text-brand-white text-sm">
                        {page.pageName || 'Unknown Page'}
                      </td>
                      <td className="py-2 px-3 text-brand-grey-300 text-sm font-mono">
                        {page.path}
                      </td>
                      <td className="py-2 px-3 text-brand-grey-300 text-sm">
                        {page.duration ? formatDuration(page.duration) : 'N/A'}
                      </td>
                    </tr>
                  ))}
                  {getAllVisitedPages(selectedVisitor).length === 0 && (
                    <tr>
                      <td colSpan={4} className="py-4 px-3 text-center text-brand-grey-500">
                        No page data available
                      </td>
                    </tr>
                  )}
                </tbody>
              </table>
            </div>

            <div className="mt-4 flex justify-end">
              <button
                onClick={() => setShowPagesModal(false)}
                className="bg-brand-grey-800 hover:bg-brand-grey-700 text-brand-white px-4 py-2 rounded"
              >
                Close
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Quote Requests Modal */}
      {showQuotesModal && selectedVisitor && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-brand-grey-900 border border-brand-grey-700 rounded-xl p-6 max-w-4xl w-full mx-4 max-h-[80vh] overflow-hidden">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-lg font-bold text-brand-white">
                Quote Requests - {selectedVisitor.visitorName || selectedVisitor.ipAddress}
              </h3>
              <button
                onClick={() => setShowQuotesModal(false)}
                className="text-brand-grey-400 hover:text-brand-white text-xl"
              >
                ×
              </button>
            </div>

            <div className="mb-4 text-brand-grey-300 text-sm">
              <div>Total Quote Requests: {selectedVisitor.totalQuoteRequests || 0}</div>
              <div>User: {selectedVisitor.userId ? `${selectedVisitor.userId.name} (${selectedVisitor.userId.email})` : 'Anonymous'}</div>
              <div>Lead Score: <span className="text-brand-purple-300 font-medium">{selectedVisitor.leadScore}</span></div>
            </div>

            <div className="overflow-y-auto max-h-96">
              {selectedVisitor.quoteRequests && selectedVisitor.quoteRequests.length > 0 ? (
                <table className="min-w-full">
                  <thead className="sticky top-0 bg-brand-grey-900">
                    <tr className="text-brand-white text-left border-b border-brand-grey-700">
                      <th className="py-2 px-3">Date & Time</th>
                      <th className="py-2 px-3">Service</th>
                      <th className="py-2 px-3">Sub-Service</th>
                      <th className="py-2 px-3">Description</th>
                      <th className="py-2 px-3">Page</th>
                    </tr>
                  </thead>
                  <tbody>
                    {selectedVisitor.quoteRequests.map((quote, index) => (
                      <tr key={index} className="border-b border-brand-grey-700 hover:bg-brand-grey-800">
                        <td className="py-2 px-3 text-brand-grey-300 text-sm">
                          {formatDate(quote.timestamp)}
                        </td>
                        <td className="py-2 px-3 text-brand-white text-sm font-medium">
                          {quote.serviceName}
                        </td>
                        <td className="py-2 px-3 text-brand-purple-300 text-sm font-medium">
                          {quote.subServiceName}
                        </td>
                        <td className="py-2 px-3 text-brand-grey-300 text-sm">
                          {quote.subServiceDescription || 'No description'}
                        </td>
                        <td className="py-2 px-3 text-brand-grey-400 text-sm font-mono">
                          {quote.path || 'Unknown'}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              ) : (
                <div className="text-center py-8 text-brand-grey-400">
                  No quote requests found for this visitor.
                </div>
              )}
            </div>

            <div className="mt-4 flex justify-end">
              <button
                onClick={() => setShowQuotesModal(false)}
                className="bg-brand-grey-800 hover:bg-brand-grey-700 text-brand-white px-4 py-2 rounded"
              >
                Close
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Cart Actions Modal */}
      {showCartModal && selectedVisitor && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-brand-grey-900 border border-brand-grey-700 rounded-xl p-6 max-w-4xl w-full mx-4 max-h-[80vh] overflow-hidden">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-lg font-bold text-brand-white">
                Cart Actions - {selectedVisitor.visitorName || selectedVisitor.ipAddress}
              </h3>
              <button
                onClick={() => setShowCartModal(false)}
                className="text-brand-grey-400 hover:text-brand-white text-xl"
              >
                ×
              </button>
            </div>

            <div className="mb-4 text-brand-grey-300 text-sm">
              <div>Total Cart Actions: {selectedVisitor.totalCartActions || 0}</div>
              <div>User: {selectedVisitor.userId ? `${selectedVisitor.userId.name} (${selectedVisitor.userId.email})` : 'Anonymous'}</div>
              <div>Lead Score: <span className="text-brand-purple-300 font-medium">{selectedVisitor.leadScore}</span></div>
            </div>

            <div className="overflow-y-auto max-h-96">
              {selectedVisitor.cartActions && selectedVisitor.cartActions.length > 0 ? (
                <table className="min-w-full">
                  <thead className="sticky top-0 bg-brand-grey-900">
                    <tr className="text-brand-white text-left border-b border-brand-grey-700">
                      <th className="py-2 px-3">Date & Time</th>
                      <th className="py-2 px-3">Action</th>
                      <th className="py-2 px-3">Service</th>
                      <th className="py-2 px-3">Sub-Service</th>
                      <th className="py-2 px-3">Page</th>
                    </tr>
                  </thead>
                  <tbody>
                    {selectedVisitor.cartActions.map((action, index) => (
                      <tr key={index} className="border-b border-brand-grey-700 hover:bg-brand-grey-800">
                        <td className="py-2 px-3 text-brand-grey-300 text-sm">
                          {formatDate(action.timestamp)}
                        </td>
                        <td className="py-2 px-3 text-sm">
                          <span className={`px-2 py-1 rounded text-xs font-medium ${
                            action.action === 'add'
                              ? 'bg-green-500/20 text-green-300'
                              : 'bg-red-500/20 text-red-300'
                          }`}>
                            {action.action.toUpperCase()}
                          </span>
                        </td>
                        <td className="py-2 px-3 text-brand-white text-sm font-medium">
                          {action.serviceName}
                        </td>
                        <td className="py-2 px-3 text-brand-purple-300 text-sm font-medium">
                          {action.subServiceName}
                        </td>
                        <td className="py-2 px-3 text-brand-grey-400 text-sm font-mono">
                          {action.path || 'Unknown'}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              ) : (
                <div className="text-center py-8 text-brand-grey-400">
                  No cart actions found for this visitor.
                </div>
              )}
            </div>

            <div className="mt-4 flex justify-end">
              <button
                onClick={() => setShowCartModal(false)}
                className="bg-brand-grey-800 hover:bg-brand-grey-700 text-brand-white px-4 py-2 rounded"
              >
                Close
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default VisitorAnalyticsTable;
