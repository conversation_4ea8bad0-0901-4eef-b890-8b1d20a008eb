#!/bin/bash

# ClickForYou Deployment Script for VPS
# Run this script on your Hostinger VPS

echo "🚀 Starting ClickForYou deployment..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Configuration
PROJECT_DIR="/var/www/ClickForYou"
DOMAIN="click4u.digital"
NGINX_SITE="clickforyou"

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if running as root
if [[ $EUID -eq 0 ]]; then
   print_error "This script should not be run as root for security reasons"
   exit 1
fi

# Update system packages
print_status "Updating system packages..."
sudo apt update && sudo apt upgrade -y

# Install Node.js if not installed
if ! command -v node &> /dev/null; then
    print_status "Installing Node.js..."
    curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
    sudo apt-get install -y nodejs
else
    print_status "Node.js is already installed: $(node --version)"
fi

# Install PM2 if not installed
if ! command -v pm2 &> /dev/null; then
    print_status "Installing PM2..."
    sudo npm install -g pm2
else
    print_status "PM2 is already installed: $(pm2 --version)"
fi

# Install Nginx if not installed
if ! command -v nginx &> /dev/null; then
    print_status "Installing Nginx..."
    sudo apt install -y nginx
else
    print_status "Nginx is already installed"
fi

# Create project directory if it doesn't exist
if [ ! -d "$PROJECT_DIR" ]; then
    print_status "Creating project directory..."
    sudo mkdir -p $PROJECT_DIR
    sudo chown -R $USER:$USER $PROJECT_DIR
fi

# Navigate to project directory
cd $PROJECT_DIR

# Install backend dependencies
print_status "Installing backend dependencies..."
cd backend
npm install --production

# Install frontend dependencies and build
print_status "Building frontend..."
cd ../Frontend
npm install
npm run build

# Configure Nginx
print_status "Configuring Nginx..."
sudo tee /etc/nginx/sites-available/$NGINX_SITE > /dev/null <<EOF
server {
    listen 80;
    server_name $DOMAIN www.$DOMAIN;
    
    location / {
        root $PROJECT_DIR/Frontend/dist;
        try_files \$uri \$uri/ /index.html;
    }
    
    location /api {
        proxy_pass http://localhost:5000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade \$http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
        proxy_cache_bypass \$http_upgrade;
    }
}
EOF

# Enable Nginx site
if [ ! -L "/etc/nginx/sites-enabled/$NGINX_SITE" ]; then
    sudo ln -s /etc/nginx/sites-available/$NGINX_SITE /etc/nginx/sites-enabled/
fi

# Remove default Nginx site if it exists
if [ -L "/etc/nginx/sites-enabled/default" ]; then
    sudo rm /etc/nginx/sites-enabled/default
fi

# Test Nginx configuration
print_status "Testing Nginx configuration..."
if sudo nginx -t; then
    print_status "Nginx configuration is valid"
    sudo systemctl reload nginx
else
    print_error "Nginx configuration is invalid"
    exit 1
fi

# Start backend with PM2
print_status "Starting backend with PM2..."
cd $PROJECT_DIR/backend

# Stop existing process if running
pm2 delete clickforyou-backend 2>/dev/null || true

# Start new process
pm2 start src/server.js --name "clickforyou-backend"

# Save PM2 configuration
pm2 save

# Setup PM2 startup script
sudo env PATH=$PATH:/usr/bin /usr/lib/node_modules/pm2/bin/pm2 startup systemd -u $USER --hp $HOME

# Install SSL certificate (optional)
read -p "Do you want to install SSL certificate with Let's Encrypt? (y/n): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    print_status "Installing Certbot..."
    sudo apt install -y certbot python3-certbot-nginx
    
    print_status "Obtaining SSL certificate..."
    sudo certbot --nginx -d $DOMAIN -d www.$DOMAIN
fi

# Final status check
print_status "Checking deployment status..."
echo
echo "=== Deployment Status ==="
echo "Backend Status:"
pm2 status
echo
echo "Nginx Status:"
sudo systemctl status nginx --no-pager -l
echo
echo "=== Deployment Complete! ==="
print_status "Your website should be available at: http://$DOMAIN"
print_warning "Don't forget to:"
print_warning "1. Update your domain DNS to point to this server"
print_warning "2. Update Frontend/.env.production with your actual domain"
print_warning "3. Update backend/.env with production settings"
print_warning "4. Test all functionality"

echo
print_status "Useful commands:"
echo "  - Check backend logs: pm2 logs clickforyou-backend"
echo "  - Restart backend: pm2 restart clickforyou-backend"
echo "  - Check Nginx logs: sudo tail -f /var/log/nginx/error.log"
echo "  - Reload Nginx: sudo systemctl reload nginx"
