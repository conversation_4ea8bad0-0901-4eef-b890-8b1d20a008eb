import express from 'express';
import {
  trackVisitor,
  getVisitorAnalytics,
  getVisitorList,
  updateLeadStatus,
  debugVisitorTracking,
  getVisitorTraffic,
  getVisitorCountryStats,
  cleanupOldVisitorData,
  fixVisitorLocations,
  geocodeLocation,
  overrideLocation,
  testIPDetection,
  testUserVisitorLinking,
  forceLinkUserToVisitor,
  linkCurrentUser,
  fixUserRegistrationIP,
  migrateVisitorSchema,
  fixCurrentVisitorRecord,
  testVisitorCreation,
  trackQuoteRequest,
  trackCartAction
} from './visitorController.js';
import { protect } from '../../shared/middleware/auth.js';

const router = express.Router();

// Public routes
router.post('/track', trackVisitor);
router.post('/quote-request', trackQuoteRequest);
router.post('/cart-action', trackCartAction);

// Protected routes (require authentication)
router.get('/analytics', protect, getVisitorAnalytics);
router.get('/list', protect, getVisitorList);
router.get('/traffic', protect, getVisitorTraffic);
router.get('/country-stats', protect, getVisitorCountryStats);
router.get('/geocode', geocodeLocation);
router.put('/:id/update-lead-status', protect, updateLeadStatus);

// Debug routes
router.get('/debug', debugVisitorTracking);
router.delete('/cleanup', cleanupOldVisitorData);
router.get('/fix-locations', fixVisitorLocations);
router.get('/test-ip', testIPDetection);
router.get('/test-linking', testUserVisitorLinking);
router.post('/force-link', protect, forceLinkUserToVisitor);
router.post('/link-current', protect, linkCurrentUser);
router.post('/fix-user-ip', fixUserRegistrationIP);
router.post('/migrate-schema', migrateVisitorSchema);
router.post('/fix-current-visitor', fixCurrentVisitorRecord);
router.post('/test', testVisitorCreation);

export default router;
