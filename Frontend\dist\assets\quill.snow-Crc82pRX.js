import{ax as mt,ay as bt,ab as _t}from"./index-hEW_vQ3f.js";import{v as Ot}from"./isEqual-CATH4cxC.js";var dt={exports:{}};/*!
 * Quill Editor v1.3.7
 * https://quilljs.com/
 * Copyright (c) 2014, <PERSON>
 * Copyright (c) 2013, salesforce.com
 */var Et=dt.exports,pt;function At(){return pt||(pt=1,function(vt,ht){(function(_,d){vt.exports=d()})(typeof self!="undefined"?self:Et,function(){return function(B){var _={};function d(P){if(_[P])return _[P].exports;var w=_[P]={i:P,l:!1,exports:{}};return B[P].call(w.exports,w,w.exports,d),w.l=!0,w.exports}return d.m=B,d.c=_,d.d=function(P,w,k){d.o(P,w)||Object.defineProperty(P,w,{configurable:!1,enumerable:!0,get:k})},d.n=function(P){var w=P&&P.__esModule?function(){return P.default}:function(){return P};return d.d(w,"a",w),w},d.o=function(P,w){return Object.prototype.hasOwnProperty.call(P,w)},d.p="",d(d.s=109)}([function(B,_,d){Object.defineProperty(_,"__esModule",{value:!0});var P=d(17),w=d(18),k=d(19),g=d(45),y=d(46),c=d(47),r=d(48),t=d(49),e=d(12),u=d(32),l=d(33),a=d(31),i=d(1),o={Scope:i.Scope,create:i.create,find:i.find,query:i.query,register:i.register,Container:P.default,Format:w.default,Leaf:k.default,Embed:r.default,Scroll:g.default,Block:c.default,Inline:y.default,Text:t.default,Attributor:{Attribute:e.default,Class:u.default,Style:l.default,Store:a.default}};_.default=o},function(B,_,d){var P=this&&this.__extends||function(){var a=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(i,o){i.__proto__=o}||function(i,o){for(var f in o)o.hasOwnProperty(f)&&(i[f]=o[f])};return function(i,o){a(i,o);function f(){this.constructor=i}i.prototype=o===null?Object.create(o):(f.prototype=o.prototype,new f)}}();Object.defineProperty(_,"__esModule",{value:!0});var w=function(a){P(i,a);function i(o){var f=this;return o="[Parchment] "+o,f=a.call(this,o)||this,f.message=o,f.name=f.constructor.name,f}return i}(Error);_.ParchmentError=w;var k={},g={},y={},c={};_.DATA_KEY="__blot";var r;(function(a){a[a.TYPE=3]="TYPE",a[a.LEVEL=12]="LEVEL",a[a.ATTRIBUTE=13]="ATTRIBUTE",a[a.BLOT=14]="BLOT",a[a.INLINE=7]="INLINE",a[a.BLOCK=11]="BLOCK",a[a.BLOCK_BLOT=10]="BLOCK_BLOT",a[a.INLINE_BLOT=6]="INLINE_BLOT",a[a.BLOCK_ATTRIBUTE=9]="BLOCK_ATTRIBUTE",a[a.INLINE_ATTRIBUTE=5]="INLINE_ATTRIBUTE",a[a.ANY=15]="ANY"})(r=_.Scope||(_.Scope={}));function t(a,i){var o=u(a);if(o==null)throw new w("Unable to create "+a+" blot");var f=o,n=a instanceof Node||a.nodeType===Node.TEXT_NODE?a:f.create(i);return new f(n,i)}_.create=t;function e(a,i){return i===void 0&&(i=!1),a==null?null:a[_.DATA_KEY]!=null?a[_.DATA_KEY].blot:i?e(a.parentNode,i):null}_.find=e;function u(a,i){i===void 0&&(i=r.ANY);var o;if(typeof a=="string")o=c[a]||k[a];else if(a instanceof Text||a.nodeType===Node.TEXT_NODE)o=c.text;else if(typeof a=="number")a&r.LEVEL&r.BLOCK?o=c.block:a&r.LEVEL&r.INLINE&&(o=c.inline);else if(a instanceof HTMLElement){var f=(a.getAttribute("class")||"").split(/\s+/);for(var n in f)if(o=g[f[n]],o)break;o=o||y[a.tagName]}return o==null?null:i&r.LEVEL&o.scope&&i&r.TYPE&o.scope?o:null}_.query=u;function l(){for(var a=[],i=0;i<arguments.length;i++)a[i]=arguments[i];if(a.length>1)return a.map(function(n){return l(n)});var o=a[0];if(typeof o.blotName!="string"&&typeof o.attrName!="string")throw new w("Invalid definition");if(o.blotName==="abstract")throw new w("Cannot register abstract class");if(c[o.blotName||o.attrName]=o,typeof o.keyName=="string")k[o.keyName]=o;else if(o.className!=null&&(g[o.className]=o),o.tagName!=null){Array.isArray(o.tagName)?o.tagName=o.tagName.map(function(n){return n.toUpperCase()}):o.tagName=o.tagName.toUpperCase();var f=Array.isArray(o.tagName)?o.tagName:[o.tagName];f.forEach(function(n){(y[n]==null||o.className==null)&&(y[n]=o)})}return o}_.register=l},function(B,_,d){var P=d(51),w=d(11),k=d(3),g=d(20),y="\0",c=function(r){Array.isArray(r)?this.ops=r:r!=null&&Array.isArray(r.ops)?this.ops=r.ops:this.ops=[]};c.prototype.insert=function(r,t){var e={};return r.length===0?this:(e.insert=r,t!=null&&typeof t=="object"&&Object.keys(t).length>0&&(e.attributes=t),this.push(e))},c.prototype.delete=function(r){return r<=0?this:this.push({delete:r})},c.prototype.retain=function(r,t){if(r<=0)return this;var e={retain:r};return t!=null&&typeof t=="object"&&Object.keys(t).length>0&&(e.attributes=t),this.push(e)},c.prototype.push=function(r){var t=this.ops.length,e=this.ops[t-1];if(r=k(!0,{},r),typeof e=="object"){if(typeof r.delete=="number"&&typeof e.delete=="number")return this.ops[t-1]={delete:e.delete+r.delete},this;if(typeof e.delete=="number"&&r.insert!=null&&(t-=1,e=this.ops[t-1],typeof e!="object"))return this.ops.unshift(r),this;if(w(r.attributes,e.attributes)){if(typeof r.insert=="string"&&typeof e.insert=="string")return this.ops[t-1]={insert:e.insert+r.insert},typeof r.attributes=="object"&&(this.ops[t-1].attributes=r.attributes),this;if(typeof r.retain=="number"&&typeof e.retain=="number")return this.ops[t-1]={retain:e.retain+r.retain},typeof r.attributes=="object"&&(this.ops[t-1].attributes=r.attributes),this}}return t===this.ops.length?this.ops.push(r):this.ops.splice(t,0,r),this},c.prototype.chop=function(){var r=this.ops[this.ops.length-1];return r&&r.retain&&!r.attributes&&this.ops.pop(),this},c.prototype.filter=function(r){return this.ops.filter(r)},c.prototype.forEach=function(r){this.ops.forEach(r)},c.prototype.map=function(r){return this.ops.map(r)},c.prototype.partition=function(r){var t=[],e=[];return this.forEach(function(u){var l=r(u)?t:e;l.push(u)}),[t,e]},c.prototype.reduce=function(r,t){return this.ops.reduce(r,t)},c.prototype.changeLength=function(){return this.reduce(function(r,t){return t.insert?r+g.length(t):t.delete?r-t.delete:r},0)},c.prototype.length=function(){return this.reduce(function(r,t){return r+g.length(t)},0)},c.prototype.slice=function(r,t){r=r||0,typeof t!="number"&&(t=1/0);for(var e=[],u=g.iterator(this.ops),l=0;l<t&&u.hasNext();){var a;l<r?a=u.next(r-l):(a=u.next(t-l),e.push(a)),l+=g.length(a)}return new c(e)},c.prototype.compose=function(r){var t=g.iterator(this.ops),e=g.iterator(r.ops),u=[],l=e.peek();if(l!=null&&typeof l.retain=="number"&&l.attributes==null){for(var a=l.retain;t.peekType()==="insert"&&t.peekLength()<=a;)a-=t.peekLength(),u.push(t.next());l.retain-a>0&&e.next(l.retain-a)}for(var i=new c(u);t.hasNext()||e.hasNext();)if(e.peekType()==="insert")i.push(e.next());else if(t.peekType()==="delete")i.push(t.next());else{var o=Math.min(t.peekLength(),e.peekLength()),f=t.next(o),n=e.next(o);if(typeof n.retain=="number"){var s={};typeof f.retain=="number"?s.retain=o:s.insert=f.insert;var E=g.attributes.compose(f.attributes,n.attributes,typeof f.retain=="number");if(E&&(s.attributes=E),i.push(s),!e.hasNext()&&w(i.ops[i.ops.length-1],s)){var m=new c(t.rest());return i.concat(m).chop()}}else typeof n.delete=="number"&&typeof f.retain=="number"&&i.push(n)}return i.chop()},c.prototype.concat=function(r){var t=new c(this.ops.slice());return r.ops.length>0&&(t.push(r.ops[0]),t.ops=t.ops.concat(r.ops.slice(1))),t},c.prototype.diff=function(r,t){if(this.ops===r.ops)return new c;var e=[this,r].map(function(o){return o.map(function(f){if(f.insert!=null)return typeof f.insert=="string"?f.insert:y;var n=o===r?"on":"with";throw new Error("diff() called "+n+" non-document")}).join("")}),u=new c,l=P(e[0],e[1],t),a=g.iterator(this.ops),i=g.iterator(r.ops);return l.forEach(function(o){for(var f=o[1].length;f>0;){var n=0;switch(o[0]){case P.INSERT:n=Math.min(i.peekLength(),f),u.push(i.next(n));break;case P.DELETE:n=Math.min(f,a.peekLength()),a.next(n),u.delete(n);break;case P.EQUAL:n=Math.min(a.peekLength(),i.peekLength(),f);var s=a.next(n),E=i.next(n);w(s.insert,E.insert)?u.retain(n,g.attributes.diff(s.attributes,E.attributes)):u.push(E).delete(n);break}f-=n}}),u.chop()},c.prototype.eachLine=function(r,t){t=t||`
`;for(var e=g.iterator(this.ops),u=new c,l=0;e.hasNext();){if(e.peekType()!=="insert")return;var a=e.peek(),i=g.length(a)-e.peekLength(),o=typeof a.insert=="string"?a.insert.indexOf(t,i)-i:-1;if(o<0)u.push(e.next());else if(o>0)u.push(e.next(o));else{if(r(u,e.next(1).attributes||{},l)===!1)return;l+=1,u=new c}}u.length()>0&&r(u,{},l)},c.prototype.transform=function(r,t){if(t=!!t,typeof r=="number")return this.transformPosition(r,t);for(var e=g.iterator(this.ops),u=g.iterator(r.ops),l=new c;e.hasNext()||u.hasNext();)if(e.peekType()==="insert"&&(t||u.peekType()!=="insert"))l.retain(g.length(e.next()));else if(u.peekType()==="insert")l.push(u.next());else{var a=Math.min(e.peekLength(),u.peekLength()),i=e.next(a),o=u.next(a);if(i.delete)continue;o.delete?l.push(o):l.retain(a,g.attributes.transform(i.attributes,o.attributes,t))}return l.chop()},c.prototype.transformPosition=function(r,t){t=!!t;for(var e=g.iterator(this.ops),u=0;e.hasNext()&&u<=r;){var l=e.peekLength(),a=e.peekType();if(e.next(),a==="delete"){r-=Math.min(l,r-u);continue}else a==="insert"&&(u<r||!t)&&(r+=l);u+=l}return r},B.exports=c},function(B,_){var d=Object.prototype.hasOwnProperty,P=Object.prototype.toString,w=Object.defineProperty,k=Object.getOwnPropertyDescriptor,g=function(e){return typeof Array.isArray=="function"?Array.isArray(e):P.call(e)==="[object Array]"},y=function(e){if(!e||P.call(e)!=="[object Object]")return!1;var u=d.call(e,"constructor"),l=e.constructor&&e.constructor.prototype&&d.call(e.constructor.prototype,"isPrototypeOf");if(e.constructor&&!u&&!l)return!1;var a;for(a in e);return typeof a=="undefined"||d.call(e,a)},c=function(e,u){w&&u.name==="__proto__"?w(e,u.name,{enumerable:!0,configurable:!0,value:u.newValue,writable:!0}):e[u.name]=u.newValue},r=function(e,u){if(u==="__proto__")if(d.call(e,u)){if(k)return k(e,u).value}else return;return e[u]};B.exports=function t(){var e,u,l,a,i,o,f=arguments[0],n=1,s=arguments.length,E=!1;for(typeof f=="boolean"&&(E=f,f=arguments[1]||{},n=2),(f==null||typeof f!="object"&&typeof f!="function")&&(f={});n<s;++n)if(e=arguments[n],e!=null)for(u in e)l=r(f,u),a=r(e,u),f!==a&&(E&&a&&(y(a)||(i=g(a)))?(i?(i=!1,o=l&&g(l)?l:[]):o=l&&y(l)?l:{},c(f,{name:u,newValue:t(E,o,a)})):typeof a!="undefined"&&c(f,{name:u,newValue:a}));return f}},function(B,_,d){Object.defineProperty(_,"__esModule",{value:!0}),_.default=_.BlockEmbed=_.bubbleFormats=void 0;var P=function(){function h(v,A){for(var T=0;T<A.length;T++){var q=A[T];q.enumerable=q.enumerable||!1,q.configurable=!0,"value"in q&&(q.writable=!0),Object.defineProperty(v,q.key,q)}}return function(v,A,T){return A&&h(v.prototype,A),T&&h(v,T),v}}(),w=function h(v,A,T){v===null&&(v=Function.prototype);var q=Object.getOwnPropertyDescriptor(v,A);if(q===void 0){var D=Object.getPrototypeOf(v);return D===null?void 0:h(D,A,T)}else{if("value"in q)return q.value;var C=q.get;return C===void 0?void 0:C.call(T)}},k=d(3),g=f(k),y=d(2),c=f(y),r=d(0),t=f(r),e=d(16),u=f(e),l=d(6),a=f(l),i=d(7),o=f(i);function f(h){return h&&h.__esModule?h:{default:h}}function n(h,v){if(!(h instanceof v))throw new TypeError("Cannot call a class as a function")}function s(h,v){if(!h)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return v&&(typeof v=="object"||typeof v=="function")?v:h}function E(h,v){if(typeof v!="function"&&v!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof v);h.prototype=Object.create(v&&v.prototype,{constructor:{value:h,enumerable:!1,writable:!0,configurable:!0}}),v&&(Object.setPrototypeOf?Object.setPrototypeOf(h,v):h.__proto__=v)}var m=1,b=function(h){E(v,h);function v(){return n(this,v),s(this,(v.__proto__||Object.getPrototypeOf(v)).apply(this,arguments))}return P(v,[{key:"attach",value:function(){w(v.prototype.__proto__||Object.getPrototypeOf(v.prototype),"attach",this).call(this),this.attributes=new t.default.Attributor.Store(this.domNode)}},{key:"delta",value:function(){return new c.default().insert(this.value(),(0,g.default)(this.formats(),this.attributes.values()))}},{key:"format",value:function(T,q){var D=t.default.query(T,t.default.Scope.BLOCK_ATTRIBUTE);D!=null&&this.attributes.attribute(D,q)}},{key:"formatAt",value:function(T,q,D,C){this.format(D,C)}},{key:"insertAt",value:function(T,q,D){if(typeof q=="string"&&q.endsWith(`
`)){var C=t.default.create(N.blotName);this.parent.insertBefore(C,T===0?this:this.next),C.insertAt(0,q.slice(0,-1))}else w(v.prototype.__proto__||Object.getPrototypeOf(v.prototype),"insertAt",this).call(this,T,q,D)}}]),v}(t.default.Embed);b.scope=t.default.Scope.BLOCK_BLOT;var N=function(h){E(v,h);function v(A){n(this,v);var T=s(this,(v.__proto__||Object.getPrototypeOf(v)).call(this,A));return T.cache={},T}return P(v,[{key:"delta",value:function(){return this.cache.delta==null&&(this.cache.delta=this.descendants(t.default.Leaf).reduce(function(T,q){return q.length()===0?T:T.insert(q.value(),p(q))},new c.default).insert(`
`,p(this))),this.cache.delta}},{key:"deleteAt",value:function(T,q){w(v.prototype.__proto__||Object.getPrototypeOf(v.prototype),"deleteAt",this).call(this,T,q),this.cache={}}},{key:"formatAt",value:function(T,q,D,C){q<=0||(t.default.query(D,t.default.Scope.BLOCK)?T+q===this.length()&&this.format(D,C):w(v.prototype.__proto__||Object.getPrototypeOf(v.prototype),"formatAt",this).call(this,T,Math.min(q,this.length()-T-1),D,C),this.cache={})}},{key:"insertAt",value:function(T,q,D){if(D!=null)return w(v.prototype.__proto__||Object.getPrototypeOf(v.prototype),"insertAt",this).call(this,T,q,D);if(q.length!==0){var C=q.split(`
`),Z=C.shift();Z.length>0&&(T<this.length()-1||this.children.tail==null?w(v.prototype.__proto__||Object.getPrototypeOf(v.prototype),"insertAt",this).call(this,Math.min(T,this.length()-1),Z):this.children.tail.insertAt(this.children.tail.length(),Z),this.cache={});var I=this;C.reduce(function(R,O){return I=I.split(R,!0),I.insertAt(0,O),O.length},T+Z.length)}}},{key:"insertBefore",value:function(T,q){var D=this.children.head;w(v.prototype.__proto__||Object.getPrototypeOf(v.prototype),"insertBefore",this).call(this,T,q),D instanceof u.default&&D.remove(),this.cache={}}},{key:"length",value:function(){return this.cache.length==null&&(this.cache.length=w(v.prototype.__proto__||Object.getPrototypeOf(v.prototype),"length",this).call(this)+m),this.cache.length}},{key:"moveChildren",value:function(T,q){w(v.prototype.__proto__||Object.getPrototypeOf(v.prototype),"moveChildren",this).call(this,T,q),this.cache={}}},{key:"optimize",value:function(T){w(v.prototype.__proto__||Object.getPrototypeOf(v.prototype),"optimize",this).call(this,T),this.cache={}}},{key:"path",value:function(T){return w(v.prototype.__proto__||Object.getPrototypeOf(v.prototype),"path",this).call(this,T,!0)}},{key:"removeChild",value:function(T){w(v.prototype.__proto__||Object.getPrototypeOf(v.prototype),"removeChild",this).call(this,T),this.cache={}}},{key:"split",value:function(T){var q=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1;if(q&&(T===0||T>=this.length()-m)){var D=this.clone();return T===0?(this.parent.insertBefore(D,this),this):(this.parent.insertBefore(D,this.next),D)}else{var C=w(v.prototype.__proto__||Object.getPrototypeOf(v.prototype),"split",this).call(this,T,q);return this.cache={},C}}}]),v}(t.default.Block);N.blotName="block",N.tagName="P",N.defaultChild="break",N.allowedChildren=[a.default,t.default.Embed,o.default];function p(h){var v=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};return h==null||(typeof h.formats=="function"&&(v=(0,g.default)(v,h.formats())),h.parent==null||h.parent.blotName=="scroll"||h.parent.statics.scope!==h.statics.scope)?v:p(h.parent,v)}_.bubbleFormats=p,_.BlockEmbed=b,_.default=N},function(B,_,d){Object.defineProperty(_,"__esModule",{value:!0}),_.default=_.overload=_.expandConfig=void 0;var P=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(I){return typeof I}:function(I){return I&&typeof Symbol=="function"&&I.constructor===Symbol&&I!==Symbol.prototype?"symbol":typeof I},w=function(){function I(R,O){var S=[],L=!0,F=!1,M=void 0;try{for(var x=R[Symbol.iterator](),j;!(L=(j=x.next()).done)&&(S.push(j.value),!(O&&S.length===O));L=!0);}catch(U){F=!0,M=U}finally{try{!L&&x.return&&x.return()}finally{if(F)throw M}}return S}return function(R,O){if(Array.isArray(R))return R;if(Symbol.iterator in Object(R))return I(R,O);throw new TypeError("Invalid attempt to destructure non-iterable instance")}}(),k=function(){function I(R,O){for(var S=0;S<O.length;S++){var L=O[S];L.enumerable=L.enumerable||!1,L.configurable=!0,"value"in L&&(L.writable=!0),Object.defineProperty(R,L.key,L)}}return function(R,O,S){return O&&I(R.prototype,O),S&&I(R,S),R}}();d(50);var g=d(2),y=p(g),c=d(14),r=p(c),t=d(8),e=p(t),u=d(9),l=p(u),a=d(0),i=p(a),o=d(15),f=p(o),n=d(3),s=p(n),E=d(10),m=p(E),b=d(34),N=p(b);function p(I){return I&&I.__esModule?I:{default:I}}function h(I,R,O){return R in I?Object.defineProperty(I,R,{value:O,enumerable:!0,configurable:!0,writable:!0}):I[R]=O,I}function v(I,R){if(!(I instanceof R))throw new TypeError("Cannot call a class as a function")}var A=(0,m.default)("quill"),T=function(){k(I,null,[{key:"debug",value:function(O){O===!0&&(O="log"),m.default.level(O)}},{key:"find",value:function(O){return O.__quill||i.default.find(O)}},{key:"import",value:function(O){return this.imports[O]==null&&A.error("Cannot import "+O+". Are you sure it was registered?"),this.imports[O]}},{key:"register",value:function(O,S){var L=this,F=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!1;if(typeof O!="string"){var M=O.attrName||O.blotName;typeof M=="string"?this.register("formats/"+M,O,S):Object.keys(O).forEach(function(x){L.register(x,O[x],S)})}else this.imports[O]!=null&&!F&&A.warn("Overwriting "+O+" with",S),this.imports[O]=S,(O.startsWith("blots/")||O.startsWith("formats/"))&&S.blotName!=="abstract"?i.default.register(S):O.startsWith("modules")&&typeof S.register=="function"&&S.register()}}]);function I(R){var O=this,S=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};if(v(this,I),this.options=q(R,S),this.container=this.options.container,this.container==null)return A.error("Invalid Quill container",R);this.options.debug&&I.debug(this.options.debug);var L=this.container.innerHTML.trim();this.container.classList.add("ql-container"),this.container.innerHTML="",this.container.__quill=this,this.root=this.addContainer("ql-editor"),this.root.classList.add("ql-blank"),this.root.setAttribute("data-gramm",!1),this.scrollingContainer=this.options.scrollingContainer||this.root,this.emitter=new e.default,this.scroll=i.default.create(this.root,{emitter:this.emitter,whitelist:this.options.formats}),this.editor=new r.default(this.scroll),this.selection=new f.default(this.scroll,this.emitter),this.theme=new this.options.theme(this,this.options),this.keyboard=this.theme.addModule("keyboard"),this.clipboard=this.theme.addModule("clipboard"),this.history=this.theme.addModule("history"),this.theme.init(),this.emitter.on(e.default.events.EDITOR_CHANGE,function(M){M===e.default.events.TEXT_CHANGE&&O.root.classList.toggle("ql-blank",O.editor.isBlank())}),this.emitter.on(e.default.events.SCROLL_UPDATE,function(M,x){var j=O.selection.lastRange,U=j&&j.length===0?j.index:void 0;D.call(O,function(){return O.editor.update(null,x,U)},M)});var F=this.clipboard.convert(`<div class='ql-editor' style="white-space: normal;">`+L+"<p><br></p></div>");this.setContents(F),this.history.clear(),this.options.placeholder&&this.root.setAttribute("data-placeholder",this.options.placeholder),this.options.readOnly&&this.disable()}return k(I,[{key:"addContainer",value:function(O){var S=arguments.length>1&&arguments[1]!==void 0?arguments[1]:null;if(typeof O=="string"){var L=O;O=document.createElement("div"),O.classList.add(L)}return this.container.insertBefore(O,S),O}},{key:"blur",value:function(){this.selection.setRange(null)}},{key:"deleteText",value:function(O,S,L){var F=this,M=C(O,S,L),x=w(M,4);return O=x[0],S=x[1],L=x[3],D.call(this,function(){return F.editor.deleteText(O,S)},L,O,-1*S)}},{key:"disable",value:function(){this.enable(!1)}},{key:"enable",value:function(){var O=arguments.length>0&&arguments[0]!==void 0?arguments[0]:!0;this.scroll.enable(O),this.container.classList.toggle("ql-disabled",!O)}},{key:"focus",value:function(){var O=this.scrollingContainer.scrollTop;this.selection.focus(),this.scrollingContainer.scrollTop=O,this.scrollIntoView()}},{key:"format",value:function(O,S){var L=this,F=arguments.length>2&&arguments[2]!==void 0?arguments[2]:e.default.sources.API;return D.call(this,function(){var M=L.getSelection(!0),x=new y.default;if(M==null)return x;if(i.default.query(O,i.default.Scope.BLOCK))x=L.editor.formatLine(M.index,M.length,h({},O,S));else{if(M.length===0)return L.selection.format(O,S),x;x=L.editor.formatText(M.index,M.length,h({},O,S))}return L.setSelection(M,e.default.sources.SILENT),x},F)}},{key:"formatLine",value:function(O,S,L,F,M){var x=this,j=void 0,U=C(O,S,L,F,M),H=w(U,4);return O=H[0],S=H[1],j=H[2],M=H[3],D.call(this,function(){return x.editor.formatLine(O,S,j)},M,O,0)}},{key:"formatText",value:function(O,S,L,F,M){var x=this,j=void 0,U=C(O,S,L,F,M),H=w(U,4);return O=H[0],S=H[1],j=H[2],M=H[3],D.call(this,function(){return x.editor.formatText(O,S,j)},M,O,0)}},{key:"getBounds",value:function(O){var S=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0,L=void 0;typeof O=="number"?L=this.selection.getBounds(O,S):L=this.selection.getBounds(O.index,O.length);var F=this.container.getBoundingClientRect();return{bottom:L.bottom-F.top,height:L.height,left:L.left-F.left,right:L.right-F.left,top:L.top-F.top,width:L.width}}},{key:"getContents",value:function(){var O=arguments.length>0&&arguments[0]!==void 0?arguments[0]:0,S=arguments.length>1&&arguments[1]!==void 0?arguments[1]:this.getLength()-O,L=C(O,S),F=w(L,2);return O=F[0],S=F[1],this.editor.getContents(O,S)}},{key:"getFormat",value:function(){var O=arguments.length>0&&arguments[0]!==void 0?arguments[0]:this.getSelection(!0),S=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0;return typeof O=="number"?this.editor.getFormat(O,S):this.editor.getFormat(O.index,O.length)}},{key:"getIndex",value:function(O){return O.offset(this.scroll)}},{key:"getLength",value:function(){return this.scroll.length()}},{key:"getLeaf",value:function(O){return this.scroll.leaf(O)}},{key:"getLine",value:function(O){return this.scroll.line(O)}},{key:"getLines",value:function(){var O=arguments.length>0&&arguments[0]!==void 0?arguments[0]:0,S=arguments.length>1&&arguments[1]!==void 0?arguments[1]:Number.MAX_VALUE;return typeof O!="number"?this.scroll.lines(O.index,O.length):this.scroll.lines(O,S)}},{key:"getModule",value:function(O){return this.theme.modules[O]}},{key:"getSelection",value:function(){var O=arguments.length>0&&arguments[0]!==void 0?arguments[0]:!1;return O&&this.focus(),this.update(),this.selection.getRange()[0]}},{key:"getText",value:function(){var O=arguments.length>0&&arguments[0]!==void 0?arguments[0]:0,S=arguments.length>1&&arguments[1]!==void 0?arguments[1]:this.getLength()-O,L=C(O,S),F=w(L,2);return O=F[0],S=F[1],this.editor.getText(O,S)}},{key:"hasFocus",value:function(){return this.selection.hasFocus()}},{key:"insertEmbed",value:function(O,S,L){var F=this,M=arguments.length>3&&arguments[3]!==void 0?arguments[3]:I.sources.API;return D.call(this,function(){return F.editor.insertEmbed(O,S,L)},M,O)}},{key:"insertText",value:function(O,S,L,F,M){var x=this,j=void 0,U=C(O,0,L,F,M),H=w(U,4);return O=H[0],j=H[2],M=H[3],D.call(this,function(){return x.editor.insertText(O,S,j)},M,O,S.length)}},{key:"isEnabled",value:function(){return!this.container.classList.contains("ql-disabled")}},{key:"off",value:function(){return this.emitter.off.apply(this.emitter,arguments)}},{key:"on",value:function(){return this.emitter.on.apply(this.emitter,arguments)}},{key:"once",value:function(){return this.emitter.once.apply(this.emitter,arguments)}},{key:"pasteHTML",value:function(O,S,L){this.clipboard.dangerouslyPasteHTML(O,S,L)}},{key:"removeFormat",value:function(O,S,L){var F=this,M=C(O,S,L),x=w(M,4);return O=x[0],S=x[1],L=x[3],D.call(this,function(){return F.editor.removeFormat(O,S)},L,O)}},{key:"scrollIntoView",value:function(){this.selection.scrollIntoView(this.scrollingContainer)}},{key:"setContents",value:function(O){var S=this,L=arguments.length>1&&arguments[1]!==void 0?arguments[1]:e.default.sources.API;return D.call(this,function(){O=new y.default(O);var F=S.getLength(),M=S.editor.deleteText(0,F),x=S.editor.applyDelta(O),j=x.ops[x.ops.length-1];j!=null&&typeof j.insert=="string"&&j.insert[j.insert.length-1]===`
`&&(S.editor.deleteText(S.getLength()-1,1),x.delete(1));var U=M.compose(x);return U},L)}},{key:"setSelection",value:function(O,S,L){if(O==null)this.selection.setRange(null,S||I.sources.API);else{var F=C(O,S,L),M=w(F,4);O=M[0],S=M[1],L=M[3],this.selection.setRange(new o.Range(O,S),L),L!==e.default.sources.SILENT&&this.selection.scrollIntoView(this.scrollingContainer)}}},{key:"setText",value:function(O){var S=arguments.length>1&&arguments[1]!==void 0?arguments[1]:e.default.sources.API,L=new y.default().insert(O);return this.setContents(L,S)}},{key:"update",value:function(){var O=arguments.length>0&&arguments[0]!==void 0?arguments[0]:e.default.sources.USER,S=this.scroll.update(O);return this.selection.update(O),S}},{key:"updateContents",value:function(O){var S=this,L=arguments.length>1&&arguments[1]!==void 0?arguments[1]:e.default.sources.API;return D.call(this,function(){return O=new y.default(O),S.editor.applyDelta(O,L)},L,!0)}}]),I}();T.DEFAULTS={bounds:null,formats:null,modules:{},placeholder:"",readOnly:!1,scrollingContainer:null,strict:!0,theme:"default"},T.events=e.default.events,T.sources=e.default.sources,T.version="1.3.7",T.imports={delta:y.default,parchment:i.default,"core/module":l.default,"core/theme":N.default};function q(I,R){if(R=(0,s.default)(!0,{container:I,modules:{clipboard:!0,keyboard:!0,history:!0}},R),!R.theme||R.theme===T.DEFAULTS.theme)R.theme=N.default;else if(R.theme=T.import("themes/"+R.theme),R.theme==null)throw new Error("Invalid theme "+R.theme+". Did you register it?");var O=(0,s.default)(!0,{},R.theme.DEFAULTS);[O,R].forEach(function(F){F.modules=F.modules||{},Object.keys(F.modules).forEach(function(M){F.modules[M]===!0&&(F.modules[M]={})})});var S=Object.keys(O.modules).concat(Object.keys(R.modules)),L=S.reduce(function(F,M){var x=T.import("modules/"+M);return x==null?A.error("Cannot load "+M+" module. Are you sure you registered it?"):F[M]=x.DEFAULTS||{},F},{});return R.modules!=null&&R.modules.toolbar&&R.modules.toolbar.constructor!==Object&&(R.modules.toolbar={container:R.modules.toolbar}),R=(0,s.default)(!0,{},T.DEFAULTS,{modules:L},O,R),["bounds","container","scrollingContainer"].forEach(function(F){typeof R[F]=="string"&&(R[F]=document.querySelector(R[F]))}),R.modules=Object.keys(R.modules).reduce(function(F,M){return R.modules[M]&&(F[M]=R.modules[M]),F},{}),R}function D(I,R,O,S){if(this.options.strict&&!this.isEnabled()&&R===e.default.sources.USER)return new y.default;var L=O==null?null:this.getSelection(),F=this.editor.delta,M=I();if(L!=null&&(O===!0&&(O=L.index),S==null?L=Z(L,M,R):S!==0&&(L=Z(L,O,S,R)),this.setSelection(L,e.default.sources.SILENT)),M.length()>0){var x,j=[e.default.events.TEXT_CHANGE,M,F,R];if((x=this.emitter).emit.apply(x,[e.default.events.EDITOR_CHANGE].concat(j)),R!==e.default.sources.SILENT){var U;(U=this.emitter).emit.apply(U,j)}}return M}function C(I,R,O,S,L){var F={};return typeof I.index=="number"&&typeof I.length=="number"?typeof R!="number"?(L=S,S=O,O=R,R=I.length,I=I.index):(R=I.length,I=I.index):typeof R!="number"&&(L=S,S=O,O=R,R=0),(typeof O=="undefined"?"undefined":P(O))==="object"?(F=O,L=S):typeof O=="string"&&(S!=null?F[O]=S:L=O),L=L||e.default.sources.API,[I,R,F,L]}function Z(I,R,O,S){if(I==null)return null;var L=void 0,F=void 0;if(R instanceof y.default){var M=[I.index,I.index+I.length].map(function(H){return R.transformPosition(H,S!==e.default.sources.USER)}),x=w(M,2);L=x[0],F=x[1]}else{var j=[I.index,I.index+I.length].map(function(H){return H<R||H===R&&S===e.default.sources.USER?H:O>=0?H+O:Math.max(R,H+O)}),U=w(j,2);L=U[0],F=U[1]}return new o.Range(L,F-L)}_.expandConfig=q,_.overload=C,_.default=T},function(B,_,d){Object.defineProperty(_,"__esModule",{value:!0});var P=function(){function a(i,o){for(var f=0;f<o.length;f++){var n=o[f];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(i,n.key,n)}}return function(i,o,f){return o&&a(i.prototype,o),f&&a(i,f),i}}(),w=function a(i,o,f){i===null&&(i=Function.prototype);var n=Object.getOwnPropertyDescriptor(i,o);if(n===void 0){var s=Object.getPrototypeOf(i);return s===null?void 0:a(s,o,f)}else{if("value"in n)return n.value;var E=n.get;return E===void 0?void 0:E.call(f)}},k=d(7),g=r(k),y=d(0),c=r(y);function r(a){return a&&a.__esModule?a:{default:a}}function t(a,i){if(!(a instanceof i))throw new TypeError("Cannot call a class as a function")}function e(a,i){if(!a)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return i&&(typeof i=="object"||typeof i=="function")?i:a}function u(a,i){if(typeof i!="function"&&i!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof i);a.prototype=Object.create(i&&i.prototype,{constructor:{value:a,enumerable:!1,writable:!0,configurable:!0}}),i&&(Object.setPrototypeOf?Object.setPrototypeOf(a,i):a.__proto__=i)}var l=function(a){u(i,a);function i(){return t(this,i),e(this,(i.__proto__||Object.getPrototypeOf(i)).apply(this,arguments))}return P(i,[{key:"formatAt",value:function(f,n,s,E){if(i.compare(this.statics.blotName,s)<0&&c.default.query(s,c.default.Scope.BLOT)){var m=this.isolate(f,n);E&&m.wrap(s,E)}else w(i.prototype.__proto__||Object.getPrototypeOf(i.prototype),"formatAt",this).call(this,f,n,s,E)}},{key:"optimize",value:function(f){if(w(i.prototype.__proto__||Object.getPrototypeOf(i.prototype),"optimize",this).call(this,f),this.parent instanceof i&&i.compare(this.statics.blotName,this.parent.statics.blotName)>0){var n=this.parent.isolate(this.offset(),this.length());this.moveChildren(n),n.wrap(this)}}}],[{key:"compare",value:function(f,n){var s=i.order.indexOf(f),E=i.order.indexOf(n);return s>=0||E>=0?s-E:f===n?0:f<n?-1:1}}]),i}(c.default.Inline);l.allowedChildren=[l,c.default.Embed,g.default],l.order=["cursor","inline","underline","strike","italic","bold","script","link","code"],_.default=l},function(B,_,d){Object.defineProperty(_,"__esModule",{value:!0});var P=d(0),w=k(P);function k(t){return t&&t.__esModule?t:{default:t}}function g(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function y(t,e){if(!t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e&&(typeof e=="object"||typeof e=="function")?e:t}function c(t,e){if(typeof e!="function"&&e!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}var r=function(t){c(e,t);function e(){return g(this,e),y(this,(e.__proto__||Object.getPrototypeOf(e)).apply(this,arguments))}return e}(w.default.Text);_.default=r},function(B,_,d){Object.defineProperty(_,"__esModule",{value:!0});var P=function(){function o(f,n){for(var s=0;s<n.length;s++){var E=n[s];E.enumerable=E.enumerable||!1,E.configurable=!0,"value"in E&&(E.writable=!0),Object.defineProperty(f,E.key,E)}}return function(f,n,s){return n&&o(f.prototype,n),s&&o(f,s),f}}(),w=function o(f,n,s){f===null&&(f=Function.prototype);var E=Object.getOwnPropertyDescriptor(f,n);if(E===void 0){var m=Object.getPrototypeOf(f);return m===null?void 0:o(m,n,s)}else{if("value"in E)return E.value;var b=E.get;return b===void 0?void 0:b.call(s)}},k=d(54),g=r(k),y=d(10),c=r(y);function r(o){return o&&o.__esModule?o:{default:o}}function t(o,f){if(!(o instanceof f))throw new TypeError("Cannot call a class as a function")}function e(o,f){if(!o)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return f&&(typeof f=="object"||typeof f=="function")?f:o}function u(o,f){if(typeof f!="function"&&f!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof f);o.prototype=Object.create(f&&f.prototype,{constructor:{value:o,enumerable:!1,writable:!0,configurable:!0}}),f&&(Object.setPrototypeOf?Object.setPrototypeOf(o,f):o.__proto__=f)}var l=(0,c.default)("quill:events"),a=["selectionchange","mousedown","mouseup","click"];a.forEach(function(o){document.addEventListener(o,function(){for(var f=arguments.length,n=Array(f),s=0;s<f;s++)n[s]=arguments[s];[].slice.call(document.querySelectorAll(".ql-container")).forEach(function(E){if(E.__quill&&E.__quill.emitter){var m;(m=E.__quill.emitter).handleDOM.apply(m,n)}})})});var i=function(o){u(f,o);function f(){t(this,f);var n=e(this,(f.__proto__||Object.getPrototypeOf(f)).call(this));return n.listeners={},n.on("error",l.error),n}return P(f,[{key:"emit",value:function(){l.log.apply(l,arguments),w(f.prototype.__proto__||Object.getPrototypeOf(f.prototype),"emit",this).apply(this,arguments)}},{key:"handleDOM",value:function(s){for(var E=arguments.length,m=Array(E>1?E-1:0),b=1;b<E;b++)m[b-1]=arguments[b];(this.listeners[s.type]||[]).forEach(function(N){var p=N.node,h=N.handler;(s.target===p||p.contains(s.target))&&h.apply(void 0,[s].concat(m))})}},{key:"listenDOM",value:function(s,E,m){this.listeners[s]||(this.listeners[s]=[]),this.listeners[s].push({node:E,handler:m})}}]),f}(g.default);i.events={EDITOR_CHANGE:"editor-change",SCROLL_BEFORE_UPDATE:"scroll-before-update",SCROLL_OPTIMIZE:"scroll-optimize",SCROLL_UPDATE:"scroll-update",SELECTION_CHANGE:"selection-change",TEXT_CHANGE:"text-change"},i.sources={API:"api",SILENT:"silent",USER:"user"},_.default=i},function(B,_,d){Object.defineProperty(_,"__esModule",{value:!0});function P(k,g){if(!(k instanceof g))throw new TypeError("Cannot call a class as a function")}var w=function k(g){var y=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};P(this,k),this.quill=g,this.options=y};w.DEFAULTS={},_.default=w},function(B,_,d){Object.defineProperty(_,"__esModule",{value:!0});var P=["error","warn","log","info"],w="warn";function k(y){if(P.indexOf(y)<=P.indexOf(w)){for(var c,r=arguments.length,t=Array(r>1?r-1:0),e=1;e<r;e++)t[e-1]=arguments[e];(c=console)[y].apply(c,t)}}function g(y){return P.reduce(function(c,r){return c[r]=k.bind(console,r,y),c},{})}k.level=g.level=function(y){w=y},_.default=g},function(B,_,d){var P=Array.prototype.slice,w=d(52),k=d(53),g=B.exports=function(t,e,u){return u||(u={}),t===e?!0:t instanceof Date&&e instanceof Date?t.getTime()===e.getTime():!t||!e||typeof t!="object"&&typeof e!="object"?u.strict?t===e:t==e:r(t,e,u)};function y(t){return t==null}function c(t){return!(!t||typeof t!="object"||typeof t.length!="number"||typeof t.copy!="function"||typeof t.slice!="function"||t.length>0&&typeof t[0]!="number")}function r(t,e,u){var l,a;if(y(t)||y(e)||t.prototype!==e.prototype)return!1;if(k(t))return k(e)?(t=P.call(t),e=P.call(e),g(t,e,u)):!1;if(c(t)){if(!c(e)||t.length!==e.length)return!1;for(l=0;l<t.length;l++)if(t[l]!==e[l])return!1;return!0}try{var i=w(t),o=w(e)}catch(f){return!1}if(i.length!=o.length)return!1;for(i.sort(),o.sort(),l=i.length-1;l>=0;l--)if(i[l]!=o[l])return!1;for(l=i.length-1;l>=0;l--)if(a=i[l],!g(t[a],e[a],u))return!1;return typeof t==typeof e}},function(B,_,d){Object.defineProperty(_,"__esModule",{value:!0});var P=d(1),w=function(){function k(g,y,c){c===void 0&&(c={}),this.attrName=g,this.keyName=y;var r=P.Scope.TYPE&P.Scope.ATTRIBUTE;c.scope!=null?this.scope=c.scope&P.Scope.LEVEL|r:this.scope=P.Scope.ATTRIBUTE,c.whitelist!=null&&(this.whitelist=c.whitelist)}return k.keys=function(g){return[].map.call(g.attributes,function(y){return y.name})},k.prototype.add=function(g,y){return this.canAdd(g,y)?(g.setAttribute(this.keyName,y),!0):!1},k.prototype.canAdd=function(g,y){var c=P.query(g,P.Scope.BLOT&(this.scope|P.Scope.TYPE));return c==null?!1:this.whitelist==null?!0:typeof y=="string"?this.whitelist.indexOf(y.replace(/["']/g,""))>-1:this.whitelist.indexOf(y)>-1},k.prototype.remove=function(g){g.removeAttribute(this.keyName)},k.prototype.value=function(g){var y=g.getAttribute(this.keyName);return this.canAdd(g,y)&&y?y:""},k}();_.default=w},function(B,_,d){Object.defineProperty(_,"__esModule",{value:!0}),_.default=_.Code=void 0;var P=function(){function b(N,p){var h=[],v=!0,A=!1,T=void 0;try{for(var q=N[Symbol.iterator](),D;!(v=(D=q.next()).done)&&(h.push(D.value),!(p&&h.length===p));v=!0);}catch(C){A=!0,T=C}finally{try{!v&&q.return&&q.return()}finally{if(A)throw T}}return h}return function(N,p){if(Array.isArray(N))return N;if(Symbol.iterator in Object(N))return b(N,p);throw new TypeError("Invalid attempt to destructure non-iterable instance")}}(),w=function(){function b(N,p){for(var h=0;h<p.length;h++){var v=p[h];v.enumerable=v.enumerable||!1,v.configurable=!0,"value"in v&&(v.writable=!0),Object.defineProperty(N,v.key,v)}}return function(N,p,h){return p&&b(N.prototype,p),h&&b(N,h),N}}(),k=function b(N,p,h){N===null&&(N=Function.prototype);var v=Object.getOwnPropertyDescriptor(N,p);if(v===void 0){var A=Object.getPrototypeOf(N);return A===null?void 0:b(A,p,h)}else{if("value"in v)return v.value;var T=v.get;return T===void 0?void 0:T.call(h)}},g=d(2),y=o(g),c=d(0),r=o(c),t=d(4),e=o(t),u=d(6),l=o(u),a=d(7),i=o(a);function o(b){return b&&b.__esModule?b:{default:b}}function f(b,N){if(!(b instanceof N))throw new TypeError("Cannot call a class as a function")}function n(b,N){if(!b)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return N&&(typeof N=="object"||typeof N=="function")?N:b}function s(b,N){if(typeof N!="function"&&N!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof N);b.prototype=Object.create(N&&N.prototype,{constructor:{value:b,enumerable:!1,writable:!0,configurable:!0}}),N&&(Object.setPrototypeOf?Object.setPrototypeOf(b,N):b.__proto__=N)}var E=function(b){s(N,b);function N(){return f(this,N),n(this,(N.__proto__||Object.getPrototypeOf(N)).apply(this,arguments))}return N}(l.default);E.blotName="code",E.tagName="CODE";var m=function(b){s(N,b);function N(){return f(this,N),n(this,(N.__proto__||Object.getPrototypeOf(N)).apply(this,arguments))}return w(N,[{key:"delta",value:function(){var h=this,v=this.domNode.textContent;return v.endsWith(`
`)&&(v=v.slice(0,-1)),v.split(`
`).reduce(function(A,T){return A.insert(T).insert(`
`,h.formats())},new y.default)}},{key:"format",value:function(h,v){if(!(h===this.statics.blotName&&v)){var A=this.descendant(i.default,this.length()-1),T=P(A,1),q=T[0];q!=null&&q.deleteAt(q.length()-1,1),k(N.prototype.__proto__||Object.getPrototypeOf(N.prototype),"format",this).call(this,h,v)}}},{key:"formatAt",value:function(h,v,A,T){if(v!==0&&!(r.default.query(A,r.default.Scope.BLOCK)==null||A===this.statics.blotName&&T===this.statics.formats(this.domNode))){var q=this.newlineIndex(h);if(!(q<0||q>=h+v)){var D=this.newlineIndex(h,!0)+1,C=q-D+1,Z=this.isolate(D,C),I=Z.next;Z.format(A,T),I instanceof N&&I.formatAt(0,h-D+v-C,A,T)}}}},{key:"insertAt",value:function(h,v,A){if(A==null){var T=this.descendant(i.default,h),q=P(T,2),D=q[0],C=q[1];D.insertAt(C,v)}}},{key:"length",value:function(){var h=this.domNode.textContent.length;return this.domNode.textContent.endsWith(`
`)?h:h+1}},{key:"newlineIndex",value:function(h){var v=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1;if(v)return this.domNode.textContent.slice(0,h).lastIndexOf(`
`);var A=this.domNode.textContent.slice(h).indexOf(`
`);return A>-1?h+A:-1}},{key:"optimize",value:function(h){this.domNode.textContent.endsWith(`
`)||this.appendChild(r.default.create("text",`
`)),k(N.prototype.__proto__||Object.getPrototypeOf(N.prototype),"optimize",this).call(this,h);var v=this.next;v!=null&&v.prev===this&&v.statics.blotName===this.statics.blotName&&this.statics.formats(this.domNode)===v.statics.formats(v.domNode)&&(v.optimize(h),v.moveChildren(this),v.remove())}},{key:"replace",value:function(h){k(N.prototype.__proto__||Object.getPrototypeOf(N.prototype),"replace",this).call(this,h),[].slice.call(this.domNode.querySelectorAll("*")).forEach(function(v){var A=r.default.find(v);A==null?v.parentNode.removeChild(v):A instanceof r.default.Embed?A.remove():A.unwrap()})}}],[{key:"create",value:function(h){var v=k(N.__proto__||Object.getPrototypeOf(N),"create",this).call(this,h);return v.setAttribute("spellcheck",!1),v}},{key:"formats",value:function(){return!0}}]),N}(e.default);m.blotName="code-block",m.tagName="PRE",m.TAB="  ",_.Code=E,_.default=m},function(B,_,d){Object.defineProperty(_,"__esModule",{value:!0});var P=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(I){return typeof I}:function(I){return I&&typeof Symbol=="function"&&I.constructor===Symbol&&I!==Symbol.prototype?"symbol":typeof I},w=function(){function I(R,O){var S=[],L=!0,F=!1,M=void 0;try{for(var x=R[Symbol.iterator](),j;!(L=(j=x.next()).done)&&(S.push(j.value),!(O&&S.length===O));L=!0);}catch(U){F=!0,M=U}finally{try{!L&&x.return&&x.return()}finally{if(F)throw M}}return S}return function(R,O){if(Array.isArray(R))return R;if(Symbol.iterator in Object(R))return I(R,O);throw new TypeError("Invalid attempt to destructure non-iterable instance")}}(),k=function(){function I(R,O){for(var S=0;S<O.length;S++){var L=O[S];L.enumerable=L.enumerable||!1,L.configurable=!0,"value"in L&&(L.writable=!0),Object.defineProperty(R,L.key,L)}}return function(R,O,S){return O&&I(R.prototype,O),S&&I(R,S),R}}(),g=d(2),y=v(g),c=d(20),r=v(c),t=d(0),e=v(t),u=d(13),l=v(u),a=d(24),i=v(a),o=d(4),f=v(o),n=d(16),s=v(n),E=d(21),m=v(E),b=d(11),N=v(b),p=d(3),h=v(p);function v(I){return I&&I.__esModule?I:{default:I}}function A(I,R,O){return R in I?Object.defineProperty(I,R,{value:O,enumerable:!0,configurable:!0,writable:!0}):I[R]=O,I}function T(I,R){if(!(I instanceof R))throw new TypeError("Cannot call a class as a function")}var q=/^[ -~]*$/,D=function(){function I(R){T(this,I),this.scroll=R,this.delta=this.getDelta()}return k(I,[{key:"applyDelta",value:function(O){var S=this,L=!1;this.scroll.update();var F=this.scroll.length();return this.scroll.batchStart(),O=Z(O),O.reduce(function(M,x){var j=x.retain||x.delete||x.insert.length||1,U=x.attributes||{};if(x.insert!=null){if(typeof x.insert=="string"){var H=x.insert;H.endsWith(`
`)&&L&&(L=!1,H=H.slice(0,-1)),M>=F&&!H.endsWith(`
`)&&(L=!0),S.scroll.insertAt(M,H);var V=S.scroll.line(M),Y=w(V,2),Q=Y[0],X=Y[1],nt=(0,h.default)({},(0,o.bubbleFormats)(Q));if(Q instanceof f.default){var rt=Q.descendant(e.default.Leaf,X),at=w(rt,1),lt=at[0];nt=(0,h.default)(nt,(0,o.bubbleFormats)(lt))}U=r.default.attributes.diff(nt,U)||{}}else if(P(x.insert)==="object"){var K=Object.keys(x.insert)[0];if(K==null)return M;S.scroll.insertAt(M,K,x.insert[K])}F+=j}return Object.keys(U).forEach(function(z){S.scroll.formatAt(M,j,z,U[z])}),M+j},0),O.reduce(function(M,x){return typeof x.delete=="number"?(S.scroll.deleteAt(M,x.delete),M):M+(x.retain||x.insert.length||1)},0),this.scroll.batchEnd(),this.update(O)}},{key:"deleteText",value:function(O,S){return this.scroll.deleteAt(O,S),this.update(new y.default().retain(O).delete(S))}},{key:"formatLine",value:function(O,S){var L=this,F=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{};return this.scroll.update(),Object.keys(F).forEach(function(M){if(!(L.scroll.whitelist!=null&&!L.scroll.whitelist[M])){var x=L.scroll.lines(O,Math.max(S,1)),j=S;x.forEach(function(U){var H=U.length();if(!(U instanceof l.default))U.format(M,F[M]);else{var V=O-U.offset(L.scroll),Y=U.newlineIndex(V+j)-V+1;U.formatAt(V,Y,M,F[M])}j-=H})}}),this.scroll.optimize(),this.update(new y.default().retain(O).retain(S,(0,m.default)(F)))}},{key:"formatText",value:function(O,S){var L=this,F=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{};return Object.keys(F).forEach(function(M){L.scroll.formatAt(O,S,M,F[M])}),this.update(new y.default().retain(O).retain(S,(0,m.default)(F)))}},{key:"getContents",value:function(O,S){return this.delta.slice(O,O+S)}},{key:"getDelta",value:function(){return this.scroll.lines().reduce(function(O,S){return O.concat(S.delta())},new y.default)}},{key:"getFormat",value:function(O){var S=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0,L=[],F=[];S===0?this.scroll.path(O).forEach(function(x){var j=w(x,1),U=j[0];U instanceof f.default?L.push(U):U instanceof e.default.Leaf&&F.push(U)}):(L=this.scroll.lines(O,S),F=this.scroll.descendants(e.default.Leaf,O,S));var M=[L,F].map(function(x){if(x.length===0)return{};for(var j=(0,o.bubbleFormats)(x.shift());Object.keys(j).length>0;){var U=x.shift();if(U==null)return j;j=C((0,o.bubbleFormats)(U),j)}return j});return h.default.apply(h.default,M)}},{key:"getText",value:function(O,S){return this.getContents(O,S).filter(function(L){return typeof L.insert=="string"}).map(function(L){return L.insert}).join("")}},{key:"insertEmbed",value:function(O,S,L){return this.scroll.insertAt(O,S,L),this.update(new y.default().retain(O).insert(A({},S,L)))}},{key:"insertText",value:function(O,S){var L=this,F=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{};return S=S.replace(/\r\n/g,`
`).replace(/\r/g,`
`),this.scroll.insertAt(O,S),Object.keys(F).forEach(function(M){L.scroll.formatAt(O,S.length,M,F[M])}),this.update(new y.default().retain(O).insert(S,(0,m.default)(F)))}},{key:"isBlank",value:function(){if(this.scroll.children.length==0)return!0;if(this.scroll.children.length>1)return!1;var O=this.scroll.children.head;return O.statics.blotName!==f.default.blotName||O.children.length>1?!1:O.children.head instanceof s.default}},{key:"removeFormat",value:function(O,S){var L=this.getText(O,S),F=this.scroll.line(O+S),M=w(F,2),x=M[0],j=M[1],U=0,H=new y.default;x!=null&&(x instanceof l.default?U=x.newlineIndex(j)-j+1:U=x.length()-j,H=x.delta().slice(j,j+U-1).insert(`
`));var V=this.getContents(O,S+U),Y=V.diff(new y.default().insert(L).concat(H)),Q=new y.default().retain(O).concat(Y);return this.applyDelta(Q)}},{key:"update",value:function(O){var S=arguments.length>1&&arguments[1]!==void 0?arguments[1]:[],L=arguments.length>2&&arguments[2]!==void 0?arguments[2]:void 0,F=this.delta;if(S.length===1&&S[0].type==="characterData"&&S[0].target.data.match(q)&&e.default.find(S[0].target)){var M=e.default.find(S[0].target),x=(0,o.bubbleFormats)(M),j=M.offset(this.scroll),U=S[0].oldValue.replace(i.default.CONTENTS,""),H=new y.default().insert(U),V=new y.default().insert(M.value()),Y=new y.default().retain(j).concat(H.diff(V,L));O=Y.reduce(function(Q,X){return X.insert?Q.insert(X.insert,x):Q.push(X)},new y.default),this.delta=F.compose(O)}else this.delta=this.getDelta(),(!O||!(0,N.default)(F.compose(O),this.delta))&&(O=F.diff(this.delta,L));return O}}]),I}();function C(I,R){return Object.keys(R).reduce(function(O,S){return I[S]==null||(R[S]===I[S]?O[S]=R[S]:Array.isArray(R[S])?R[S].indexOf(I[S])<0&&(O[S]=R[S].concat([I[S]])):O[S]=[R[S],I[S]]),O},{})}function Z(I){return I.reduce(function(R,O){if(O.insert===1){var S=(0,m.default)(O.attributes);return delete S.image,R.insert({image:O.attributes.image},S)}if(O.attributes!=null&&(O.attributes.list===!0||O.attributes.bullet===!0)&&(O=(0,m.default)(O),O.attributes.list?O.attributes.list="ordered":(O.attributes.list="bullet",delete O.attributes.bullet)),typeof O.insert=="string"){var L=O.insert.replace(/\r\n/g,`
`).replace(/\r/g,`
`);return R.insert(L,O.attributes)}return R.push(O)},new y.default)}_.default=D},function(B,_,d){Object.defineProperty(_,"__esModule",{value:!0}),_.default=_.Range=void 0;var P=function(){function b(N,p){var h=[],v=!0,A=!1,T=void 0;try{for(var q=N[Symbol.iterator](),D;!(v=(D=q.next()).done)&&(h.push(D.value),!(p&&h.length===p));v=!0);}catch(C){A=!0,T=C}finally{try{!v&&q.return&&q.return()}finally{if(A)throw T}}return h}return function(N,p){if(Array.isArray(N))return N;if(Symbol.iterator in Object(N))return b(N,p);throw new TypeError("Invalid attempt to destructure non-iterable instance")}}(),w=function(){function b(N,p){for(var h=0;h<p.length;h++){var v=p[h];v.enumerable=v.enumerable||!1,v.configurable=!0,"value"in v&&(v.writable=!0),Object.defineProperty(N,v.key,v)}}return function(N,p,h){return p&&b(N.prototype,p),h&&b(N,h),N}}(),k=d(0),g=i(k),y=d(21),c=i(y),r=d(11),t=i(r),e=d(8),u=i(e),l=d(10),a=i(l);function i(b){return b&&b.__esModule?b:{default:b}}function o(b){if(Array.isArray(b)){for(var N=0,p=Array(b.length);N<b.length;N++)p[N]=b[N];return p}else return Array.from(b)}function f(b,N){if(!(b instanceof N))throw new TypeError("Cannot call a class as a function")}var n=(0,a.default)("quill:selection"),s=function b(N){var p=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0;f(this,b),this.index=N,this.length=p},E=function(){function b(N,p){var h=this;f(this,b),this.emitter=p,this.scroll=N,this.composing=!1,this.mouseDown=!1,this.root=this.scroll.domNode,this.cursor=g.default.create("cursor",this),this.lastRange=this.savedRange=new s(0,0),this.handleComposition(),this.handleDragging(),this.emitter.listenDOM("selectionchange",document,function(){h.mouseDown||setTimeout(h.update.bind(h,u.default.sources.USER),1)}),this.emitter.on(u.default.events.EDITOR_CHANGE,function(v,A){v===u.default.events.TEXT_CHANGE&&A.length()>0&&h.update(u.default.sources.SILENT)}),this.emitter.on(u.default.events.SCROLL_BEFORE_UPDATE,function(){if(h.hasFocus()){var v=h.getNativeRange();v!=null&&v.start.node!==h.cursor.textNode&&h.emitter.once(u.default.events.SCROLL_UPDATE,function(){try{h.setNativeRange(v.start.node,v.start.offset,v.end.node,v.end.offset)}catch(A){}})}}),this.emitter.on(u.default.events.SCROLL_OPTIMIZE,function(v,A){if(A.range){var T=A.range,q=T.startNode,D=T.startOffset,C=T.endNode,Z=T.endOffset;h.setNativeRange(q,D,C,Z)}}),this.update(u.default.sources.SILENT)}return w(b,[{key:"handleComposition",value:function(){var p=this;this.root.addEventListener("compositionstart",function(){p.composing=!0}),this.root.addEventListener("compositionend",function(){if(p.composing=!1,p.cursor.parent){var h=p.cursor.restore();if(!h)return;setTimeout(function(){p.setNativeRange(h.startNode,h.startOffset,h.endNode,h.endOffset)},1)}})}},{key:"handleDragging",value:function(){var p=this;this.emitter.listenDOM("mousedown",document.body,function(){p.mouseDown=!0}),this.emitter.listenDOM("mouseup",document.body,function(){p.mouseDown=!1,p.update(u.default.sources.USER)})}},{key:"focus",value:function(){this.hasFocus()||(this.root.focus(),this.setRange(this.savedRange))}},{key:"format",value:function(p,h){if(!(this.scroll.whitelist!=null&&!this.scroll.whitelist[p])){this.scroll.update();var v=this.getNativeRange();if(!(v==null||!v.native.collapsed||g.default.query(p,g.default.Scope.BLOCK))){if(v.start.node!==this.cursor.textNode){var A=g.default.find(v.start.node,!1);if(A==null)return;if(A instanceof g.default.Leaf){var T=A.split(v.start.offset);A.parent.insertBefore(this.cursor,T)}else A.insertBefore(this.cursor,v.start.node);this.cursor.attach()}this.cursor.format(p,h),this.scroll.optimize(),this.setNativeRange(this.cursor.textNode,this.cursor.textNode.data.length),this.update()}}}},{key:"getBounds",value:function(p){var h=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0,v=this.scroll.length();p=Math.min(p,v-1),h=Math.min(p+h,v-1)-p;var A=void 0,T=this.scroll.leaf(p),q=P(T,2),D=q[0],C=q[1];if(D==null)return null;var Z=D.position(C,!0),I=P(Z,2);A=I[0],C=I[1];var R=document.createRange();if(h>0){R.setStart(A,C);var O=this.scroll.leaf(p+h),S=P(O,2);if(D=S[0],C=S[1],D==null)return null;var L=D.position(C,!0),F=P(L,2);return A=F[0],C=F[1],R.setEnd(A,C),R.getBoundingClientRect()}else{var M="left",x=void 0;return A instanceof Text?(C<A.data.length?(R.setStart(A,C),R.setEnd(A,C+1)):(R.setStart(A,C-1),R.setEnd(A,C),M="right"),x=R.getBoundingClientRect()):(x=D.domNode.getBoundingClientRect(),C>0&&(M="right")),{bottom:x.top+x.height,height:x.height,left:x[M],right:x[M],top:x.top,width:0}}}},{key:"getNativeRange",value:function(){var p=document.getSelection();if(p==null||p.rangeCount<=0)return null;var h=p.getRangeAt(0);if(h==null)return null;var v=this.normalizeNative(h);return n.info("getNativeRange",v),v}},{key:"getRange",value:function(){var p=this.getNativeRange();if(p==null)return[null,null];var h=this.normalizedToRange(p);return[h,p]}},{key:"hasFocus",value:function(){return document.activeElement===this.root}},{key:"normalizedToRange",value:function(p){var h=this,v=[[p.start.node,p.start.offset]];p.native.collapsed||v.push([p.end.node,p.end.offset]);var A=v.map(function(D){var C=P(D,2),Z=C[0],I=C[1],R=g.default.find(Z,!0),O=R.offset(h.scroll);return I===0?O:R instanceof g.default.Container?O+R.length():O+R.index(Z,I)}),T=Math.min(Math.max.apply(Math,o(A)),this.scroll.length()-1),q=Math.min.apply(Math,[T].concat(o(A)));return new s(q,T-q)}},{key:"normalizeNative",value:function(p){if(!m(this.root,p.startContainer)||!p.collapsed&&!m(this.root,p.endContainer))return null;var h={start:{node:p.startContainer,offset:p.startOffset},end:{node:p.endContainer,offset:p.endOffset},native:p};return[h.start,h.end].forEach(function(v){for(var A=v.node,T=v.offset;!(A instanceof Text)&&A.childNodes.length>0;)if(A.childNodes.length>T)A=A.childNodes[T],T=0;else if(A.childNodes.length===T)A=A.lastChild,T=A instanceof Text?A.data.length:A.childNodes.length+1;else break;v.node=A,v.offset=T}),h}},{key:"rangeToNative",value:function(p){var h=this,v=p.collapsed?[p.index]:[p.index,p.index+p.length],A=[],T=this.scroll.length();return v.forEach(function(q,D){q=Math.min(T-1,q);var C=void 0,Z=h.scroll.leaf(q),I=P(Z,2),R=I[0],O=I[1],S=R.position(O,D!==0),L=P(S,2);C=L[0],O=L[1],A.push(C,O)}),A.length<2&&(A=A.concat(A)),A}},{key:"scrollIntoView",value:function(p){var h=this.lastRange;if(h!=null){var v=this.getBounds(h.index,h.length);if(v!=null){var A=this.scroll.length()-1,T=this.scroll.line(Math.min(h.index,A)),q=P(T,1),D=q[0],C=D;if(h.length>0){var Z=this.scroll.line(Math.min(h.index+h.length,A)),I=P(Z,1);C=I[0]}if(!(D==null||C==null)){var R=p.getBoundingClientRect();v.top<R.top?p.scrollTop-=R.top-v.top:v.bottom>R.bottom&&(p.scrollTop+=v.bottom-R.bottom)}}}}},{key:"setNativeRange",value:function(p,h){var v=arguments.length>2&&arguments[2]!==void 0?arguments[2]:p,A=arguments.length>3&&arguments[3]!==void 0?arguments[3]:h,T=arguments.length>4&&arguments[4]!==void 0?arguments[4]:!1;if(n.info("setNativeRange",p,h,v,A),!(p!=null&&(this.root.parentNode==null||p.parentNode==null||v.parentNode==null))){var q=document.getSelection();if(q!=null)if(p!=null){this.hasFocus()||this.root.focus();var D=(this.getNativeRange()||{}).native;if(D==null||T||p!==D.startContainer||h!==D.startOffset||v!==D.endContainer||A!==D.endOffset){p.tagName=="BR"&&(h=[].indexOf.call(p.parentNode.childNodes,p),p=p.parentNode),v.tagName=="BR"&&(A=[].indexOf.call(v.parentNode.childNodes,v),v=v.parentNode);var C=document.createRange();C.setStart(p,h),C.setEnd(v,A),q.removeAllRanges(),q.addRange(C)}}else q.removeAllRanges(),this.root.blur(),document.body.focus()}}},{key:"setRange",value:function(p){var h=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1,v=arguments.length>2&&arguments[2]!==void 0?arguments[2]:u.default.sources.API;if(typeof h=="string"&&(v=h,h=!1),n.info("setRange",p),p!=null){var A=this.rangeToNative(p);this.setNativeRange.apply(this,o(A).concat([h]))}else this.setNativeRange(null);this.update(v)}},{key:"update",value:function(){var p=arguments.length>0&&arguments[0]!==void 0?arguments[0]:u.default.sources.USER,h=this.lastRange,v=this.getRange(),A=P(v,2),T=A[0],q=A[1];if(this.lastRange=T,this.lastRange!=null&&(this.savedRange=this.lastRange),!(0,t.default)(h,this.lastRange)){var D;!this.composing&&q!=null&&q.native.collapsed&&q.start.node!==this.cursor.textNode&&this.cursor.restore();var C=[u.default.events.SELECTION_CHANGE,(0,c.default)(this.lastRange),(0,c.default)(h),p];if((D=this.emitter).emit.apply(D,[u.default.events.EDITOR_CHANGE].concat(C)),p!==u.default.sources.SILENT){var Z;(Z=this.emitter).emit.apply(Z,C)}}}}]),b}();function m(b,N){try{N.parentNode}catch(p){return!1}return N instanceof Text&&(N=N.parentNode),b.contains(N)}_.Range=s,_.default=E},function(B,_,d){Object.defineProperty(_,"__esModule",{value:!0});var P=function(){function u(l,a){for(var i=0;i<a.length;i++){var o=a[i];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(l,o.key,o)}}return function(l,a,i){return a&&u(l.prototype,a),i&&u(l,i),l}}(),w=function u(l,a,i){l===null&&(l=Function.prototype);var o=Object.getOwnPropertyDescriptor(l,a);if(o===void 0){var f=Object.getPrototypeOf(l);return f===null?void 0:u(f,a,i)}else{if("value"in o)return o.value;var n=o.get;return n===void 0?void 0:n.call(i)}},k=d(0),g=y(k);function y(u){return u&&u.__esModule?u:{default:u}}function c(u,l){if(!(u instanceof l))throw new TypeError("Cannot call a class as a function")}function r(u,l){if(!u)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return l&&(typeof l=="object"||typeof l=="function")?l:u}function t(u,l){if(typeof l!="function"&&l!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof l);u.prototype=Object.create(l&&l.prototype,{constructor:{value:u,enumerable:!1,writable:!0,configurable:!0}}),l&&(Object.setPrototypeOf?Object.setPrototypeOf(u,l):u.__proto__=l)}var e=function(u){t(l,u);function l(){return c(this,l),r(this,(l.__proto__||Object.getPrototypeOf(l)).apply(this,arguments))}return P(l,[{key:"insertInto",value:function(i,o){i.children.length===0?w(l.prototype.__proto__||Object.getPrototypeOf(l.prototype),"insertInto",this).call(this,i,o):this.remove()}},{key:"length",value:function(){return 0}},{key:"value",value:function(){return""}}],[{key:"value",value:function(){}}]),l}(g.default.Embed);e.blotName="break",e.tagName="BR",_.default=e},function(B,_,d){var P=this&&this.__extends||function(){var r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var u in e)e.hasOwnProperty(u)&&(t[u]=e[u])};return function(t,e){r(t,e);function u(){this.constructor=t}t.prototype=e===null?Object.create(e):(u.prototype=e.prototype,new u)}}();Object.defineProperty(_,"__esModule",{value:!0});var w=d(44),k=d(30),g=d(1),y=function(r){P(t,r);function t(e){var u=r.call(this,e)||this;return u.build(),u}return t.prototype.appendChild=function(e){this.insertBefore(e)},t.prototype.attach=function(){r.prototype.attach.call(this),this.children.forEach(function(e){e.attach()})},t.prototype.build=function(){var e=this;this.children=new w.default,[].slice.call(this.domNode.childNodes).reverse().forEach(function(u){try{var l=c(u);e.insertBefore(l,e.children.head||void 0)}catch(a){if(a instanceof g.ParchmentError)return;throw a}})},t.prototype.deleteAt=function(e,u){if(e===0&&u===this.length())return this.remove();this.children.forEachAt(e,u,function(l,a,i){l.deleteAt(a,i)})},t.prototype.descendant=function(e,u){var l=this.children.find(u),a=l[0],i=l[1];return e.blotName==null&&e(a)||e.blotName!=null&&a instanceof e?[a,i]:a instanceof t?a.descendant(e,i):[null,-1]},t.prototype.descendants=function(e,u,l){u===void 0&&(u=0),l===void 0&&(l=Number.MAX_VALUE);var a=[],i=l;return this.children.forEachAt(u,l,function(o,f,n){(e.blotName==null&&e(o)||e.blotName!=null&&o instanceof e)&&a.push(o),o instanceof t&&(a=a.concat(o.descendants(e,f,i))),i-=n}),a},t.prototype.detach=function(){this.children.forEach(function(e){e.detach()}),r.prototype.detach.call(this)},t.prototype.formatAt=function(e,u,l,a){this.children.forEachAt(e,u,function(i,o,f){i.formatAt(o,f,l,a)})},t.prototype.insertAt=function(e,u,l){var a=this.children.find(e),i=a[0],o=a[1];if(i)i.insertAt(o,u,l);else{var f=l==null?g.create("text",u):g.create(u,l);this.appendChild(f)}},t.prototype.insertBefore=function(e,u){if(this.statics.allowedChildren!=null&&!this.statics.allowedChildren.some(function(l){return e instanceof l}))throw new g.ParchmentError("Cannot insert "+e.statics.blotName+" into "+this.statics.blotName);e.insertInto(this,u)},t.prototype.length=function(){return this.children.reduce(function(e,u){return e+u.length()},0)},t.prototype.moveChildren=function(e,u){this.children.forEach(function(l){e.insertBefore(l,u)})},t.prototype.optimize=function(e){if(r.prototype.optimize.call(this,e),this.children.length===0)if(this.statics.defaultChild!=null){var u=g.create(this.statics.defaultChild);this.appendChild(u),u.optimize(e)}else this.remove()},t.prototype.path=function(e,u){u===void 0&&(u=!1);var l=this.children.find(e,u),a=l[0],i=l[1],o=[[this,e]];return a instanceof t?o.concat(a.path(i,u)):(a!=null&&o.push([a,i]),o)},t.prototype.removeChild=function(e){this.children.remove(e)},t.prototype.replace=function(e){e instanceof t&&e.moveChildren(this),r.prototype.replace.call(this,e)},t.prototype.split=function(e,u){if(u===void 0&&(u=!1),!u){if(e===0)return this;if(e===this.length())return this.next}var l=this.clone();return this.parent.insertBefore(l,this.next),this.children.forEachAt(e,this.length(),function(a,i,o){a=a.split(i,u),l.appendChild(a)}),l},t.prototype.unwrap=function(){this.moveChildren(this.parent,this.next),this.remove()},t.prototype.update=function(e,u){var l=this,a=[],i=[];e.forEach(function(o){o.target===l.domNode&&o.type==="childList"&&(a.push.apply(a,o.addedNodes),i.push.apply(i,o.removedNodes))}),i.forEach(function(o){if(!(o.parentNode!=null&&o.tagName!=="IFRAME"&&document.body.compareDocumentPosition(o)&Node.DOCUMENT_POSITION_CONTAINED_BY)){var f=g.find(o);f!=null&&(f.domNode.parentNode==null||f.domNode.parentNode===l.domNode)&&f.detach()}}),a.filter(function(o){return o.parentNode==l.domNode}).sort(function(o,f){return o===f?0:o.compareDocumentPosition(f)&Node.DOCUMENT_POSITION_FOLLOWING?1:-1}).forEach(function(o){var f=null;o.nextSibling!=null&&(f=g.find(o.nextSibling));var n=c(o);(n.next!=f||n.next==null)&&(n.parent!=null&&n.parent.removeChild(l),l.insertBefore(n,f||void 0))})},t}(k.default);function c(r){var t=g.find(r);if(t==null)try{t=g.create(r)}catch(e){t=g.create(g.Scope.INLINE),[].slice.call(r.childNodes).forEach(function(u){t.domNode.appendChild(u)}),r.parentNode&&r.parentNode.replaceChild(t.domNode,r),t.attach()}return t}_.default=y},function(B,_,d){var P=this&&this.__extends||function(){var r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var u in e)e.hasOwnProperty(u)&&(t[u]=e[u])};return function(t,e){r(t,e);function u(){this.constructor=t}t.prototype=e===null?Object.create(e):(u.prototype=e.prototype,new u)}}();Object.defineProperty(_,"__esModule",{value:!0});var w=d(12),k=d(31),g=d(17),y=d(1),c=function(r){P(t,r);function t(e){var u=r.call(this,e)||this;return u.attributes=new k.default(u.domNode),u}return t.formats=function(e){if(typeof this.tagName=="string")return!0;if(Array.isArray(this.tagName))return e.tagName.toLowerCase()},t.prototype.format=function(e,u){var l=y.query(e);l instanceof w.default?this.attributes.attribute(l,u):u&&l!=null&&(e!==this.statics.blotName||this.formats()[e]!==u)&&this.replaceWith(e,u)},t.prototype.formats=function(){var e=this.attributes.values(),u=this.statics.formats(this.domNode);return u!=null&&(e[this.statics.blotName]=u),e},t.prototype.replaceWith=function(e,u){var l=r.prototype.replaceWith.call(this,e,u);return this.attributes.copy(l),l},t.prototype.update=function(e,u){var l=this;r.prototype.update.call(this,e,u),e.some(function(a){return a.target===l.domNode&&a.type==="attributes"})&&this.attributes.build()},t.prototype.wrap=function(e,u){var l=r.prototype.wrap.call(this,e,u);return l instanceof t&&l.statics.scope===this.statics.scope&&this.attributes.move(l),l},t}(g.default);_.default=c},function(B,_,d){var P=this&&this.__extends||function(){var y=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(c,r){c.__proto__=r}||function(c,r){for(var t in r)r.hasOwnProperty(t)&&(c[t]=r[t])};return function(c,r){y(c,r);function t(){this.constructor=c}c.prototype=r===null?Object.create(r):(t.prototype=r.prototype,new t)}}();Object.defineProperty(_,"__esModule",{value:!0});var w=d(30),k=d(1),g=function(y){P(c,y);function c(){return y!==null&&y.apply(this,arguments)||this}return c.value=function(r){return!0},c.prototype.index=function(r,t){return this.domNode===r||this.domNode.compareDocumentPosition(r)&Node.DOCUMENT_POSITION_CONTAINED_BY?Math.min(t,1):-1},c.prototype.position=function(r,t){var e=[].indexOf.call(this.parent.domNode.childNodes,this.domNode);return r>0&&(e+=1),[this.parent.domNode,e]},c.prototype.value=function(){var r;return r={},r[this.statics.blotName]=this.statics.value(this.domNode)||!0,r},c.scope=k.Scope.INLINE_BLOT,c}(w.default);_.default=g},function(B,_,d){var P=d(11),w=d(3),k={attributes:{compose:function(y,c,r){typeof y!="object"&&(y={}),typeof c!="object"&&(c={});var t=w(!0,{},c);r||(t=Object.keys(t).reduce(function(u,l){return t[l]!=null&&(u[l]=t[l]),u},{}));for(var e in y)y[e]!==void 0&&c[e]===void 0&&(t[e]=y[e]);return Object.keys(t).length>0?t:void 0},diff:function(y,c){typeof y!="object"&&(y={}),typeof c!="object"&&(c={});var r=Object.keys(y).concat(Object.keys(c)).reduce(function(t,e){return P(y[e],c[e])||(t[e]=c[e]===void 0?null:c[e]),t},{});return Object.keys(r).length>0?r:void 0},transform:function(y,c,r){if(typeof y!="object")return c;if(typeof c=="object"){if(!r)return c;var t=Object.keys(c).reduce(function(e,u){return y[u]===void 0&&(e[u]=c[u]),e},{});return Object.keys(t).length>0?t:void 0}}},iterator:function(y){return new g(y)},length:function(y){return typeof y.delete=="number"?y.delete:typeof y.retain=="number"?y.retain:typeof y.insert=="string"?y.insert.length:1}};function g(y){this.ops=y,this.index=0,this.offset=0}g.prototype.hasNext=function(){return this.peekLength()<1/0},g.prototype.next=function(y){y||(y=1/0);var c=this.ops[this.index];if(c){var r=this.offset,t=k.length(c);if(y>=t-r?(y=t-r,this.index+=1,this.offset=0):this.offset+=y,typeof c.delete=="number")return{delete:y};var e={};return c.attributes&&(e.attributes=c.attributes),typeof c.retain=="number"?e.retain=y:typeof c.insert=="string"?e.insert=c.insert.substr(r,y):e.insert=c.insert,e}else return{retain:1/0}},g.prototype.peek=function(){return this.ops[this.index]},g.prototype.peekLength=function(){return this.ops[this.index]?k.length(this.ops[this.index])-this.offset:1/0},g.prototype.peekType=function(){return this.ops[this.index]?typeof this.ops[this.index].delete=="number"?"delete":typeof this.ops[this.index].retain=="number"?"retain":"insert":"retain"},g.prototype.rest=function(){if(this.hasNext()){if(this.offset===0)return this.ops.slice(this.index);var y=this.offset,c=this.index,r=this.next(),t=this.ops.slice(this.index);return this.offset=y,this.index=c,[r].concat(t)}else return[]},B.exports=k},function(B,_){var d=function(){function P(l,a){return a!=null&&l instanceof a}var w;try{w=Map}catch(l){w=function(){}}var k;try{k=Set}catch(l){k=function(){}}var g;try{g=Promise}catch(l){g=function(){}}function y(l,a,i,o,f){typeof a=="object"&&(i=a.depth,o=a.prototype,f=a.includeNonEnumerable,a=a.circular);var n=[],s=[],E=typeof Buffer!="undefined";typeof a=="undefined"&&(a=!0),typeof i=="undefined"&&(i=1/0);function m(b,N){if(b===null)return null;if(N===0)return b;var p,h;if(typeof b!="object")return b;if(P(b,w))p=new w;else if(P(b,k))p=new k;else if(P(b,g))p=new g(function(R,O){b.then(function(S){R(m(S,N-1))},function(S){O(m(S,N-1))})});else if(y.__isArray(b))p=[];else if(y.__isRegExp(b))p=new RegExp(b.source,u(b)),b.lastIndex&&(p.lastIndex=b.lastIndex);else if(y.__isDate(b))p=new Date(b.getTime());else{if(E&&Buffer.isBuffer(b))return Buffer.allocUnsafe?p=Buffer.allocUnsafe(b.length):p=new Buffer(b.length),b.copy(p),p;P(b,Error)?p=Object.create(b):typeof o=="undefined"?(h=Object.getPrototypeOf(b),p=Object.create(h)):(p=Object.create(o),h=o)}if(a){var v=n.indexOf(b);if(v!=-1)return s[v];n.push(b),s.push(p)}P(b,w)&&b.forEach(function(R,O){var S=m(O,N-1),L=m(R,N-1);p.set(S,L)}),P(b,k)&&b.forEach(function(R){var O=m(R,N-1);p.add(O)});for(var A in b){var T;h&&(T=Object.getOwnPropertyDescriptor(h,A)),!(T&&T.set==null)&&(p[A]=m(b[A],N-1))}if(Object.getOwnPropertySymbols)for(var q=Object.getOwnPropertySymbols(b),A=0;A<q.length;A++){var D=q[A],C=Object.getOwnPropertyDescriptor(b,D);C&&!C.enumerable&&!f||(p[D]=m(b[D],N-1),C.enumerable||Object.defineProperty(p,D,{enumerable:!1}))}if(f)for(var Z=Object.getOwnPropertyNames(b),A=0;A<Z.length;A++){var I=Z[A],C=Object.getOwnPropertyDescriptor(b,I);C&&C.enumerable||(p[I]=m(b[I],N-1),Object.defineProperty(p,I,{enumerable:!1}))}return p}return m(l,i)}y.clonePrototype=function(a){if(a===null)return null;var i=function(){};return i.prototype=a,new i};function c(l){return Object.prototype.toString.call(l)}y.__objToStr=c;function r(l){return typeof l=="object"&&c(l)==="[object Date]"}y.__isDate=r;function t(l){return typeof l=="object"&&c(l)==="[object Array]"}y.__isArray=t;function e(l){return typeof l=="object"&&c(l)==="[object RegExp]"}y.__isRegExp=e;function u(l){var a="";return l.global&&(a+="g"),l.ignoreCase&&(a+="i"),l.multiline&&(a+="m"),a}return y.__getRegExpFlags=u,y}();typeof B=="object"&&B.exports&&(B.exports=d)},function(B,_,d){Object.defineProperty(_,"__esModule",{value:!0});var P=function(){function p(h,v){var A=[],T=!0,q=!1,D=void 0;try{for(var C=h[Symbol.iterator](),Z;!(T=(Z=C.next()).done)&&(A.push(Z.value),!(v&&A.length===v));T=!0);}catch(I){q=!0,D=I}finally{try{!T&&C.return&&C.return()}finally{if(q)throw D}}return A}return function(h,v){if(Array.isArray(h))return h;if(Symbol.iterator in Object(h))return p(h,v);throw new TypeError("Invalid attempt to destructure non-iterable instance")}}(),w=function(){function p(h,v){for(var A=0;A<v.length;A++){var T=v[A];T.enumerable=T.enumerable||!1,T.configurable=!0,"value"in T&&(T.writable=!0),Object.defineProperty(h,T.key,T)}}return function(h,v,A){return v&&p(h.prototype,v),A&&p(h,A),h}}(),k=function p(h,v,A){h===null&&(h=Function.prototype);var T=Object.getOwnPropertyDescriptor(h,v);if(T===void 0){var q=Object.getPrototypeOf(h);return q===null?void 0:p(q,v,A)}else{if("value"in T)return T.value;var D=T.get;return D===void 0?void 0:D.call(A)}},g=d(0),y=n(g),c=d(8),r=n(c),t=d(4),e=n(t),u=d(16),l=n(u),a=d(13),i=n(a),o=d(25),f=n(o);function n(p){return p&&p.__esModule?p:{default:p}}function s(p,h){if(!(p instanceof h))throw new TypeError("Cannot call a class as a function")}function E(p,h){if(!p)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return h&&(typeof h=="object"||typeof h=="function")?h:p}function m(p,h){if(typeof h!="function"&&h!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof h);p.prototype=Object.create(h&&h.prototype,{constructor:{value:p,enumerable:!1,writable:!0,configurable:!0}}),h&&(Object.setPrototypeOf?Object.setPrototypeOf(p,h):p.__proto__=h)}function b(p){return p instanceof e.default||p instanceof t.BlockEmbed}var N=function(p){m(h,p);function h(v,A){s(this,h);var T=E(this,(h.__proto__||Object.getPrototypeOf(h)).call(this,v));return T.emitter=A.emitter,Array.isArray(A.whitelist)&&(T.whitelist=A.whitelist.reduce(function(q,D){return q[D]=!0,q},{})),T.domNode.addEventListener("DOMNodeInserted",function(){}),T.optimize(),T.enable(),T}return w(h,[{key:"batchStart",value:function(){this.batch=!0}},{key:"batchEnd",value:function(){this.batch=!1,this.optimize()}},{key:"deleteAt",value:function(A,T){var q=this.line(A),D=P(q,2),C=D[0],Z=D[1],I=this.line(A+T),R=P(I,1),O=R[0];if(k(h.prototype.__proto__||Object.getPrototypeOf(h.prototype),"deleteAt",this).call(this,A,T),O!=null&&C!==O&&Z>0){if(C instanceof t.BlockEmbed||O instanceof t.BlockEmbed){this.optimize();return}if(C instanceof i.default){var S=C.newlineIndex(C.length(),!0);if(S>-1&&(C=C.split(S+1),C===O)){this.optimize();return}}else if(O instanceof i.default){var L=O.newlineIndex(0);L>-1&&O.split(L+1)}var F=O.children.head instanceof l.default?null:O.children.head;C.moveChildren(O,F),C.remove()}this.optimize()}},{key:"enable",value:function(){var A=arguments.length>0&&arguments[0]!==void 0?arguments[0]:!0;this.domNode.setAttribute("contenteditable",A)}},{key:"formatAt",value:function(A,T,q,D){this.whitelist!=null&&!this.whitelist[q]||(k(h.prototype.__proto__||Object.getPrototypeOf(h.prototype),"formatAt",this).call(this,A,T,q,D),this.optimize())}},{key:"insertAt",value:function(A,T,q){if(!(q!=null&&this.whitelist!=null&&!this.whitelist[T])){if(A>=this.length())if(q==null||y.default.query(T,y.default.Scope.BLOCK)==null){var D=y.default.create(this.statics.defaultChild);this.appendChild(D),q==null&&T.endsWith(`
`)&&(T=T.slice(0,-1)),D.insertAt(0,T,q)}else{var C=y.default.create(T,q);this.appendChild(C)}else k(h.prototype.__proto__||Object.getPrototypeOf(h.prototype),"insertAt",this).call(this,A,T,q);this.optimize()}}},{key:"insertBefore",value:function(A,T){if(A.statics.scope===y.default.Scope.INLINE_BLOT){var q=y.default.create(this.statics.defaultChild);q.appendChild(A),A=q}k(h.prototype.__proto__||Object.getPrototypeOf(h.prototype),"insertBefore",this).call(this,A,T)}},{key:"leaf",value:function(A){return this.path(A).pop()||[null,-1]}},{key:"line",value:function(A){return A===this.length()?this.line(A-1):this.descendant(b,A)}},{key:"lines",value:function(){var A=arguments.length>0&&arguments[0]!==void 0?arguments[0]:0,T=arguments.length>1&&arguments[1]!==void 0?arguments[1]:Number.MAX_VALUE,q=function D(C,Z,I){var R=[],O=I;return C.children.forEachAt(Z,I,function(S,L,F){b(S)?R.push(S):S instanceof y.default.Container&&(R=R.concat(D(S,L,O))),O-=F}),R};return q(this,A,T)}},{key:"optimize",value:function(){var A=arguments.length>0&&arguments[0]!==void 0?arguments[0]:[],T=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};this.batch!==!0&&(k(h.prototype.__proto__||Object.getPrototypeOf(h.prototype),"optimize",this).call(this,A,T),A.length>0&&this.emitter.emit(r.default.events.SCROLL_OPTIMIZE,A,T))}},{key:"path",value:function(A){return k(h.prototype.__proto__||Object.getPrototypeOf(h.prototype),"path",this).call(this,A).slice(1)}},{key:"update",value:function(A){if(this.batch!==!0){var T=r.default.sources.USER;typeof A=="string"&&(T=A),Array.isArray(A)||(A=this.observer.takeRecords()),A.length>0&&this.emitter.emit(r.default.events.SCROLL_BEFORE_UPDATE,T,A),k(h.prototype.__proto__||Object.getPrototypeOf(h.prototype),"update",this).call(this,A.concat([])),A.length>0&&this.emitter.emit(r.default.events.SCROLL_UPDATE,T,A)}}}]),h}(y.default.Scroll);N.blotName="scroll",N.className="ql-editor",N.tagName="DIV",N.defaultChild="block",N.allowedChildren=[e.default,t.BlockEmbed,f.default],_.default=N},function(B,_,d){Object.defineProperty(_,"__esModule",{value:!0}),_.SHORTKEY=_.default=void 0;var P=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(x){return typeof x}:function(x){return x&&typeof Symbol=="function"&&x.constructor===Symbol&&x!==Symbol.prototype?"symbol":typeof x},w=function(){function x(j,U){var H=[],V=!0,Y=!1,Q=void 0;try{for(var X=j[Symbol.iterator](),nt;!(V=(nt=X.next()).done)&&(H.push(nt.value),!(U&&H.length===U));V=!0);}catch(rt){Y=!0,Q=rt}finally{try{!V&&X.return&&X.return()}finally{if(Y)throw Q}}return H}return function(j,U){if(Array.isArray(j))return j;if(Symbol.iterator in Object(j))return x(j,U);throw new TypeError("Invalid attempt to destructure non-iterable instance")}}(),k=function(){function x(j,U){for(var H=0;H<U.length;H++){var V=U[H];V.enumerable=V.enumerable||!1,V.configurable=!0,"value"in V&&(V.writable=!0),Object.defineProperty(j,V.key,V)}}return function(j,U,H){return U&&x(j.prototype,U),H&&x(j,H),j}}(),g=d(21),y=p(g),c=d(11),r=p(c),t=d(3),e=p(t),u=d(2),l=p(u),a=d(20),i=p(a),o=d(0),f=p(o),n=d(5),s=p(n),E=d(10),m=p(E),b=d(9),N=p(b);function p(x){return x&&x.__esModule?x:{default:x}}function h(x,j,U){return j in x?Object.defineProperty(x,j,{value:U,enumerable:!0,configurable:!0,writable:!0}):x[j]=U,x}function v(x,j){if(!(x instanceof j))throw new TypeError("Cannot call a class as a function")}function A(x,j){if(!x)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return j&&(typeof j=="object"||typeof j=="function")?j:x}function T(x,j){if(typeof j!="function"&&j!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof j);x.prototype=Object.create(j&&j.prototype,{constructor:{value:x,enumerable:!1,writable:!0,configurable:!0}}),j&&(Object.setPrototypeOf?Object.setPrototypeOf(x,j):x.__proto__=j)}var q=(0,m.default)("quill:keyboard"),D=/Mac/i.test(navigator.platform)?"metaKey":"ctrlKey",C=function(x){T(j,x),k(j,null,[{key:"match",value:function(H,V){return V=M(V),["altKey","ctrlKey","metaKey","shiftKey"].some(function(Y){return!!V[Y]!==H[Y]&&V[Y]!==null})?!1:V.key===(H.which||H.keyCode)}}]);function j(U,H){v(this,j);var V=A(this,(j.__proto__||Object.getPrototypeOf(j)).call(this,U,H));return V.bindings={},Object.keys(V.options.bindings).forEach(function(Y){Y==="list autofill"&&U.scroll.whitelist!=null&&!U.scroll.whitelist.list||V.options.bindings[Y]&&V.addBinding(V.options.bindings[Y])}),V.addBinding({key:j.keys.ENTER,shiftKey:null},S),V.addBinding({key:j.keys.ENTER,metaKey:null,ctrlKey:null,altKey:null},function(){}),/Firefox/i.test(navigator.userAgent)?(V.addBinding({key:j.keys.BACKSPACE},{collapsed:!0},I),V.addBinding({key:j.keys.DELETE},{collapsed:!0},R)):(V.addBinding({key:j.keys.BACKSPACE},{collapsed:!0,prefix:/^.?$/},I),V.addBinding({key:j.keys.DELETE},{collapsed:!0,suffix:/^.?$/},R)),V.addBinding({key:j.keys.BACKSPACE},{collapsed:!1},O),V.addBinding({key:j.keys.DELETE},{collapsed:!1},O),V.addBinding({key:j.keys.BACKSPACE,altKey:null,ctrlKey:null,metaKey:null,shiftKey:null},{collapsed:!0,offset:0},I),V.listen(),V}return k(j,[{key:"addBinding",value:function(H){var V=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},Y=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{},Q=M(H);if(Q==null||Q.key==null)return q.warn("Attempted to add invalid keyboard binding",Q);typeof V=="function"&&(V={handler:V}),typeof Y=="function"&&(Y={handler:Y}),Q=(0,e.default)(Q,V,Y),this.bindings[Q.key]=this.bindings[Q.key]||[],this.bindings[Q.key].push(Q)}},{key:"listen",value:function(){var H=this;this.quill.root.addEventListener("keydown",function(V){if(!V.defaultPrevented){var Y=V.which||V.keyCode,Q=(H.bindings[Y]||[]).filter(function(ot){return j.match(V,ot)});if(Q.length!==0){var X=H.quill.getSelection();if(!(X==null||!H.quill.hasFocus())){var nt=H.quill.getLine(X.index),rt=w(nt,2),at=rt[0],lt=rt[1],K=H.quill.getLeaf(X.index),z=w(K,2),$=z[0],G=z[1],W=X.length===0?[$,G]:H.quill.getLeaf(X.index+X.length),J=w(W,2),tt=J[0],et=J[1],ut=$ instanceof f.default.Text?$.value().slice(0,G):"",st=tt instanceof f.default.Text?tt.value().slice(et):"",it={collapsed:X.length===0,empty:X.length===0&&at.length()<=1,format:H.quill.getFormat(X),offset:lt,prefix:ut,suffix:st},gt=Q.some(function(ot){if(ot.collapsed!=null&&ot.collapsed!==it.collapsed||ot.empty!=null&&ot.empty!==it.empty||ot.offset!=null&&ot.offset!==it.offset)return!1;if(Array.isArray(ot.format)){if(ot.format.every(function(ct){return it.format[ct]==null}))return!1}else if(P(ot.format)==="object"&&!Object.keys(ot.format).every(function(ct){return ot.format[ct]===!0?it.format[ct]!=null:ot.format[ct]===!1?it.format[ct]==null:(0,r.default)(ot.format[ct],it.format[ct])}))return!1;return ot.prefix!=null&&!ot.prefix.test(it.prefix)||ot.suffix!=null&&!ot.suffix.test(it.suffix)?!1:ot.handler.call(H,X,it)!==!0});gt&&V.preventDefault()}}}})}}]),j}(N.default);C.keys={BACKSPACE:8,TAB:9,ENTER:13,ESCAPE:27,LEFT:37,UP:38,RIGHT:39,DOWN:40,DELETE:46},C.DEFAULTS={bindings:{bold:F("bold"),italic:F("italic"),underline:F("underline"),indent:{key:C.keys.TAB,format:["blockquote","indent","list"],handler:function(j,U){if(U.collapsed&&U.offset!==0)return!0;this.quill.format("indent","+1",s.default.sources.USER)}},outdent:{key:C.keys.TAB,shiftKey:!0,format:["blockquote","indent","list"],handler:function(j,U){if(U.collapsed&&U.offset!==0)return!0;this.quill.format("indent","-1",s.default.sources.USER)}},"outdent backspace":{key:C.keys.BACKSPACE,collapsed:!0,shiftKey:null,metaKey:null,ctrlKey:null,altKey:null,format:["indent","list"],offset:0,handler:function(j,U){U.format.indent!=null?this.quill.format("indent","-1",s.default.sources.USER):U.format.list!=null&&this.quill.format("list",!1,s.default.sources.USER)}},"indent code-block":L(!0),"outdent code-block":L(!1),"remove tab":{key:C.keys.TAB,shiftKey:!0,collapsed:!0,prefix:/\t$/,handler:function(j){this.quill.deleteText(j.index-1,1,s.default.sources.USER)}},tab:{key:C.keys.TAB,handler:function(j){this.quill.history.cutoff();var U=new l.default().retain(j.index).delete(j.length).insert("	");this.quill.updateContents(U,s.default.sources.USER),this.quill.history.cutoff(),this.quill.setSelection(j.index+1,s.default.sources.SILENT)}},"list empty enter":{key:C.keys.ENTER,collapsed:!0,format:["list"],empty:!0,handler:function(j,U){this.quill.format("list",!1,s.default.sources.USER),U.format.indent&&this.quill.format("indent",!1,s.default.sources.USER)}},"checklist enter":{key:C.keys.ENTER,collapsed:!0,format:{list:"checked"},handler:function(j){var U=this.quill.getLine(j.index),H=w(U,2),V=H[0],Y=H[1],Q=(0,e.default)({},V.formats(),{list:"checked"}),X=new l.default().retain(j.index).insert(`
`,Q).retain(V.length()-Y-1).retain(1,{list:"unchecked"});this.quill.updateContents(X,s.default.sources.USER),this.quill.setSelection(j.index+1,s.default.sources.SILENT),this.quill.scrollIntoView()}},"header enter":{key:C.keys.ENTER,collapsed:!0,format:["header"],suffix:/^$/,handler:function(j,U){var H=this.quill.getLine(j.index),V=w(H,2),Y=V[0],Q=V[1],X=new l.default().retain(j.index).insert(`
`,U.format).retain(Y.length()-Q-1).retain(1,{header:null});this.quill.updateContents(X,s.default.sources.USER),this.quill.setSelection(j.index+1,s.default.sources.SILENT),this.quill.scrollIntoView()}},"list autofill":{key:" ",collapsed:!0,format:{list:!1},prefix:/^\s*?(\d+\.|-|\*|\[ ?\]|\[x\])$/,handler:function(j,U){var H=U.prefix.length,V=this.quill.getLine(j.index),Y=w(V,2),Q=Y[0],X=Y[1];if(X>H)return!0;var nt=void 0;switch(U.prefix.trim()){case"[]":case"[ ]":nt="unchecked";break;case"[x]":nt="checked";break;case"-":case"*":nt="bullet";break;default:nt="ordered"}this.quill.insertText(j.index," ",s.default.sources.USER),this.quill.history.cutoff();var rt=new l.default().retain(j.index-X).delete(H+1).retain(Q.length()-2-X).retain(1,{list:nt});this.quill.updateContents(rt,s.default.sources.USER),this.quill.history.cutoff(),this.quill.setSelection(j.index-H,s.default.sources.SILENT)}},"code exit":{key:C.keys.ENTER,collapsed:!0,format:["code-block"],prefix:/\n\n$/,suffix:/^\s+$/,handler:function(j){var U=this.quill.getLine(j.index),H=w(U,2),V=H[0],Y=H[1],Q=new l.default().retain(j.index+V.length()-Y-2).retain(1,{"code-block":null}).delete(1);this.quill.updateContents(Q,s.default.sources.USER)}},"embed left":Z(C.keys.LEFT,!1),"embed left shift":Z(C.keys.LEFT,!0),"embed right":Z(C.keys.RIGHT,!1),"embed right shift":Z(C.keys.RIGHT,!0)}};function Z(x,j){var U,H=x===C.keys.LEFT?"prefix":"suffix";return U={key:x,shiftKey:j,altKey:null},h(U,H,/^$/),h(U,"handler",function(Y){var Q=Y.index;x===C.keys.RIGHT&&(Q+=Y.length+1);var X=this.quill.getLeaf(Q),nt=w(X,1),rt=nt[0];return rt instanceof f.default.Embed?(x===C.keys.LEFT?j?this.quill.setSelection(Y.index-1,Y.length+1,s.default.sources.USER):this.quill.setSelection(Y.index-1,s.default.sources.USER):j?this.quill.setSelection(Y.index,Y.length+1,s.default.sources.USER):this.quill.setSelection(Y.index+Y.length+1,s.default.sources.USER),!1):!0}),U}function I(x,j){if(!(x.index===0||this.quill.getLength()<=1)){var U=this.quill.getLine(x.index),H=w(U,1),V=H[0],Y={};if(j.offset===0){var Q=this.quill.getLine(x.index-1),X=w(Q,1),nt=X[0];if(nt!=null&&nt.length()>1){var rt=V.formats(),at=this.quill.getFormat(x.index-1,1);Y=i.default.attributes.diff(rt,at)||{}}}var lt=/[\uD800-\uDBFF][\uDC00-\uDFFF]$/.test(j.prefix)?2:1;this.quill.deleteText(x.index-lt,lt,s.default.sources.USER),Object.keys(Y).length>0&&this.quill.formatLine(x.index-lt,lt,Y,s.default.sources.USER),this.quill.focus()}}function R(x,j){var U=/^[\uD800-\uDBFF][\uDC00-\uDFFF]/.test(j.suffix)?2:1;if(!(x.index>=this.quill.getLength()-U)){var H={},V=0,Y=this.quill.getLine(x.index),Q=w(Y,1),X=Q[0];if(j.offset>=X.length()-1){var nt=this.quill.getLine(x.index+1),rt=w(nt,1),at=rt[0];if(at){var lt=X.formats(),K=this.quill.getFormat(x.index,1);H=i.default.attributes.diff(lt,K)||{},V=at.length()}}this.quill.deleteText(x.index,U,s.default.sources.USER),Object.keys(H).length>0&&this.quill.formatLine(x.index+V-1,U,H,s.default.sources.USER)}}function O(x){var j=this.quill.getLines(x),U={};if(j.length>1){var H=j[0].formats(),V=j[j.length-1].formats();U=i.default.attributes.diff(V,H)||{}}this.quill.deleteText(x,s.default.sources.USER),Object.keys(U).length>0&&this.quill.formatLine(x.index,1,U,s.default.sources.USER),this.quill.setSelection(x.index,s.default.sources.SILENT),this.quill.focus()}function S(x,j){var U=this;x.length>0&&this.quill.scroll.deleteAt(x.index,x.length);var H=Object.keys(j.format).reduce(function(V,Y){return f.default.query(Y,f.default.Scope.BLOCK)&&!Array.isArray(j.format[Y])&&(V[Y]=j.format[Y]),V},{});this.quill.insertText(x.index,`
`,H,s.default.sources.USER),this.quill.setSelection(x.index+1,s.default.sources.SILENT),this.quill.focus(),Object.keys(j.format).forEach(function(V){H[V]==null&&(Array.isArray(j.format[V])||V!=="link"&&U.quill.format(V,j.format[V],s.default.sources.USER))})}function L(x){return{key:C.keys.TAB,shiftKey:!x,format:{"code-block":!0},handler:function(U){var H=f.default.query("code-block"),V=U.index,Y=U.length,Q=this.quill.scroll.descendant(H,V),X=w(Q,2),nt=X[0],rt=X[1];if(nt!=null){var at=this.quill.getIndex(nt),lt=nt.newlineIndex(rt,!0)+1,K=nt.newlineIndex(at+rt+Y),z=nt.domNode.textContent.slice(lt,K).split(`
`);rt=0,z.forEach(function($,G){x?(nt.insertAt(lt+rt,H.TAB),rt+=H.TAB.length,G===0?V+=H.TAB.length:Y+=H.TAB.length):$.startsWith(H.TAB)&&(nt.deleteAt(lt+rt,H.TAB.length),rt-=H.TAB.length,G===0?V-=H.TAB.length:Y-=H.TAB.length),rt+=$.length+1}),this.quill.update(s.default.sources.USER),this.quill.setSelection(V,Y,s.default.sources.SILENT)}}}}function F(x){return{key:x[0].toUpperCase(),shortKey:!0,handler:function(U,H){this.quill.format(x,!H.format[x],s.default.sources.USER)}}}function M(x){if(typeof x=="string"||typeof x=="number")return M({key:x});if((typeof x=="undefined"?"undefined":P(x))==="object"&&(x=(0,y.default)(x,!1)),typeof x.key=="string")if(C.keys[x.key.toUpperCase()]!=null)x.key=C.keys[x.key.toUpperCase()];else if(x.key.length===1)x.key=x.key.toUpperCase().charCodeAt(0);else return null;return x.shortKey&&(x[D]=x.shortKey,delete x.shortKey),x}_.default=C,_.SHORTKEY=D},function(B,_,d){Object.defineProperty(_,"__esModule",{value:!0});var P=function(){function i(o,f){var n=[],s=!0,E=!1,m=void 0;try{for(var b=o[Symbol.iterator](),N;!(s=(N=b.next()).done)&&(n.push(N.value),!(f&&n.length===f));s=!0);}catch(p){E=!0,m=p}finally{try{!s&&b.return&&b.return()}finally{if(E)throw m}}return n}return function(o,f){if(Array.isArray(o))return o;if(Symbol.iterator in Object(o))return i(o,f);throw new TypeError("Invalid attempt to destructure non-iterable instance")}}(),w=function i(o,f,n){o===null&&(o=Function.prototype);var s=Object.getOwnPropertyDescriptor(o,f);if(s===void 0){var E=Object.getPrototypeOf(o);return E===null?void 0:i(E,f,n)}else{if("value"in s)return s.value;var m=s.get;return m===void 0?void 0:m.call(n)}},k=function(){function i(o,f){for(var n=0;n<f.length;n++){var s=f[n];s.enumerable=s.enumerable||!1,s.configurable=!0,"value"in s&&(s.writable=!0),Object.defineProperty(o,s.key,s)}}return function(o,f,n){return f&&i(o.prototype,f),n&&i(o,n),o}}(),g=d(0),y=t(g),c=d(7),r=t(c);function t(i){return i&&i.__esModule?i:{default:i}}function e(i,o){if(!(i instanceof o))throw new TypeError("Cannot call a class as a function")}function u(i,o){if(!i)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return o&&(typeof o=="object"||typeof o=="function")?o:i}function l(i,o){if(typeof o!="function"&&o!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof o);i.prototype=Object.create(o&&o.prototype,{constructor:{value:i,enumerable:!1,writable:!0,configurable:!0}}),o&&(Object.setPrototypeOf?Object.setPrototypeOf(i,o):i.__proto__=o)}var a=function(i){l(o,i),k(o,null,[{key:"value",value:function(){}}]);function o(f,n){e(this,o);var s=u(this,(o.__proto__||Object.getPrototypeOf(o)).call(this,f));return s.selection=n,s.textNode=document.createTextNode(o.CONTENTS),s.domNode.appendChild(s.textNode),s._length=0,s}return k(o,[{key:"detach",value:function(){this.parent!=null&&this.parent.removeChild(this)}},{key:"format",value:function(n,s){if(this._length!==0)return w(o.prototype.__proto__||Object.getPrototypeOf(o.prototype),"format",this).call(this,n,s);for(var E=this,m=0;E!=null&&E.statics.scope!==y.default.Scope.BLOCK_BLOT;)m+=E.offset(E.parent),E=E.parent;E!=null&&(this._length=o.CONTENTS.length,E.optimize(),E.formatAt(m,o.CONTENTS.length,n,s),this._length=0)}},{key:"index",value:function(n,s){return n===this.textNode?0:w(o.prototype.__proto__||Object.getPrototypeOf(o.prototype),"index",this).call(this,n,s)}},{key:"length",value:function(){return this._length}},{key:"position",value:function(){return[this.textNode,this.textNode.data.length]}},{key:"remove",value:function(){w(o.prototype.__proto__||Object.getPrototypeOf(o.prototype),"remove",this).call(this),this.parent=null}},{key:"restore",value:function(){if(!(this.selection.composing||this.parent==null)){var n=this.textNode,s=this.selection.getNativeRange(),E=void 0,m=void 0,b=void 0;if(s!=null&&s.start.node===n&&s.end.node===n){var N=[n,s.start.offset,s.end.offset];E=N[0],m=N[1],b=N[2]}for(;this.domNode.lastChild!=null&&this.domNode.lastChild!==this.textNode;)this.domNode.parentNode.insertBefore(this.domNode.lastChild,this.domNode);if(this.textNode.data!==o.CONTENTS){var p=this.textNode.data.split(o.CONTENTS).join("");this.next instanceof r.default?(E=this.next.domNode,this.next.insertAt(0,p),this.textNode.data=o.CONTENTS):(this.textNode.data=p,this.parent.insertBefore(y.default.create(this.textNode),this),this.textNode=document.createTextNode(o.CONTENTS),this.domNode.appendChild(this.textNode))}if(this.remove(),m!=null){var h=[m,b].map(function(A){return Math.max(0,Math.min(E.data.length,A-1))}),v=P(h,2);return m=v[0],b=v[1],{startNode:E,startOffset:m,endNode:E,endOffset:b}}}}},{key:"update",value:function(n,s){var E=this;if(n.some(function(b){return b.type==="characterData"&&b.target===E.textNode})){var m=this.restore();m&&(s.range=m)}}},{key:"value",value:function(){return""}}]),o}(y.default.Embed);a.blotName="cursor",a.className="ql-cursor",a.tagName="span",a.CONTENTS="\uFEFF",_.default=a},function(B,_,d){Object.defineProperty(_,"__esModule",{value:!0});var P=d(0),w=y(P),k=d(4),g=y(k);function y(u){return u&&u.__esModule?u:{default:u}}function c(u,l){if(!(u instanceof l))throw new TypeError("Cannot call a class as a function")}function r(u,l){if(!u)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return l&&(typeof l=="object"||typeof l=="function")?l:u}function t(u,l){if(typeof l!="function"&&l!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof l);u.prototype=Object.create(l&&l.prototype,{constructor:{value:u,enumerable:!1,writable:!0,configurable:!0}}),l&&(Object.setPrototypeOf?Object.setPrototypeOf(u,l):u.__proto__=l)}var e=function(u){t(l,u);function l(){return c(this,l),r(this,(l.__proto__||Object.getPrototypeOf(l)).apply(this,arguments))}return l}(w.default.Container);e.allowedChildren=[g.default,k.BlockEmbed,e],_.default=e},function(B,_,d){Object.defineProperty(_,"__esModule",{value:!0}),_.ColorStyle=_.ColorClass=_.ColorAttributor=void 0;var P=function(){function a(i,o){for(var f=0;f<o.length;f++){var n=o[f];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(i,n.key,n)}}return function(i,o,f){return o&&a(i.prototype,o),f&&a(i,f),i}}(),w=function a(i,o,f){i===null&&(i=Function.prototype);var n=Object.getOwnPropertyDescriptor(i,o);if(n===void 0){var s=Object.getPrototypeOf(i);return s===null?void 0:a(s,o,f)}else{if("value"in n)return n.value;var E=n.get;return E===void 0?void 0:E.call(f)}},k=d(0),g=y(k);function y(a){return a&&a.__esModule?a:{default:a}}function c(a,i){if(!(a instanceof i))throw new TypeError("Cannot call a class as a function")}function r(a,i){if(!a)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return i&&(typeof i=="object"||typeof i=="function")?i:a}function t(a,i){if(typeof i!="function"&&i!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof i);a.prototype=Object.create(i&&i.prototype,{constructor:{value:a,enumerable:!1,writable:!0,configurable:!0}}),i&&(Object.setPrototypeOf?Object.setPrototypeOf(a,i):a.__proto__=i)}var e=function(a){t(i,a);function i(){return c(this,i),r(this,(i.__proto__||Object.getPrototypeOf(i)).apply(this,arguments))}return P(i,[{key:"value",value:function(f){var n=w(i.prototype.__proto__||Object.getPrototypeOf(i.prototype),"value",this).call(this,f);return n.startsWith("rgb(")?(n=n.replace(/^[^\d]+/,"").replace(/[^\d]+$/,""),"#"+n.split(",").map(function(s){return("00"+parseInt(s).toString(16)).slice(-2)}).join("")):n}}]),i}(g.default.Attributor.Style),u=new g.default.Attributor.Class("color","ql-color",{scope:g.default.Scope.INLINE}),l=new e("color","color",{scope:g.default.Scope.INLINE});_.ColorAttributor=e,_.ColorClass=u,_.ColorStyle=l},function(B,_,d){Object.defineProperty(_,"__esModule",{value:!0}),_.sanitize=_.default=void 0;var P=function(){function l(a,i){for(var o=0;o<i.length;o++){var f=i[o];f.enumerable=f.enumerable||!1,f.configurable=!0,"value"in f&&(f.writable=!0),Object.defineProperty(a,f.key,f)}}return function(a,i,o){return i&&l(a.prototype,i),o&&l(a,o),a}}(),w=function l(a,i,o){a===null&&(a=Function.prototype);var f=Object.getOwnPropertyDescriptor(a,i);if(f===void 0){var n=Object.getPrototypeOf(a);return n===null?void 0:l(n,i,o)}else{if("value"in f)return f.value;var s=f.get;return s===void 0?void 0:s.call(o)}},k=d(6),g=y(k);function y(l){return l&&l.__esModule?l:{default:l}}function c(l,a){if(!(l instanceof a))throw new TypeError("Cannot call a class as a function")}function r(l,a){if(!l)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return a&&(typeof a=="object"||typeof a=="function")?a:l}function t(l,a){if(typeof a!="function"&&a!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof a);l.prototype=Object.create(a&&a.prototype,{constructor:{value:l,enumerable:!1,writable:!0,configurable:!0}}),a&&(Object.setPrototypeOf?Object.setPrototypeOf(l,a):l.__proto__=a)}var e=function(l){t(a,l);function a(){return c(this,a),r(this,(a.__proto__||Object.getPrototypeOf(a)).apply(this,arguments))}return P(a,[{key:"format",value:function(o,f){if(o!==this.statics.blotName||!f)return w(a.prototype.__proto__||Object.getPrototypeOf(a.prototype),"format",this).call(this,o,f);f=this.constructor.sanitize(f),this.domNode.setAttribute("href",f)}}],[{key:"create",value:function(o){var f=w(a.__proto__||Object.getPrototypeOf(a),"create",this).call(this,o);return o=this.sanitize(o),f.setAttribute("href",o),f.setAttribute("rel","noopener noreferrer"),f.setAttribute("target","_blank"),f}},{key:"formats",value:function(o){return o.getAttribute("href")}},{key:"sanitize",value:function(o){return u(o,this.PROTOCOL_WHITELIST)?o:this.SANITIZED_URL}}]),a}(g.default);e.blotName="link",e.tagName="A",e.SANITIZED_URL="about:blank",e.PROTOCOL_WHITELIST=["http","https","mailto","tel"];function u(l,a){var i=document.createElement("a");i.href=l;var o=i.href.slice(0,i.href.indexOf(":"));return a.indexOf(o)>-1}_.default=e,_.sanitize=u},function(B,_,d){Object.defineProperty(_,"__esModule",{value:!0});var P=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(a){return typeof a}:function(a){return a&&typeof Symbol=="function"&&a.constructor===Symbol&&a!==Symbol.prototype?"symbol":typeof a},w=function(){function a(i,o){for(var f=0;f<o.length;f++){var n=o[f];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(i,n.key,n)}}return function(i,o,f){return o&&a(i.prototype,o),f&&a(i,f),i}}(),k=d(23),g=r(k),y=d(107),c=r(y);function r(a){return a&&a.__esModule?a:{default:a}}function t(a,i){if(!(a instanceof i))throw new TypeError("Cannot call a class as a function")}var e=0;function u(a,i){a.setAttribute(i,a.getAttribute(i)!=="true")}var l=function(){function a(i){var o=this;t(this,a),this.select=i,this.container=document.createElement("span"),this.buildPicker(),this.select.style.display="none",this.select.parentNode.insertBefore(this.container,this.select),this.label.addEventListener("mousedown",function(){o.togglePicker()}),this.label.addEventListener("keydown",function(f){switch(f.keyCode){case g.default.keys.ENTER:o.togglePicker();break;case g.default.keys.ESCAPE:o.escape(),f.preventDefault();break}}),this.select.addEventListener("change",this.update.bind(this))}return w(a,[{key:"togglePicker",value:function(){this.container.classList.toggle("ql-expanded"),u(this.label,"aria-expanded"),u(this.options,"aria-hidden")}},{key:"buildItem",value:function(o){var f=this,n=document.createElement("span");return n.tabIndex="0",n.setAttribute("role","button"),n.classList.add("ql-picker-item"),o.hasAttribute("value")&&n.setAttribute("data-value",o.getAttribute("value")),o.textContent&&n.setAttribute("data-label",o.textContent),n.addEventListener("click",function(){f.selectItem(n,!0)}),n.addEventListener("keydown",function(s){switch(s.keyCode){case g.default.keys.ENTER:f.selectItem(n,!0),s.preventDefault();break;case g.default.keys.ESCAPE:f.escape(),s.preventDefault();break}}),n}},{key:"buildLabel",value:function(){var o=document.createElement("span");return o.classList.add("ql-picker-label"),o.innerHTML=c.default,o.tabIndex="0",o.setAttribute("role","button"),o.setAttribute("aria-expanded","false"),this.container.appendChild(o),o}},{key:"buildOptions",value:function(){var o=this,f=document.createElement("span");f.classList.add("ql-picker-options"),f.setAttribute("aria-hidden","true"),f.tabIndex="-1",f.id="ql-picker-options-"+e,e+=1,this.label.setAttribute("aria-controls",f.id),this.options=f,[].slice.call(this.select.options).forEach(function(n){var s=o.buildItem(n);f.appendChild(s),n.selected===!0&&o.selectItem(s)}),this.container.appendChild(f)}},{key:"buildPicker",value:function(){var o=this;[].slice.call(this.select.attributes).forEach(function(f){o.container.setAttribute(f.name,f.value)}),this.container.classList.add("ql-picker"),this.label=this.buildLabel(),this.buildOptions()}},{key:"escape",value:function(){var o=this;this.close(),setTimeout(function(){return o.label.focus()},1)}},{key:"close",value:function(){this.container.classList.remove("ql-expanded"),this.label.setAttribute("aria-expanded","false"),this.options.setAttribute("aria-hidden","true")}},{key:"selectItem",value:function(o){var f=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1,n=this.container.querySelector(".ql-selected");if(o!==n&&(n!=null&&n.classList.remove("ql-selected"),o!=null&&(o.classList.add("ql-selected"),this.select.selectedIndex=[].indexOf.call(o.parentNode.children,o),o.hasAttribute("data-value")?this.label.setAttribute("data-value",o.getAttribute("data-value")):this.label.removeAttribute("data-value"),o.hasAttribute("data-label")?this.label.setAttribute("data-label",o.getAttribute("data-label")):this.label.removeAttribute("data-label"),f))){if(typeof Event=="function")this.select.dispatchEvent(new Event("change"));else if((typeof Event=="undefined"?"undefined":P(Event))==="object"){var s=document.createEvent("Event");s.initEvent("change",!0,!0),this.select.dispatchEvent(s)}this.close()}}},{key:"update",value:function(){var o=void 0;if(this.select.selectedIndex>-1){var f=this.container.querySelector(".ql-picker-options").children[this.select.selectedIndex];o=this.select.options[this.select.selectedIndex],this.selectItem(f)}else this.selectItem(null);var n=o!=null&&o!==this.select.querySelector("option[selected]");this.label.classList.toggle("ql-active",n)}}]),a}();_.default=l},function(B,_,d){Object.defineProperty(_,"__esModule",{value:!0});var P=d(0),w=q(P),k=d(5),g=q(k),y=d(4),c=q(y),r=d(16),t=q(r),e=d(25),u=q(e),l=d(24),a=q(l),i=d(35),o=q(i),f=d(6),n=q(f),s=d(22),E=q(s),m=d(7),b=q(m),N=d(55),p=q(N),h=d(42),v=q(h),A=d(23),T=q(A);function q(D){return D&&D.__esModule?D:{default:D}}g.default.register({"blots/block":c.default,"blots/block/embed":y.BlockEmbed,"blots/break":t.default,"blots/container":u.default,"blots/cursor":a.default,"blots/embed":o.default,"blots/inline":n.default,"blots/scroll":E.default,"blots/text":b.default,"modules/clipboard":p.default,"modules/history":v.default,"modules/keyboard":T.default}),w.default.register(c.default,t.default,a.default,n.default,E.default,b.default),_.default=g.default},function(B,_,d){Object.defineProperty(_,"__esModule",{value:!0});var P=d(1),w=function(){function k(g){this.domNode=g,this.domNode[P.DATA_KEY]={blot:this}}return Object.defineProperty(k.prototype,"statics",{get:function(){return this.constructor},enumerable:!0,configurable:!0}),k.create=function(g){if(this.tagName==null)throw new P.ParchmentError("Blot definition missing tagName");var y;return Array.isArray(this.tagName)?(typeof g=="string"&&(g=g.toUpperCase(),parseInt(g).toString()===g&&(g=parseInt(g))),typeof g=="number"?y=document.createElement(this.tagName[g-1]):this.tagName.indexOf(g)>-1?y=document.createElement(g):y=document.createElement(this.tagName[0])):y=document.createElement(this.tagName),this.className&&y.classList.add(this.className),y},k.prototype.attach=function(){this.parent!=null&&(this.scroll=this.parent.scroll)},k.prototype.clone=function(){var g=this.domNode.cloneNode(!1);return P.create(g)},k.prototype.detach=function(){this.parent!=null&&this.parent.removeChild(this),delete this.domNode[P.DATA_KEY]},k.prototype.deleteAt=function(g,y){var c=this.isolate(g,y);c.remove()},k.prototype.formatAt=function(g,y,c,r){var t=this.isolate(g,y);if(P.query(c,P.Scope.BLOT)!=null&&r)t.wrap(c,r);else if(P.query(c,P.Scope.ATTRIBUTE)!=null){var e=P.create(this.statics.scope);t.wrap(e),e.format(c,r)}},k.prototype.insertAt=function(g,y,c){var r=c==null?P.create("text",y):P.create(y,c),t=this.split(g);this.parent.insertBefore(r,t)},k.prototype.insertInto=function(g,y){y===void 0&&(y=null),this.parent!=null&&this.parent.children.remove(this);var c=null;g.children.insertBefore(this,y),y!=null&&(c=y.domNode),(this.domNode.parentNode!=g.domNode||this.domNode.nextSibling!=c)&&g.domNode.insertBefore(this.domNode,c),this.parent=g,this.attach()},k.prototype.isolate=function(g,y){var c=this.split(g);return c.split(y),c},k.prototype.length=function(){return 1},k.prototype.offset=function(g){return g===void 0&&(g=this.parent),this.parent==null||this==g?0:this.parent.children.offset(this)+this.parent.offset(g)},k.prototype.optimize=function(g){this.domNode[P.DATA_KEY]!=null&&delete this.domNode[P.DATA_KEY].mutations},k.prototype.remove=function(){this.domNode.parentNode!=null&&this.domNode.parentNode.removeChild(this.domNode),this.detach()},k.prototype.replace=function(g){g.parent!=null&&(g.parent.insertBefore(this,g.next),g.remove())},k.prototype.replaceWith=function(g,y){var c=typeof g=="string"?P.create(g,y):g;return c.replace(this),c},k.prototype.split=function(g,y){return g===0?this:this.next},k.prototype.update=function(g,y){},k.prototype.wrap=function(g,y){var c=typeof g=="string"?P.create(g,y):g;return this.parent!=null&&this.parent.insertBefore(c,this.next),c.appendChild(this),c},k.blotName="abstract",k}();_.default=w},function(B,_,d){Object.defineProperty(_,"__esModule",{value:!0});var P=d(12),w=d(32),k=d(33),g=d(1),y=function(){function c(r){this.attributes={},this.domNode=r,this.build()}return c.prototype.attribute=function(r,t){t?r.add(this.domNode,t)&&(r.value(this.domNode)!=null?this.attributes[r.attrName]=r:delete this.attributes[r.attrName]):(r.remove(this.domNode),delete this.attributes[r.attrName])},c.prototype.build=function(){var r=this;this.attributes={};var t=P.default.keys(this.domNode),e=w.default.keys(this.domNode),u=k.default.keys(this.domNode);t.concat(e).concat(u).forEach(function(l){var a=g.query(l,g.Scope.ATTRIBUTE);a instanceof P.default&&(r.attributes[a.attrName]=a)})},c.prototype.copy=function(r){var t=this;Object.keys(this.attributes).forEach(function(e){var u=t.attributes[e].value(t.domNode);r.format(e,u)})},c.prototype.move=function(r){var t=this;this.copy(r),Object.keys(this.attributes).forEach(function(e){t.attributes[e].remove(t.domNode)}),this.attributes={}},c.prototype.values=function(){var r=this;return Object.keys(this.attributes).reduce(function(t,e){return t[e]=r.attributes[e].value(r.domNode),t},{})},c}();_.default=y},function(B,_,d){var P=this&&this.__extends||function(){var y=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(c,r){c.__proto__=r}||function(c,r){for(var t in r)r.hasOwnProperty(t)&&(c[t]=r[t])};return function(c,r){y(c,r);function t(){this.constructor=c}c.prototype=r===null?Object.create(r):(t.prototype=r.prototype,new t)}}();Object.defineProperty(_,"__esModule",{value:!0});var w=d(12);function k(y,c){var r=y.getAttribute("class")||"";return r.split(/\s+/).filter(function(t){return t.indexOf(c+"-")===0})}var g=function(y){P(c,y);function c(){return y!==null&&y.apply(this,arguments)||this}return c.keys=function(r){return(r.getAttribute("class")||"").split(/\s+/).map(function(t){return t.split("-").slice(0,-1).join("-")})},c.prototype.add=function(r,t){return this.canAdd(r,t)?(this.remove(r),r.classList.add(this.keyName+"-"+t),!0):!1},c.prototype.remove=function(r){var t=k(r,this.keyName);t.forEach(function(e){r.classList.remove(e)}),r.classList.length===0&&r.removeAttribute("class")},c.prototype.value=function(r){var t=k(r,this.keyName)[0]||"",e=t.slice(this.keyName.length+1);return this.canAdd(r,e)?e:""},c}(w.default);_.default=g},function(B,_,d){var P=this&&this.__extends||function(){var y=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(c,r){c.__proto__=r}||function(c,r){for(var t in r)r.hasOwnProperty(t)&&(c[t]=r[t])};return function(c,r){y(c,r);function t(){this.constructor=c}c.prototype=r===null?Object.create(r):(t.prototype=r.prototype,new t)}}();Object.defineProperty(_,"__esModule",{value:!0});var w=d(12);function k(y){var c=y.split("-"),r=c.slice(1).map(function(t){return t[0].toUpperCase()+t.slice(1)}).join("");return c[0]+r}var g=function(y){P(c,y);function c(){return y!==null&&y.apply(this,arguments)||this}return c.keys=function(r){return(r.getAttribute("style")||"").split(";").map(function(t){var e=t.split(":");return e[0].trim()})},c.prototype.add=function(r,t){return this.canAdd(r,t)?(r.style[k(this.keyName)]=t,!0):!1},c.prototype.remove=function(r){r.style[k(this.keyName)]="",r.getAttribute("style")||r.removeAttribute("style")},c.prototype.value=function(r){var t=r.style[k(this.keyName)];return this.canAdd(r,t)?t:""},c}(w.default);_.default=g},function(B,_,d){Object.defineProperty(_,"__esModule",{value:!0});var P=function(){function g(y,c){for(var r=0;r<c.length;r++){var t=c[r];t.enumerable=t.enumerable||!1,t.configurable=!0,"value"in t&&(t.writable=!0),Object.defineProperty(y,t.key,t)}}return function(y,c,r){return c&&g(y.prototype,c),r&&g(y,r),y}}();function w(g,y){if(!(g instanceof y))throw new TypeError("Cannot call a class as a function")}var k=function(){function g(y,c){w(this,g),this.quill=y,this.options=c,this.modules={}}return P(g,[{key:"init",value:function(){var c=this;Object.keys(this.options.modules).forEach(function(r){c.modules[r]==null&&c.addModule(r)})}},{key:"addModule",value:function(c){var r=this.quill.constructor.import("modules/"+c);return this.modules[c]=new r(this.quill,this.options.modules[c]||{}),this.modules[c]}}]),g}();k.DEFAULTS={modules:{}},k.themes={default:k},_.default=k},function(B,_,d){Object.defineProperty(_,"__esModule",{value:!0});var P=function(){function i(o,f){for(var n=0;n<f.length;n++){var s=f[n];s.enumerable=s.enumerable||!1,s.configurable=!0,"value"in s&&(s.writable=!0),Object.defineProperty(o,s.key,s)}}return function(o,f,n){return f&&i(o.prototype,f),n&&i(o,n),o}}(),w=function i(o,f,n){o===null&&(o=Function.prototype);var s=Object.getOwnPropertyDescriptor(o,f);if(s===void 0){var E=Object.getPrototypeOf(o);return E===null?void 0:i(E,f,n)}else{if("value"in s)return s.value;var m=s.get;return m===void 0?void 0:m.call(n)}},k=d(0),g=r(k),y=d(7),c=r(y);function r(i){return i&&i.__esModule?i:{default:i}}function t(i,o){if(!(i instanceof o))throw new TypeError("Cannot call a class as a function")}function e(i,o){if(!i)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return o&&(typeof o=="object"||typeof o=="function")?o:i}function u(i,o){if(typeof o!="function"&&o!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof o);i.prototype=Object.create(o&&o.prototype,{constructor:{value:i,enumerable:!1,writable:!0,configurable:!0}}),o&&(Object.setPrototypeOf?Object.setPrototypeOf(i,o):i.__proto__=o)}var l="\uFEFF",a=function(i){u(o,i);function o(f){t(this,o);var n=e(this,(o.__proto__||Object.getPrototypeOf(o)).call(this,f));return n.contentNode=document.createElement("span"),n.contentNode.setAttribute("contenteditable",!1),[].slice.call(n.domNode.childNodes).forEach(function(s){n.contentNode.appendChild(s)}),n.leftGuard=document.createTextNode(l),n.rightGuard=document.createTextNode(l),n.domNode.appendChild(n.leftGuard),n.domNode.appendChild(n.contentNode),n.domNode.appendChild(n.rightGuard),n}return P(o,[{key:"index",value:function(n,s){return n===this.leftGuard?0:n===this.rightGuard?1:w(o.prototype.__proto__||Object.getPrototypeOf(o.prototype),"index",this).call(this,n,s)}},{key:"restore",value:function(n){var s=void 0,E=void 0,m=n.data.split(l).join("");if(n===this.leftGuard)if(this.prev instanceof c.default){var b=this.prev.length();this.prev.insertAt(b,m),s={startNode:this.prev.domNode,startOffset:b+m.length}}else E=document.createTextNode(m),this.parent.insertBefore(g.default.create(E),this),s={startNode:E,startOffset:m.length};else n===this.rightGuard&&(this.next instanceof c.default?(this.next.insertAt(0,m),s={startNode:this.next.domNode,startOffset:m.length}):(E=document.createTextNode(m),this.parent.insertBefore(g.default.create(E),this.next),s={startNode:E,startOffset:m.length}));return n.data=l,s}},{key:"update",value:function(n,s){var E=this;n.forEach(function(m){if(m.type==="characterData"&&(m.target===E.leftGuard||m.target===E.rightGuard)){var b=E.restore(m.target);b&&(s.range=b)}})}}]),o}(g.default.Embed);_.default=a},function(B,_,d){Object.defineProperty(_,"__esModule",{value:!0}),_.AlignStyle=_.AlignClass=_.AlignAttribute=void 0;var P=d(0),w=k(P);function k(t){return t&&t.__esModule?t:{default:t}}var g={scope:w.default.Scope.BLOCK,whitelist:["right","center","justify"]},y=new w.default.Attributor.Attribute("align","align",g),c=new w.default.Attributor.Class("align","ql-align",g),r=new w.default.Attributor.Style("align","text-align",g);_.AlignAttribute=y,_.AlignClass=c,_.AlignStyle=r},function(B,_,d){Object.defineProperty(_,"__esModule",{value:!0}),_.BackgroundStyle=_.BackgroundClass=void 0;var P=d(0),w=g(P),k=d(26);function g(r){return r&&r.__esModule?r:{default:r}}var y=new w.default.Attributor.Class("background","ql-bg",{scope:w.default.Scope.INLINE}),c=new k.ColorAttributor("background","background-color",{scope:w.default.Scope.INLINE});_.BackgroundClass=y,_.BackgroundStyle=c},function(B,_,d){Object.defineProperty(_,"__esModule",{value:!0}),_.DirectionStyle=_.DirectionClass=_.DirectionAttribute=void 0;var P=d(0),w=k(P);function k(t){return t&&t.__esModule?t:{default:t}}var g={scope:w.default.Scope.BLOCK,whitelist:["rtl"]},y=new w.default.Attributor.Attribute("direction","dir",g),c=new w.default.Attributor.Class("direction","ql-direction",g),r=new w.default.Attributor.Style("direction","direction",g);_.DirectionAttribute=y,_.DirectionClass=c,_.DirectionStyle=r},function(B,_,d){Object.defineProperty(_,"__esModule",{value:!0}),_.FontClass=_.FontStyle=void 0;var P=function(){function i(o,f){for(var n=0;n<f.length;n++){var s=f[n];s.enumerable=s.enumerable||!1,s.configurable=!0,"value"in s&&(s.writable=!0),Object.defineProperty(o,s.key,s)}}return function(o,f,n){return f&&i(o.prototype,f),n&&i(o,n),o}}(),w=function i(o,f,n){o===null&&(o=Function.prototype);var s=Object.getOwnPropertyDescriptor(o,f);if(s===void 0){var E=Object.getPrototypeOf(o);return E===null?void 0:i(E,f,n)}else{if("value"in s)return s.value;var m=s.get;return m===void 0?void 0:m.call(n)}},k=d(0),g=y(k);function y(i){return i&&i.__esModule?i:{default:i}}function c(i,o){if(!(i instanceof o))throw new TypeError("Cannot call a class as a function")}function r(i,o){if(!i)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return o&&(typeof o=="object"||typeof o=="function")?o:i}function t(i,o){if(typeof o!="function"&&o!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof o);i.prototype=Object.create(o&&o.prototype,{constructor:{value:i,enumerable:!1,writable:!0,configurable:!0}}),o&&(Object.setPrototypeOf?Object.setPrototypeOf(i,o):i.__proto__=o)}var e={scope:g.default.Scope.INLINE,whitelist:["serif","monospace"]},u=new g.default.Attributor.Class("font","ql-font",e),l=function(i){t(o,i);function o(){return c(this,o),r(this,(o.__proto__||Object.getPrototypeOf(o)).apply(this,arguments))}return P(o,[{key:"value",value:function(n){return w(o.prototype.__proto__||Object.getPrototypeOf(o.prototype),"value",this).call(this,n).replace(/["']/g,"")}}]),o}(g.default.Attributor.Style),a=new l("font","font-family",e);_.FontStyle=a,_.FontClass=u},function(B,_,d){Object.defineProperty(_,"__esModule",{value:!0}),_.SizeStyle=_.SizeClass=void 0;var P=d(0),w=k(P);function k(c){return c&&c.__esModule?c:{default:c}}var g=new w.default.Attributor.Class("size","ql-size",{scope:w.default.Scope.INLINE,whitelist:["small","large","huge"]}),y=new w.default.Attributor.Style("size","font-size",{scope:w.default.Scope.INLINE,whitelist:["10px","18px","32px"]});_.SizeClass=g,_.SizeStyle=y},function(B,_,d){B.exports={align:{"":d(76),center:d(77),right:d(78),justify:d(79)},background:d(80),blockquote:d(81),bold:d(82),clean:d(83),code:d(58),"code-block":d(58),color:d(84),direction:{"":d(85),rtl:d(86)},float:{center:d(87),full:d(88),left:d(89),right:d(90)},formula:d(91),header:{1:d(92),2:d(93)},italic:d(94),image:d(95),indent:{"+1":d(96),"-1":d(97)},link:d(98),list:{ordered:d(99),bullet:d(100),check:d(101)},script:{sub:d(102),super:d(103)},strike:d(104),underline:d(105),video:d(106)}},function(B,_,d){Object.defineProperty(_,"__esModule",{value:!0}),_.getLastChangeIndex=_.default=void 0;var P=function(){function f(n,s){for(var E=0;E<s.length;E++){var m=s[E];m.enumerable=m.enumerable||!1,m.configurable=!0,"value"in m&&(m.writable=!0),Object.defineProperty(n,m.key,m)}}return function(n,s,E){return s&&f(n.prototype,s),E&&f(n,E),n}}(),w=d(0),k=t(w),g=d(5),y=t(g),c=d(9),r=t(c);function t(f){return f&&f.__esModule?f:{default:f}}function e(f,n){if(!(f instanceof n))throw new TypeError("Cannot call a class as a function")}function u(f,n){if(!f)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return n&&(typeof n=="object"||typeof n=="function")?n:f}function l(f,n){if(typeof n!="function"&&n!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof n);f.prototype=Object.create(n&&n.prototype,{constructor:{value:f,enumerable:!1,writable:!0,configurable:!0}}),n&&(Object.setPrototypeOf?Object.setPrototypeOf(f,n):f.__proto__=n)}var a=function(f){l(n,f);function n(s,E){e(this,n);var m=u(this,(n.__proto__||Object.getPrototypeOf(n)).call(this,s,E));return m.lastRecorded=0,m.ignoreChange=!1,m.clear(),m.quill.on(y.default.events.EDITOR_CHANGE,function(b,N,p,h){b!==y.default.events.TEXT_CHANGE||m.ignoreChange||(!m.options.userOnly||h===y.default.sources.USER?m.record(N,p):m.transform(N))}),m.quill.keyboard.addBinding({key:"Z",shortKey:!0},m.undo.bind(m)),m.quill.keyboard.addBinding({key:"Z",shortKey:!0,shiftKey:!0},m.redo.bind(m)),/Win/i.test(navigator.platform)&&m.quill.keyboard.addBinding({key:"Y",shortKey:!0},m.redo.bind(m)),m}return P(n,[{key:"change",value:function(E,m){if(this.stack[E].length!==0){var b=this.stack[E].pop();this.stack[m].push(b),this.lastRecorded=0,this.ignoreChange=!0,this.quill.updateContents(b[E],y.default.sources.USER),this.ignoreChange=!1;var N=o(b[E]);this.quill.setSelection(N)}}},{key:"clear",value:function(){this.stack={undo:[],redo:[]}}},{key:"cutoff",value:function(){this.lastRecorded=0}},{key:"record",value:function(E,m){if(E.ops.length!==0){this.stack.redo=[];var b=this.quill.getContents().diff(m),N=Date.now();if(this.lastRecorded+this.options.delay>N&&this.stack.undo.length>0){var p=this.stack.undo.pop();b=b.compose(p.undo),E=p.redo.compose(E)}else this.lastRecorded=N;this.stack.undo.push({redo:E,undo:b}),this.stack.undo.length>this.options.maxStack&&this.stack.undo.shift()}}},{key:"redo",value:function(){this.change("redo","undo")}},{key:"transform",value:function(E){this.stack.undo.forEach(function(m){m.undo=E.transform(m.undo,!0),m.redo=E.transform(m.redo,!0)}),this.stack.redo.forEach(function(m){m.undo=E.transform(m.undo,!0),m.redo=E.transform(m.redo,!0)})}},{key:"undo",value:function(){this.change("undo","redo")}}]),n}(r.default);a.DEFAULTS={delay:1e3,maxStack:100,userOnly:!1};function i(f){var n=f.ops[f.ops.length-1];return n==null?!1:n.insert!=null?typeof n.insert=="string"&&n.insert.endsWith(`
`):n.attributes!=null?Object.keys(n.attributes).some(function(s){return k.default.query(s,k.default.Scope.BLOCK)!=null}):!1}function o(f){var n=f.reduce(function(E,m){return E+=m.delete||0,E},0),s=f.length()-n;return i(f)&&(s-=1),s}_.default=a,_.getLastChangeIndex=o},function(B,_,d){Object.defineProperty(_,"__esModule",{value:!0}),_.default=_.BaseTooltip=void 0;var P=function(){function S(L,F){for(var M=0;M<F.length;M++){var x=F[M];x.enumerable=x.enumerable||!1,x.configurable=!0,"value"in x&&(x.writable=!0),Object.defineProperty(L,x.key,x)}}return function(L,F,M){return F&&S(L.prototype,F),M&&S(L,M),L}}(),w=function S(L,F,M){L===null&&(L=Function.prototype);var x=Object.getOwnPropertyDescriptor(L,F);if(x===void 0){var j=Object.getPrototypeOf(L);return j===null?void 0:S(j,F,M)}else{if("value"in x)return x.value;var U=x.get;return U===void 0?void 0:U.call(M)}},k=d(3),g=N(k),y=d(2),c=N(y),r=d(8),t=N(r),e=d(23),u=N(e),l=d(34),a=N(l),i=d(59),o=N(i),f=d(60),n=N(f),s=d(28),E=N(s),m=d(61),b=N(m);function N(S){return S&&S.__esModule?S:{default:S}}function p(S,L){if(!(S instanceof L))throw new TypeError("Cannot call a class as a function")}function h(S,L){if(!S)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return L&&(typeof L=="object"||typeof L=="function")?L:S}function v(S,L){if(typeof L!="function"&&L!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof L);S.prototype=Object.create(L&&L.prototype,{constructor:{value:S,enumerable:!1,writable:!0,configurable:!0}}),L&&(Object.setPrototypeOf?Object.setPrototypeOf(S,L):S.__proto__=L)}var A=[!1,"center","right","justify"],T=["#000000","#e60000","#ff9900","#ffff00","#008a00","#0066cc","#9933ff","#ffffff","#facccc","#ffebcc","#ffffcc","#cce8cc","#cce0f5","#ebd6ff","#bbbbbb","#f06666","#ffc266","#ffff66","#66b966","#66a3e0","#c285ff","#888888","#a10000","#b26b00","#b2b200","#006100","#0047b2","#6b24b2","#444444","#5c0000","#663d00","#666600","#003700","#002966","#3d1466"],q=[!1,"serif","monospace"],D=["1","2","3",!1],C=["small",!1,"large","huge"],Z=function(S){v(L,S);function L(F,M){p(this,L);var x=h(this,(L.__proto__||Object.getPrototypeOf(L)).call(this,F,M)),j=function U(H){if(!document.body.contains(F.root))return document.body.removeEventListener("click",U);x.tooltip!=null&&!x.tooltip.root.contains(H.target)&&document.activeElement!==x.tooltip.textbox&&!x.quill.hasFocus()&&x.tooltip.hide(),x.pickers!=null&&x.pickers.forEach(function(V){V.container.contains(H.target)||V.close()})};return F.emitter.listenDOM("click",document.body,j),x}return P(L,[{key:"addModule",value:function(M){var x=w(L.prototype.__proto__||Object.getPrototypeOf(L.prototype),"addModule",this).call(this,M);return M==="toolbar"&&this.extendToolbar(x),x}},{key:"buildButtons",value:function(M,x){M.forEach(function(j){var U=j.getAttribute("class")||"";U.split(/\s+/).forEach(function(H){if(H.startsWith("ql-")&&(H=H.slice(3),x[H]!=null))if(H==="direction")j.innerHTML=x[H][""]+x[H].rtl;else if(typeof x[H]=="string")j.innerHTML=x[H];else{var V=j.value||"";V!=null&&x[H][V]&&(j.innerHTML=x[H][V])}})})}},{key:"buildPickers",value:function(M,x){var j=this;this.pickers=M.map(function(H){if(H.classList.contains("ql-align"))return H.querySelector("option")==null&&O(H,A),new n.default(H,x.align);if(H.classList.contains("ql-background")||H.classList.contains("ql-color")){var V=H.classList.contains("ql-background")?"background":"color";return H.querySelector("option")==null&&O(H,T,V==="background"?"#ffffff":"#000000"),new o.default(H,x[V])}else return H.querySelector("option")==null&&(H.classList.contains("ql-font")?O(H,q):H.classList.contains("ql-header")?O(H,D):H.classList.contains("ql-size")&&O(H,C)),new E.default(H)});var U=function(){j.pickers.forEach(function(V){V.update()})};this.quill.on(t.default.events.EDITOR_CHANGE,U)}}]),L}(a.default);Z.DEFAULTS=(0,g.default)(!0,{},a.default.DEFAULTS,{modules:{toolbar:{handlers:{formula:function(){this.quill.theme.tooltip.edit("formula")},image:function(){var L=this,F=this.container.querySelector("input.ql-image[type=file]");F==null&&(F=document.createElement("input"),F.setAttribute("type","file"),F.setAttribute("accept","image/png, image/gif, image/jpeg, image/bmp, image/x-icon"),F.classList.add("ql-image"),F.addEventListener("change",function(){if(F.files!=null&&F.files[0]!=null){var M=new FileReader;M.onload=function(x){var j=L.quill.getSelection(!0);L.quill.updateContents(new c.default().retain(j.index).delete(j.length).insert({image:x.target.result}),t.default.sources.USER),L.quill.setSelection(j.index+1,t.default.sources.SILENT),F.value=""},M.readAsDataURL(F.files[0])}}),this.container.appendChild(F)),F.click()},video:function(){this.quill.theme.tooltip.edit("video")}}}}});var I=function(S){v(L,S);function L(F,M){p(this,L);var x=h(this,(L.__proto__||Object.getPrototypeOf(L)).call(this,F,M));return x.textbox=x.root.querySelector('input[type="text"]'),x.listen(),x}return P(L,[{key:"listen",value:function(){var M=this;this.textbox.addEventListener("keydown",function(x){u.default.match(x,"enter")?(M.save(),x.preventDefault()):u.default.match(x,"escape")&&(M.cancel(),x.preventDefault())})}},{key:"cancel",value:function(){this.hide()}},{key:"edit",value:function(){var M=arguments.length>0&&arguments[0]!==void 0?arguments[0]:"link",x=arguments.length>1&&arguments[1]!==void 0?arguments[1]:null;this.root.classList.remove("ql-hidden"),this.root.classList.add("ql-editing"),x!=null?this.textbox.value=x:M!==this.root.getAttribute("data-mode")&&(this.textbox.value=""),this.position(this.quill.getBounds(this.quill.selection.savedRange)),this.textbox.select(),this.textbox.setAttribute("placeholder",this.textbox.getAttribute("data-"+M)||""),this.root.setAttribute("data-mode",M)}},{key:"restoreFocus",value:function(){var M=this.quill.scrollingContainer.scrollTop;this.quill.focus(),this.quill.scrollingContainer.scrollTop=M}},{key:"save",value:function(){var M=this.textbox.value;switch(this.root.getAttribute("data-mode")){case"link":{var x=this.quill.root.scrollTop;this.linkRange?(this.quill.formatText(this.linkRange,"link",M,t.default.sources.USER),delete this.linkRange):(this.restoreFocus(),this.quill.format("link",M,t.default.sources.USER)),this.quill.root.scrollTop=x;break}case"video":M=R(M);case"formula":{if(!M)break;var j=this.quill.getSelection(!0);if(j!=null){var U=j.index+j.length;this.quill.insertEmbed(U,this.root.getAttribute("data-mode"),M,t.default.sources.USER),this.root.getAttribute("data-mode")==="formula"&&this.quill.insertText(U+1," ",t.default.sources.USER),this.quill.setSelection(U+2,t.default.sources.USER)}break}}this.textbox.value="",this.hide()}}]),L}(b.default);function R(S){var L=S.match(/^(?:(https?):\/\/)?(?:(?:www|m)\.)?youtube\.com\/watch.*v=([a-zA-Z0-9_-]+)/)||S.match(/^(?:(https?):\/\/)?(?:(?:www|m)\.)?youtu\.be\/([a-zA-Z0-9_-]+)/);return L?(L[1]||"https")+"://www.youtube.com/embed/"+L[2]+"?showinfo=0":(L=S.match(/^(?:(https?):\/\/)?(?:www\.)?vimeo\.com\/(\d+)/))?(L[1]||"https")+"://player.vimeo.com/video/"+L[2]+"/":S}function O(S,L){var F=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!1;L.forEach(function(M){var x=document.createElement("option");M===F?x.setAttribute("selected","selected"):x.setAttribute("value",M),S.appendChild(x)})}_.BaseTooltip=I,_.default=Z},function(B,_,d){Object.defineProperty(_,"__esModule",{value:!0});var P=function(){function w(){this.head=this.tail=null,this.length=0}return w.prototype.append=function(){for(var k=[],g=0;g<arguments.length;g++)k[g]=arguments[g];this.insertBefore(k[0],null),k.length>1&&this.append.apply(this,k.slice(1))},w.prototype.contains=function(k){for(var g,y=this.iterator();g=y();)if(g===k)return!0;return!1},w.prototype.insertBefore=function(k,g){k&&(k.next=g,g!=null?(k.prev=g.prev,g.prev!=null&&(g.prev.next=k),g.prev=k,g===this.head&&(this.head=k)):this.tail!=null?(this.tail.next=k,k.prev=this.tail,this.tail=k):(k.prev=null,this.head=this.tail=k),this.length+=1)},w.prototype.offset=function(k){for(var g=0,y=this.head;y!=null;){if(y===k)return g;g+=y.length(),y=y.next}return-1},w.prototype.remove=function(k){this.contains(k)&&(k.prev!=null&&(k.prev.next=k.next),k.next!=null&&(k.next.prev=k.prev),k===this.head&&(this.head=k.next),k===this.tail&&(this.tail=k.prev),this.length-=1)},w.prototype.iterator=function(k){return k===void 0&&(k=this.head),function(){var g=k;return k!=null&&(k=k.next),g}},w.prototype.find=function(k,g){g===void 0&&(g=!1);for(var y,c=this.iterator();y=c();){var r=y.length();if(k<r||g&&k===r&&(y.next==null||y.next.length()!==0))return[y,k];k-=r}return[null,0]},w.prototype.forEach=function(k){for(var g,y=this.iterator();g=y();)k(g)},w.prototype.forEachAt=function(k,g,y){if(!(g<=0))for(var c=this.find(k),r=c[0],t=c[1],e,u=k-t,l=this.iterator(r);(e=l())&&u<k+g;){var a=e.length();k>u?y(e,k-u,Math.min(g,u+a-k)):y(e,0,Math.min(a,k+g-u)),u+=a}},w.prototype.map=function(k){return this.reduce(function(g,y){return g.push(k(y)),g},[])},w.prototype.reduce=function(k,g){for(var y,c=this.iterator();y=c();)g=k(g,y);return g},w}();_.default=P},function(B,_,d){var P=this&&this.__extends||function(){var r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var u in e)e.hasOwnProperty(u)&&(t[u]=e[u])};return function(t,e){r(t,e);function u(){this.constructor=t}t.prototype=e===null?Object.create(e):(u.prototype=e.prototype,new u)}}();Object.defineProperty(_,"__esModule",{value:!0});var w=d(17),k=d(1),g={attributes:!0,characterData:!0,characterDataOldValue:!0,childList:!0,subtree:!0},y=100,c=function(r){P(t,r);function t(e){var u=r.call(this,e)||this;return u.scroll=u,u.observer=new MutationObserver(function(l){u.update(l)}),u.observer.observe(u.domNode,g),u.attach(),u}return t.prototype.detach=function(){r.prototype.detach.call(this),this.observer.disconnect()},t.prototype.deleteAt=function(e,u){this.update(),e===0&&u===this.length()?this.children.forEach(function(l){l.remove()}):r.prototype.deleteAt.call(this,e,u)},t.prototype.formatAt=function(e,u,l,a){this.update(),r.prototype.formatAt.call(this,e,u,l,a)},t.prototype.insertAt=function(e,u,l){this.update(),r.prototype.insertAt.call(this,e,u,l)},t.prototype.optimize=function(e,u){var l=this;e===void 0&&(e=[]),u===void 0&&(u={}),r.prototype.optimize.call(this,u);for(var a=[].slice.call(this.observer.takeRecords());a.length>0;)e.push(a.pop());for(var i=function(s,E){E===void 0&&(E=!0),!(s==null||s===l)&&s.domNode.parentNode!=null&&(s.domNode[k.DATA_KEY].mutations==null&&(s.domNode[k.DATA_KEY].mutations=[]),E&&i(s.parent))},o=function(s){s.domNode[k.DATA_KEY]==null||s.domNode[k.DATA_KEY].mutations==null||(s instanceof w.default&&s.children.forEach(o),s.optimize(u))},f=e,n=0;f.length>0;n+=1){if(n>=y)throw new Error("[Parchment] Maximum optimize iterations reached");for(f.forEach(function(s){var E=k.find(s.target,!0);E!=null&&(E.domNode===s.target&&(s.type==="childList"?(i(k.find(s.previousSibling,!1)),[].forEach.call(s.addedNodes,function(m){var b=k.find(m,!1);i(b,!1),b instanceof w.default&&b.children.forEach(function(N){i(N,!1)})})):s.type==="attributes"&&i(E.prev)),i(E))}),this.children.forEach(o),f=[].slice.call(this.observer.takeRecords()),a=f.slice();a.length>0;)e.push(a.pop())}},t.prototype.update=function(e,u){var l=this;u===void 0&&(u={}),e=e||this.observer.takeRecords(),e.map(function(a){var i=k.find(a.target,!0);return i==null?null:i.domNode[k.DATA_KEY].mutations==null?(i.domNode[k.DATA_KEY].mutations=[a],i):(i.domNode[k.DATA_KEY].mutations.push(a),null)}).forEach(function(a){a==null||a===l||a.domNode[k.DATA_KEY]==null||a.update(a.domNode[k.DATA_KEY].mutations||[],u)}),this.domNode[k.DATA_KEY].mutations!=null&&r.prototype.update.call(this,this.domNode[k.DATA_KEY].mutations,u),this.optimize(e,u)},t.blotName="scroll",t.defaultChild="block",t.scope=k.Scope.BLOCK_BLOT,t.tagName="DIV",t}(w.default);_.default=c},function(B,_,d){var P=this&&this.__extends||function(){var c=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(r,t){r.__proto__=t}||function(r,t){for(var e in t)t.hasOwnProperty(e)&&(r[e]=t[e])};return function(r,t){c(r,t);function e(){this.constructor=r}r.prototype=t===null?Object.create(t):(e.prototype=t.prototype,new e)}}();Object.defineProperty(_,"__esModule",{value:!0});var w=d(18),k=d(1);function g(c,r){if(Object.keys(c).length!==Object.keys(r).length)return!1;for(var t in c)if(c[t]!==r[t])return!1;return!0}var y=function(c){P(r,c);function r(){return c!==null&&c.apply(this,arguments)||this}return r.formats=function(t){if(t.tagName!==r.tagName)return c.formats.call(this,t)},r.prototype.format=function(t,e){var u=this;t===this.statics.blotName&&!e?(this.children.forEach(function(l){l instanceof w.default||(l=l.wrap(r.blotName,!0)),u.attributes.copy(l)}),this.unwrap()):c.prototype.format.call(this,t,e)},r.prototype.formatAt=function(t,e,u,l){if(this.formats()[u]!=null||k.query(u,k.Scope.ATTRIBUTE)){var a=this.isolate(t,e);a.format(u,l)}else c.prototype.formatAt.call(this,t,e,u,l)},r.prototype.optimize=function(t){c.prototype.optimize.call(this,t);var e=this.formats();if(Object.keys(e).length===0)return this.unwrap();var u=this.next;u instanceof r&&u.prev===this&&g(e,u.formats())&&(u.moveChildren(this),u.remove())},r.blotName="inline",r.scope=k.Scope.INLINE_BLOT,r.tagName="SPAN",r}(w.default);_.default=y},function(B,_,d){var P=this&&this.__extends||function(){var y=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(c,r){c.__proto__=r}||function(c,r){for(var t in r)r.hasOwnProperty(t)&&(c[t]=r[t])};return function(c,r){y(c,r);function t(){this.constructor=c}c.prototype=r===null?Object.create(r):(t.prototype=r.prototype,new t)}}();Object.defineProperty(_,"__esModule",{value:!0});var w=d(18),k=d(1),g=function(y){P(c,y);function c(){return y!==null&&y.apply(this,arguments)||this}return c.formats=function(r){var t=k.query(c.blotName).tagName;if(r.tagName!==t)return y.formats.call(this,r)},c.prototype.format=function(r,t){k.query(r,k.Scope.BLOCK)!=null&&(r===this.statics.blotName&&!t?this.replaceWith(c.blotName):y.prototype.format.call(this,r,t))},c.prototype.formatAt=function(r,t,e,u){k.query(e,k.Scope.BLOCK)!=null?this.format(e,u):y.prototype.formatAt.call(this,r,t,e,u)},c.prototype.insertAt=function(r,t,e){if(e==null||k.query(t,k.Scope.INLINE)!=null)y.prototype.insertAt.call(this,r,t,e);else{var u=this.split(r),l=k.create(t,e);u.parent.insertBefore(l,u)}},c.prototype.update=function(r,t){navigator.userAgent.match(/Trident/)?this.build():y.prototype.update.call(this,r,t)},c.blotName="block",c.scope=k.Scope.BLOCK_BLOT,c.tagName="P",c}(w.default);_.default=g},function(B,_,d){var P=this&&this.__extends||function(){var g=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(y,c){y.__proto__=c}||function(y,c){for(var r in c)c.hasOwnProperty(r)&&(y[r]=c[r])};return function(y,c){g(y,c);function r(){this.constructor=y}y.prototype=c===null?Object.create(c):(r.prototype=c.prototype,new r)}}();Object.defineProperty(_,"__esModule",{value:!0});var w=d(19),k=function(g){P(y,g);function y(){return g!==null&&g.apply(this,arguments)||this}return y.formats=function(c){},y.prototype.format=function(c,r){g.prototype.formatAt.call(this,0,this.length(),c,r)},y.prototype.formatAt=function(c,r,t,e){c===0&&r===this.length()?this.format(t,e):g.prototype.formatAt.call(this,c,r,t,e)},y.prototype.formats=function(){return this.statics.formats(this.domNode)},y}(w.default);_.default=k},function(B,_,d){var P=this&&this.__extends||function(){var y=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(c,r){c.__proto__=r}||function(c,r){for(var t in r)r.hasOwnProperty(t)&&(c[t]=r[t])};return function(c,r){y(c,r);function t(){this.constructor=c}c.prototype=r===null?Object.create(r):(t.prototype=r.prototype,new t)}}();Object.defineProperty(_,"__esModule",{value:!0});var w=d(19),k=d(1),g=function(y){P(c,y);function c(r){var t=y.call(this,r)||this;return t.text=t.statics.value(t.domNode),t}return c.create=function(r){return document.createTextNode(r)},c.value=function(r){var t=r.data;return t.normalize&&(t=t.normalize()),t},c.prototype.deleteAt=function(r,t){this.domNode.data=this.text=this.text.slice(0,r)+this.text.slice(r+t)},c.prototype.index=function(r,t){return this.domNode===r?t:-1},c.prototype.insertAt=function(r,t,e){e==null?(this.text=this.text.slice(0,r)+t+this.text.slice(r),this.domNode.data=this.text):y.prototype.insertAt.call(this,r,t,e)},c.prototype.length=function(){return this.text.length},c.prototype.optimize=function(r){y.prototype.optimize.call(this,r),this.text=this.statics.value(this.domNode),this.text.length===0?this.remove():this.next instanceof c&&this.next.prev===this&&(this.insertAt(this.length(),this.next.value()),this.next.remove())},c.prototype.position=function(r,t){return[this.domNode,r]},c.prototype.split=function(r,t){if(t===void 0&&(t=!1),!t){if(r===0)return this;if(r===this.length())return this.next}var e=k.create(this.domNode.splitText(r));return this.parent.insertBefore(e,this.next),this.text=this.statics.value(this.domNode),e},c.prototype.update=function(r,t){var e=this;r.some(function(u){return u.type==="characterData"&&u.target===e.domNode})&&(this.text=this.statics.value(this.domNode))},c.prototype.value=function(){return this.text},c.blotName="text",c.scope=k.Scope.INLINE_BLOT,c}(w.default);_.default=g},function(B,_,d){var P=document.createElement("div");if(P.classList.toggle("test-class",!1),P.classList.contains("test-class")){var w=DOMTokenList.prototype.toggle;DOMTokenList.prototype.toggle=function(k,g){return arguments.length>1&&!this.contains(k)==!g?g:w.call(this,k)}}String.prototype.startsWith||(String.prototype.startsWith=function(k,g){return g=g||0,this.substr(g,k.length)===k}),String.prototype.endsWith||(String.prototype.endsWith=function(k,g){var y=this.toString();(typeof g!="number"||!isFinite(g)||Math.floor(g)!==g||g>y.length)&&(g=y.length),g-=k.length;var c=y.indexOf(k,g);return c!==-1&&c===g}),Array.prototype.find||Object.defineProperty(Array.prototype,"find",{value:function(g){if(this===null)throw new TypeError("Array.prototype.find called on null or undefined");if(typeof g!="function")throw new TypeError("predicate must be a function");for(var y=Object(this),c=y.length>>>0,r=arguments[1],t,e=0;e<c;e++)if(t=y[e],g.call(r,t,e,y))return t}}),document.addEventListener("DOMContentLoaded",function(){document.execCommand("enableObjectResizing",!1,!1),document.execCommand("autoUrlDetect",!1,!1)})},function(B,_){var d=-1,P=1,w=0;function k(n,s,E){if(n==s)return n?[[w,n]]:[];(E<0||n.length<E)&&(E=null);var m=r(n,s),b=n.substring(0,m);n=n.substring(m),s=s.substring(m),m=t(n,s);var N=n.substring(n.length-m);n=n.substring(0,n.length-m),s=s.substring(0,s.length-m);var p=g(n,s);return b&&p.unshift([w,b]),N&&p.push([w,N]),u(p),E!=null&&(p=i(p,E)),p=o(p),p}function g(n,s){var E;if(!n)return[[P,s]];if(!s)return[[d,n]];var m=n.length>s.length?n:s,b=n.length>s.length?s:n,N=m.indexOf(b);if(N!=-1)return E=[[P,m.substring(0,N)],[w,b],[P,m.substring(N+b.length)]],n.length>s.length&&(E[0][0]=E[2][0]=d),E;if(b.length==1)return[[d,n],[P,s]];var p=e(n,s);if(p){var h=p[0],v=p[1],A=p[2],T=p[3],q=p[4],D=k(h,A),C=k(v,T);return D.concat([[w,q]],C)}return y(n,s)}function y(n,s){for(var E=n.length,m=s.length,b=Math.ceil((E+m)/2),N=b,p=2*b,h=new Array(p),v=new Array(p),A=0;A<p;A++)h[A]=-1,v[A]=-1;h[N+1]=0,v[N+1]=0;for(var T=E-m,q=T%2!=0,D=0,C=0,Z=0,I=0,R=0;R<b;R++){for(var O=-R+D;O<=R-C;O+=2){var S=N+O,L;O==-R||O!=R&&h[S-1]<h[S+1]?L=h[S+1]:L=h[S-1]+1;for(var F=L-O;L<E&&F<m&&n.charAt(L)==s.charAt(F);)L++,F++;if(h[S]=L,L>E)C+=2;else if(F>m)D+=2;else if(q){var M=N+T-O;if(M>=0&&M<p&&v[M]!=-1){var x=E-v[M];if(L>=x)return c(n,s,L,F)}}}for(var j=-R+Z;j<=R-I;j+=2){var M=N+j,x;j==-R||j!=R&&v[M-1]<v[M+1]?x=v[M+1]:x=v[M-1]+1;for(var U=x-j;x<E&&U<m&&n.charAt(E-x-1)==s.charAt(m-U-1);)x++,U++;if(v[M]=x,x>E)I+=2;else if(U>m)Z+=2;else if(!q){var S=N+T-j;if(S>=0&&S<p&&h[S]!=-1){var L=h[S],F=N+L-S;if(x=E-x,L>=x)return c(n,s,L,F)}}}}return[[d,n],[P,s]]}function c(n,s,E,m){var b=n.substring(0,E),N=s.substring(0,m),p=n.substring(E),h=s.substring(m),v=k(b,N),A=k(p,h);return v.concat(A)}function r(n,s){if(!n||!s||n.charAt(0)!=s.charAt(0))return 0;for(var E=0,m=Math.min(n.length,s.length),b=m,N=0;E<b;)n.substring(N,b)==s.substring(N,b)?(E=b,N=E):m=b,b=Math.floor((m-E)/2+E);return b}function t(n,s){if(!n||!s||n.charAt(n.length-1)!=s.charAt(s.length-1))return 0;for(var E=0,m=Math.min(n.length,s.length),b=m,N=0;E<b;)n.substring(n.length-b,n.length-N)==s.substring(s.length-b,s.length-N)?(E=b,N=E):m=b,b=Math.floor((m-E)/2+E);return b}function e(n,s){var E=n.length>s.length?n:s,m=n.length>s.length?s:n;if(E.length<4||m.length*2<E.length)return null;function b(C,Z,I){for(var R=C.substring(I,I+Math.floor(C.length/4)),O=-1,S="",L,F,M,x;(O=Z.indexOf(R,O+1))!=-1;){var j=r(C.substring(I),Z.substring(O)),U=t(C.substring(0,I),Z.substring(0,O));S.length<U+j&&(S=Z.substring(O-U,O)+Z.substring(O,O+j),L=C.substring(0,I-U),F=C.substring(I+j),M=Z.substring(0,O-U),x=Z.substring(O+j))}return S.length*2>=C.length?[L,F,M,x,S]:null}var N=b(E,m,Math.ceil(E.length/4)),p=b(E,m,Math.ceil(E.length/2)),h;if(!N&&!p)return null;p?N?h=N[4].length>p[4].length?N:p:h=p:h=N;var v,A,T,q;n.length>s.length?(v=h[0],A=h[1],T=h[2],q=h[3]):(T=h[0],q=h[1],v=h[2],A=h[3]);var D=h[4];return[v,A,T,q,D]}function u(n){n.push([w,""]);for(var s=0,E=0,m=0,b="",N="",p;s<n.length;)switch(n[s][0]){case P:m++,N+=n[s][1],s++;break;case d:E++,b+=n[s][1],s++;break;case w:E+m>1?(E!==0&&m!==0&&(p=r(N,b),p!==0&&(s-E-m>0&&n[s-E-m-1][0]==w?n[s-E-m-1][1]+=N.substring(0,p):(n.splice(0,0,[w,N.substring(0,p)]),s++),N=N.substring(p),b=b.substring(p)),p=t(N,b),p!==0&&(n[s][1]=N.substring(N.length-p)+n[s][1],N=N.substring(0,N.length-p),b=b.substring(0,b.length-p))),E===0?n.splice(s-m,E+m,[P,N]):m===0?n.splice(s-E,E+m,[d,b]):n.splice(s-E-m,E+m,[d,b],[P,N]),s=s-E-m+(E?1:0)+(m?1:0)+1):s!==0&&n[s-1][0]==w?(n[s-1][1]+=n[s][1],n.splice(s,1)):s++,m=0,E=0,b="",N="";break}n[n.length-1][1]===""&&n.pop();var h=!1;for(s=1;s<n.length-1;)n[s-1][0]==w&&n[s+1][0]==w&&(n[s][1].substring(n[s][1].length-n[s-1][1].length)==n[s-1][1]?(n[s][1]=n[s-1][1]+n[s][1].substring(0,n[s][1].length-n[s-1][1].length),n[s+1][1]=n[s-1][1]+n[s+1][1],n.splice(s-1,1),h=!0):n[s][1].substring(0,n[s+1][1].length)==n[s+1][1]&&(n[s-1][1]+=n[s+1][1],n[s][1]=n[s][1].substring(n[s+1][1].length)+n[s+1][1],n.splice(s+1,1),h=!0)),s++;h&&u(n)}var l=k;l.INSERT=P,l.DELETE=d,l.EQUAL=w,B.exports=l;function a(n,s){if(s===0)return[w,n];for(var E=0,m=0;m<n.length;m++){var b=n[m];if(b[0]===d||b[0]===w){var N=E+b[1].length;if(s===N)return[m+1,n];if(s<N){n=n.slice();var p=s-E,h=[b[0],b[1].slice(0,p)],v=[b[0],b[1].slice(p)];return n.splice(m,1,h,v),[m+1,n]}else E=N}}throw new Error("cursor_pos is out of bounds!")}function i(n,s){var E=a(n,s),m=E[1],b=E[0],N=m[b],p=m[b+1];if(N==null)return n;if(N[0]!==w)return n;if(p!=null&&N[1]+p[1]===p[1]+N[1])return m.splice(b,2,p,N),f(m,b,2);if(p!=null&&p[1].indexOf(N[1])===0){m.splice(b,2,[p[0],N[1]],[0,N[1]]);var h=p[1].slice(N[1].length);return h.length>0&&m.splice(b+2,0,[p[0],h]),f(m,b,3)}else return n}function o(n){for(var s=!1,E=function(p){return p.charCodeAt(0)>=56320&&p.charCodeAt(0)<=57343},m=function(p){return p.charCodeAt(p.length-1)>=55296&&p.charCodeAt(p.length-1)<=56319},b=2;b<n.length;b+=1)n[b-2][0]===w&&m(n[b-2][1])&&n[b-1][0]===d&&E(n[b-1][1])&&n[b][0]===P&&E(n[b][1])&&(s=!0,n[b-1][1]=n[b-2][1].slice(-1)+n[b-1][1],n[b][1]=n[b-2][1].slice(-1)+n[b][1],n[b-2][1]=n[b-2][1].slice(0,-1));if(!s)return n;for(var N=[],b=0;b<n.length;b+=1)n[b][1].length>0&&N.push(n[b]);return N}function f(n,s,E){for(var m=s+E-1;m>=0&&m>=s-1;m--)if(m+1<n.length){var b=n[m],N=n[m+1];b[0]===N[1]&&n.splice(m,2,[b[0],b[1]+N[1]])}return n}},function(B,_){_=B.exports=typeof Object.keys=="function"?Object.keys:d,_.shim=d;function d(P){var w=[];for(var k in P)w.push(k);return w}},function(B,_){var d=function(){return Object.prototype.toString.call(arguments)}()=="[object Arguments]";_=B.exports=d?P:w,_.supported=P;function P(k){return Object.prototype.toString.call(k)=="[object Arguments]"}_.unsupported=w;function w(k){return k&&typeof k=="object"&&typeof k.length=="number"&&Object.prototype.hasOwnProperty.call(k,"callee")&&!Object.prototype.propertyIsEnumerable.call(k,"callee")||!1}},function(B,_){var d=Object.prototype.hasOwnProperty,P="~";function w(){}Object.create&&(w.prototype=Object.create(null),new w().__proto__||(P=!1));function k(y,c,r){this.fn=y,this.context=c,this.once=r||!1}function g(){this._events=new w,this._eventsCount=0}g.prototype.eventNames=function(){var c=[],r,t;if(this._eventsCount===0)return c;for(t in r=this._events)d.call(r,t)&&c.push(P?t.slice(1):t);return Object.getOwnPropertySymbols?c.concat(Object.getOwnPropertySymbols(r)):c},g.prototype.listeners=function(c,r){var t=P?P+c:c,e=this._events[t];if(r)return!!e;if(!e)return[];if(e.fn)return[e.fn];for(var u=0,l=e.length,a=new Array(l);u<l;u++)a[u]=e[u].fn;return a},g.prototype.emit=function(c,r,t,e,u,l){var a=P?P+c:c;if(!this._events[a])return!1;var i=this._events[a],o=arguments.length,f,n;if(i.fn){switch(i.once&&this.removeListener(c,i.fn,void 0,!0),o){case 1:return i.fn.call(i.context),!0;case 2:return i.fn.call(i.context,r),!0;case 3:return i.fn.call(i.context,r,t),!0;case 4:return i.fn.call(i.context,r,t,e),!0;case 5:return i.fn.call(i.context,r,t,e,u),!0;case 6:return i.fn.call(i.context,r,t,e,u,l),!0}for(n=1,f=new Array(o-1);n<o;n++)f[n-1]=arguments[n];i.fn.apply(i.context,f)}else{var s=i.length,E;for(n=0;n<s;n++)switch(i[n].once&&this.removeListener(c,i[n].fn,void 0,!0),o){case 1:i[n].fn.call(i[n].context);break;case 2:i[n].fn.call(i[n].context,r);break;case 3:i[n].fn.call(i[n].context,r,t);break;case 4:i[n].fn.call(i[n].context,r,t,e);break;default:if(!f)for(E=1,f=new Array(o-1);E<o;E++)f[E-1]=arguments[E];i[n].fn.apply(i[n].context,f)}}return!0},g.prototype.on=function(c,r,t){var e=new k(r,t||this),u=P?P+c:c;return this._events[u]?this._events[u].fn?this._events[u]=[this._events[u],e]:this._events[u].push(e):(this._events[u]=e,this._eventsCount++),this},g.prototype.once=function(c,r,t){var e=new k(r,t||this,!0),u=P?P+c:c;return this._events[u]?this._events[u].fn?this._events[u]=[this._events[u],e]:this._events[u].push(e):(this._events[u]=e,this._eventsCount++),this},g.prototype.removeListener=function(c,r,t,e){var u=P?P+c:c;if(!this._events[u])return this;if(!r)return--this._eventsCount===0?this._events=new w:delete this._events[u],this;var l=this._events[u];if(l.fn)l.fn===r&&(!e||l.once)&&(!t||l.context===t)&&(--this._eventsCount===0?this._events=new w:delete this._events[u]);else{for(var a=0,i=[],o=l.length;a<o;a++)(l[a].fn!==r||e&&!l[a].once||t&&l[a].context!==t)&&i.push(l[a]);i.length?this._events[u]=i.length===1?i[0]:i:--this._eventsCount===0?this._events=new w:delete this._events[u]}return this},g.prototype.removeAllListeners=function(c){var r;return c?(r=P?P+c:c,this._events[r]&&(--this._eventsCount===0?this._events=new w:delete this._events[r])):(this._events=new w,this._eventsCount=0),this},g.prototype.off=g.prototype.removeListener,g.prototype.addListener=g.prototype.on,g.prototype.setMaxListeners=function(){return this},g.prefixed=P,g.EventEmitter=g,typeof B!="undefined"&&(B.exports=g)},function(B,_,d){Object.defineProperty(_,"__esModule",{value:!0}),_.matchText=_.matchSpacing=_.matchNewline=_.matchBlot=_.matchAttributor=_.default=void 0;var P=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(K){return typeof K}:function(K){return K&&typeof Symbol=="function"&&K.constructor===Symbol&&K!==Symbol.prototype?"symbol":typeof K},w=function(){function K(z,$){var G=[],W=!0,J=!1,tt=void 0;try{for(var et=z[Symbol.iterator](),ut;!(W=(ut=et.next()).done)&&(G.push(ut.value),!($&&G.length===$));W=!0);}catch(st){J=!0,tt=st}finally{try{!W&&et.return&&et.return()}finally{if(J)throw tt}}return G}return function(z,$){if(Array.isArray(z))return z;if(Symbol.iterator in Object(z))return K(z,$);throw new TypeError("Invalid attempt to destructure non-iterable instance")}}(),k=function(){function K(z,$){for(var G=0;G<$.length;G++){var W=$[G];W.enumerable=W.enumerable||!1,W.configurable=!0,"value"in W&&(W.writable=!0),Object.defineProperty(z,W.key,W)}}return function(z,$,G){return $&&K(z.prototype,$),G&&K(z,G),z}}(),g=d(3),y=v(g),c=d(2),r=v(c),t=d(0),e=v(t),u=d(5),l=v(u),a=d(10),i=v(a),o=d(9),f=v(o),n=d(36),s=d(37),E=d(13),m=v(E),b=d(26),N=d(38),p=d(39),h=d(40);function v(K){return K&&K.__esModule?K:{default:K}}function A(K,z,$){return z in K?Object.defineProperty(K,z,{value:$,enumerable:!0,configurable:!0,writable:!0}):K[z]=$,K}function T(K,z){if(!(K instanceof z))throw new TypeError("Cannot call a class as a function")}function q(K,z){if(!K)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return z&&(typeof z=="object"||typeof z=="function")?z:K}function D(K,z){if(typeof z!="function"&&z!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof z);K.prototype=Object.create(z&&z.prototype,{constructor:{value:K,enumerable:!1,writable:!0,configurable:!0}}),z&&(Object.setPrototypeOf?Object.setPrototypeOf(K,z):K.__proto__=z)}var C=(0,i.default)("quill:clipboard"),Z="__ql-matcher",I=[[Node.TEXT_NODE,lt],[Node.TEXT_NODE,nt],["br",Y],[Node.ELEMENT_NODE,nt],[Node.ELEMENT_NODE,V],[Node.ELEMENT_NODE,rt],[Node.ELEMENT_NODE,H],[Node.ELEMENT_NODE,at],["li",X],["b",U.bind(U,"bold")],["i",U.bind(U,"italic")],["style",Q]],R=[n.AlignAttribute,N.DirectionAttribute].reduce(function(K,z){return K[z.keyName]=z,K},{}),O=[n.AlignStyle,s.BackgroundStyle,b.ColorStyle,N.DirectionStyle,p.FontStyle,h.SizeStyle].reduce(function(K,z){return K[z.keyName]=z,K},{}),S=function(K){D(z,K);function z($,G){T(this,z);var W=q(this,(z.__proto__||Object.getPrototypeOf(z)).call(this,$,G));return W.quill.root.addEventListener("paste",W.onPaste.bind(W)),W.container=W.quill.addContainer("ql-clipboard"),W.container.setAttribute("contenteditable",!0),W.container.setAttribute("tabindex",-1),W.matchers=[],I.concat(W.options.matchers).forEach(function(J){var tt=w(J,2),et=tt[0],ut=tt[1];!G.matchVisual&&ut===rt||W.addMatcher(et,ut)}),W}return k(z,[{key:"addMatcher",value:function(G,W){this.matchers.push([G,W])}},{key:"convert",value:function(G){if(typeof G=="string")return this.container.innerHTML=G.replace(/\>\r?\n +\</g,"><"),this.convert();var W=this.quill.getFormat(this.quill.selection.savedRange.index);if(W[m.default.blotName]){var J=this.container.innerText;return this.container.innerHTML="",new r.default().insert(J,A({},m.default.blotName,W[m.default.blotName]))}var tt=this.prepareMatching(),et=w(tt,2),ut=et[0],st=et[1],it=j(this.container,ut,st);return M(it,`
`)&&it.ops[it.ops.length-1].attributes==null&&(it=it.compose(new r.default().retain(it.length()-1).delete(1))),C.log("convert",this.container.innerHTML,it),this.container.innerHTML="",it}},{key:"dangerouslyPasteHTML",value:function(G,W){var J=arguments.length>2&&arguments[2]!==void 0?arguments[2]:l.default.sources.API;if(typeof G=="string")this.quill.setContents(this.convert(G),W),this.quill.setSelection(0,l.default.sources.SILENT);else{var tt=this.convert(W);this.quill.updateContents(new r.default().retain(G).concat(tt),J),this.quill.setSelection(G+tt.length(),l.default.sources.SILENT)}}},{key:"onPaste",value:function(G){var W=this;if(!(G.defaultPrevented||!this.quill.isEnabled())){var J=this.quill.getSelection(),tt=new r.default().retain(J.index),et=this.quill.scrollingContainer.scrollTop;this.container.focus(),this.quill.selection.update(l.default.sources.SILENT),setTimeout(function(){tt=tt.concat(W.convert()).delete(J.length),W.quill.updateContents(tt,l.default.sources.USER),W.quill.setSelection(tt.length()-J.length,l.default.sources.SILENT),W.quill.scrollingContainer.scrollTop=et,W.quill.focus()},1)}}},{key:"prepareMatching",value:function(){var G=this,W=[],J=[];return this.matchers.forEach(function(tt){var et=w(tt,2),ut=et[0],st=et[1];switch(ut){case Node.TEXT_NODE:J.push(st);break;case Node.ELEMENT_NODE:W.push(st);break;default:[].forEach.call(G.container.querySelectorAll(ut),function(it){it[Z]=it[Z]||[],it[Z].push(st)});break}}),[W,J]}}]),z}(f.default);S.DEFAULTS={matchers:[],matchVisual:!0};function L(K,z,$){return(typeof z=="undefined"?"undefined":P(z))==="object"?Object.keys(z).reduce(function(G,W){return L(G,W,z[W])},K):K.reduce(function(G,W){return W.attributes&&W.attributes[z]?G.push(W):G.insert(W.insert,(0,y.default)({},A({},z,$),W.attributes))},new r.default)}function F(K){if(K.nodeType!==Node.ELEMENT_NODE)return{};var z="__ql-computed-style";return K[z]||(K[z]=window.getComputedStyle(K))}function M(K,z){for(var $="",G=K.ops.length-1;G>=0&&$.length<z.length;--G){var W=K.ops[G];if(typeof W.insert!="string")break;$=W.insert+$}return $.slice(-1*z.length)===z}function x(K){if(K.childNodes.length===0)return!1;var z=F(K);return["block","list-item"].indexOf(z.display)>-1}function j(K,z,$){return K.nodeType===K.TEXT_NODE?$.reduce(function(G,W){return W(K,G)},new r.default):K.nodeType===K.ELEMENT_NODE?[].reduce.call(K.childNodes||[],function(G,W){var J=j(W,z,$);return W.nodeType===K.ELEMENT_NODE&&(J=z.reduce(function(tt,et){return et(W,tt)},J),J=(W[Z]||[]).reduce(function(tt,et){return et(W,tt)},J)),G.concat(J)},new r.default):new r.default}function U(K,z,$){return L($,K,!0)}function H(K,z){var $=e.default.Attributor.Attribute.keys(K),G=e.default.Attributor.Class.keys(K),W=e.default.Attributor.Style.keys(K),J={};return $.concat(G).concat(W).forEach(function(tt){var et=e.default.query(tt,e.default.Scope.ATTRIBUTE);et!=null&&(J[et.attrName]=et.value(K),J[et.attrName])||(et=R[tt],et!=null&&(et.attrName===tt||et.keyName===tt)&&(J[et.attrName]=et.value(K)||void 0),et=O[tt],et!=null&&(et.attrName===tt||et.keyName===tt)&&(et=O[tt],J[et.attrName]=et.value(K)||void 0))}),Object.keys(J).length>0&&(z=L(z,J)),z}function V(K,z){var $=e.default.query(K);if($==null)return z;if($.prototype instanceof e.default.Embed){var G={},W=$.value(K);W!=null&&(G[$.blotName]=W,z=new r.default().insert(G,$.formats(K)))}else typeof $.formats=="function"&&(z=L(z,$.blotName,$.formats(K)));return z}function Y(K,z){return M(z,`
`)||z.insert(`
`),z}function Q(){return new r.default}function X(K,z){var $=e.default.query(K);if($==null||$.blotName!=="list-item"||!M(z,`
`))return z;for(var G=-1,W=K.parentNode;!W.classList.contains("ql-clipboard");)(e.default.query(W)||{}).blotName==="list"&&(G+=1),W=W.parentNode;return G<=0?z:z.compose(new r.default().retain(z.length()-1).retain(1,{indent:G}))}function nt(K,z){return M(z,`
`)||(x(K)||z.length()>0&&K.nextSibling&&x(K.nextSibling))&&z.insert(`
`),z}function rt(K,z){if(x(K)&&K.nextElementSibling!=null&&!M(z,`

`)){var $=K.offsetHeight+parseFloat(F(K).marginTop)+parseFloat(F(K).marginBottom);K.nextElementSibling.offsetTop>K.offsetTop+$*1.5&&z.insert(`
`)}return z}function at(K,z){var $={},G=K.style||{};return G.fontStyle&&F(K).fontStyle==="italic"&&($.italic=!0),G.fontWeight&&(F(K).fontWeight.startsWith("bold")||parseInt(F(K).fontWeight)>=700)&&($.bold=!0),Object.keys($).length>0&&(z=L(z,$)),parseFloat(G.textIndent||0)>0&&(z=new r.default().insert("	").concat(z)),z}function lt(K,z){var $=K.data;if(K.parentNode.tagName==="O:P")return z.insert($.trim());if($.trim().length===0&&K.parentNode.classList.contains("ql-clipboard"))return z;if(!F(K.parentNode).whiteSpace.startsWith("pre")){var G=function(J,tt){return tt=tt.replace(/[^\u00a0]/g,""),tt.length<1&&J?" ":tt};$=$.replace(/\r\n/g," ").replace(/\n/g," "),$=$.replace(/\s\s+/g,G.bind(G,!0)),(K.previousSibling==null&&x(K.parentNode)||K.previousSibling!=null&&x(K.previousSibling))&&($=$.replace(/^\s+/,G.bind(G,!1))),(K.nextSibling==null&&x(K.parentNode)||K.nextSibling!=null&&x(K.nextSibling))&&($=$.replace(/\s+$/,G.bind(G,!1)))}return z.insert($)}_.default=S,_.matchAttributor=H,_.matchBlot=V,_.matchNewline=nt,_.matchSpacing=rt,_.matchText=lt},function(B,_,d){Object.defineProperty(_,"__esModule",{value:!0});var P=function(){function u(l,a){for(var i=0;i<a.length;i++){var o=a[i];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(l,o.key,o)}}return function(l,a,i){return a&&u(l.prototype,a),i&&u(l,i),l}}(),w=function u(l,a,i){l===null&&(l=Function.prototype);var o=Object.getOwnPropertyDescriptor(l,a);if(o===void 0){var f=Object.getPrototypeOf(l);return f===null?void 0:u(f,a,i)}else{if("value"in o)return o.value;var n=o.get;return n===void 0?void 0:n.call(i)}},k=d(6),g=y(k);function y(u){return u&&u.__esModule?u:{default:u}}function c(u,l){if(!(u instanceof l))throw new TypeError("Cannot call a class as a function")}function r(u,l){if(!u)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return l&&(typeof l=="object"||typeof l=="function")?l:u}function t(u,l){if(typeof l!="function"&&l!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof l);u.prototype=Object.create(l&&l.prototype,{constructor:{value:u,enumerable:!1,writable:!0,configurable:!0}}),l&&(Object.setPrototypeOf?Object.setPrototypeOf(u,l):u.__proto__=l)}var e=function(u){t(l,u);function l(){return c(this,l),r(this,(l.__proto__||Object.getPrototypeOf(l)).apply(this,arguments))}return P(l,[{key:"optimize",value:function(i){w(l.prototype.__proto__||Object.getPrototypeOf(l.prototype),"optimize",this).call(this,i),this.domNode.tagName!==this.statics.tagName[0]&&this.replaceWith(this.statics.blotName)}}],[{key:"create",value:function(){return w(l.__proto__||Object.getPrototypeOf(l),"create",this).call(this)}},{key:"formats",value:function(){return!0}}]),l}(g.default);e.blotName="bold",e.tagName=["STRONG","B"],_.default=e},function(B,_,d){Object.defineProperty(_,"__esModule",{value:!0}),_.addControls=_.default=void 0;var P=function(){function h(v,A){var T=[],q=!0,D=!1,C=void 0;try{for(var Z=v[Symbol.iterator](),I;!(q=(I=Z.next()).done)&&(T.push(I.value),!(A&&T.length===A));q=!0);}catch(R){D=!0,C=R}finally{try{!q&&Z.return&&Z.return()}finally{if(D)throw C}}return T}return function(v,A){if(Array.isArray(v))return v;if(Symbol.iterator in Object(v))return h(v,A);throw new TypeError("Invalid attempt to destructure non-iterable instance")}}(),w=function(){function h(v,A){for(var T=0;T<A.length;T++){var q=A[T];q.enumerable=q.enumerable||!1,q.configurable=!0,"value"in q&&(q.writable=!0),Object.defineProperty(v,q.key,q)}}return function(v,A,T){return A&&h(v.prototype,A),T&&h(v,T),v}}(),k=d(2),g=i(k),y=d(0),c=i(y),r=d(5),t=i(r),e=d(10),u=i(e),l=d(9),a=i(l);function i(h){return h&&h.__esModule?h:{default:h}}function o(h,v,A){return v in h?Object.defineProperty(h,v,{value:A,enumerable:!0,configurable:!0,writable:!0}):h[v]=A,h}function f(h,v){if(!(h instanceof v))throw new TypeError("Cannot call a class as a function")}function n(h,v){if(!h)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return v&&(typeof v=="object"||typeof v=="function")?v:h}function s(h,v){if(typeof v!="function"&&v!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof v);h.prototype=Object.create(v&&v.prototype,{constructor:{value:h,enumerable:!1,writable:!0,configurable:!0}}),v&&(Object.setPrototypeOf?Object.setPrototypeOf(h,v):h.__proto__=v)}var E=(0,u.default)("quill:toolbar"),m=function(h){s(v,h);function v(A,T){f(this,v);var q=n(this,(v.__proto__||Object.getPrototypeOf(v)).call(this,A,T));if(Array.isArray(q.options.container)){var D=document.createElement("div");N(D,q.options.container),A.container.parentNode.insertBefore(D,A.container),q.container=D}else typeof q.options.container=="string"?q.container=document.querySelector(q.options.container):q.container=q.options.container;if(!(q.container instanceof HTMLElement)){var C;return C=E.error("Container required for toolbar",q.options),n(q,C)}return q.container.classList.add("ql-toolbar"),q.controls=[],q.handlers={},Object.keys(q.options.handlers).forEach(function(Z){q.addHandler(Z,q.options.handlers[Z])}),[].forEach.call(q.container.querySelectorAll("button, select"),function(Z){q.attach(Z)}),q.quill.on(t.default.events.EDITOR_CHANGE,function(Z,I){Z===t.default.events.SELECTION_CHANGE&&q.update(I)}),q.quill.on(t.default.events.SCROLL_OPTIMIZE,function(){var Z=q.quill.selection.getRange(),I=P(Z,1),R=I[0];q.update(R)}),q}return w(v,[{key:"addHandler",value:function(T,q){this.handlers[T]=q}},{key:"attach",value:function(T){var q=this,D=[].find.call(T.classList,function(Z){return Z.indexOf("ql-")===0});if(D){if(D=D.slice(3),T.tagName==="BUTTON"&&T.setAttribute("type","button"),this.handlers[D]==null){if(this.quill.scroll.whitelist!=null&&this.quill.scroll.whitelist[D]==null){E.warn("ignoring attaching to disabled format",D,T);return}if(c.default.query(D)==null){E.warn("ignoring attaching to nonexistent format",D,T);return}}var C=T.tagName==="SELECT"?"change":"click";T.addEventListener(C,function(Z){var I=void 0;if(T.tagName==="SELECT"){if(T.selectedIndex<0)return;var R=T.options[T.selectedIndex];R.hasAttribute("selected")?I=!1:I=R.value||!1}else T.classList.contains("ql-active")?I=!1:I=T.value||!T.hasAttribute("value"),Z.preventDefault();q.quill.focus();var O=q.quill.selection.getRange(),S=P(O,1),L=S[0];if(q.handlers[D]!=null)q.handlers[D].call(q,I);else if(c.default.query(D).prototype instanceof c.default.Embed){if(I=prompt("Enter "+D),!I)return;q.quill.updateContents(new g.default().retain(L.index).delete(L.length).insert(o({},D,I)),t.default.sources.USER)}else q.quill.format(D,I,t.default.sources.USER);q.update(L)}),this.controls.push([D,T])}}},{key:"update",value:function(T){var q=T==null?{}:this.quill.getFormat(T);this.controls.forEach(function(D){var C=P(D,2),Z=C[0],I=C[1];if(I.tagName==="SELECT"){var R=void 0;if(T==null)R=null;else if(q[Z]==null)R=I.querySelector("option[selected]");else if(!Array.isArray(q[Z])){var O=q[Z];typeof O=="string"&&(O=O.replace(/\"/g,'\\"')),R=I.querySelector('option[value="'+O+'"]')}R==null?(I.value="",I.selectedIndex=-1):R.selected=!0}else if(T==null)I.classList.remove("ql-active");else if(I.hasAttribute("value")){var S=q[Z]===I.getAttribute("value")||q[Z]!=null&&q[Z].toString()===I.getAttribute("value")||q[Z]==null&&!I.getAttribute("value");I.classList.toggle("ql-active",S)}else I.classList.toggle("ql-active",q[Z]!=null)})}}]),v}(a.default);m.DEFAULTS={};function b(h,v,A){var T=document.createElement("button");T.setAttribute("type","button"),T.classList.add("ql-"+v),A!=null&&(T.value=A),h.appendChild(T)}function N(h,v){Array.isArray(v[0])||(v=[v]),v.forEach(function(A){var T=document.createElement("span");T.classList.add("ql-formats"),A.forEach(function(q){if(typeof q=="string")b(T,q);else{var D=Object.keys(q)[0],C=q[D];Array.isArray(C)?p(T,D,C):b(T,D,C)}}),h.appendChild(T)})}function p(h,v,A){var T=document.createElement("select");T.classList.add("ql-"+v),A.forEach(function(q){var D=document.createElement("option");q!==!1?D.setAttribute("value",q):D.setAttribute("selected","selected"),T.appendChild(D)}),h.appendChild(T)}m.DEFAULTS={container:null,handlers:{clean:function(){var v=this,A=this.quill.getSelection();if(A!=null)if(A.length==0){var T=this.quill.getFormat();Object.keys(T).forEach(function(q){c.default.query(q,c.default.Scope.INLINE)!=null&&v.quill.format(q,!1)})}else this.quill.removeFormat(A,t.default.sources.USER)},direction:function(v){var A=this.quill.getFormat().align;v==="rtl"&&A==null?this.quill.format("align","right",t.default.sources.USER):!v&&A==="right"&&this.quill.format("align",!1,t.default.sources.USER),this.quill.format("direction",v,t.default.sources.USER)},indent:function(v){var A=this.quill.getSelection(),T=this.quill.getFormat(A),q=parseInt(T.indent||0);if(v==="+1"||v==="-1"){var D=v==="+1"?1:-1;T.direction==="rtl"&&(D*=-1),this.quill.format("indent",q+D,t.default.sources.USER)}},link:function(v){v===!0&&(v=prompt("Enter link URL:")),this.quill.format("link",v,t.default.sources.USER)},list:function(v){var A=this.quill.getSelection(),T=this.quill.getFormat(A);v==="check"?T.list==="checked"||T.list==="unchecked"?this.quill.format("list",!1,t.default.sources.USER):this.quill.format("list","unchecked",t.default.sources.USER):this.quill.format("list",v,t.default.sources.USER)}}},_.default=m,_.addControls=N},function(B,_){B.exports='<svg viewbox="0 0 18 18"> <polyline class="ql-even ql-stroke" points="5 7 3 9 5 11"></polyline> <polyline class="ql-even ql-stroke" points="13 7 15 9 13 11"></polyline> <line class=ql-stroke x1=10 x2=8 y1=5 y2=13></line> </svg>'},function(B,_,d){Object.defineProperty(_,"__esModule",{value:!0});var P=function(){function u(l,a){for(var i=0;i<a.length;i++){var o=a[i];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(l,o.key,o)}}return function(l,a,i){return a&&u(l.prototype,a),i&&u(l,i),l}}(),w=function u(l,a,i){l===null&&(l=Function.prototype);var o=Object.getOwnPropertyDescriptor(l,a);if(o===void 0){var f=Object.getPrototypeOf(l);return f===null?void 0:u(f,a,i)}else{if("value"in o)return o.value;var n=o.get;return n===void 0?void 0:n.call(i)}},k=d(28),g=y(k);function y(u){return u&&u.__esModule?u:{default:u}}function c(u,l){if(!(u instanceof l))throw new TypeError("Cannot call a class as a function")}function r(u,l){if(!u)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return l&&(typeof l=="object"||typeof l=="function")?l:u}function t(u,l){if(typeof l!="function"&&l!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof l);u.prototype=Object.create(l&&l.prototype,{constructor:{value:u,enumerable:!1,writable:!0,configurable:!0}}),l&&(Object.setPrototypeOf?Object.setPrototypeOf(u,l):u.__proto__=l)}var e=function(u){t(l,u);function l(a,i){c(this,l);var o=r(this,(l.__proto__||Object.getPrototypeOf(l)).call(this,a));return o.label.innerHTML=i,o.container.classList.add("ql-color-picker"),[].slice.call(o.container.querySelectorAll(".ql-picker-item"),0,7).forEach(function(f){f.classList.add("ql-primary")}),o}return P(l,[{key:"buildItem",value:function(i){var o=w(l.prototype.__proto__||Object.getPrototypeOf(l.prototype),"buildItem",this).call(this,i);return o.style.backgroundColor=i.getAttribute("value")||"",o}},{key:"selectItem",value:function(i,o){w(l.prototype.__proto__||Object.getPrototypeOf(l.prototype),"selectItem",this).call(this,i,o);var f=this.label.querySelector(".ql-color-label"),n=i&&i.getAttribute("data-value")||"";f&&(f.tagName==="line"?f.style.stroke=n:f.style.fill=n)}}]),l}(g.default);_.default=e},function(B,_,d){Object.defineProperty(_,"__esModule",{value:!0});var P=function(){function u(l,a){for(var i=0;i<a.length;i++){var o=a[i];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(l,o.key,o)}}return function(l,a,i){return a&&u(l.prototype,a),i&&u(l,i),l}}(),w=function u(l,a,i){l===null&&(l=Function.prototype);var o=Object.getOwnPropertyDescriptor(l,a);if(o===void 0){var f=Object.getPrototypeOf(l);return f===null?void 0:u(f,a,i)}else{if("value"in o)return o.value;var n=o.get;return n===void 0?void 0:n.call(i)}},k=d(28),g=y(k);function y(u){return u&&u.__esModule?u:{default:u}}function c(u,l){if(!(u instanceof l))throw new TypeError("Cannot call a class as a function")}function r(u,l){if(!u)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return l&&(typeof l=="object"||typeof l=="function")?l:u}function t(u,l){if(typeof l!="function"&&l!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof l);u.prototype=Object.create(l&&l.prototype,{constructor:{value:u,enumerable:!1,writable:!0,configurable:!0}}),l&&(Object.setPrototypeOf?Object.setPrototypeOf(u,l):u.__proto__=l)}var e=function(u){t(l,u);function l(a,i){c(this,l);var o=r(this,(l.__proto__||Object.getPrototypeOf(l)).call(this,a));return o.container.classList.add("ql-icon-picker"),[].forEach.call(o.container.querySelectorAll(".ql-picker-item"),function(f){f.innerHTML=i[f.getAttribute("data-value")||""]}),o.defaultItem=o.container.querySelector(".ql-selected"),o.selectItem(o.defaultItem),o}return P(l,[{key:"selectItem",value:function(i,o){w(l.prototype.__proto__||Object.getPrototypeOf(l.prototype),"selectItem",this).call(this,i,o),i=i||this.defaultItem,this.label.innerHTML=i.innerHTML}}]),l}(g.default);_.default=e},function(B,_,d){Object.defineProperty(_,"__esModule",{value:!0});var P=function(){function g(y,c){for(var r=0;r<c.length;r++){var t=c[r];t.enumerable=t.enumerable||!1,t.configurable=!0,"value"in t&&(t.writable=!0),Object.defineProperty(y,t.key,t)}}return function(y,c,r){return c&&g(y.prototype,c),r&&g(y,r),y}}();function w(g,y){if(!(g instanceof y))throw new TypeError("Cannot call a class as a function")}var k=function(){function g(y,c){var r=this;w(this,g),this.quill=y,this.boundsContainer=c||document.body,this.root=y.addContainer("ql-tooltip"),this.root.innerHTML=this.constructor.TEMPLATE,this.quill.root===this.quill.scrollingContainer&&this.quill.root.addEventListener("scroll",function(){r.root.style.marginTop=-1*r.quill.root.scrollTop+"px"}),this.hide()}return P(g,[{key:"hide",value:function(){this.root.classList.add("ql-hidden")}},{key:"position",value:function(c){var r=c.left+c.width/2-this.root.offsetWidth/2,t=c.bottom+this.quill.root.scrollTop;this.root.style.left=r+"px",this.root.style.top=t+"px",this.root.classList.remove("ql-flip");var e=this.boundsContainer.getBoundingClientRect(),u=this.root.getBoundingClientRect(),l=0;if(u.right>e.right&&(l=e.right-u.right,this.root.style.left=r+l+"px"),u.left<e.left&&(l=e.left-u.left,this.root.style.left=r+l+"px"),u.bottom>e.bottom){var a=u.bottom-u.top,i=c.bottom-c.top+a;this.root.style.top=t-i+"px",this.root.classList.add("ql-flip")}return l}},{key:"show",value:function(){this.root.classList.remove("ql-editing"),this.root.classList.remove("ql-hidden")}}]),g}();_.default=k},function(B,_,d){Object.defineProperty(_,"__esModule",{value:!0});var P=function(){function p(h,v){var A=[],T=!0,q=!1,D=void 0;try{for(var C=h[Symbol.iterator](),Z;!(T=(Z=C.next()).done)&&(A.push(Z.value),!(v&&A.length===v));T=!0);}catch(I){q=!0,D=I}finally{try{!T&&C.return&&C.return()}finally{if(q)throw D}}return A}return function(h,v){if(Array.isArray(h))return h;if(Symbol.iterator in Object(h))return p(h,v);throw new TypeError("Invalid attempt to destructure non-iterable instance")}}(),w=function p(h,v,A){h===null&&(h=Function.prototype);var T=Object.getOwnPropertyDescriptor(h,v);if(T===void 0){var q=Object.getPrototypeOf(h);return q===null?void 0:p(q,v,A)}else{if("value"in T)return T.value;var D=T.get;return D===void 0?void 0:D.call(A)}},k=function(){function p(h,v){for(var A=0;A<v.length;A++){var T=v[A];T.enumerable=T.enumerable||!1,T.configurable=!0,"value"in T&&(T.writable=!0),Object.defineProperty(h,T.key,T)}}return function(h,v,A){return v&&p(h.prototype,v),A&&p(h,A),h}}(),g=d(3),y=f(g),c=d(8),r=f(c),t=d(43),e=f(t),u=d(27),l=f(u),a=d(15),i=d(41),o=f(i);function f(p){return p&&p.__esModule?p:{default:p}}function n(p,h){if(!(p instanceof h))throw new TypeError("Cannot call a class as a function")}function s(p,h){if(!p)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return h&&(typeof h=="object"||typeof h=="function")?h:p}function E(p,h){if(typeof h!="function"&&h!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof h);p.prototype=Object.create(h&&h.prototype,{constructor:{value:p,enumerable:!1,writable:!0,configurable:!0}}),h&&(Object.setPrototypeOf?Object.setPrototypeOf(p,h):p.__proto__=h)}var m=[[{header:["1","2","3",!1]}],["bold","italic","underline","link"],[{list:"ordered"},{list:"bullet"}],["clean"]],b=function(p){E(h,p);function h(v,A){n(this,h),A.modules.toolbar!=null&&A.modules.toolbar.container==null&&(A.modules.toolbar.container=m);var T=s(this,(h.__proto__||Object.getPrototypeOf(h)).call(this,v,A));return T.quill.container.classList.add("ql-snow"),T}return k(h,[{key:"extendToolbar",value:function(A){A.container.classList.add("ql-snow"),this.buildButtons([].slice.call(A.container.querySelectorAll("button")),o.default),this.buildPickers([].slice.call(A.container.querySelectorAll("select")),o.default),this.tooltip=new N(this.quill,this.options.bounds),A.container.querySelector(".ql-link")&&this.quill.keyboard.addBinding({key:"K",shortKey:!0},function(T,q){A.handlers.link.call(A,!q.format.link)})}}]),h}(e.default);b.DEFAULTS=(0,y.default)(!0,{},e.default.DEFAULTS,{modules:{toolbar:{handlers:{link:function(h){if(h){var v=this.quill.getSelection();if(v==null||v.length==0)return;var A=this.quill.getText(v);/^\S+@\S+\.\S+$/.test(A)&&A.indexOf("mailto:")!==0&&(A="mailto:"+A);var T=this.quill.theme.tooltip;T.edit("link",A)}else this.quill.format("link",!1)}}}}});var N=function(p){E(h,p);function h(v,A){n(this,h);var T=s(this,(h.__proto__||Object.getPrototypeOf(h)).call(this,v,A));return T.preview=T.root.querySelector("a.ql-preview"),T}return k(h,[{key:"listen",value:function(){var A=this;w(h.prototype.__proto__||Object.getPrototypeOf(h.prototype),"listen",this).call(this),this.root.querySelector("a.ql-action").addEventListener("click",function(T){A.root.classList.contains("ql-editing")?A.save():A.edit("link",A.preview.textContent),T.preventDefault()}),this.root.querySelector("a.ql-remove").addEventListener("click",function(T){if(A.linkRange!=null){var q=A.linkRange;A.restoreFocus(),A.quill.formatText(q,"link",!1,r.default.sources.USER),delete A.linkRange}T.preventDefault(),A.hide()}),this.quill.on(r.default.events.SELECTION_CHANGE,function(T,q,D){if(T!=null){if(T.length===0&&D===r.default.sources.USER){var C=A.quill.scroll.descendant(l.default,T.index),Z=P(C,2),I=Z[0],R=Z[1];if(I!=null){A.linkRange=new a.Range(T.index-R,I.length());var O=l.default.formats(I.domNode);A.preview.textContent=O,A.preview.setAttribute("href",O),A.show(),A.position(A.quill.getBounds(A.linkRange));return}}else delete A.linkRange;A.hide()}})}},{key:"show",value:function(){w(h.prototype.__proto__||Object.getPrototypeOf(h.prototype),"show",this).call(this),this.root.removeAttribute("data-mode")}}]),h}(t.BaseTooltip);N.TEMPLATE=['<a class="ql-preview" rel="noopener noreferrer" target="_blank" href="about:blank"></a>','<input type="text" data-formula="e=mc^2" data-link="https://quilljs.com" data-video="Embed URL">','<a class="ql-action"></a>','<a class="ql-remove"></a>'].join(""),_.default=b},function(B,_,d){Object.defineProperty(_,"__esModule",{value:!0});var P=d(29),w=W(P),k=d(36),g=d(38),y=d(64),c=d(65),r=W(c),t=d(66),e=W(t),u=d(67),l=W(u),a=d(37),i=d(26),o=d(39),f=d(40),n=d(56),s=W(n),E=d(68),m=W(E),b=d(27),N=W(b),p=d(69),h=W(p),v=d(70),A=W(v),T=d(71),q=W(T),D=d(72),C=W(D),Z=d(73),I=W(Z),R=d(13),O=W(R),S=d(74),L=W(S),F=d(75),M=W(F),x=d(57),j=W(x),U=d(41),H=W(U),V=d(28),Y=W(V),Q=d(59),X=W(Q),nt=d(60),rt=W(nt),at=d(61),lt=W(at),K=d(108),z=W(K),$=d(62),G=W($);function W(J){return J&&J.__esModule?J:{default:J}}w.default.register({"attributors/attribute/direction":g.DirectionAttribute,"attributors/class/align":k.AlignClass,"attributors/class/background":a.BackgroundClass,"attributors/class/color":i.ColorClass,"attributors/class/direction":g.DirectionClass,"attributors/class/font":o.FontClass,"attributors/class/size":f.SizeClass,"attributors/style/align":k.AlignStyle,"attributors/style/background":a.BackgroundStyle,"attributors/style/color":i.ColorStyle,"attributors/style/direction":g.DirectionStyle,"attributors/style/font":o.FontStyle,"attributors/style/size":f.SizeStyle},!0),w.default.register({"formats/align":k.AlignClass,"formats/direction":g.DirectionClass,"formats/indent":y.IndentClass,"formats/background":a.BackgroundStyle,"formats/color":i.ColorStyle,"formats/font":o.FontClass,"formats/size":f.SizeClass,"formats/blockquote":r.default,"formats/code-block":O.default,"formats/header":e.default,"formats/list":l.default,"formats/bold":s.default,"formats/code":R.Code,"formats/italic":m.default,"formats/link":N.default,"formats/script":h.default,"formats/strike":A.default,"formats/underline":q.default,"formats/image":C.default,"formats/video":I.default,"formats/list/item":u.ListItem,"modules/formula":L.default,"modules/syntax":M.default,"modules/toolbar":j.default,"themes/bubble":z.default,"themes/snow":G.default,"ui/icons":H.default,"ui/picker":Y.default,"ui/icon-picker":rt.default,"ui/color-picker":X.default,"ui/tooltip":lt.default},!0),_.default=w.default},function(B,_,d){Object.defineProperty(_,"__esModule",{value:!0}),_.IndentClass=void 0;var P=function(){function l(a,i){for(var o=0;o<i.length;o++){var f=i[o];f.enumerable=f.enumerable||!1,f.configurable=!0,"value"in f&&(f.writable=!0),Object.defineProperty(a,f.key,f)}}return function(a,i,o){return i&&l(a.prototype,i),o&&l(a,o),a}}(),w=function l(a,i,o){a===null&&(a=Function.prototype);var f=Object.getOwnPropertyDescriptor(a,i);if(f===void 0){var n=Object.getPrototypeOf(a);return n===null?void 0:l(n,i,o)}else{if("value"in f)return f.value;var s=f.get;return s===void 0?void 0:s.call(o)}},k=d(0),g=y(k);function y(l){return l&&l.__esModule?l:{default:l}}function c(l,a){if(!(l instanceof a))throw new TypeError("Cannot call a class as a function")}function r(l,a){if(!l)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return a&&(typeof a=="object"||typeof a=="function")?a:l}function t(l,a){if(typeof a!="function"&&a!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof a);l.prototype=Object.create(a&&a.prototype,{constructor:{value:l,enumerable:!1,writable:!0,configurable:!0}}),a&&(Object.setPrototypeOf?Object.setPrototypeOf(l,a):l.__proto__=a)}var e=function(l){t(a,l);function a(){return c(this,a),r(this,(a.__proto__||Object.getPrototypeOf(a)).apply(this,arguments))}return P(a,[{key:"add",value:function(o,f){if(f==="+1"||f==="-1"){var n=this.value(o)||0;f=f==="+1"?n+1:n-1}return f===0?(this.remove(o),!0):w(a.prototype.__proto__||Object.getPrototypeOf(a.prototype),"add",this).call(this,o,f)}},{key:"canAdd",value:function(o,f){return w(a.prototype.__proto__||Object.getPrototypeOf(a.prototype),"canAdd",this).call(this,o,f)||w(a.prototype.__proto__||Object.getPrototypeOf(a.prototype),"canAdd",this).call(this,o,parseInt(f))}},{key:"value",value:function(o){return parseInt(w(a.prototype.__proto__||Object.getPrototypeOf(a.prototype),"value",this).call(this,o))||void 0}}]),a}(g.default.Attributor.Class),u=new e("indent","ql-indent",{scope:g.default.Scope.BLOCK,whitelist:[1,2,3,4,5,6,7,8]});_.IndentClass=u},function(B,_,d){Object.defineProperty(_,"__esModule",{value:!0});var P=d(4),w=k(P);function k(t){return t&&t.__esModule?t:{default:t}}function g(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function y(t,e){if(!t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e&&(typeof e=="object"||typeof e=="function")?e:t}function c(t,e){if(typeof e!="function"&&e!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}var r=function(t){c(e,t);function e(){return g(this,e),y(this,(e.__proto__||Object.getPrototypeOf(e)).apply(this,arguments))}return e}(w.default);r.blotName="blockquote",r.tagName="blockquote",_.default=r},function(B,_,d){Object.defineProperty(_,"__esModule",{value:!0});var P=function(){function e(u,l){for(var a=0;a<l.length;a++){var i=l[a];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(u,i.key,i)}}return function(u,l,a){return l&&e(u.prototype,l),a&&e(u,a),u}}(),w=d(4),k=g(w);function g(e){return e&&e.__esModule?e:{default:e}}function y(e,u){if(!(e instanceof u))throw new TypeError("Cannot call a class as a function")}function c(e,u){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return u&&(typeof u=="object"||typeof u=="function")?u:e}function r(e,u){if(typeof u!="function"&&u!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof u);e.prototype=Object.create(u&&u.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),u&&(Object.setPrototypeOf?Object.setPrototypeOf(e,u):e.__proto__=u)}var t=function(e){r(u,e);function u(){return y(this,u),c(this,(u.__proto__||Object.getPrototypeOf(u)).apply(this,arguments))}return P(u,null,[{key:"formats",value:function(a){return this.tagName.indexOf(a.tagName)+1}}]),u}(k.default);t.blotName="header",t.tagName=["H1","H2","H3","H4","H5","H6"],_.default=t},function(B,_,d){Object.defineProperty(_,"__esModule",{value:!0}),_.default=_.ListItem=void 0;var P=function(){function n(s,E){for(var m=0;m<E.length;m++){var b=E[m];b.enumerable=b.enumerable||!1,b.configurable=!0,"value"in b&&(b.writable=!0),Object.defineProperty(s,b.key,b)}}return function(s,E,m){return E&&n(s.prototype,E),m&&n(s,m),s}}(),w=function n(s,E,m){s===null&&(s=Function.prototype);var b=Object.getOwnPropertyDescriptor(s,E);if(b===void 0){var N=Object.getPrototypeOf(s);return N===null?void 0:n(N,E,m)}else{if("value"in b)return b.value;var p=b.get;return p===void 0?void 0:p.call(m)}},k=d(0),g=e(k),y=d(4),c=e(y),r=d(25),t=e(r);function e(n){return n&&n.__esModule?n:{default:n}}function u(n,s,E){return s in n?Object.defineProperty(n,s,{value:E,enumerable:!0,configurable:!0,writable:!0}):n[s]=E,n}function l(n,s){if(!(n instanceof s))throw new TypeError("Cannot call a class as a function")}function a(n,s){if(!n)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return s&&(typeof s=="object"||typeof s=="function")?s:n}function i(n,s){if(typeof s!="function"&&s!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof s);n.prototype=Object.create(s&&s.prototype,{constructor:{value:n,enumerable:!1,writable:!0,configurable:!0}}),s&&(Object.setPrototypeOf?Object.setPrototypeOf(n,s):n.__proto__=s)}var o=function(n){i(s,n);function s(){return l(this,s),a(this,(s.__proto__||Object.getPrototypeOf(s)).apply(this,arguments))}return P(s,[{key:"format",value:function(m,b){m===f.blotName&&!b?this.replaceWith(g.default.create(this.statics.scope)):w(s.prototype.__proto__||Object.getPrototypeOf(s.prototype),"format",this).call(this,m,b)}},{key:"remove",value:function(){this.prev==null&&this.next==null?this.parent.remove():w(s.prototype.__proto__||Object.getPrototypeOf(s.prototype),"remove",this).call(this)}},{key:"replaceWith",value:function(m,b){return this.parent.isolate(this.offset(this.parent),this.length()),m===this.parent.statics.blotName?(this.parent.replaceWith(m,b),this):(this.parent.unwrap(),w(s.prototype.__proto__||Object.getPrototypeOf(s.prototype),"replaceWith",this).call(this,m,b))}}],[{key:"formats",value:function(m){return m.tagName===this.tagName?void 0:w(s.__proto__||Object.getPrototypeOf(s),"formats",this).call(this,m)}}]),s}(c.default);o.blotName="list-item",o.tagName="LI";var f=function(n){i(s,n),P(s,null,[{key:"create",value:function(m){var b=m==="ordered"?"OL":"UL",N=w(s.__proto__||Object.getPrototypeOf(s),"create",this).call(this,b);return(m==="checked"||m==="unchecked")&&N.setAttribute("data-checked",m==="checked"),N}},{key:"formats",value:function(m){if(m.tagName==="OL")return"ordered";if(m.tagName==="UL")return m.hasAttribute("data-checked")?m.getAttribute("data-checked")==="true"?"checked":"unchecked":"bullet"}}]);function s(E){l(this,s);var m=a(this,(s.__proto__||Object.getPrototypeOf(s)).call(this,E)),b=function(p){if(p.target.parentNode===E){var h=m.statics.formats(E),v=g.default.find(p.target);h==="checked"?v.format("list","unchecked"):h==="unchecked"&&v.format("list","checked")}};return E.addEventListener("touchstart",b),E.addEventListener("mousedown",b),m}return P(s,[{key:"format",value:function(m,b){this.children.length>0&&this.children.tail.format(m,b)}},{key:"formats",value:function(){return u({},this.statics.blotName,this.statics.formats(this.domNode))}},{key:"insertBefore",value:function(m,b){if(m instanceof o)w(s.prototype.__proto__||Object.getPrototypeOf(s.prototype),"insertBefore",this).call(this,m,b);else{var N=b==null?this.length():b.offset(this),p=this.split(N);p.parent.insertBefore(m,p)}}},{key:"optimize",value:function(m){w(s.prototype.__proto__||Object.getPrototypeOf(s.prototype),"optimize",this).call(this,m);var b=this.next;b!=null&&b.prev===this&&b.statics.blotName===this.statics.blotName&&b.domNode.tagName===this.domNode.tagName&&b.domNode.getAttribute("data-checked")===this.domNode.getAttribute("data-checked")&&(b.moveChildren(this),b.remove())}},{key:"replace",value:function(m){if(m.statics.blotName!==this.statics.blotName){var b=g.default.create(this.statics.defaultChild);m.moveChildren(b),this.appendChild(b)}w(s.prototype.__proto__||Object.getPrototypeOf(s.prototype),"replace",this).call(this,m)}}]),s}(t.default);f.blotName="list",f.scope=g.default.Scope.BLOCK_BLOT,f.tagName=["OL","UL"],f.defaultChild="list-item",f.allowedChildren=[o],_.ListItem=o,_.default=f},function(B,_,d){Object.defineProperty(_,"__esModule",{value:!0});var P=d(56),w=k(P);function k(t){return t&&t.__esModule?t:{default:t}}function g(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function y(t,e){if(!t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e&&(typeof e=="object"||typeof e=="function")?e:t}function c(t,e){if(typeof e!="function"&&e!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}var r=function(t){c(e,t);function e(){return g(this,e),y(this,(e.__proto__||Object.getPrototypeOf(e)).apply(this,arguments))}return e}(w.default);r.blotName="italic",r.tagName=["EM","I"],_.default=r},function(B,_,d){Object.defineProperty(_,"__esModule",{value:!0});var P=function(){function u(l,a){for(var i=0;i<a.length;i++){var o=a[i];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(l,o.key,o)}}return function(l,a,i){return a&&u(l.prototype,a),i&&u(l,i),l}}(),w=function u(l,a,i){l===null&&(l=Function.prototype);var o=Object.getOwnPropertyDescriptor(l,a);if(o===void 0){var f=Object.getPrototypeOf(l);return f===null?void 0:u(f,a,i)}else{if("value"in o)return o.value;var n=o.get;return n===void 0?void 0:n.call(i)}},k=d(6),g=y(k);function y(u){return u&&u.__esModule?u:{default:u}}function c(u,l){if(!(u instanceof l))throw new TypeError("Cannot call a class as a function")}function r(u,l){if(!u)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return l&&(typeof l=="object"||typeof l=="function")?l:u}function t(u,l){if(typeof l!="function"&&l!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof l);u.prototype=Object.create(l&&l.prototype,{constructor:{value:u,enumerable:!1,writable:!0,configurable:!0}}),l&&(Object.setPrototypeOf?Object.setPrototypeOf(u,l):u.__proto__=l)}var e=function(u){t(l,u);function l(){return c(this,l),r(this,(l.__proto__||Object.getPrototypeOf(l)).apply(this,arguments))}return P(l,null,[{key:"create",value:function(i){return i==="super"?document.createElement("sup"):i==="sub"?document.createElement("sub"):w(l.__proto__||Object.getPrototypeOf(l),"create",this).call(this,i)}},{key:"formats",value:function(i){if(i.tagName==="SUB")return"sub";if(i.tagName==="SUP")return"super"}}]),l}(g.default);e.blotName="script",e.tagName=["SUB","SUP"],_.default=e},function(B,_,d){Object.defineProperty(_,"__esModule",{value:!0});var P=d(6),w=k(P);function k(t){return t&&t.__esModule?t:{default:t}}function g(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function y(t,e){if(!t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e&&(typeof e=="object"||typeof e=="function")?e:t}function c(t,e){if(typeof e!="function"&&e!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}var r=function(t){c(e,t);function e(){return g(this,e),y(this,(e.__proto__||Object.getPrototypeOf(e)).apply(this,arguments))}return e}(w.default);r.blotName="strike",r.tagName="S",_.default=r},function(B,_,d){Object.defineProperty(_,"__esModule",{value:!0});var P=d(6),w=k(P);function k(t){return t&&t.__esModule?t:{default:t}}function g(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function y(t,e){if(!t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e&&(typeof e=="object"||typeof e=="function")?e:t}function c(t,e){if(typeof e!="function"&&e!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}var r=function(t){c(e,t);function e(){return g(this,e),y(this,(e.__proto__||Object.getPrototypeOf(e)).apply(this,arguments))}return e}(w.default);r.blotName="underline",r.tagName="U",_.default=r},function(B,_,d){Object.defineProperty(_,"__esModule",{value:!0});var P=function(){function a(i,o){for(var f=0;f<o.length;f++){var n=o[f];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(i,n.key,n)}}return function(i,o,f){return o&&a(i.prototype,o),f&&a(i,f),i}}(),w=function a(i,o,f){i===null&&(i=Function.prototype);var n=Object.getOwnPropertyDescriptor(i,o);if(n===void 0){var s=Object.getPrototypeOf(i);return s===null?void 0:a(s,o,f)}else{if("value"in n)return n.value;var E=n.get;return E===void 0?void 0:E.call(f)}},k=d(0),g=c(k),y=d(27);function c(a){return a&&a.__esModule?a:{default:a}}function r(a,i){if(!(a instanceof i))throw new TypeError("Cannot call a class as a function")}function t(a,i){if(!a)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return i&&(typeof i=="object"||typeof i=="function")?i:a}function e(a,i){if(typeof i!="function"&&i!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof i);a.prototype=Object.create(i&&i.prototype,{constructor:{value:a,enumerable:!1,writable:!0,configurable:!0}}),i&&(Object.setPrototypeOf?Object.setPrototypeOf(a,i):a.__proto__=i)}var u=["alt","height","width"],l=function(a){e(i,a);function i(){return r(this,i),t(this,(i.__proto__||Object.getPrototypeOf(i)).apply(this,arguments))}return P(i,[{key:"format",value:function(f,n){u.indexOf(f)>-1?n?this.domNode.setAttribute(f,n):this.domNode.removeAttribute(f):w(i.prototype.__proto__||Object.getPrototypeOf(i.prototype),"format",this).call(this,f,n)}}],[{key:"create",value:function(f){var n=w(i.__proto__||Object.getPrototypeOf(i),"create",this).call(this,f);return typeof f=="string"&&n.setAttribute("src",this.sanitize(f)),n}},{key:"formats",value:function(f){return u.reduce(function(n,s){return f.hasAttribute(s)&&(n[s]=f.getAttribute(s)),n},{})}},{key:"match",value:function(f){return/\.(jpe?g|gif|png)$/.test(f)||/^data:image\/.+;base64/.test(f)}},{key:"sanitize",value:function(f){return(0,y.sanitize)(f,["http","https","data"])?f:"//:0"}},{key:"value",value:function(f){return f.getAttribute("src")}}]),i}(g.default.Embed);l.blotName="image",l.tagName="IMG",_.default=l},function(B,_,d){Object.defineProperty(_,"__esModule",{value:!0});var P=function(){function a(i,o){for(var f=0;f<o.length;f++){var n=o[f];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(i,n.key,n)}}return function(i,o,f){return o&&a(i.prototype,o),f&&a(i,f),i}}(),w=function a(i,o,f){i===null&&(i=Function.prototype);var n=Object.getOwnPropertyDescriptor(i,o);if(n===void 0){var s=Object.getPrototypeOf(i);return s===null?void 0:a(s,o,f)}else{if("value"in n)return n.value;var E=n.get;return E===void 0?void 0:E.call(f)}},k=d(4),g=d(27),y=c(g);function c(a){return a&&a.__esModule?a:{default:a}}function r(a,i){if(!(a instanceof i))throw new TypeError("Cannot call a class as a function")}function t(a,i){if(!a)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return i&&(typeof i=="object"||typeof i=="function")?i:a}function e(a,i){if(typeof i!="function"&&i!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof i);a.prototype=Object.create(i&&i.prototype,{constructor:{value:a,enumerable:!1,writable:!0,configurable:!0}}),i&&(Object.setPrototypeOf?Object.setPrototypeOf(a,i):a.__proto__=i)}var u=["height","width"],l=function(a){e(i,a);function i(){return r(this,i),t(this,(i.__proto__||Object.getPrototypeOf(i)).apply(this,arguments))}return P(i,[{key:"format",value:function(f,n){u.indexOf(f)>-1?n?this.domNode.setAttribute(f,n):this.domNode.removeAttribute(f):w(i.prototype.__proto__||Object.getPrototypeOf(i.prototype),"format",this).call(this,f,n)}}],[{key:"create",value:function(f){var n=w(i.__proto__||Object.getPrototypeOf(i),"create",this).call(this,f);return n.setAttribute("frameborder","0"),n.setAttribute("allowfullscreen",!0),n.setAttribute("src",this.sanitize(f)),n}},{key:"formats",value:function(f){return u.reduce(function(n,s){return f.hasAttribute(s)&&(n[s]=f.getAttribute(s)),n},{})}},{key:"sanitize",value:function(f){return y.default.sanitize(f)}},{key:"value",value:function(f){return f.getAttribute("src")}}]),i}(k.BlockEmbed);l.blotName="video",l.className="ql-video",l.tagName="IFRAME",_.default=l},function(B,_,d){Object.defineProperty(_,"__esModule",{value:!0}),_.default=_.FormulaBlot=void 0;var P=function(){function f(n,s){for(var E=0;E<s.length;E++){var m=s[E];m.enumerable=m.enumerable||!1,m.configurable=!0,"value"in m&&(m.writable=!0),Object.defineProperty(n,m.key,m)}}return function(n,s,E){return s&&f(n.prototype,s),E&&f(n,E),n}}(),w=function f(n,s,E){n===null&&(n=Function.prototype);var m=Object.getOwnPropertyDescriptor(n,s);if(m===void 0){var b=Object.getPrototypeOf(n);return b===null?void 0:f(b,s,E)}else{if("value"in m)return m.value;var N=m.get;return N===void 0?void 0:N.call(E)}},k=d(35),g=e(k),y=d(5),c=e(y),r=d(9),t=e(r);function e(f){return f&&f.__esModule?f:{default:f}}function u(f,n){if(!(f instanceof n))throw new TypeError("Cannot call a class as a function")}function l(f,n){if(!f)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return n&&(typeof n=="object"||typeof n=="function")?n:f}function a(f,n){if(typeof n!="function"&&n!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof n);f.prototype=Object.create(n&&n.prototype,{constructor:{value:f,enumerable:!1,writable:!0,configurable:!0}}),n&&(Object.setPrototypeOf?Object.setPrototypeOf(f,n):f.__proto__=n)}var i=function(f){a(n,f);function n(){return u(this,n),l(this,(n.__proto__||Object.getPrototypeOf(n)).apply(this,arguments))}return P(n,null,[{key:"create",value:function(E){var m=w(n.__proto__||Object.getPrototypeOf(n),"create",this).call(this,E);return typeof E=="string"&&(window.katex.render(E,m,{throwOnError:!1,errorColor:"#f00"}),m.setAttribute("data-value",E)),m}},{key:"value",value:function(E){return E.getAttribute("data-value")}}]),n}(g.default);i.blotName="formula",i.className="ql-formula",i.tagName="SPAN";var o=function(f){a(n,f),P(n,null,[{key:"register",value:function(){c.default.register(i,!0)}}]);function n(){u(this,n);var s=l(this,(n.__proto__||Object.getPrototypeOf(n)).call(this));if(window.katex==null)throw new Error("Formula module requires KaTeX.");return s}return n}(t.default);_.FormulaBlot=i,_.default=o},function(B,_,d){Object.defineProperty(_,"__esModule",{value:!0}),_.default=_.CodeToken=_.CodeBlock=void 0;var P=function(){function E(m,b){for(var N=0;N<b.length;N++){var p=b[N];p.enumerable=p.enumerable||!1,p.configurable=!0,"value"in p&&(p.writable=!0),Object.defineProperty(m,p.key,p)}}return function(m,b,N){return b&&E(m.prototype,b),N&&E(m,N),m}}(),w=function E(m,b,N){m===null&&(m=Function.prototype);var p=Object.getOwnPropertyDescriptor(m,b);if(p===void 0){var h=Object.getPrototypeOf(m);return h===null?void 0:E(h,b,N)}else{if("value"in p)return p.value;var v=p.get;return v===void 0?void 0:v.call(N)}},k=d(0),g=l(k),y=d(5),c=l(y),r=d(9),t=l(r),e=d(13),u=l(e);function l(E){return E&&E.__esModule?E:{default:E}}function a(E,m){if(!(E instanceof m))throw new TypeError("Cannot call a class as a function")}function i(E,m){if(!E)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return m&&(typeof m=="object"||typeof m=="function")?m:E}function o(E,m){if(typeof m!="function"&&m!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof m);E.prototype=Object.create(m&&m.prototype,{constructor:{value:E,enumerable:!1,writable:!0,configurable:!0}}),m&&(Object.setPrototypeOf?Object.setPrototypeOf(E,m):E.__proto__=m)}var f=function(E){o(m,E);function m(){return a(this,m),i(this,(m.__proto__||Object.getPrototypeOf(m)).apply(this,arguments))}return P(m,[{key:"replaceWith",value:function(N){this.domNode.textContent=this.domNode.textContent,this.attach(),w(m.prototype.__proto__||Object.getPrototypeOf(m.prototype),"replaceWith",this).call(this,N)}},{key:"highlight",value:function(N){var p=this.domNode.textContent;this.cachedText!==p&&((p.trim().length>0||this.cachedText==null)&&(this.domNode.innerHTML=N(p),this.domNode.normalize(),this.attach()),this.cachedText=p)}}]),m}(u.default);f.className="ql-syntax";var n=new g.default.Attributor.Class("token","hljs",{scope:g.default.Scope.INLINE}),s=function(E){o(m,E),P(m,null,[{key:"register",value:function(){c.default.register(n,!0),c.default.register(f,!0)}}]);function m(b,N){a(this,m);var p=i(this,(m.__proto__||Object.getPrototypeOf(m)).call(this,b,N));if(typeof p.options.highlight!="function")throw new Error("Syntax module requires highlight.js. Please include the library on the page before Quill.");var h=null;return p.quill.on(c.default.events.SCROLL_OPTIMIZE,function(){clearTimeout(h),h=setTimeout(function(){p.highlight(),h=null},p.options.interval)}),p.highlight(),p}return P(m,[{key:"highlight",value:function(){var N=this;if(!this.quill.selection.composing){this.quill.update(c.default.sources.USER);var p=this.quill.getSelection();this.quill.scroll.descendants(f).forEach(function(h){h.highlight(N.options.highlight)}),this.quill.update(c.default.sources.SILENT),p!=null&&this.quill.setSelection(p,c.default.sources.SILENT)}}}]),m}(t.default);s.DEFAULTS={highlight:function(){return window.hljs==null?null:function(E){var m=window.hljs.highlightAuto(E);return m.value}}(),interval:1e3},_.CodeBlock=f,_.CodeToken=n,_.default=s},function(B,_){B.exports='<svg viewbox="0 0 18 18"> <line class=ql-stroke x1=3 x2=15 y1=9 y2=9></line> <line class=ql-stroke x1=3 x2=13 y1=14 y2=14></line> <line class=ql-stroke x1=3 x2=9 y1=4 y2=4></line> </svg>'},function(B,_){B.exports='<svg viewbox="0 0 18 18"> <line class=ql-stroke x1=15 x2=3 y1=9 y2=9></line> <line class=ql-stroke x1=14 x2=4 y1=14 y2=14></line> <line class=ql-stroke x1=12 x2=6 y1=4 y2=4></line> </svg>'},function(B,_){B.exports='<svg viewbox="0 0 18 18"> <line class=ql-stroke x1=15 x2=3 y1=9 y2=9></line> <line class=ql-stroke x1=15 x2=5 y1=14 y2=14></line> <line class=ql-stroke x1=15 x2=9 y1=4 y2=4></line> </svg>'},function(B,_){B.exports='<svg viewbox="0 0 18 18"> <line class=ql-stroke x1=15 x2=3 y1=9 y2=9></line> <line class=ql-stroke x1=15 x2=3 y1=14 y2=14></line> <line class=ql-stroke x1=15 x2=3 y1=4 y2=4></line> </svg>'},function(B,_){B.exports='<svg viewbox="0 0 18 18"> <g class="ql-fill ql-color-label"> <polygon points="6 6.868 6 6 5 6 5 7 5.942 7 6 6.868"></polygon> <rect height=1 width=1 x=4 y=4></rect> <polygon points="6.817 5 6 5 6 6 6.38 6 6.817 5"></polygon> <rect height=1 width=1 x=2 y=6></rect> <rect height=1 width=1 x=3 y=5></rect> <rect height=1 width=1 x=4 y=7></rect> <polygon points="4 11.439 4 11 3 11 3 12 3.755 12 4 11.439"></polygon> <rect height=1 width=1 x=2 y=12></rect> <rect height=1 width=1 x=2 y=9></rect> <rect height=1 width=1 x=2 y=15></rect> <polygon points="4.63 10 4 10 4 11 4.192 11 4.63 10"></polygon> <rect height=1 width=1 x=3 y=8></rect> <path d=M10.832,4.2L11,4.582V4H10.708A1.948,1.948,0,0,1,10.832,4.2Z></path> <path d=M7,4.582L7.168,4.2A1.929,1.929,0,0,1,7.292,4H7V4.582Z></path> <path d=M8,13H7.683l-0.351.8a1.933,1.933,0,0,1-.124.2H8V13Z></path> <rect height=1 width=1 x=12 y=2></rect> <rect height=1 width=1 x=11 y=3></rect> <path d=M9,3H8V3.282A1.985,1.985,0,0,1,9,3Z></path> <rect height=1 width=1 x=2 y=3></rect> <rect height=1 width=1 x=6 y=2></rect> <rect height=1 width=1 x=3 y=2></rect> <rect height=1 width=1 x=5 y=3></rect> <rect height=1 width=1 x=9 y=2></rect> <rect height=1 width=1 x=15 y=14></rect> <polygon points="13.447 10.174 13.469 10.225 13.472 10.232 13.808 11 14 11 14 10 13.37 10 13.447 10.174"></polygon> <rect height=1 width=1 x=13 y=7></rect> <rect height=1 width=1 x=15 y=5></rect> <rect height=1 width=1 x=14 y=6></rect> <rect height=1 width=1 x=15 y=8></rect> <rect height=1 width=1 x=14 y=9></rect> <path d=M3.775,14H3v1H4V14.314A1.97,1.97,0,0,1,3.775,14Z></path> <rect height=1 width=1 x=14 y=3></rect> <polygon points="12 6.868 12 6 11.62 6 12 6.868"></polygon> <rect height=1 width=1 x=15 y=2></rect> <rect height=1 width=1 x=12 y=5></rect> <rect height=1 width=1 x=13 y=4></rect> <polygon points="12.933 9 13 9 13 8 12.495 8 12.933 9"></polygon> <rect height=1 width=1 x=9 y=14></rect> <rect height=1 width=1 x=8 y=15></rect> <path d=M6,14.926V15H7V14.316A1.993,1.993,0,0,1,6,14.926Z></path> <rect height=1 width=1 x=5 y=15></rect> <path d=M10.668,13.8L10.317,13H10v1h0.792A1.947,1.947,0,0,1,10.668,13.8Z></path> <rect height=1 width=1 x=11 y=15></rect> <path d=M14.332,12.2a1.99,1.99,0,0,1,.166.8H15V12H14.245Z></path> <rect height=1 width=1 x=14 y=15></rect> <rect height=1 width=1 x=15 y=11></rect> </g> <polyline class=ql-stroke points="5.5 13 9 5 12.5 13"></polyline> <line class=ql-stroke x1=11.63 x2=6.38 y1=11 y2=11></line> </svg>'},function(B,_){B.exports='<svg viewbox="0 0 18 18"> <rect class="ql-fill ql-stroke" height=3 width=3 x=4 y=5></rect> <rect class="ql-fill ql-stroke" height=3 width=3 x=11 y=5></rect> <path class="ql-even ql-fill ql-stroke" d=M7,8c0,4.031-3,5-3,5></path> <path class="ql-even ql-fill ql-stroke" d=M14,8c0,4.031-3,5-3,5></path> </svg>'},function(B,_){B.exports='<svg viewbox="0 0 18 18"> <path class=ql-stroke d=M5,4H9.5A2.5,2.5,0,0,1,12,6.5v0A2.5,2.5,0,0,1,9.5,9H5A0,0,0,0,1,5,9V4A0,0,0,0,1,5,4Z></path> <path class=ql-stroke d=M5,9h5.5A2.5,2.5,0,0,1,13,11.5v0A2.5,2.5,0,0,1,10.5,14H5a0,0,0,0,1,0,0V9A0,0,0,0,1,5,9Z></path> </svg>'},function(B,_){B.exports='<svg class="" viewbox="0 0 18 18"> <line class=ql-stroke x1=5 x2=13 y1=3 y2=3></line> <line class=ql-stroke x1=6 x2=9.35 y1=12 y2=3></line> <line class=ql-stroke x1=11 x2=15 y1=11 y2=15></line> <line class=ql-stroke x1=15 x2=11 y1=11 y2=15></line> <rect class=ql-fill height=1 rx=0.5 ry=0.5 width=7 x=2 y=14></rect> </svg>'},function(B,_){B.exports='<svg viewbox="0 0 18 18"> <line class="ql-color-label ql-stroke ql-transparent" x1=3 x2=15 y1=15 y2=15></line> <polyline class=ql-stroke points="5.5 11 9 3 12.5 11"></polyline> <line class=ql-stroke x1=11.63 x2=6.38 y1=9 y2=9></line> </svg>'},function(B,_){B.exports='<svg viewbox="0 0 18 18"> <polygon class="ql-stroke ql-fill" points="3 11 5 9 3 7 3 11"></polygon> <line class="ql-stroke ql-fill" x1=15 x2=11 y1=4 y2=4></line> <path class=ql-fill d=M11,3a3,3,0,0,0,0,6h1V3H11Z></path> <rect class=ql-fill height=11 width=1 x=11 y=4></rect> <rect class=ql-fill height=11 width=1 x=13 y=4></rect> </svg>'},function(B,_){B.exports='<svg viewbox="0 0 18 18"> <polygon class="ql-stroke ql-fill" points="15 12 13 10 15 8 15 12"></polygon> <line class="ql-stroke ql-fill" x1=9 x2=5 y1=4 y2=4></line> <path class=ql-fill d=M5,3A3,3,0,0,0,5,9H6V3H5Z></path> <rect class=ql-fill height=11 width=1 x=5 y=4></rect> <rect class=ql-fill height=11 width=1 x=7 y=4></rect> </svg>'},function(B,_){B.exports='<svg viewbox="0 0 18 18"> <path class=ql-fill d=M14,16H4a1,1,0,0,1,0-2H14A1,1,0,0,1,14,16Z /> <path class=ql-fill d=M14,4H4A1,1,0,0,1,4,2H14A1,1,0,0,1,14,4Z /> <rect class=ql-fill x=3 y=6 width=12 height=6 rx=1 ry=1 /> </svg>'},function(B,_){B.exports='<svg viewbox="0 0 18 18"> <path class=ql-fill d=M13,16H5a1,1,0,0,1,0-2h8A1,1,0,0,1,13,16Z /> <path class=ql-fill d=M13,4H5A1,1,0,0,1,5,2h8A1,1,0,0,1,13,4Z /> <rect class=ql-fill x=2 y=6 width=14 height=6 rx=1 ry=1 /> </svg>'},function(B,_){B.exports='<svg viewbox="0 0 18 18"> <path class=ql-fill d=M15,8H13a1,1,0,0,1,0-2h2A1,1,0,0,1,15,8Z /> <path class=ql-fill d=M15,12H13a1,1,0,0,1,0-2h2A1,1,0,0,1,15,12Z /> <path class=ql-fill d=M15,16H5a1,1,0,0,1,0-2H15A1,1,0,0,1,15,16Z /> <path class=ql-fill d=M15,4H5A1,1,0,0,1,5,2H15A1,1,0,0,1,15,4Z /> <rect class=ql-fill x=2 y=6 width=8 height=6 rx=1 ry=1 /> </svg>'},function(B,_){B.exports='<svg viewbox="0 0 18 18"> <path class=ql-fill d=M5,8H3A1,1,0,0,1,3,6H5A1,1,0,0,1,5,8Z /> <path class=ql-fill d=M5,12H3a1,1,0,0,1,0-2H5A1,1,0,0,1,5,12Z /> <path class=ql-fill d=M13,16H3a1,1,0,0,1,0-2H13A1,1,0,0,1,13,16Z /> <path class=ql-fill d=M13,4H3A1,1,0,0,1,3,2H13A1,1,0,0,1,13,4Z /> <rect class=ql-fill x=8 y=6 width=8 height=6 rx=1 ry=1 transform="translate(24 18) rotate(-180)"/> </svg>'},function(B,_){B.exports='<svg viewbox="0 0 18 18"> <path class=ql-fill d=M11.759,2.482a2.561,2.561,0,0,0-3.53.607A7.656,7.656,0,0,0,6.8,6.2C6.109,9.188,5.275,14.677,4.15,14.927a1.545,1.545,0,0,0-1.3-.933A0.922,0.922,0,0,0,2,15.036S1.954,16,4.119,16s3.091-2.691,3.7-5.553c0.177-.826.36-1.726,0.554-2.6L8.775,6.2c0.381-1.421.807-2.521,1.306-2.676a1.014,1.014,0,0,0,1.02.56A0.966,0.966,0,0,0,11.759,2.482Z></path> <rect class=ql-fill height=1.6 rx=0.8 ry=0.8 width=5 x=5.15 y=6.2></rect> <path class=ql-fill d=M13.663,12.027a1.662,1.662,0,0,1,.266-0.276q0.193,0.069.456,0.138a2.1,2.1,0,0,0,.535.069,1.075,1.075,0,0,0,.767-0.3,1.044,1.044,0,0,0,.314-0.8,0.84,0.84,0,0,0-.238-0.619,0.8,0.8,0,0,0-.594-0.239,1.154,1.154,0,0,0-.781.3,4.607,4.607,0,0,0-.781,1q-0.091.15-.218,0.346l-0.246.38c-0.068-.288-0.137-0.582-0.212-0.885-0.459-1.847-2.494-.984-2.941-0.8-0.482.2-.353,0.647-0.094,0.529a0.869,0.869,0,0,1,1.281.585c0.217,0.751.377,1.436,0.527,2.038a5.688,5.688,0,0,1-.362.467,2.69,2.69,0,0,1-.264.271q-0.221-.08-0.471-0.147a2.029,2.029,0,0,0-.522-0.066,1.079,1.079,0,0,0-.768.3A1.058,1.058,0,0,0,9,15.131a0.82,0.82,0,0,0,.832.852,1.134,1.134,0,0,0,.787-0.3,5.11,5.11,0,0,0,.776-0.993q0.141-.219.215-0.34c0.046-.076.122-0.194,0.223-0.346a2.786,2.786,0,0,0,.918,1.726,2.582,2.582,0,0,0,2.376-.185c0.317-.181.212-0.565,0-0.494A0.807,0.807,0,0,1,14.176,15a5.159,5.159,0,0,1-.913-2.446l0,0Q13.487,12.24,13.663,12.027Z></path> </svg>'},function(B,_){B.exports='<svg viewBox="0 0 18 18"> <path class=ql-fill d=M10,4V14a1,1,0,0,1-2,0V10H3v4a1,1,0,0,1-2,0V4A1,1,0,0,1,3,4V8H8V4a1,1,0,0,1,2,0Zm6.06787,9.209H14.98975V7.59863a.54085.54085,0,0,0-.605-.60547h-.62744a1.01119,1.01119,0,0,0-.748.29688L11.645,8.56641a.5435.5435,0,0,0-.022.8584l.28613.30762a.53861.53861,0,0,0,.84717.0332l.09912-.08789a1.2137,1.2137,0,0,0,.2417-.35254h.02246s-.01123.30859-.01123.60547V13.209H12.041a.54085.54085,0,0,0-.605.60547v.43945a.54085.54085,0,0,0,.605.60547h4.02686a.54085.54085,0,0,0,.605-.60547v-.43945A.54085.54085,0,0,0,16.06787,13.209Z /> </svg>'},function(B,_){B.exports='<svg viewBox="0 0 18 18"> <path class=ql-fill d=M16.73975,13.81445v.43945a.54085.54085,0,0,1-.605.60547H11.855a.58392.58392,0,0,1-.64893-.60547V14.0127c0-2.90527,3.39941-3.42187,3.39941-4.55469a.77675.77675,0,0,0-.84717-.78125,1.17684,1.17684,0,0,0-.83594.38477c-.2749.26367-.561.374-.85791.13184l-.4292-.34082c-.30811-.24219-.38525-.51758-.1543-.81445a2.97155,2.97155,0,0,1,2.45361-1.17676,2.45393,2.45393,0,0,1,2.68408,2.40918c0,2.45312-3.1792,2.92676-3.27832,3.93848h2.79443A.54085.54085,0,0,1,16.73975,13.81445ZM9,3A.99974.99974,0,0,0,8,4V8H3V4A1,1,0,0,0,1,4V14a1,1,0,0,0,2,0V10H8v4a1,1,0,0,0,2,0V4A.99974.99974,0,0,0,9,3Z /> </svg>'},function(B,_){B.exports='<svg viewbox="0 0 18 18"> <line class=ql-stroke x1=7 x2=13 y1=4 y2=4></line> <line class=ql-stroke x1=5 x2=11 y1=14 y2=14></line> <line class=ql-stroke x1=8 x2=10 y1=14 y2=4></line> </svg>'},function(B,_){B.exports='<svg viewbox="0 0 18 18"> <rect class=ql-stroke height=10 width=12 x=3 y=4></rect> <circle class=ql-fill cx=6 cy=7 r=1></circle> <polyline class="ql-even ql-fill" points="5 12 5 11 7 9 8 10 11 7 13 9 13 12 5 12"></polyline> </svg>'},function(B,_){B.exports='<svg viewbox="0 0 18 18"> <line class=ql-stroke x1=3 x2=15 y1=14 y2=14></line> <line class=ql-stroke x1=3 x2=15 y1=4 y2=4></line> <line class=ql-stroke x1=9 x2=15 y1=9 y2=9></line> <polyline class="ql-fill ql-stroke" points="3 7 3 11 5 9 3 7"></polyline> </svg>'},function(B,_){B.exports='<svg viewbox="0 0 18 18"> <line class=ql-stroke x1=3 x2=15 y1=14 y2=14></line> <line class=ql-stroke x1=3 x2=15 y1=4 y2=4></line> <line class=ql-stroke x1=9 x2=15 y1=9 y2=9></line> <polyline class=ql-stroke points="5 7 5 11 3 9 5 7"></polyline> </svg>'},function(B,_){B.exports='<svg viewbox="0 0 18 18"> <line class=ql-stroke x1=7 x2=11 y1=7 y2=11></line> <path class="ql-even ql-stroke" d=M8.9,4.577a3.476,3.476,0,0,1,.36,4.679A3.476,3.476,0,0,1,4.577,8.9C3.185,7.5,2.035,6.4,4.217,4.217S7.5,3.185,8.9,4.577Z></path> <path class="ql-even ql-stroke" d=M13.423,9.1a3.476,3.476,0,0,0-4.679-.36,3.476,3.476,0,0,0,.36,4.679c1.392,1.392,2.5,2.542,4.679.36S14.815,10.5,13.423,9.1Z></path> </svg>'},function(B,_){B.exports='<svg viewbox="0 0 18 18"> <line class=ql-stroke x1=7 x2=15 y1=4 y2=4></line> <line class=ql-stroke x1=7 x2=15 y1=9 y2=9></line> <line class=ql-stroke x1=7 x2=15 y1=14 y2=14></line> <line class="ql-stroke ql-thin" x1=2.5 x2=4.5 y1=5.5 y2=5.5></line> <path class=ql-fill d=M3.5,6A0.5,0.5,0,0,1,3,5.5V3.085l-0.276.138A0.5,0.5,0,0,1,2.053,3c-0.124-.247-0.023-0.324.224-0.447l1-.5A0.5,0.5,0,0,1,4,2.5v3A0.5,0.5,0,0,1,3.5,6Z></path> <path class="ql-stroke ql-thin" d=M4.5,10.5h-2c0-.234,1.85-1.076,1.85-2.234A0.959,0.959,0,0,0,2.5,8.156></path> <path class="ql-stroke ql-thin" d=M2.5,14.846a0.959,0.959,0,0,0,1.85-.109A0.7,0.7,0,0,0,3.75,14a0.688,0.688,0,0,0,.6-0.736,0.959,0.959,0,0,0-1.85-.109></path> </svg>'},function(B,_){B.exports='<svg viewbox="0 0 18 18"> <line class=ql-stroke x1=6 x2=15 y1=4 y2=4></line> <line class=ql-stroke x1=6 x2=15 y1=9 y2=9></line> <line class=ql-stroke x1=6 x2=15 y1=14 y2=14></line> <line class=ql-stroke x1=3 x2=3 y1=4 y2=4></line> <line class=ql-stroke x1=3 x2=3 y1=9 y2=9></line> <line class=ql-stroke x1=3 x2=3 y1=14 y2=14></line> </svg>'},function(B,_){B.exports='<svg class="" viewbox="0 0 18 18"> <line class=ql-stroke x1=9 x2=15 y1=4 y2=4></line> <polyline class=ql-stroke points="3 4 4 5 6 3"></polyline> <line class=ql-stroke x1=9 x2=15 y1=14 y2=14></line> <polyline class=ql-stroke points="3 14 4 15 6 13"></polyline> <line class=ql-stroke x1=9 x2=15 y1=9 y2=9></line> <polyline class=ql-stroke points="3 9 4 10 6 8"></polyline> </svg>'},function(B,_){B.exports='<svg viewbox="0 0 18 18"> <path class=ql-fill d=M15.5,15H13.861a3.858,3.858,0,0,0,1.914-2.975,1.8,1.8,0,0,0-1.6-1.751A1.921,1.921,0,0,0,12.021,11.7a0.50013,0.50013,0,1,0,.957.291h0a0.914,0.914,0,0,1,1.053-.725,0.81,0.81,0,0,1,.744.762c0,1.076-1.16971,1.86982-1.93971,2.43082A1.45639,1.45639,0,0,0,12,15.5a0.5,0.5,0,0,0,.5.5h3A0.5,0.5,0,0,0,15.5,15Z /> <path class=ql-fill d=M9.65,5.241a1,1,0,0,0-1.409.108L6,7.964,3.759,5.349A1,1,0,0,0,2.192,6.59178Q2.21541,6.6213,2.241,6.649L4.684,9.5,2.241,12.35A1,1,0,0,0,3.71,13.70722q0.02557-.02768.049-0.05722L6,11.036,8.241,13.65a1,1,0,1,0,1.567-1.24277Q9.78459,12.3777,9.759,12.35L7.316,9.5,9.759,6.651A1,1,0,0,0,9.65,5.241Z /> </svg>'},function(B,_){B.exports='<svg viewbox="0 0 18 18"> <path class=ql-fill d=M15.5,7H13.861a4.015,4.015,0,0,0,1.914-2.975,1.8,1.8,0,0,0-1.6-1.751A1.922,1.922,0,0,0,12.021,3.7a0.5,0.5,0,1,0,.957.291,0.917,0.917,0,0,1,1.053-.725,0.81,0.81,0,0,1,.744.762c0,1.077-1.164,1.925-1.934,2.486A1.423,1.423,0,0,0,12,7.5a0.5,0.5,0,0,0,.5.5h3A0.5,0.5,0,0,0,15.5,7Z /> <path class=ql-fill d=M9.651,5.241a1,1,0,0,0-1.41.108L6,7.964,3.759,5.349a1,1,0,1,0-1.519,1.3L4.683,9.5,2.241,12.35a1,1,0,1,0,1.519,1.3L6,11.036,8.241,13.65a1,1,0,0,0,1.519-1.3L7.317,9.5,9.759,6.651A1,1,0,0,0,9.651,5.241Z /> </svg>'},function(B,_){B.exports='<svg viewbox="0 0 18 18"> <line class="ql-stroke ql-thin" x1=15.5 x2=2.5 y1=8.5 y2=9.5></line> <path class=ql-fill d=M9.007,8C6.542,7.791,6,7.519,6,6.5,6,5.792,7.283,5,9,5c1.571,0,2.765.679,2.969,1.309a1,1,0,0,0,1.9-.617C13.356,4.106,11.354,3,9,3,6.2,3,4,4.538,4,6.5a3.2,3.2,0,0,0,.5,1.843Z></path> <path class=ql-fill d=M8.984,10C11.457,10.208,12,10.479,12,11.5c0,0.708-1.283,1.5-3,1.5-1.571,0-2.765-.679-2.969-1.309a1,1,0,1,0-1.9.617C4.644,13.894,6.646,15,9,15c2.8,0,5-1.538,5-3.5a3.2,3.2,0,0,0-.5-1.843Z></path> </svg>'},function(B,_){B.exports='<svg viewbox="0 0 18 18"> <path class=ql-stroke d=M5,3V9a4.012,4.012,0,0,0,4,4H9a4.012,4.012,0,0,0,4-4V3></path> <rect class=ql-fill height=1 rx=0.5 ry=0.5 width=12 x=3 y=15></rect> </svg>'},function(B,_){B.exports='<svg viewbox="0 0 18 18"> <rect class=ql-stroke height=12 width=12 x=3 y=3></rect> <rect class=ql-fill height=12 width=1 x=5 y=3></rect> <rect class=ql-fill height=12 width=1 x=12 y=3></rect> <rect class=ql-fill height=2 width=8 x=5 y=8></rect> <rect class=ql-fill height=1 width=3 x=3 y=5></rect> <rect class=ql-fill height=1 width=3 x=3 y=7></rect> <rect class=ql-fill height=1 width=3 x=3 y=10></rect> <rect class=ql-fill height=1 width=3 x=3 y=12></rect> <rect class=ql-fill height=1 width=3 x=12 y=5></rect> <rect class=ql-fill height=1 width=3 x=12 y=7></rect> <rect class=ql-fill height=1 width=3 x=12 y=10></rect> <rect class=ql-fill height=1 width=3 x=12 y=12></rect> </svg>'},function(B,_){B.exports='<svg viewbox="0 0 18 18"> <polygon class=ql-stroke points="7 11 9 13 11 11 7 11"></polygon> <polygon class=ql-stroke points="7 7 9 5 11 7 7 7"></polygon> </svg>'},function(B,_,d){Object.defineProperty(_,"__esModule",{value:!0}),_.default=_.BubbleTooltip=void 0;var P=function m(b,N,p){b===null&&(b=Function.prototype);var h=Object.getOwnPropertyDescriptor(b,N);if(h===void 0){var v=Object.getPrototypeOf(b);return v===null?void 0:m(v,N,p)}else{if("value"in h)return h.value;var A=h.get;return A===void 0?void 0:A.call(p)}},w=function(){function m(b,N){for(var p=0;p<N.length;p++){var h=N[p];h.enumerable=h.enumerable||!1,h.configurable=!0,"value"in h&&(h.writable=!0),Object.defineProperty(b,h.key,h)}}return function(b,N,p){return N&&m(b.prototype,N),p&&m(b,p),b}}(),k=d(3),g=a(k),y=d(8),c=a(y),r=d(43),t=a(r),e=d(15),u=d(41),l=a(u);function a(m){return m&&m.__esModule?m:{default:m}}function i(m,b){if(!(m instanceof b))throw new TypeError("Cannot call a class as a function")}function o(m,b){if(!m)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return b&&(typeof b=="object"||typeof b=="function")?b:m}function f(m,b){if(typeof b!="function"&&b!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof b);m.prototype=Object.create(b&&b.prototype,{constructor:{value:m,enumerable:!1,writable:!0,configurable:!0}}),b&&(Object.setPrototypeOf?Object.setPrototypeOf(m,b):m.__proto__=b)}var n=[["bold","italic","link"],[{header:1},{header:2},"blockquote"]],s=function(m){f(b,m);function b(N,p){i(this,b),p.modules.toolbar!=null&&p.modules.toolbar.container==null&&(p.modules.toolbar.container=n);var h=o(this,(b.__proto__||Object.getPrototypeOf(b)).call(this,N,p));return h.quill.container.classList.add("ql-bubble"),h}return w(b,[{key:"extendToolbar",value:function(p){this.tooltip=new E(this.quill,this.options.bounds),this.tooltip.root.appendChild(p.container),this.buildButtons([].slice.call(p.container.querySelectorAll("button")),l.default),this.buildPickers([].slice.call(p.container.querySelectorAll("select")),l.default)}}]),b}(t.default);s.DEFAULTS=(0,g.default)(!0,{},t.default.DEFAULTS,{modules:{toolbar:{handlers:{link:function(b){b?this.quill.theme.tooltip.edit():this.quill.format("link",!1)}}}}});var E=function(m){f(b,m);function b(N,p){i(this,b);var h=o(this,(b.__proto__||Object.getPrototypeOf(b)).call(this,N,p));return h.quill.on(c.default.events.EDITOR_CHANGE,function(v,A,T,q){if(v===c.default.events.SELECTION_CHANGE)if(A!=null&&A.length>0&&q===c.default.sources.USER){h.show(),h.root.style.left="0px",h.root.style.width="",h.root.style.width=h.root.offsetWidth+"px";var D=h.quill.getLines(A.index,A.length);if(D.length===1)h.position(h.quill.getBounds(A));else{var C=D[D.length-1],Z=h.quill.getIndex(C),I=Math.min(C.length()-1,A.index+A.length-Z),R=h.quill.getBounds(new e.Range(Z,I));h.position(R)}}else document.activeElement!==h.textbox&&h.quill.hasFocus()&&h.hide()}),h}return w(b,[{key:"listen",value:function(){var p=this;P(b.prototype.__proto__||Object.getPrototypeOf(b.prototype),"listen",this).call(this),this.root.querySelector(".ql-close").addEventListener("click",function(){p.root.classList.remove("ql-editing")}),this.quill.on(c.default.events.SCROLL_OPTIMIZE,function(){setTimeout(function(){if(!p.root.classList.contains("ql-hidden")){var h=p.quill.getSelection();h!=null&&p.position(p.quill.getBounds(h))}},1)})}},{key:"cancel",value:function(){this.show()}},{key:"position",value:function(p){var h=P(b.prototype.__proto__||Object.getPrototypeOf(b.prototype),"position",this).call(this,p),v=this.root.querySelector(".ql-tooltip-arrow");if(v.style.marginLeft="",h===0)return h;v.style.marginLeft=-1*h-v.offsetWidth/2+"px"}}]),b}(r.BaseTooltip);E.TEMPLATE=['<span class="ql-tooltip-arrow"></span>','<div class="ql-tooltip-editor">','<input type="text" data-formula="e=mc^2" data-link="https://quilljs.com" data-video="Embed URL">','<a class="ql-close"></a>',"</div>"].join(""),_.BubbleTooltip=E,_.default=s},function(B,_,d){B.exports=d(63)}]).default})}(dt)),dt.exports}var ft,yt;function kt(){if(yt)return ft;yt=1;var vt=ft&&ft.__extends||function(){var c=function(r,t){return c=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,u){e.__proto__=u}||function(e,u){for(var l in u)u.hasOwnProperty(l)&&(e[l]=u[l])},c(r,t)};return function(r,t){c(r,t);function e(){this.constructor=r}r.prototype=t===null?Object.create(t):(e.prototype=t.prototype,new e)}}(),ht=ft&&ft.__assign||function(){return ht=Object.assign||function(c){for(var r,t=1,e=arguments.length;t<e;t++){r=arguments[t];for(var u in r)Object.prototype.hasOwnProperty.call(r,u)&&(c[u]=r[u])}return c},ht.apply(this,arguments)},B=ft&&ft.__spreadArrays||function(){for(var c=0,r=0,t=arguments.length;r<t;r++)c+=arguments[r].length;for(var e=Array(c),u=0,r=0;r<t;r++)for(var l=arguments[r],a=0,i=l.length;a<i;a++,u++)e[u]=l[a];return e},_=ft&&ft.__importDefault||function(c){return c&&c.__esModule?c:{default:c}},d=_(mt()),P=_(bt()),w=_(Ot()),k=_(At()),g=function(c){vt(r,c);function r(t){var e=c.call(this,t)||this;e.dirtyProps=["modules","formats","bounds","theme","children"],e.cleanProps=["id","className","style","placeholder","tabIndex","onChange","onChangeSelection","onFocus","onBlur","onKeyPress","onKeyDown","onKeyUp"],e.state={generation:0},e.selection=null,e.onEditorChange=function(l,a,i,o){var f,n,s,E;l==="text-change"?(n=(f=e).onEditorChangeText)===null||n===void 0||n.call(f,e.editor.root.innerHTML,a,o,e.unprivilegedEditor):l==="selection-change"&&((E=(s=e).onEditorChangeSelection)===null||E===void 0||E.call(s,a,o,e.unprivilegedEditor))};var u=e.isControlled()?t.value:t.defaultValue;return e.value=u!=null?u:"",e}return r.prototype.validateProps=function(t){var e;if(d.default.Children.count(t.children)>1)throw new Error("The Quill editing area can only be composed of a single React element.");if(d.default.Children.count(t.children)){var u=d.default.Children.only(t.children);if(((e=u)===null||e===void 0?void 0:e.type)==="textarea")throw new Error("Quill does not support editing on a <textarea>. Use a <div> instead.")}if(this.lastDeltaChangeSet&&t.value===this.lastDeltaChangeSet)throw new Error("You are passing the `delta` object from the `onChange` event back as `value`. You most probably want `editor.getContents()` instead. See: https://github.com/zenoamaro/react-quill#using-deltas")},r.prototype.shouldComponentUpdate=function(t,e){var u=this,l;if(this.validateProps(t),!this.editor||this.state.generation!==e.generation)return!0;if("value"in t){var a=this.getEditorContents(),i=(l=t.value,l!=null?l:"");this.isEqualValue(i,a)||this.setEditorContents(this.editor,i)}return t.readOnly!==this.props.readOnly&&this.setEditorReadOnly(this.editor,t.readOnly),B(this.cleanProps,this.dirtyProps).some(function(o){return!w.default(t[o],u.props[o])})},r.prototype.shouldComponentRegenerate=function(t){var e=this;return this.dirtyProps.some(function(u){return!w.default(t[u],e.props[u])})},r.prototype.componentDidMount=function(){this.instantiateEditor(),this.setEditorContents(this.editor,this.getEditorContents())},r.prototype.componentWillUnmount=function(){this.destroyEditor()},r.prototype.componentDidUpdate=function(t,e){var u=this;if(this.editor&&this.shouldComponentRegenerate(t)){var l=this.editor.getContents(),a=this.editor.getSelection();this.regenerationSnapshot={delta:l,selection:a},this.setState({generation:this.state.generation+1}),this.destroyEditor()}if(this.state.generation!==e.generation){var i=this.regenerationSnapshot,l=i.delta,o=i.selection;delete this.regenerationSnapshot,this.instantiateEditor();var f=this.editor;f.setContents(l),y(function(){return u.setEditorSelection(f,o)})}},r.prototype.instantiateEditor=function(){this.editor?this.hookEditor(this.editor):this.editor=this.createEditor(this.getEditingArea(),this.getEditorConfig())},r.prototype.destroyEditor=function(){this.editor&&this.unhookEditor(this.editor)},r.prototype.isControlled=function(){return"value"in this.props},r.prototype.getEditorConfig=function(){return{bounds:this.props.bounds,formats:this.props.formats,modules:this.props.modules,placeholder:this.props.placeholder,readOnly:this.props.readOnly,scrollingContainer:this.props.scrollingContainer,tabIndex:this.props.tabIndex,theme:this.props.theme}},r.prototype.getEditor=function(){if(!this.editor)throw new Error("Accessing non-instantiated editor");return this.editor},r.prototype.createEditor=function(t,e){var u=new k.default(t,e);return e.tabIndex!=null&&this.setEditorTabIndex(u,e.tabIndex),this.hookEditor(u),u},r.prototype.hookEditor=function(t){this.unprivilegedEditor=this.makeUnprivilegedEditor(t),t.on("editor-change",this.onEditorChange)},r.prototype.unhookEditor=function(t){t.off("editor-change",this.onEditorChange)},r.prototype.getEditorContents=function(){return this.value},r.prototype.getEditorSelection=function(){return this.selection},r.prototype.isDelta=function(t){return t&&t.ops},r.prototype.isEqualValue=function(t,e){return this.isDelta(t)&&this.isDelta(e)?w.default(t.ops,e.ops):w.default(t,e)},r.prototype.setEditorContents=function(t,e){var u=this;this.value=e;var l=this.getEditorSelection();typeof e=="string"?t.setContents(t.clipboard.convert(e)):t.setContents(e),y(function(){return u.setEditorSelection(t,l)})},r.prototype.setEditorSelection=function(t,e){if(this.selection=e,e){var u=t.getLength();e.index=Math.max(0,Math.min(e.index,u-1)),e.length=Math.max(0,Math.min(e.length,u-1-e.index)),t.setSelection(e)}},r.prototype.setEditorTabIndex=function(t,e){var u,l;!((l=(u=t)===null||u===void 0?void 0:u.scroll)===null||l===void 0)&&l.domNode&&(t.scroll.domNode.tabIndex=e)},r.prototype.setEditorReadOnly=function(t,e){e?t.disable():t.enable()},r.prototype.makeUnprivilegedEditor=function(t){var e=t;return{getHTML:function(){return e.root.innerHTML},getLength:e.getLength.bind(e),getText:e.getText.bind(e),getContents:e.getContents.bind(e),getSelection:e.getSelection.bind(e),getBounds:e.getBounds.bind(e)}},r.prototype.getEditingArea=function(){if(!this.editingArea)throw new Error("Instantiating on missing editing area");var t=P.default.findDOMNode(this.editingArea);if(!t)throw new Error("Cannot find element for editing area");if(t.nodeType===3)throw new Error("Editing area cannot be a text node");return t},r.prototype.renderEditingArea=function(){var t=this,e=this.props,u=e.children,l=e.preserveWhitespace,a=this.state.generation,i={key:a,ref:function(o){t.editingArea=o}};return d.default.Children.count(u)?d.default.cloneElement(d.default.Children.only(u),i):l?d.default.createElement("pre",ht({},i)):d.default.createElement("div",ht({},i))},r.prototype.render=function(){var t;return d.default.createElement("div",{id:this.props.id,style:this.props.style,key:this.state.generation,className:"quill "+(t=this.props.className,t!=null?t:""),onKeyPress:this.props.onKeyPress,onKeyDown:this.props.onKeyDown,onKeyUp:this.props.onKeyUp},this.renderEditingArea())},r.prototype.onEditorChangeText=function(t,e,u,l){var a,i;if(this.editor){var o=this.isDelta(this.value)?l.getContents():l.getHTML();o!==this.getEditorContents()&&(this.lastDeltaChangeSet=e,this.value=o,(i=(a=this.props).onChange)===null||i===void 0||i.call(a,t,e,u,l))}},r.prototype.onEditorChangeSelection=function(t,e,u){var l,a,i,o,f,n;if(this.editor){var s=this.getEditorSelection(),E=!s&&t,m=s&&!t;w.default(t,s)||(this.selection=t,(a=(l=this.props).onChangeSelection)===null||a===void 0||a.call(l,t,e,u),E?(o=(i=this.props).onFocus)===null||o===void 0||o.call(i,t,e,u):m&&((n=(f=this.props).onBlur)===null||n===void 0||n.call(f,s,e,u)))}},r.prototype.focus=function(){this.editor&&this.editor.focus()},r.prototype.blur=function(){this.editor&&(this.selection=null,this.editor.blur())},r.displayName="React Quill",r.Quill=k.default,r.defaultProps={theme:"snow",modules:{},readOnly:!1},r}(d.default.Component);function y(c){Promise.resolve().then(c)}return ft=g,ft}var wt=kt();const xt=_t(wt);export{xt as R};
