// Utility to geocode {region, country} to [lat, lon] using backend geocoding service, with localStorage caching
export async function geocodeRegion(region: string, country: string): Promise<[number, number] | null> {
  const cacheKey = `geocode_${region}_${country}`;

  // Check localStorage cache first
  const cached = localStorage.getItem(cacheKey);
  if (cached) {
    try {
      const parsed = JSON.parse(cached);
      if (Array.isArray(parsed) && parsed.length === 2 && typeof parsed[0] === 'number' && typeof parsed[1] === 'number') {
        console.log(`[DEBUG] Using cached coordinates for ${region}, ${country}:`, parsed);
        return [parsed[0], parsed[1]];
      }
    } catch (error) {
      console.warn(`[DEBUG] Failed to parse cached coordinates:`, error);
    }
  }

  // Use backend geocoding service to avoid CORS issues
  const url = `http://localhost:5000/api/visitor/geocode?region=${encodeURIComponent(region)}&country=${encodeURIComponent(country)}`;

  try {
    console.log(`[DEBUG] Geocoding ${region}, ${country} via backend...`);
    const response = await fetch(url);

    if (!response.ok) {
      console.warn(`[DEBUG] Backend geocoding failed with status: ${response.status}`);
      return null;
    }

    const data = await response.json();

    if (data.success && data.coordinates && Array.isArray(data.coordinates) && data.coordinates.length === 2) {
      const coordinates: [number, number] = [data.coordinates[0], data.coordinates[1]];

      // Cache the result
      localStorage.setItem(cacheKey, JSON.stringify(coordinates));
      console.log(`[DEBUG] Successfully geocoded ${region}, ${country} to:`, coordinates);

      return coordinates;
    } else {
      console.warn(`[DEBUG] Backend geocoding returned invalid data:`, data);
      return null;
    }
  } catch (error) {
    console.error(`[DEBUG] Backend geocoding failed:`, error);
    return null;
  }
}