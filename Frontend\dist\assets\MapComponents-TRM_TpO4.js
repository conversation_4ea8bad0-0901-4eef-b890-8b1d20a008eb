var ms=Object.defineProperty,ps=Object.defineProperties;var gs=Object.getOwnPropertyDescriptors;var Pe=Object.getOwnPropertySymbols;var kn=Object.prototype.hasOwnProperty,On=Object.prototype.propertyIsEnumerable;var zn=(d,l,a)=>l in d?ms(d,l,{enumerable:!0,configurable:!0,writable:!0,value:a}):d[l]=a,Le=(d,l)=>{for(var a in l||(l={}))kn.call(l,a)&&zn(d,a,l[a]);if(Pe)for(var a of Pe(l))On.call(l,a)&&zn(d,a,l[a]);return d},In=(d,l)=>ps(d,gs(l));var be=(d,l)=>{var a={};for(var f in d)kn.call(d,f)&&l.indexOf(f)<0&&(a[f]=d[f]);if(d!=null&&Pe)for(var f of Pe(d))l.indexOf(f)<0&&On.call(d,f)&&(a[f]=d[f]);return a};import{r as k,k as ci,aI as vs,ab as Nn,j as xt}from"./index-hEW_vQ3f.js";import{g as Ac}from"./MarketingDashboard-B-b1bZoZ.js";function Rn(d,l){const a=k.useRef(l);k.useEffect(function(){l!==a.current&&d.attributionControl!=null&&(a.current!=null&&d.attributionControl.removeAttribution(a.current),l!=null&&d.attributionControl.addAttribution(l)),a.current=l},[d,l])}function ys(d,l,a){l.center!==a.center&&d.setLatLng(l.center),l.radius!=null&&l.radius!==a.radius&&d.setRadius(l.radius)}const ws=1;function xs(d){return Object.freeze({__version:ws,map:d})}function Ps(d,l){return Object.freeze(Le(Le({},d),l))}const Dn=k.createContext(null),Hn=Dn.Provider;function di(){const d=k.useContext(Dn);if(d==null)throw new Error("No context provided: useLeafletContext() can only be used in a descendant of <MapContainer>");return d}function Ls(d){function l(a,f){const{instance:_,context:A}=d(a).current;return k.useImperativeHandle(f,()=>_),a.children==null?null:ci.createElement(Hn,{value:A},a.children)}return k.forwardRef(l)}function bs(d){function l(a,f){const[_,A]=k.useState(!1),{instance:p}=d(a,A).current;k.useImperativeHandle(f,()=>p),k.useEffect(function(){_&&p.update()},[p,_,a.children]);const G=p._contentNode;return G?vs.createPortal(a.children,G):null}return k.forwardRef(l)}function Ts(d){function l(a,f){const{instance:_}=d(a).current;return k.useImperativeHandle(f,()=>_),null}return k.forwardRef(l)}function fi(d,l){const a=k.useRef();k.useEffect(function(){return l!=null&&d.instance.on(l),a.current=l,function(){a.current!=null&&d.instance.off(a.current),a.current=null}},[d,l])}function Te(d,l){var f;const a=(f=d.pane)!=null?f:l.pane;return a?In(Le({},d),{pane:a}):d}function Ms(d,l){return function(f,_){const A=di(),p=d(Te(f,A),A);return Rn(A.map,f.attribution),fi(p.current,f.eventHandlers),l(p.current,A,f,_),p}}var Qt={exports:{}};/* @preserve
 * Leaflet 1.9.4, a JS library for interactive maps. https://leafletjs.com
 * (c) 2010-2023 Vladimir Agafonkin, (c) 2010-2011 CloudMade
 */var Cs=Qt.exports,Bn;function Ss(){return Bn||(Bn=1,function(d,l){(function(a,f){f(l)})(Cs,function(a){var f="1.9.4";function _(t){var e,i,n,o;for(i=1,n=arguments.length;i<n;i++){o=arguments[i];for(e in o)t[e]=o[e]}return t}var A=Object.create||function(){function t(){}return function(e){return t.prototype=e,new t}}();function p(t,e){var i=Array.prototype.slice;if(t.bind)return t.bind.apply(t,i.call(arguments,1));var n=i.call(arguments,2);return function(){return t.apply(e,n.length?n.concat(i.call(arguments)):arguments)}}var G=0;function P(t){return"_leaflet_id"in t||(t._leaflet_id=++G),t._leaflet_id}function ot(t,e,i){var n,o,s,r;return r=function(){n=!1,o&&(s.apply(i,o),o=!1)},s=function(){n?o=arguments:(t.apply(i,arguments),setTimeout(r,e),n=!0)},s}function st(t,e,i){var n=e[1],o=e[0],s=n-o;return t===n&&i?t:((t-o)%s+s)%s+o}function I(){return!1}function et(t,e){if(e===!1)return t;var i=Math.pow(10,e===void 0?6:e);return Math.round(t*i)/i}function Ct(t){return t.trim?t.trim():t.replace(/^\s+|\s+$/g,"")}function ct(t){return Ct(t).split(/\s+/)}function M(t,e){Object.prototype.hasOwnProperty.call(t,"options")||(t.options=t.options?A(t.options):{});for(var i in e)t.options[i]=e[i];return t.options}function te(t,e,i){var n=[];for(var o in t)n.push(encodeURIComponent(i?o.toUpperCase():o)+"="+encodeURIComponent(t[o]));return(!e||e.indexOf("?")===-1?"?":"&")+n.join("&")}var Me=/\{ *([\w_ -]+) *\}/g;function ee(t,e){return t.replace(Me,function(i,n){var o=e[n];if(o===void 0)throw new Error("No value provided for variable "+i);return typeof o=="function"&&(o=o(e)),o})}var W=Array.isArray||function(t){return Object.prototype.toString.call(t)==="[object Array]"};function gt(t,e){for(var i=0;i<t.length;i++)if(t[i]===e)return i;return-1}var ie="data:image/gif;base64,R0lGODlhAQABAAD/ACwAAAAAAQABAAACADs=";function Ce(t){return window["webkit"+t]||window["moz"+t]||window["ms"+t]}var pi=0;function gi(t){var e=+new Date,i=Math.max(0,16-(e-pi));return pi=e+i,window.setTimeout(t,i)}var Se=window.requestAnimationFrame||Ce("RequestAnimationFrame")||gi,vi=window.cancelAnimationFrame||Ce("CancelAnimationFrame")||Ce("CancelRequestAnimationFrame")||function(t){window.clearTimeout(t)};function K(t,e,i){if(i&&Se===gi)t.call(e);else return Se.call(window,p(t,e))}function X(t){t&&vi.call(window,t)}var Wn={__proto__:null,extend:_,create:A,bind:p,get lastId(){return G},stamp:P,throttle:ot,wrapNum:st,falseFn:I,formatNum:et,trim:Ct,splitWords:ct,setOptions:M,getParamString:te,template:ee,isArray:W,indexOf:gt,emptyImageUrl:ie,requestFn:Se,cancelFn:vi,requestAnimFrame:K,cancelAnimFrame:X};function lt(){}lt.extend=function(t){var e=function(){M(this),this.initialize&&this.initialize.apply(this,arguments),this.callInitHooks()},i=e.__super__=this.prototype,n=A(i);n.constructor=e,e.prototype=n;for(var o in this)Object.prototype.hasOwnProperty.call(this,o)&&o!=="prototype"&&o!=="__super__"&&(e[o]=this[o]);return t.statics&&_(e,t.statics),t.includes&&(Un(t.includes),_.apply(null,[n].concat(t.includes))),_(n,t),delete n.statics,delete n.includes,n.options&&(n.options=i.options?A(i.options):{},_(n.options,t.options)),n._initHooks=[],n.callInitHooks=function(){if(!this._initHooksCalled){i.callInitHooks&&i.callInitHooks.call(this),this._initHooksCalled=!0;for(var s=0,r=n._initHooks.length;s<r;s++)n._initHooks[s].call(this)}},e},lt.include=function(t){var e=this.prototype.options;return _(this.prototype,t),t.options&&(this.prototype.options=e,this.mergeOptions(t.options)),this},lt.mergeOptions=function(t){return _(this.prototype.options,t),this},lt.addInitHook=function(t){var e=Array.prototype.slice.call(arguments,1),i=typeof t=="function"?t:function(){this[t].apply(this,e)};return this.prototype._initHooks=this.prototype._initHooks||[],this.prototype._initHooks.push(i),this};function Un(t){if(!(typeof L=="undefined"||!L||!L.Mixin)){t=W(t)?t:[t];for(var e=0;e<t.length;e++)t[e]===L.Mixin.Events&&console.warn("Deprecated include of L.Mixin.Events: this property will be removed in future releases, please inherit from L.Evented instead.",new Error().stack)}}var Y={on:function(t,e,i){if(typeof t=="object")for(var n in t)this._on(n,t[n],e);else{t=ct(t);for(var o=0,s=t.length;o<s;o++)this._on(t[o],e,i)}return this},off:function(t,e,i){if(!arguments.length)delete this._events;else if(typeof t=="object")for(var n in t)this._off(n,t[n],e);else{t=ct(t);for(var o=arguments.length===1,s=0,r=t.length;s<r;s++)o?this._off(t[s]):this._off(t[s],e,i)}return this},_on:function(t,e,i,n){if(typeof e!="function"){console.warn("wrong listener type: "+typeof e);return}if(this._listens(t,e,i)===!1){i===this&&(i=void 0);var o={fn:e,ctx:i};n&&(o.once=!0),this._events=this._events||{},this._events[t]=this._events[t]||[],this._events[t].push(o)}},_off:function(t,e,i){var n,o,s;if(this._events&&(n=this._events[t],!!n)){if(arguments.length===1){if(this._firingCount)for(o=0,s=n.length;o<s;o++)n[o].fn=I;delete this._events[t];return}if(typeof e!="function"){console.warn("wrong listener type: "+typeof e);return}var r=this._listens(t,e,i);if(r!==!1){var h=n[r];this._firingCount&&(h.fn=I,this._events[t]=n=n.slice()),n.splice(r,1)}}},fire:function(t,e,i){if(!this.listens(t,i))return this;var n=_({},e,{type:t,target:this,sourceTarget:e&&e.sourceTarget||this});if(this._events){var o=this._events[t];if(o){this._firingCount=this._firingCount+1||1;for(var s=0,r=o.length;s<r;s++){var h=o[s],u=h.fn;h.once&&this.off(t,u,h.ctx),u.call(h.ctx||this,n)}this._firingCount--}}return i&&this._propagateEvent(n),this},listens:function(t,e,i,n){typeof t!="string"&&console.warn('"string" type argument expected');var o=e;typeof e!="function"&&(n=!!e,o=void 0,i=void 0);var s=this._events&&this._events[t];if(s&&s.length&&this._listens(t,o,i)!==!1)return!0;if(n){for(var r in this._eventParents)if(this._eventParents[r].listens(t,e,i,n))return!0}return!1},_listens:function(t,e,i){if(!this._events)return!1;var n=this._events[t]||[];if(!e)return!!n.length;i===this&&(i=void 0);for(var o=0,s=n.length;o<s;o++)if(n[o].fn===e&&n[o].ctx===i)return o;return!1},once:function(t,e,i){if(typeof t=="object")for(var n in t)this._on(n,t[n],e,!0);else{t=ct(t);for(var o=0,s=t.length;o<s;o++)this._on(t[o],e,i,!0)}return this},addEventParent:function(t){return this._eventParents=this._eventParents||{},this._eventParents[P(t)]=t,this},removeEventParent:function(t){return this._eventParents&&delete this._eventParents[P(t)],this},_propagateEvent:function(t){for(var e in this._eventParents)this._eventParents[e].fire(t.type,_({layer:t.target,propagatedFrom:t.target},t),!0)}};Y.addEventListener=Y.on,Y.removeEventListener=Y.clearAllEventListeners=Y.off,Y.addOneTimeEventListener=Y.once,Y.fireEvent=Y.fire,Y.hasEventListeners=Y.listens;var Nt=lt.extend(Y);function w(t,e,i){this.x=i?Math.round(t):t,this.y=i?Math.round(e):e}var yi=Math.trunc||function(t){return t>0?Math.floor(t):Math.ceil(t)};w.prototype={clone:function(){return new w(this.x,this.y)},add:function(t){return this.clone()._add(y(t))},_add:function(t){return this.x+=t.x,this.y+=t.y,this},subtract:function(t){return this.clone()._subtract(y(t))},_subtract:function(t){return this.x-=t.x,this.y-=t.y,this},divideBy:function(t){return this.clone()._divideBy(t)},_divideBy:function(t){return this.x/=t,this.y/=t,this},multiplyBy:function(t){return this.clone()._multiplyBy(t)},_multiplyBy:function(t){return this.x*=t,this.y*=t,this},scaleBy:function(t){return new w(this.x*t.x,this.y*t.y)},unscaleBy:function(t){return new w(this.x/t.x,this.y/t.y)},round:function(){return this.clone()._round()},_round:function(){return this.x=Math.round(this.x),this.y=Math.round(this.y),this},floor:function(){return this.clone()._floor()},_floor:function(){return this.x=Math.floor(this.x),this.y=Math.floor(this.y),this},ceil:function(){return this.clone()._ceil()},_ceil:function(){return this.x=Math.ceil(this.x),this.y=Math.ceil(this.y),this},trunc:function(){return this.clone()._trunc()},_trunc:function(){return this.x=yi(this.x),this.y=yi(this.y),this},distanceTo:function(t){t=y(t);var e=t.x-this.x,i=t.y-this.y;return Math.sqrt(e*e+i*i)},equals:function(t){return t=y(t),t.x===this.x&&t.y===this.y},contains:function(t){return t=y(t),Math.abs(t.x)<=Math.abs(this.x)&&Math.abs(t.y)<=Math.abs(this.y)},toString:function(){return"Point("+et(this.x)+", "+et(this.y)+")"}};function y(t,e,i){return t instanceof w?t:W(t)?new w(t[0],t[1]):t==null?t:typeof t=="object"&&"x"in t&&"y"in t?new w(t.x,t.y):new w(t,e,i)}function B(t,e){if(t)for(var i=e?[t,e]:t,n=0,o=i.length;n<o;n++)this.extend(i[n])}B.prototype={extend:function(t){var e,i;if(!t)return this;if(t instanceof w||typeof t[0]=="number"||"x"in t)e=i=y(t);else if(t=q(t),e=t.min,i=t.max,!e||!i)return this;return!this.min&&!this.max?(this.min=e.clone(),this.max=i.clone()):(this.min.x=Math.min(e.x,this.min.x),this.max.x=Math.max(i.x,this.max.x),this.min.y=Math.min(e.y,this.min.y),this.max.y=Math.max(i.y,this.max.y)),this},getCenter:function(t){return y((this.min.x+this.max.x)/2,(this.min.y+this.max.y)/2,t)},getBottomLeft:function(){return y(this.min.x,this.max.y)},getTopRight:function(){return y(this.max.x,this.min.y)},getTopLeft:function(){return this.min},getBottomRight:function(){return this.max},getSize:function(){return this.max.subtract(this.min)},contains:function(t){var e,i;return typeof t[0]=="number"||t instanceof w?t=y(t):t=q(t),t instanceof B?(e=t.min,i=t.max):e=i=t,e.x>=this.min.x&&i.x<=this.max.x&&e.y>=this.min.y&&i.y<=this.max.y},intersects:function(t){t=q(t);var e=this.min,i=this.max,n=t.min,o=t.max,s=o.x>=e.x&&n.x<=i.x,r=o.y>=e.y&&n.y<=i.y;return s&&r},overlaps:function(t){t=q(t);var e=this.min,i=this.max,n=t.min,o=t.max,s=o.x>e.x&&n.x<i.x,r=o.y>e.y&&n.y<i.y;return s&&r},isValid:function(){return!!(this.min&&this.max)},pad:function(t){var e=this.min,i=this.max,n=Math.abs(e.x-i.x)*t,o=Math.abs(e.y-i.y)*t;return q(y(e.x-n,e.y-o),y(i.x+n,i.y+o))},equals:function(t){return t?(t=q(t),this.min.equals(t.getTopLeft())&&this.max.equals(t.getBottomRight())):!1}};function q(t,e){return!t||t instanceof B?t:new B(t,e)}function j(t,e){if(t)for(var i=e?[t,e]:t,n=0,o=i.length;n<o;n++)this.extend(i[n])}j.prototype={extend:function(t){var e=this._southWest,i=this._northEast,n,o;if(t instanceof z)n=t,o=t;else if(t instanceof j){if(n=t._southWest,o=t._northEast,!n||!o)return this}else return t?this.extend(C(t)||R(t)):this;return!e&&!i?(this._southWest=new z(n.lat,n.lng),this._northEast=new z(o.lat,o.lng)):(e.lat=Math.min(n.lat,e.lat),e.lng=Math.min(n.lng,e.lng),i.lat=Math.max(o.lat,i.lat),i.lng=Math.max(o.lng,i.lng)),this},pad:function(t){var e=this._southWest,i=this._northEast,n=Math.abs(e.lat-i.lat)*t,o=Math.abs(e.lng-i.lng)*t;return new j(new z(e.lat-n,e.lng-o),new z(i.lat+n,i.lng+o))},getCenter:function(){return new z((this._southWest.lat+this._northEast.lat)/2,(this._southWest.lng+this._northEast.lng)/2)},getSouthWest:function(){return this._southWest},getNorthEast:function(){return this._northEast},getNorthWest:function(){return new z(this.getNorth(),this.getWest())},getSouthEast:function(){return new z(this.getSouth(),this.getEast())},getWest:function(){return this._southWest.lng},getSouth:function(){return this._southWest.lat},getEast:function(){return this._northEast.lng},getNorth:function(){return this._northEast.lat},contains:function(t){typeof t[0]=="number"||t instanceof z||"lat"in t?t=C(t):t=R(t);var e=this._southWest,i=this._northEast,n,o;return t instanceof j?(n=t.getSouthWest(),o=t.getNorthEast()):n=o=t,n.lat>=e.lat&&o.lat<=i.lat&&n.lng>=e.lng&&o.lng<=i.lng},intersects:function(t){t=R(t);var e=this._southWest,i=this._northEast,n=t.getSouthWest(),o=t.getNorthEast(),s=o.lat>=e.lat&&n.lat<=i.lat,r=o.lng>=e.lng&&n.lng<=i.lng;return s&&r},overlaps:function(t){t=R(t);var e=this._southWest,i=this._northEast,n=t.getSouthWest(),o=t.getNorthEast(),s=o.lat>e.lat&&n.lat<i.lat,r=o.lng>e.lng&&n.lng<i.lng;return s&&r},toBBoxString:function(){return[this.getWest(),this.getSouth(),this.getEast(),this.getNorth()].join(",")},equals:function(t,e){return t?(t=R(t),this._southWest.equals(t.getSouthWest(),e)&&this._northEast.equals(t.getNorthEast(),e)):!1},isValid:function(){return!!(this._southWest&&this._northEast)}};function R(t,e){return t instanceof j?t:new j(t,e)}function z(t,e,i){if(isNaN(t)||isNaN(e))throw new Error("Invalid LatLng object: ("+t+", "+e+")");this.lat=+t,this.lng=+e,i!==void 0&&(this.alt=+i)}z.prototype={equals:function(t,e){if(!t)return!1;t=C(t);var i=Math.max(Math.abs(this.lat-t.lat),Math.abs(this.lng-t.lng));return i<=(e===void 0?1e-9:e)},toString:function(t){return"LatLng("+et(this.lat,t)+", "+et(this.lng,t)+")"},distanceTo:function(t){return vt.distance(this,C(t))},wrap:function(){return vt.wrapLatLng(this)},toBounds:function(t){var e=180*t/40075017,i=e/Math.cos(Math.PI/180*this.lat);return R([this.lat-e,this.lng-i],[this.lat+e,this.lng+i])},clone:function(){return new z(this.lat,this.lng,this.alt)}};function C(t,e,i){return t instanceof z?t:W(t)&&typeof t[0]!="object"?t.length===3?new z(t[0],t[1],t[2]):t.length===2?new z(t[0],t[1]):null:t==null?t:typeof t=="object"&&"lat"in t?new z(t.lat,"lng"in t?t.lng:t.lon,t.alt):e===void 0?null:new z(t,e,i)}var dt={latLngToPoint:function(t,e){var i=this.projection.project(t),n=this.scale(e);return this.transformation._transform(i,n)},pointToLatLng:function(t,e){var i=this.scale(e),n=this.transformation.untransform(t,i);return this.projection.unproject(n)},project:function(t){return this.projection.project(t)},unproject:function(t){return this.projection.unproject(t)},scale:function(t){return 256*Math.pow(2,t)},zoom:function(t){return Math.log(t/256)/Math.LN2},getProjectedBounds:function(t){if(this.infinite)return null;var e=this.projection.bounds,i=this.scale(t),n=this.transformation.transform(e.min,i),o=this.transformation.transform(e.max,i);return new B(n,o)},infinite:!1,wrapLatLng:function(t){var e=this.wrapLng?st(t.lng,this.wrapLng,!0):t.lng,i=this.wrapLat?st(t.lat,this.wrapLat,!0):t.lat,n=t.alt;return new z(i,e,n)},wrapLatLngBounds:function(t){var e=t.getCenter(),i=this.wrapLatLng(e),n=e.lat-i.lat,o=e.lng-i.lng;if(n===0&&o===0)return t;var s=t.getSouthWest(),r=t.getNorthEast(),h=new z(s.lat-n,s.lng-o),u=new z(r.lat-n,r.lng-o);return new j(h,u)}},vt=_({},dt,{wrapLng:[-180,180],R:6371e3,distance:function(t,e){var i=Math.PI/180,n=t.lat*i,o=e.lat*i,s=Math.sin((e.lat-t.lat)*i/2),r=Math.sin((e.lng-t.lng)*i/2),h=s*s+Math.cos(n)*Math.cos(o)*r*r,u=2*Math.atan2(Math.sqrt(h),Math.sqrt(1-h));return this.R*u}}),wi=6378137,Ae={R:wi,MAX_LATITUDE:85.0511287798,project:function(t){var e=Math.PI/180,i=this.MAX_LATITUDE,n=Math.max(Math.min(i,t.lat),-i),o=Math.sin(n*e);return new w(this.R*t.lng*e,this.R*Math.log((1+o)/(1-o))/2)},unproject:function(t){var e=180/Math.PI;return new z((2*Math.atan(Math.exp(t.y/this.R))-Math.PI/2)*e,t.x*e/this.R)},bounds:function(){var t=wi*Math.PI;return new B([-t,-t],[t,t])}()};function Ee(t,e,i,n){if(W(t)){this._a=t[0],this._b=t[1],this._c=t[2],this._d=t[3];return}this._a=t,this._b=e,this._c=i,this._d=n}Ee.prototype={transform:function(t,e){return this._transform(t.clone(),e)},_transform:function(t,e){return e=e||1,t.x=e*(this._a*t.x+this._b),t.y=e*(this._c*t.y+this._d),t},untransform:function(t,e){return e=e||1,new w((t.x/e-this._b)/this._a,(t.y/e-this._d)/this._c)}};function Rt(t,e,i,n){return new Ee(t,e,i,n)}var ze=_({},vt,{code:"EPSG:3857",projection:Ae,transformation:function(){var t=.5/(Math.PI*Ae.R);return Rt(t,.5,-t,.5)}()}),Gn=_({},ze,{code:"EPSG:900913"});function xi(t){return document.createElementNS("http://www.w3.org/2000/svg",t)}function Pi(t,e){var i="",n,o,s,r,h,u;for(n=0,s=t.length;n<s;n++){for(h=t[n],o=0,r=h.length;o<r;o++)u=h[o],i+=(o?"L":"M")+u.x+" "+u.y;i+=e?g.svg?"z":"x":""}return i||"M0 0"}var ke=document.documentElement.style,ne="ActiveXObject"in window,Vn=ne&&!document.addEventListener,Li="msLaunchUri"in navigator&&!("documentMode"in document),Oe=rt("webkit"),bi=rt("android"),Ti=rt("android 2")||rt("android 3"),Kn=parseInt(/WebKit\/([0-9]+)|$/.exec(navigator.userAgent)[1],10),qn=bi&&rt("Google")&&Kn<537&&!("AudioNode"in window),Ie=!!window.opera,Mi=!Li&&rt("chrome"),Ci=rt("gecko")&&!Oe&&!Ie&&!ne,jn=!Mi&&rt("safari"),Si=rt("phantom"),Ai="OTransition"in ke,Jn=navigator.platform.indexOf("Win")===0,Ei=ne&&"transition"in ke,Be="WebKitCSSMatrix"in window&&"m11"in new window.WebKitCSSMatrix&&!Ti,zi="MozPerspective"in ke,Yn=!window.L_DISABLE_3D&&(Ei||Be||zi)&&!Ai&&!Si,Dt=typeof orientation!="undefined"||rt("mobile"),Xn=Dt&&Oe,Qn=Dt&&Be,ki=!window.PointerEvent&&window.MSPointerEvent,Oi=!!(window.PointerEvent||ki),Ii="ontouchstart"in window||!!window.TouchEvent,$n=!window.L_NO_TOUCH&&(Ii||Oi),to=Dt&&Ie,eo=Dt&&Ci,io=(window.devicePixelRatio||window.screen.deviceXDPI/window.screen.logicalXDPI)>1,no=function(){var t=!1;try{var e=Object.defineProperty({},"passive",{get:function(){t=!0}});window.addEventListener("testPassiveEventSupport",I,e),window.removeEventListener("testPassiveEventSupport",I,e)}catch(i){}return t}(),oo=function(){return!!document.createElement("canvas").getContext}(),Ze=!!(document.createElementNS&&xi("svg").createSVGRect),so=!!Ze&&function(){var t=document.createElement("div");return t.innerHTML="<svg/>",(t.firstChild&&t.firstChild.namespaceURI)==="http://www.w3.org/2000/svg"}(),ro=!Ze&&function(){try{var t=document.createElement("div");t.innerHTML='<v:shape adj="1"/>';var e=t.firstChild;return e.style.behavior="url(#default#VML)",e&&typeof e.adj=="object"}catch(i){return!1}}(),ao=navigator.platform.indexOf("Mac")===0,ho=navigator.platform.indexOf("Linux")===0;function rt(t){return navigator.userAgent.toLowerCase().indexOf(t)>=0}var g={ie:ne,ielt9:Vn,edge:Li,webkit:Oe,android:bi,android23:Ti,androidStock:qn,opera:Ie,chrome:Mi,gecko:Ci,safari:jn,phantom:Si,opera12:Ai,win:Jn,ie3d:Ei,webkit3d:Be,gecko3d:zi,any3d:Yn,mobile:Dt,mobileWebkit:Xn,mobileWebkit3d:Qn,msPointer:ki,pointer:Oi,touch:$n,touchNative:Ii,mobileOpera:to,mobileGecko:eo,retina:io,passiveEvents:no,canvas:oo,svg:Ze,vml:ro,inlineSvg:so,mac:ao,linux:ho},Bi=g.msPointer?"MSPointerDown":"pointerdown",Zi=g.msPointer?"MSPointerMove":"pointermove",Ni=g.msPointer?"MSPointerUp":"pointerup",Ri=g.msPointer?"MSPointerCancel":"pointercancel",Ne={touchstart:Bi,touchmove:Zi,touchend:Ni,touchcancel:Ri},Di={touchstart:mo,touchmove:oe,touchend:oe,touchcancel:oe},St={},Hi=!1;function uo(t,e,i){return e==="touchstart"&&_o(),Di[e]?(i=Di[e].bind(this,i),t.addEventListener(Ne[e],i,!1),i):(console.warn("wrong event specified:",e),I)}function co(t,e,i){if(!Ne[e]){console.warn("wrong event specified:",e);return}t.removeEventListener(Ne[e],i,!1)}function lo(t){St[t.pointerId]=t}function fo(t){St[t.pointerId]&&(St[t.pointerId]=t)}function Fi(t){delete St[t.pointerId]}function _o(){Hi||(document.addEventListener(Bi,lo,!0),document.addEventListener(Zi,fo,!0),document.addEventListener(Ni,Fi,!0),document.addEventListener(Ri,Fi,!0),Hi=!0)}function oe(t,e){if(e.pointerType!==(e.MSPOINTER_TYPE_MOUSE||"mouse")){e.touches=[];for(var i in St)e.touches.push(St[i]);e.changedTouches=[e],t(e)}}function mo(t,e){e.MSPOINTER_TYPE_TOUCH&&e.pointerType===e.MSPOINTER_TYPE_TOUCH&&U(e),oe(t,e)}function po(t){var e={},i,n;for(n in t)i=t[n],e[n]=i&&i.bind?i.bind(t):i;return t=e,e.type="dblclick",e.detail=2,e.isTrusted=!1,e._simulated=!0,e}var go=200;function vo(t,e){t.addEventListener("dblclick",e);var i=0,n;function o(s){if(s.detail!==1){n=s.detail;return}if(!(s.pointerType==="mouse"||s.sourceCapabilities&&!s.sourceCapabilities.firesTouchEvents)){var r=Ki(s);if(!(r.some(function(u){return u instanceof HTMLLabelElement&&u.attributes.for})&&!r.some(function(u){return u instanceof HTMLInputElement||u instanceof HTMLSelectElement}))){var h=Date.now();h-i<=go?(n++,n===2&&e(po(s))):n=1,i=h}}}return t.addEventListener("click",o),{dblclick:e,simDblclick:o}}function yo(t,e){t.removeEventListener("dblclick",e.dblclick),t.removeEventListener("click",e.simDblclick)}var Re=ae(["transform","webkitTransform","OTransform","MozTransform","msTransform"]),Ht=ae(["webkitTransition","transition","OTransition","MozTransition","msTransition"]),Wi=Ht==="webkitTransition"||Ht==="OTransition"?Ht+"End":"transitionend";function Ui(t){return typeof t=="string"?document.getElementById(t):t}function Ft(t,e){var i=t.style[e]||t.currentStyle&&t.currentStyle[e];if((!i||i==="auto")&&document.defaultView){var n=document.defaultView.getComputedStyle(t,null);i=n?n[e]:null}return i==="auto"?null:i}function E(t,e,i){var n=document.createElement(t);return n.className=e||"",i&&i.appendChild(n),n}function Z(t){var e=t.parentNode;e&&e.removeChild(t)}function se(t){for(;t.firstChild;)t.removeChild(t.firstChild)}function At(t){var e=t.parentNode;e&&e.lastChild!==t&&e.appendChild(t)}function Et(t){var e=t.parentNode;e&&e.firstChild!==t&&e.insertBefore(t,e.firstChild)}function De(t,e){if(t.classList!==void 0)return t.classList.contains(e);var i=re(t);return i.length>0&&new RegExp("(^|\\s)"+e+"(\\s|$)").test(i)}function b(t,e){if(t.classList!==void 0)for(var i=ct(e),n=0,o=i.length;n<o;n++)t.classList.add(i[n]);else if(!De(t,e)){var s=re(t);He(t,(s?s+" ":"")+e)}}function N(t,e){t.classList!==void 0?t.classList.remove(e):He(t,Ct((" "+re(t)+" ").replace(" "+e+" "," ")))}function He(t,e){t.className.baseVal===void 0?t.className=e:t.className.baseVal=e}function re(t){return t.correspondingElement&&(t=t.correspondingElement),t.className.baseVal===void 0?t.className:t.className.baseVal}function Q(t,e){"opacity"in t.style?t.style.opacity=e:"filter"in t.style&&wo(t,e)}function wo(t,e){var i=!1,n="DXImageTransform.Microsoft.Alpha";try{i=t.filters.item(n)}catch(o){if(e===1)return}e=Math.round(e*100),i?(i.Enabled=e!==100,i.Opacity=e):t.style.filter+=" progid:"+n+"(opacity="+e+")"}function ae(t){for(var e=document.documentElement.style,i=0;i<t.length;i++)if(t[i]in e)return t[i];return!1}function Pt(t,e,i){var n=e||new w(0,0);t.style[Re]=(g.ie3d?"translate("+n.x+"px,"+n.y+"px)":"translate3d("+n.x+"px,"+n.y+"px,0)")+(i?" scale("+i+")":"")}function D(t,e){t._leaflet_pos=e,g.any3d?Pt(t,e):(t.style.left=e.x+"px",t.style.top=e.y+"px")}function Lt(t){return t._leaflet_pos||new w(0,0)}var Wt,Ut,Fe;if("onselectstart"in document)Wt=function(){x(window,"selectstart",U)},Ut=function(){O(window,"selectstart",U)};else{var Gt=ae(["userSelect","WebkitUserSelect","OUserSelect","MozUserSelect","msUserSelect"]);Wt=function(){if(Gt){var t=document.documentElement.style;Fe=t[Gt],t[Gt]="none"}},Ut=function(){Gt&&(document.documentElement.style[Gt]=Fe,Fe=void 0)}}function We(){x(window,"dragstart",U)}function Ue(){O(window,"dragstart",U)}var he,Ge;function Ve(t){for(;t.tabIndex===-1;)t=t.parentNode;t.style&&(ue(),he=t,Ge=t.style.outlineStyle,t.style.outlineStyle="none",x(window,"keydown",ue))}function ue(){he&&(he.style.outlineStyle=Ge,he=void 0,Ge=void 0,O(window,"keydown",ue))}function Gi(t){do t=t.parentNode;while((!t.offsetWidth||!t.offsetHeight)&&t!==document.body);return t}function Ke(t){var e=t.getBoundingClientRect();return{x:e.width/t.offsetWidth||1,y:e.height/t.offsetHeight||1,boundingClientRect:e}}var xo={__proto__:null,TRANSFORM:Re,TRANSITION:Ht,TRANSITION_END:Wi,get:Ui,getStyle:Ft,create:E,remove:Z,empty:se,toFront:At,toBack:Et,hasClass:De,addClass:b,removeClass:N,setClass:He,getClass:re,setOpacity:Q,testProp:ae,setTransform:Pt,setPosition:D,getPosition:Lt,get disableTextSelection(){return Wt},get enableTextSelection(){return Ut},disableImageDrag:We,enableImageDrag:Ue,preventOutline:Ve,restoreOutline:ue,getSizedParentNode:Gi,getScale:Ke};function x(t,e,i,n){if(e&&typeof e=="object")for(var o in e)je(t,o,e[o],i);else{e=ct(e);for(var s=0,r=e.length;s<r;s++)je(t,e[s],i,n)}return this}var at="_leaflet_events";function O(t,e,i,n){if(arguments.length===1)Vi(t),delete t[at];else if(e&&typeof e=="object")for(var o in e)Je(t,o,e[o],i);else if(e=ct(e),arguments.length===2)Vi(t,function(h){return gt(e,h)!==-1});else for(var s=0,r=e.length;s<r;s++)Je(t,e[s],i,n);return this}function Vi(t,e){for(var i in t[at]){var n=i.split(/\d/)[0];(!e||e(n))&&Je(t,n,null,null,i)}}var qe={mouseenter:"mouseover",mouseleave:"mouseout",wheel:!("onwheel"in window)&&"mousewheel"};function je(t,e,i,n){var o=e+P(i)+(n?"_"+P(n):"");if(t[at]&&t[at][o])return this;var s=function(h){return i.call(n||t,h||window.event)},r=s;!g.touchNative&&g.pointer&&e.indexOf("touch")===0?s=uo(t,e,s):g.touch&&e==="dblclick"?s=vo(t,s):"addEventListener"in t?e==="touchstart"||e==="touchmove"||e==="wheel"||e==="mousewheel"?t.addEventListener(qe[e]||e,s,g.passiveEvents?{passive:!1}:!1):e==="mouseenter"||e==="mouseleave"?(s=function(h){h=h||window.event,Xe(t,h)&&r(h)},t.addEventListener(qe[e],s,!1)):t.addEventListener(e,r,!1):t.attachEvent("on"+e,s),t[at]=t[at]||{},t[at][o]=s}function Je(t,e,i,n,o){o=o||e+P(i)+(n?"_"+P(n):"");var s=t[at]&&t[at][o];if(!s)return this;!g.touchNative&&g.pointer&&e.indexOf("touch")===0?co(t,e,s):g.touch&&e==="dblclick"?yo(t,s):"removeEventListener"in t?t.removeEventListener(qe[e]||e,s,!1):t.detachEvent("on"+e,s),t[at][o]=null}function bt(t){return t.stopPropagation?t.stopPropagation():t.originalEvent?t.originalEvent._stopped=!0:t.cancelBubble=!0,this}function Ye(t){return je(t,"wheel",bt),this}function Vt(t){return x(t,"mousedown touchstart dblclick contextmenu",bt),t._leaflet_disable_click=!0,this}function U(t){return t.preventDefault?t.preventDefault():t.returnValue=!1,this}function Tt(t){return U(t),bt(t),this}function Ki(t){if(t.composedPath)return t.composedPath();for(var e=[],i=t.target;i;)e.push(i),i=i.parentNode;return e}function qi(t,e){if(!e)return new w(t.clientX,t.clientY);var i=Ke(e),n=i.boundingClientRect;return new w((t.clientX-n.left)/i.x-e.clientLeft,(t.clientY-n.top)/i.y-e.clientTop)}var Po=g.linux&&g.chrome?window.devicePixelRatio:g.mac?window.devicePixelRatio*3:window.devicePixelRatio>0?2*window.devicePixelRatio:1;function ji(t){return g.edge?t.wheelDeltaY/2:t.deltaY&&t.deltaMode===0?-t.deltaY/Po:t.deltaY&&t.deltaMode===1?-t.deltaY*20:t.deltaY&&t.deltaMode===2?-t.deltaY*60:t.deltaX||t.deltaZ?0:t.wheelDelta?(t.wheelDeltaY||t.wheelDelta)/2:t.detail&&Math.abs(t.detail)<32765?-t.detail*20:t.detail?t.detail/-32765*60:0}function Xe(t,e){var i=e.relatedTarget;if(!i)return!0;try{for(;i&&i!==t;)i=i.parentNode}catch(n){return!1}return i!==t}var Lo={__proto__:null,on:x,off:O,stopPropagation:bt,disableScrollPropagation:Ye,disableClickPropagation:Vt,preventDefault:U,stop:Tt,getPropagationPath:Ki,getMousePosition:qi,getWheelDelta:ji,isExternalTarget:Xe,addListener:x,removeListener:O},Ji=Nt.extend({run:function(t,e,i,n){this.stop(),this._el=t,this._inProgress=!0,this._duration=i||.25,this._easeOutPower=1/Math.max(n||.5,.2),this._startPos=Lt(t),this._offset=e.subtract(this._startPos),this._startTime=+new Date,this.fire("start"),this._animate()},stop:function(){this._inProgress&&(this._step(!0),this._complete())},_animate:function(){this._animId=K(this._animate,this),this._step()},_step:function(t){var e=+new Date-this._startTime,i=this._duration*1e3;e<i?this._runFrame(this._easeOut(e/i),t):(this._runFrame(1),this._complete())},_runFrame:function(t,e){var i=this._startPos.add(this._offset.multiplyBy(t));e&&i._round(),D(this._el,i),this.fire("step")},_complete:function(){X(this._animId),this._inProgress=!1,this.fire("end")},_easeOut:function(t){return 1-Math.pow(1-t,this._easeOutPower)}}),S=Nt.extend({options:{crs:ze,center:void 0,zoom:void 0,minZoom:void 0,maxZoom:void 0,layers:[],maxBounds:void 0,renderer:void 0,zoomAnimation:!0,zoomAnimationThreshold:4,fadeAnimation:!0,markerZoomAnimation:!0,transform3DLimit:8388608,zoomSnap:1,zoomDelta:1,trackResize:!0},initialize:function(t,e){e=M(this,e),this._handlers=[],this._layers={},this._zoomBoundLayers={},this._sizeChanged=!0,this._initContainer(t),this._initLayout(),this._onResize=p(this._onResize,this),this._initEvents(),e.maxBounds&&this.setMaxBounds(e.maxBounds),e.zoom!==void 0&&(this._zoom=this._limitZoom(e.zoom)),e.center&&e.zoom!==void 0&&this.setView(C(e.center),e.zoom,{reset:!0}),this.callInitHooks(),this._zoomAnimated=Ht&&g.any3d&&!g.mobileOpera&&this.options.zoomAnimation,this._zoomAnimated&&(this._createAnimProxy(),x(this._proxy,Wi,this._catchTransitionEnd,this)),this._addLayers(this.options.layers)},setView:function(t,e,i){if(e=e===void 0?this._zoom:this._limitZoom(e),t=this._limitCenter(C(t),e,this.options.maxBounds),i=i||{},this._stop(),this._loaded&&!i.reset&&i!==!0){i.animate!==void 0&&(i.zoom=_({animate:i.animate},i.zoom),i.pan=_({animate:i.animate,duration:i.duration},i.pan));var n=this._zoom!==e?this._tryAnimatedZoom&&this._tryAnimatedZoom(t,e,i.zoom):this._tryAnimatedPan(t,i.pan);if(n)return clearTimeout(this._sizeTimer),this}return this._resetView(t,e,i.pan&&i.pan.noMoveStart),this},setZoom:function(t,e){return this._loaded?this.setView(this.getCenter(),t,{zoom:e}):(this._zoom=t,this)},zoomIn:function(t,e){return t=t||(g.any3d?this.options.zoomDelta:1),this.setZoom(this._zoom+t,e)},zoomOut:function(t,e){return t=t||(g.any3d?this.options.zoomDelta:1),this.setZoom(this._zoom-t,e)},setZoomAround:function(t,e,i){var n=this.getZoomScale(e),o=this.getSize().divideBy(2),s=t instanceof w?t:this.latLngToContainerPoint(t),r=s.subtract(o).multiplyBy(1-1/n),h=this.containerPointToLatLng(o.add(r));return this.setView(h,e,{zoom:i})},_getBoundsCenterZoom:function(t,e){e=e||{},t=t.getBounds?t.getBounds():R(t);var i=y(e.paddingTopLeft||e.padding||[0,0]),n=y(e.paddingBottomRight||e.padding||[0,0]),o=this.getBoundsZoom(t,!1,i.add(n));if(o=typeof e.maxZoom=="number"?Math.min(e.maxZoom,o):o,o===1/0)return{center:t.getCenter(),zoom:o};var s=n.subtract(i).divideBy(2),r=this.project(t.getSouthWest(),o),h=this.project(t.getNorthEast(),o),u=this.unproject(r.add(h).divideBy(2).add(s),o);return{center:u,zoom:o}},fitBounds:function(t,e){if(t=R(t),!t.isValid())throw new Error("Bounds are not valid.");var i=this._getBoundsCenterZoom(t,e);return this.setView(i.center,i.zoom,e)},fitWorld:function(t){return this.fitBounds([[-90,-180],[90,180]],t)},panTo:function(t,e){return this.setView(t,this._zoom,{pan:e})},panBy:function(t,e){if(t=y(t).round(),e=e||{},!t.x&&!t.y)return this.fire("moveend");if(e.animate!==!0&&!this.getSize().contains(t))return this._resetView(this.unproject(this.project(this.getCenter()).add(t)),this.getZoom()),this;if(this._panAnim||(this._panAnim=new Ji,this._panAnim.on({step:this._onPanTransitionStep,end:this._onPanTransitionEnd},this)),e.noMoveStart||this.fire("movestart"),e.animate!==!1){b(this._mapPane,"leaflet-pan-anim");var i=this._getMapPanePos().subtract(t).round();this._panAnim.run(this._mapPane,i,e.duration||.25,e.easeLinearity)}else this._rawPanBy(t),this.fire("move").fire("moveend");return this},flyTo:function(t,e,i){if(i=i||{},i.animate===!1||!g.any3d)return this.setView(t,e,i);this._stop();var n=this.project(this.getCenter()),o=this.project(t),s=this.getSize(),r=this._zoom;t=C(t),e=e===void 0?r:e;var h=Math.max(s.x,s.y),u=h*this.getZoomScale(r,e),c=o.distanceTo(n)||1,m=1.42,v=m*m;function T(H){var xe=H?-1:1,ls=H?u:h,ds=u*u-h*h+xe*v*v*c*c,fs=2*ls*v*c,hi=ds/fs,En=Math.sqrt(hi*hi+1)-hi,_s=En<1e-9?-18:Math.log(En);return _s}function V(H){return(Math.exp(H)-Math.exp(-H))/2}function F(H){return(Math.exp(H)+Math.exp(-H))/2}function tt(H){return V(H)/F(H)}var J=T(0);function Zt(H){return h*(F(J)/F(J+m*H))}function as(H){return h*(F(J)*tt(J+m*H)-V(J))/v}function hs(H){return 1-Math.pow(1-H,1.5)}var us=Date.now(),Sn=(T(1)-J)/m,cs=i.duration?1e3*i.duration:1e3*Sn*.8;function An(){var H=(Date.now()-us)/cs,xe=hs(H)*Sn;H<=1?(this._flyToFrame=K(An,this),this._move(this.unproject(n.add(o.subtract(n).multiplyBy(as(xe)/c)),r),this.getScaleZoom(h/Zt(xe),r),{flyTo:!0})):this._move(t,e)._moveEnd(!0)}return this._moveStart(!0,i.noMoveStart),An.call(this),this},flyToBounds:function(t,e){var i=this._getBoundsCenterZoom(t,e);return this.flyTo(i.center,i.zoom,e)},setMaxBounds:function(t){return t=R(t),this.listens("moveend",this._panInsideMaxBounds)&&this.off("moveend",this._panInsideMaxBounds),t.isValid()?(this.options.maxBounds=t,this._loaded&&this._panInsideMaxBounds(),this.on("moveend",this._panInsideMaxBounds)):(this.options.maxBounds=null,this)},setMinZoom:function(t){var e=this.options.minZoom;return this.options.minZoom=t,this._loaded&&e!==t&&(this.fire("zoomlevelschange"),this.getZoom()<this.options.minZoom)?this.setZoom(t):this},setMaxZoom:function(t){var e=this.options.maxZoom;return this.options.maxZoom=t,this._loaded&&e!==t&&(this.fire("zoomlevelschange"),this.getZoom()>this.options.maxZoom)?this.setZoom(t):this},panInsideBounds:function(t,e){this._enforcingBounds=!0;var i=this.getCenter(),n=this._limitCenter(i,this._zoom,R(t));return i.equals(n)||this.panTo(n,e),this._enforcingBounds=!1,this},panInside:function(t,e){e=e||{};var i=y(e.paddingTopLeft||e.padding||[0,0]),n=y(e.paddingBottomRight||e.padding||[0,0]),o=this.project(this.getCenter()),s=this.project(t),r=this.getPixelBounds(),h=q([r.min.add(i),r.max.subtract(n)]),u=h.getSize();if(!h.contains(s)){this._enforcingBounds=!0;var c=s.subtract(h.getCenter()),m=h.extend(s).getSize().subtract(u);o.x+=c.x<0?-m.x:m.x,o.y+=c.y<0?-m.y:m.y,this.panTo(this.unproject(o),e),this._enforcingBounds=!1}return this},invalidateSize:function(t){if(!this._loaded)return this;t=_({animate:!1,pan:!0},t===!0?{animate:!0}:t);var e=this.getSize();this._sizeChanged=!0,this._lastCenter=null;var i=this.getSize(),n=e.divideBy(2).round(),o=i.divideBy(2).round(),s=n.subtract(o);return!s.x&&!s.y?this:(t.animate&&t.pan?this.panBy(s):(t.pan&&this._rawPanBy(s),this.fire("move"),t.debounceMoveend?(clearTimeout(this._sizeTimer),this._sizeTimer=setTimeout(p(this.fire,this,"moveend"),200)):this.fire("moveend")),this.fire("resize",{oldSize:e,newSize:i}))},stop:function(){return this.setZoom(this._limitZoom(this._zoom)),this.options.zoomSnap||this.fire("viewreset"),this._stop()},locate:function(t){if(t=this._locateOptions=_({timeout:1e4,watch:!1},t),!("geolocation"in navigator))return this._handleGeolocationError({code:0,message:"Geolocation not supported."}),this;var e=p(this._handleGeolocationResponse,this),i=p(this._handleGeolocationError,this);return t.watch?this._locationWatchId=navigator.geolocation.watchPosition(e,i,t):navigator.geolocation.getCurrentPosition(e,i,t),this},stopLocate:function(){return navigator.geolocation&&navigator.geolocation.clearWatch&&navigator.geolocation.clearWatch(this._locationWatchId),this._locateOptions&&(this._locateOptions.setView=!1),this},_handleGeolocationError:function(t){if(this._container._leaflet_id){var e=t.code,i=t.message||(e===1?"permission denied":e===2?"position unavailable":"timeout");this._locateOptions.setView&&!this._loaded&&this.fitWorld(),this.fire("locationerror",{code:e,message:"Geolocation error: "+i+"."})}},_handleGeolocationResponse:function(t){if(this._container._leaflet_id){var e=t.coords.latitude,i=t.coords.longitude,n=new z(e,i),o=n.toBounds(t.coords.accuracy*2),s=this._locateOptions;if(s.setView){var r=this.getBoundsZoom(o);this.setView(n,s.maxZoom?Math.min(r,s.maxZoom):r)}var h={latlng:n,bounds:o,timestamp:t.timestamp};for(var u in t.coords)typeof t.coords[u]=="number"&&(h[u]=t.coords[u]);this.fire("locationfound",h)}},addHandler:function(t,e){if(!e)return this;var i=this[t]=new e(this);return this._handlers.push(i),this.options[t]&&i.enable(),this},remove:function(){if(this._initEvents(!0),this.options.maxBounds&&this.off("moveend",this._panInsideMaxBounds),this._containerId!==this._container._leaflet_id)throw new Error("Map container is being reused by another instance");try{delete this._container._leaflet_id,delete this._containerId}catch(e){this._container._leaflet_id=void 0,this._containerId=void 0}this._locationWatchId!==void 0&&this.stopLocate(),this._stop(),Z(this._mapPane),this._clearControlPos&&this._clearControlPos(),this._resizeRequest&&(X(this._resizeRequest),this._resizeRequest=null),this._clearHandlers(),this._loaded&&this.fire("unload");var t;for(t in this._layers)this._layers[t].remove();for(t in this._panes)Z(this._panes[t]);return this._layers=[],this._panes=[],delete this._mapPane,delete this._renderer,this},createPane:function(t,e){var i="leaflet-pane"+(t?" leaflet-"+t.replace("Pane","")+"-pane":""),n=E("div",i,e||this._mapPane);return t&&(this._panes[t]=n),n},getCenter:function(){return this._checkIfLoaded(),this._lastCenter&&!this._moved()?this._lastCenter.clone():this.layerPointToLatLng(this._getCenterLayerPoint())},getZoom:function(){return this._zoom},getBounds:function(){var t=this.getPixelBounds(),e=this.unproject(t.getBottomLeft()),i=this.unproject(t.getTopRight());return new j(e,i)},getMinZoom:function(){return this.options.minZoom===void 0?this._layersMinZoom||0:this.options.minZoom},getMaxZoom:function(){return this.options.maxZoom===void 0?this._layersMaxZoom===void 0?1/0:this._layersMaxZoom:this.options.maxZoom},getBoundsZoom:function(t,e,i){t=R(t),i=y(i||[0,0]);var n=this.getZoom()||0,o=this.getMinZoom(),s=this.getMaxZoom(),r=t.getNorthWest(),h=t.getSouthEast(),u=this.getSize().subtract(i),c=q(this.project(h,n),this.project(r,n)).getSize(),m=g.any3d?this.options.zoomSnap:1,v=u.x/c.x,T=u.y/c.y,V=e?Math.max(v,T):Math.min(v,T);return n=this.getScaleZoom(V,n),m&&(n=Math.round(n/(m/100))*(m/100),n=e?Math.ceil(n/m)*m:Math.floor(n/m)*m),Math.max(o,Math.min(s,n))},getSize:function(){return(!this._size||this._sizeChanged)&&(this._size=new w(this._container.clientWidth||0,this._container.clientHeight||0),this._sizeChanged=!1),this._size.clone()},getPixelBounds:function(t,e){var i=this._getTopLeftPoint(t,e);return new B(i,i.add(this.getSize()))},getPixelOrigin:function(){return this._checkIfLoaded(),this._pixelOrigin},getPixelWorldBounds:function(t){return this.options.crs.getProjectedBounds(t===void 0?this.getZoom():t)},getPane:function(t){return typeof t=="string"?this._panes[t]:t},getPanes:function(){return this._panes},getContainer:function(){return this._container},getZoomScale:function(t,e){var i=this.options.crs;return e=e===void 0?this._zoom:e,i.scale(t)/i.scale(e)},getScaleZoom:function(t,e){var i=this.options.crs;e=e===void 0?this._zoom:e;var n=i.zoom(t*i.scale(e));return isNaN(n)?1/0:n},project:function(t,e){return e=e===void 0?this._zoom:e,this.options.crs.latLngToPoint(C(t),e)},unproject:function(t,e){return e=e===void 0?this._zoom:e,this.options.crs.pointToLatLng(y(t),e)},layerPointToLatLng:function(t){var e=y(t).add(this.getPixelOrigin());return this.unproject(e)},latLngToLayerPoint:function(t){var e=this.project(C(t))._round();return e._subtract(this.getPixelOrigin())},wrapLatLng:function(t){return this.options.crs.wrapLatLng(C(t))},wrapLatLngBounds:function(t){return this.options.crs.wrapLatLngBounds(R(t))},distance:function(t,e){return this.options.crs.distance(C(t),C(e))},containerPointToLayerPoint:function(t){return y(t).subtract(this._getMapPanePos())},layerPointToContainerPoint:function(t){return y(t).add(this._getMapPanePos())},containerPointToLatLng:function(t){var e=this.containerPointToLayerPoint(y(t));return this.layerPointToLatLng(e)},latLngToContainerPoint:function(t){return this.layerPointToContainerPoint(this.latLngToLayerPoint(C(t)))},mouseEventToContainerPoint:function(t){return qi(t,this._container)},mouseEventToLayerPoint:function(t){return this.containerPointToLayerPoint(this.mouseEventToContainerPoint(t))},mouseEventToLatLng:function(t){return this.layerPointToLatLng(this.mouseEventToLayerPoint(t))},_initContainer:function(t){var e=this._container=Ui(t);if(e){if(e._leaflet_id)throw new Error("Map container is already initialized.")}else throw new Error("Map container not found.");x(e,"scroll",this._onScroll,this),this._containerId=P(e)},_initLayout:function(){var t=this._container;this._fadeAnimated=this.options.fadeAnimation&&g.any3d,b(t,"leaflet-container"+(g.touch?" leaflet-touch":"")+(g.retina?" leaflet-retina":"")+(g.ielt9?" leaflet-oldie":"")+(g.safari?" leaflet-safari":"")+(this._fadeAnimated?" leaflet-fade-anim":""));var e=Ft(t,"position");e!=="absolute"&&e!=="relative"&&e!=="fixed"&&e!=="sticky"&&(t.style.position="relative"),this._initPanes(),this._initControlPos&&this._initControlPos()},_initPanes:function(){var t=this._panes={};this._paneRenderers={},this._mapPane=this.createPane("mapPane",this._container),D(this._mapPane,new w(0,0)),this.createPane("tilePane"),this.createPane("overlayPane"),this.createPane("shadowPane"),this.createPane("markerPane"),this.createPane("tooltipPane"),this.createPane("popupPane"),this.options.markerZoomAnimation||(b(t.markerPane,"leaflet-zoom-hide"),b(t.shadowPane,"leaflet-zoom-hide"))},_resetView:function(t,e,i){D(this._mapPane,new w(0,0));var n=!this._loaded;this._loaded=!0,e=this._limitZoom(e),this.fire("viewprereset");var o=this._zoom!==e;this._moveStart(o,i)._move(t,e)._moveEnd(o),this.fire("viewreset"),n&&this.fire("load")},_moveStart:function(t,e){return t&&this.fire("zoomstart"),e||this.fire("movestart"),this},_move:function(t,e,i,n){e===void 0&&(e=this._zoom);var o=this._zoom!==e;return this._zoom=e,this._lastCenter=t,this._pixelOrigin=this._getNewPixelOrigin(t),n?i&&i.pinch&&this.fire("zoom",i):((o||i&&i.pinch)&&this.fire("zoom",i),this.fire("move",i)),this},_moveEnd:function(t){return t&&this.fire("zoomend"),this.fire("moveend")},_stop:function(){return X(this._flyToFrame),this._panAnim&&this._panAnim.stop(),this},_rawPanBy:function(t){D(this._mapPane,this._getMapPanePos().subtract(t))},_getZoomSpan:function(){return this.getMaxZoom()-this.getMinZoom()},_panInsideMaxBounds:function(){this._enforcingBounds||this.panInsideBounds(this.options.maxBounds)},_checkIfLoaded:function(){if(!this._loaded)throw new Error("Set map center and zoom first.")},_initEvents:function(t){this._targets={},this._targets[P(this._container)]=this;var e=t?O:x;e(this._container,"click dblclick mousedown mouseup mouseover mouseout mousemove contextmenu keypress keydown keyup",this._handleDOMEvent,this),this.options.trackResize&&e(window,"resize",this._onResize,this),g.any3d&&this.options.transform3DLimit&&(t?this.off:this.on).call(this,"moveend",this._onMoveEnd)},_onResize:function(){X(this._resizeRequest),this._resizeRequest=K(function(){this.invalidateSize({debounceMoveend:!0})},this)},_onScroll:function(){this._container.scrollTop=0,this._container.scrollLeft=0},_onMoveEnd:function(){var t=this._getMapPanePos();Math.max(Math.abs(t.x),Math.abs(t.y))>=this.options.transform3DLimit&&this._resetView(this.getCenter(),this.getZoom())},_findEventTargets:function(t,e){for(var i=[],n,o=e==="mouseout"||e==="mouseover",s=t.target||t.srcElement,r=!1;s;){if(n=this._targets[P(s)],n&&(e==="click"||e==="preclick")&&this._draggableMoved(n)){r=!0;break}if(n&&n.listens(e,!0)&&(o&&!Xe(s,t)||(i.push(n),o))||s===this._container)break;s=s.parentNode}return!i.length&&!r&&!o&&this.listens(e,!0)&&(i=[this]),i},_isClickDisabled:function(t){for(;t&&t!==this._container;){if(t._leaflet_disable_click)return!0;t=t.parentNode}},_handleDOMEvent:function(t){var e=t.target||t.srcElement;if(!(!this._loaded||e._leaflet_disable_events||t.type==="click"&&this._isClickDisabled(e))){var i=t.type;i==="mousedown"&&Ve(e),this._fireDOMEvent(t,i)}},_mouseEvents:["click","dblclick","mouseover","mouseout","contextmenu"],_fireDOMEvent:function(t,e,i){if(t.type==="click"){var n=_({},t);n.type="preclick",this._fireDOMEvent(n,n.type,i)}var o=this._findEventTargets(t,e);if(i){for(var s=[],r=0;r<i.length;r++)i[r].listens(e,!0)&&s.push(i[r]);o=s.concat(o)}if(o.length){e==="contextmenu"&&U(t);var h=o[0],u={originalEvent:t};if(t.type!=="keypress"&&t.type!=="keydown"&&t.type!=="keyup"){var c=h.getLatLng&&(!h._radius||h._radius<=10);u.containerPoint=c?this.latLngToContainerPoint(h.getLatLng()):this.mouseEventToContainerPoint(t),u.layerPoint=this.containerPointToLayerPoint(u.containerPoint),u.latlng=c?h.getLatLng():this.layerPointToLatLng(u.layerPoint)}for(r=0;r<o.length;r++)if(o[r].fire(e,u,!0),u.originalEvent._stopped||o[r].options.bubblingMouseEvents===!1&&gt(this._mouseEvents,e)!==-1)return}},_draggableMoved:function(t){return t=t.dragging&&t.dragging.enabled()?t:this,t.dragging&&t.dragging.moved()||this.boxZoom&&this.boxZoom.moved()},_clearHandlers:function(){for(var t=0,e=this._handlers.length;t<e;t++)this._handlers[t].disable()},whenReady:function(t,e){return this._loaded?t.call(e||this,{target:this}):this.on("load",t,e),this},_getMapPanePos:function(){return Lt(this._mapPane)||new w(0,0)},_moved:function(){var t=this._getMapPanePos();return t&&!t.equals([0,0])},_getTopLeftPoint:function(t,e){var i=t&&e!==void 0?this._getNewPixelOrigin(t,e):this.getPixelOrigin();return i.subtract(this._getMapPanePos())},_getNewPixelOrigin:function(t,e){var i=this.getSize()._divideBy(2);return this.project(t,e)._subtract(i)._add(this._getMapPanePos())._round()},_latLngToNewLayerPoint:function(t,e,i){var n=this._getNewPixelOrigin(i,e);return this.project(t,e)._subtract(n)},_latLngBoundsToNewLayerBounds:function(t,e,i){var n=this._getNewPixelOrigin(i,e);return q([this.project(t.getSouthWest(),e)._subtract(n),this.project(t.getNorthWest(),e)._subtract(n),this.project(t.getSouthEast(),e)._subtract(n),this.project(t.getNorthEast(),e)._subtract(n)])},_getCenterLayerPoint:function(){return this.containerPointToLayerPoint(this.getSize()._divideBy(2))},_getCenterOffset:function(t){return this.latLngToLayerPoint(t).subtract(this._getCenterLayerPoint())},_limitCenter:function(t,e,i){if(!i)return t;var n=this.project(t,e),o=this.getSize().divideBy(2),s=new B(n.subtract(o),n.add(o)),r=this._getBoundsOffset(s,i,e);return Math.abs(r.x)<=1&&Math.abs(r.y)<=1?t:this.unproject(n.add(r),e)},_limitOffset:function(t,e){if(!e)return t;var i=this.getPixelBounds(),n=new B(i.min.add(t),i.max.add(t));return t.add(this._getBoundsOffset(n,e))},_getBoundsOffset:function(t,e,i){var n=q(this.project(e.getNorthEast(),i),this.project(e.getSouthWest(),i)),o=n.min.subtract(t.min),s=n.max.subtract(t.max),r=this._rebound(o.x,-s.x),h=this._rebound(o.y,-s.y);return new w(r,h)},_rebound:function(t,e){return t+e>0?Math.round(t-e)/2:Math.max(0,Math.ceil(t))-Math.max(0,Math.floor(e))},_limitZoom:function(t){var e=this.getMinZoom(),i=this.getMaxZoom(),n=g.any3d?this.options.zoomSnap:1;return n&&(t=Math.round(t/n)*n),Math.max(e,Math.min(i,t))},_onPanTransitionStep:function(){this.fire("move")},_onPanTransitionEnd:function(){N(this._mapPane,"leaflet-pan-anim"),this.fire("moveend")},_tryAnimatedPan:function(t,e){var i=this._getCenterOffset(t)._trunc();return(e&&e.animate)!==!0&&!this.getSize().contains(i)?!1:(this.panBy(i,e),!0)},_createAnimProxy:function(){var t=this._proxy=E("div","leaflet-proxy leaflet-zoom-animated");this._panes.mapPane.appendChild(t),this.on("zoomanim",function(e){var i=Re,n=this._proxy.style[i];Pt(this._proxy,this.project(e.center,e.zoom),this.getZoomScale(e.zoom,1)),n===this._proxy.style[i]&&this._animatingZoom&&this._onZoomTransitionEnd()},this),this.on("load moveend",this._animMoveEnd,this),this._on("unload",this._destroyAnimProxy,this)},_destroyAnimProxy:function(){Z(this._proxy),this.off("load moveend",this._animMoveEnd,this),delete this._proxy},_animMoveEnd:function(){var t=this.getCenter(),e=this.getZoom();Pt(this._proxy,this.project(t,e),this.getZoomScale(e,1))},_catchTransitionEnd:function(t){this._animatingZoom&&t.propertyName.indexOf("transform")>=0&&this._onZoomTransitionEnd()},_nothingToAnimate:function(){return!this._container.getElementsByClassName("leaflet-zoom-animated").length},_tryAnimatedZoom:function(t,e,i){if(this._animatingZoom)return!0;if(i=i||{},!this._zoomAnimated||i.animate===!1||this._nothingToAnimate()||Math.abs(e-this._zoom)>this.options.zoomAnimationThreshold)return!1;var n=this.getZoomScale(e),o=this._getCenterOffset(t)._divideBy(1-1/n);return i.animate!==!0&&!this.getSize().contains(o)?!1:(K(function(){this._moveStart(!0,i.noMoveStart||!1)._animateZoom(t,e,!0)},this),!0)},_animateZoom:function(t,e,i,n){this._mapPane&&(i&&(this._animatingZoom=!0,this._animateToCenter=t,this._animateToZoom=e,b(this._mapPane,"leaflet-zoom-anim")),this.fire("zoomanim",{center:t,zoom:e,noUpdate:n}),this._tempFireZoomEvent||(this._tempFireZoomEvent=this._zoom!==this._animateToZoom),this._move(this._animateToCenter,this._animateToZoom,void 0,!0),setTimeout(p(this._onZoomTransitionEnd,this),250))},_onZoomTransitionEnd:function(){this._animatingZoom&&(this._mapPane&&N(this._mapPane,"leaflet-zoom-anim"),this._animatingZoom=!1,this._move(this._animateToCenter,this._animateToZoom,void 0,!0),this._tempFireZoomEvent&&this.fire("zoom"),delete this._tempFireZoomEvent,this.fire("move"),this._moveEnd(!0))}});function bo(t,e){return new S(t,e)}var it=lt.extend({options:{position:"topright"},initialize:function(t){M(this,t)},getPosition:function(){return this.options.position},setPosition:function(t){var e=this._map;return e&&e.removeControl(this),this.options.position=t,e&&e.addControl(this),this},getContainer:function(){return this._container},addTo:function(t){this.remove(),this._map=t;var e=this._container=this.onAdd(t),i=this.getPosition(),n=t._controlCorners[i];return b(e,"leaflet-control"),i.indexOf("bottom")!==-1?n.insertBefore(e,n.firstChild):n.appendChild(e),this._map.on("unload",this.remove,this),this},remove:function(){return this._map?(Z(this._container),this.onRemove&&this.onRemove(this._map),this._map.off("unload",this.remove,this),this._map=null,this):this},_refocusOnMap:function(t){this._map&&t&&t.screenX>0&&t.screenY>0&&this._map.getContainer().focus()}}),Kt=function(t){return new it(t)};S.include({addControl:function(t){return t.addTo(this),this},removeControl:function(t){return t.remove(),this},_initControlPos:function(){var t=this._controlCorners={},e="leaflet-",i=this._controlContainer=E("div",e+"control-container",this._container);function n(o,s){var r=e+o+" "+e+s;t[o+s]=E("div",r,i)}n("top","left"),n("top","right"),n("bottom","left"),n("bottom","right")},_clearControlPos:function(){for(var t in this._controlCorners)Z(this._controlCorners[t]);Z(this._controlContainer),delete this._controlCorners,delete this._controlContainer}});var Yi=it.extend({options:{collapsed:!0,position:"topright",autoZIndex:!0,hideSingleBase:!1,sortLayers:!1,sortFunction:function(t,e,i,n){return i<n?-1:n<i?1:0}},initialize:function(t,e,i){M(this,i),this._layerControlInputs=[],this._layers=[],this._lastZIndex=0,this._handlingClick=!1,this._preventClick=!1;for(var n in t)this._addLayer(t[n],n);for(n in e)this._addLayer(e[n],n,!0)},onAdd:function(t){this._initLayout(),this._update(),this._map=t,t.on("zoomend",this._checkDisabledLayers,this);for(var e=0;e<this._layers.length;e++)this._layers[e].layer.on("add remove",this._onLayerChange,this);return this._container},addTo:function(t){return it.prototype.addTo.call(this,t),this._expandIfNotCollapsed()},onRemove:function(){this._map.off("zoomend",this._checkDisabledLayers,this);for(var t=0;t<this._layers.length;t++)this._layers[t].layer.off("add remove",this._onLayerChange,this)},addBaseLayer:function(t,e){return this._addLayer(t,e),this._map?this._update():this},addOverlay:function(t,e){return this._addLayer(t,e,!0),this._map?this._update():this},removeLayer:function(t){t.off("add remove",this._onLayerChange,this);var e=this._getLayer(P(t));return e&&this._layers.splice(this._layers.indexOf(e),1),this._map?this._update():this},expand:function(){b(this._container,"leaflet-control-layers-expanded"),this._section.style.height=null;var t=this._map.getSize().y-(this._container.offsetTop+50);return t<this._section.clientHeight?(b(this._section,"leaflet-control-layers-scrollbar"),this._section.style.height=t+"px"):N(this._section,"leaflet-control-layers-scrollbar"),this._checkDisabledLayers(),this},collapse:function(){return N(this._container,"leaflet-control-layers-expanded"),this},_initLayout:function(){var t="leaflet-control-layers",e=this._container=E("div",t),i=this.options.collapsed;e.setAttribute("aria-haspopup",!0),Vt(e),Ye(e);var n=this._section=E("section",t+"-list");i&&(this._map.on("click",this.collapse,this),x(e,{mouseenter:this._expandSafely,mouseleave:this.collapse},this));var o=this._layersLink=E("a",t+"-toggle",e);o.href="#",o.title="Layers",o.setAttribute("role","button"),x(o,{keydown:function(s){s.keyCode===13&&this._expandSafely()},click:function(s){U(s),this._expandSafely()}},this),i||this.expand(),this._baseLayersList=E("div",t+"-base",n),this._separator=E("div",t+"-separator",n),this._overlaysList=E("div",t+"-overlays",n),e.appendChild(n)},_getLayer:function(t){for(var e=0;e<this._layers.length;e++)if(this._layers[e]&&P(this._layers[e].layer)===t)return this._layers[e]},_addLayer:function(t,e,i){this._map&&t.on("add remove",this._onLayerChange,this),this._layers.push({layer:t,name:e,overlay:i}),this.options.sortLayers&&this._layers.sort(p(function(n,o){return this.options.sortFunction(n.layer,o.layer,n.name,o.name)},this)),this.options.autoZIndex&&t.setZIndex&&(this._lastZIndex++,t.setZIndex(this._lastZIndex)),this._expandIfNotCollapsed()},_update:function(){if(!this._container)return this;se(this._baseLayersList),se(this._overlaysList),this._layerControlInputs=[];var t,e,i,n,o=0;for(i=0;i<this._layers.length;i++)n=this._layers[i],this._addItem(n),e=e||n.overlay,t=t||!n.overlay,o+=n.overlay?0:1;return this.options.hideSingleBase&&(t=t&&o>1,this._baseLayersList.style.display=t?"":"none"),this._separator.style.display=e&&t?"":"none",this},_onLayerChange:function(t){this._handlingClick||this._update();var e=this._getLayer(P(t.target)),i=e.overlay?t.type==="add"?"overlayadd":"overlayremove":t.type==="add"?"baselayerchange":null;i&&this._map.fire(i,e)},_createRadioElement:function(t,e){var i='<input type="radio" class="leaflet-control-layers-selector" name="'+t+'"'+(e?' checked="checked"':"")+"/>",n=document.createElement("div");return n.innerHTML=i,n.firstChild},_addItem:function(t){var e=document.createElement("label"),i=this._map.hasLayer(t.layer),n;t.overlay?(n=document.createElement("input"),n.type="checkbox",n.className="leaflet-control-layers-selector",n.defaultChecked=i):n=this._createRadioElement("leaflet-base-layers_"+P(this),i),this._layerControlInputs.push(n),n.layerId=P(t.layer),x(n,"click",this._onInputClick,this);var o=document.createElement("span");o.innerHTML=" "+t.name;var s=document.createElement("span");e.appendChild(s),s.appendChild(n),s.appendChild(o);var r=t.overlay?this._overlaysList:this._baseLayersList;return r.appendChild(e),this._checkDisabledLayers(),e},_onInputClick:function(){if(!this._preventClick){var t=this._layerControlInputs,e,i,n=[],o=[];this._handlingClick=!0;for(var s=t.length-1;s>=0;s--)e=t[s],i=this._getLayer(e.layerId).layer,e.checked?n.push(i):e.checked||o.push(i);for(s=0;s<o.length;s++)this._map.hasLayer(o[s])&&this._map.removeLayer(o[s]);for(s=0;s<n.length;s++)this._map.hasLayer(n[s])||this._map.addLayer(n[s]);this._handlingClick=!1,this._refocusOnMap()}},_checkDisabledLayers:function(){for(var t=this._layerControlInputs,e,i,n=this._map.getZoom(),o=t.length-1;o>=0;o--)e=t[o],i=this._getLayer(e.layerId).layer,e.disabled=i.options.minZoom!==void 0&&n<i.options.minZoom||i.options.maxZoom!==void 0&&n>i.options.maxZoom},_expandIfNotCollapsed:function(){return this._map&&!this.options.collapsed&&this.expand(),this},_expandSafely:function(){var t=this._section;this._preventClick=!0,x(t,"click",U),this.expand();var e=this;setTimeout(function(){O(t,"click",U),e._preventClick=!1})}}),To=function(t,e,i){return new Yi(t,e,i)},Qe=it.extend({options:{position:"topleft",zoomInText:'<span aria-hidden="true">+</span>',zoomInTitle:"Zoom in",zoomOutText:'<span aria-hidden="true">&#x2212;</span>',zoomOutTitle:"Zoom out"},onAdd:function(t){var e="leaflet-control-zoom",i=E("div",e+" leaflet-bar"),n=this.options;return this._zoomInButton=this._createButton(n.zoomInText,n.zoomInTitle,e+"-in",i,this._zoomIn),this._zoomOutButton=this._createButton(n.zoomOutText,n.zoomOutTitle,e+"-out",i,this._zoomOut),this._updateDisabled(),t.on("zoomend zoomlevelschange",this._updateDisabled,this),i},onRemove:function(t){t.off("zoomend zoomlevelschange",this._updateDisabled,this)},disable:function(){return this._disabled=!0,this._updateDisabled(),this},enable:function(){return this._disabled=!1,this._updateDisabled(),this},_zoomIn:function(t){!this._disabled&&this._map._zoom<this._map.getMaxZoom()&&this._map.zoomIn(this._map.options.zoomDelta*(t.shiftKey?3:1))},_zoomOut:function(t){!this._disabled&&this._map._zoom>this._map.getMinZoom()&&this._map.zoomOut(this._map.options.zoomDelta*(t.shiftKey?3:1))},_createButton:function(t,e,i,n,o){var s=E("a",i,n);return s.innerHTML=t,s.href="#",s.title=e,s.setAttribute("role","button"),s.setAttribute("aria-label",e),Vt(s),x(s,"click",Tt),x(s,"click",o,this),x(s,"click",this._refocusOnMap,this),s},_updateDisabled:function(){var t=this._map,e="leaflet-disabled";N(this._zoomInButton,e),N(this._zoomOutButton,e),this._zoomInButton.setAttribute("aria-disabled","false"),this._zoomOutButton.setAttribute("aria-disabled","false"),(this._disabled||t._zoom===t.getMinZoom())&&(b(this._zoomOutButton,e),this._zoomOutButton.setAttribute("aria-disabled","true")),(this._disabled||t._zoom===t.getMaxZoom())&&(b(this._zoomInButton,e),this._zoomInButton.setAttribute("aria-disabled","true"))}});S.mergeOptions({zoomControl:!0}),S.addInitHook(function(){this.options.zoomControl&&(this.zoomControl=new Qe,this.addControl(this.zoomControl))});var Mo=function(t){return new Qe(t)},Xi=it.extend({options:{position:"bottomleft",maxWidth:100,metric:!0,imperial:!0},onAdd:function(t){var e="leaflet-control-scale",i=E("div",e),n=this.options;return this._addScales(n,e+"-line",i),t.on(n.updateWhenIdle?"moveend":"move",this._update,this),t.whenReady(this._update,this),i},onRemove:function(t){t.off(this.options.updateWhenIdle?"moveend":"move",this._update,this)},_addScales:function(t,e,i){t.metric&&(this._mScale=E("div",e,i)),t.imperial&&(this._iScale=E("div",e,i))},_update:function(){var t=this._map,e=t.getSize().y/2,i=t.distance(t.containerPointToLatLng([0,e]),t.containerPointToLatLng([this.options.maxWidth,e]));this._updateScales(i)},_updateScales:function(t){this.options.metric&&t&&this._updateMetric(t),this.options.imperial&&t&&this._updateImperial(t)},_updateMetric:function(t){var e=this._getRoundNum(t),i=e<1e3?e+" m":e/1e3+" km";this._updateScale(this._mScale,i,e/t)},_updateImperial:function(t){var e=t*3.2808399,i,n,o;e>5280?(i=e/5280,n=this._getRoundNum(i),this._updateScale(this._iScale,n+" mi",n/i)):(o=this._getRoundNum(e),this._updateScale(this._iScale,o+" ft",o/e))},_updateScale:function(t,e,i){t.style.width=Math.round(this.options.maxWidth*i)+"px",t.innerHTML=e},_getRoundNum:function(t){var e=Math.pow(10,(Math.floor(t)+"").length-1),i=t/e;return i=i>=10?10:i>=5?5:i>=3?3:i>=2?2:1,e*i}}),Co=function(t){return new Xi(t)},So='<svg aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="12" height="8" viewBox="0 0 12 8" class="leaflet-attribution-flag"><path fill="#4C7BE1" d="M0 0h12v4H0z"/><path fill="#FFD500" d="M0 4h12v3H0z"/><path fill="#E0BC00" d="M0 7h12v1H0z"/></svg>',$e=it.extend({options:{position:"bottomright",prefix:'<a href="https://leafletjs.com" title="A JavaScript library for interactive maps">'+(g.inlineSvg?So+" ":"")+"Leaflet</a>"},initialize:function(t){M(this,t),this._attributions={}},onAdd:function(t){t.attributionControl=this,this._container=E("div","leaflet-control-attribution"),Vt(this._container);for(var e in t._layers)t._layers[e].getAttribution&&this.addAttribution(t._layers[e].getAttribution());return this._update(),t.on("layeradd",this._addAttribution,this),this._container},onRemove:function(t){t.off("layeradd",this._addAttribution,this)},_addAttribution:function(t){t.layer.getAttribution&&(this.addAttribution(t.layer.getAttribution()),t.layer.once("remove",function(){this.removeAttribution(t.layer.getAttribution())},this))},setPrefix:function(t){return this.options.prefix=t,this._update(),this},addAttribution:function(t){return t?(this._attributions[t]||(this._attributions[t]=0),this._attributions[t]++,this._update(),this):this},removeAttribution:function(t){return t?(this._attributions[t]&&(this._attributions[t]--,this._update()),this):this},_update:function(){if(this._map){var t=[];for(var e in this._attributions)this._attributions[e]&&t.push(e);var i=[];this.options.prefix&&i.push(this.options.prefix),t.length&&i.push(t.join(", ")),this._container.innerHTML=i.join(' <span aria-hidden="true">|</span> ')}}});S.mergeOptions({attributionControl:!0}),S.addInitHook(function(){this.options.attributionControl&&new $e().addTo(this)});var Ao=function(t){return new $e(t)};it.Layers=Yi,it.Zoom=Qe,it.Scale=Xi,it.Attribution=$e,Kt.layers=To,Kt.zoom=Mo,Kt.scale=Co,Kt.attribution=Ao;var ht=lt.extend({initialize:function(t){this._map=t},enable:function(){return this._enabled?this:(this._enabled=!0,this.addHooks(),this)},disable:function(){return this._enabled?(this._enabled=!1,this.removeHooks(),this):this},enabled:function(){return!!this._enabled}});ht.addTo=function(t,e){return t.addHandler(e,this),this};var Eo={Events:Y},Qi=g.touch?"touchstart mousedown":"mousedown",yt=Nt.extend({options:{clickTolerance:3},initialize:function(t,e,i,n){M(this,n),this._element=t,this._dragStartTarget=e||t,this._preventOutline=i},enable:function(){this._enabled||(x(this._dragStartTarget,Qi,this._onDown,this),this._enabled=!0)},disable:function(){this._enabled&&(yt._dragging===this&&this.finishDrag(!0),O(this._dragStartTarget,Qi,this._onDown,this),this._enabled=!1,this._moved=!1)},_onDown:function(t){if(this._enabled&&(this._moved=!1,!De(this._element,"leaflet-zoom-anim"))){if(t.touches&&t.touches.length!==1){yt._dragging===this&&this.finishDrag();return}if(!(yt._dragging||t.shiftKey||t.which!==1&&t.button!==1&&!t.touches)&&(yt._dragging=this,this._preventOutline&&Ve(this._element),We(),Wt(),!this._moving)){this.fire("down");var e=t.touches?t.touches[0]:t,i=Gi(this._element);this._startPoint=new w(e.clientX,e.clientY),this._startPos=Lt(this._element),this._parentScale=Ke(i);var n=t.type==="mousedown";x(document,n?"mousemove":"touchmove",this._onMove,this),x(document,n?"mouseup":"touchend touchcancel",this._onUp,this)}}},_onMove:function(t){if(this._enabled){if(t.touches&&t.touches.length>1){this._moved=!0;return}var e=t.touches&&t.touches.length===1?t.touches[0]:t,i=new w(e.clientX,e.clientY)._subtract(this._startPoint);!i.x&&!i.y||Math.abs(i.x)+Math.abs(i.y)<this.options.clickTolerance||(i.x/=this._parentScale.x,i.y/=this._parentScale.y,U(t),this._moved||(this.fire("dragstart"),this._moved=!0,b(document.body,"leaflet-dragging"),this._lastTarget=t.target||t.srcElement,window.SVGElementInstance&&this._lastTarget instanceof window.SVGElementInstance&&(this._lastTarget=this._lastTarget.correspondingUseElement),b(this._lastTarget,"leaflet-drag-target")),this._newPos=this._startPos.add(i),this._moving=!0,this._lastEvent=t,this._updatePosition())}},_updatePosition:function(){var t={originalEvent:this._lastEvent};this.fire("predrag",t),D(this._element,this._newPos),this.fire("drag",t)},_onUp:function(){this._enabled&&this.finishDrag()},finishDrag:function(t){N(document.body,"leaflet-dragging"),this._lastTarget&&(N(this._lastTarget,"leaflet-drag-target"),this._lastTarget=null),O(document,"mousemove touchmove",this._onMove,this),O(document,"mouseup touchend touchcancel",this._onUp,this),Ue(),Ut();var e=this._moved&&this._moving;this._moving=!1,yt._dragging=!1,e&&this.fire("dragend",{noInertia:t,distance:this._newPos.distanceTo(this._startPos)})}});function $i(t,e,i){var n,o=[1,4,2,8],s,r,h,u,c,m,v,T;for(s=0,m=t.length;s<m;s++)t[s]._code=Mt(t[s],e);for(h=0;h<4;h++){for(v=o[h],n=[],s=0,m=t.length,r=m-1;s<m;r=s++)u=t[s],c=t[r],u._code&v?c._code&v||(T=ce(c,u,v,e,i),T._code=Mt(T,e),n.push(T)):(c._code&v&&(T=ce(c,u,v,e,i),T._code=Mt(T,e),n.push(T)),n.push(u));t=n}return t}function tn(t,e){var i,n,o,s,r,h,u,c,m;if(!t||t.length===0)throw new Error("latlngs not passed");$(t)||(console.warn("latlngs are not flat! Only the first ring will be used"),t=t[0]);var v=C([0,0]),T=R(t),V=T.getNorthWest().distanceTo(T.getSouthWest())*T.getNorthEast().distanceTo(T.getNorthWest());V<1700&&(v=ti(t));var F=t.length,tt=[];for(i=0;i<F;i++){var J=C(t[i]);tt.push(e.project(C([J.lat-v.lat,J.lng-v.lng])))}for(h=u=c=0,i=0,n=F-1;i<F;n=i++)o=tt[i],s=tt[n],r=o.y*s.x-s.y*o.x,u+=(o.x+s.x)*r,c+=(o.y+s.y)*r,h+=r*3;h===0?m=tt[0]:m=[u/h,c/h];var Zt=e.unproject(y(m));return C([Zt.lat+v.lat,Zt.lng+v.lng])}function ti(t){for(var e=0,i=0,n=0,o=0;o<t.length;o++){var s=C(t[o]);e+=s.lat,i+=s.lng,n++}return C([e/n,i/n])}var zo={__proto__:null,clipPolygon:$i,polygonCenter:tn,centroid:ti};function en(t,e){if(!e||!t.length)return t.slice();var i=e*e;return t=Io(t,i),t=Oo(t,i),t}function nn(t,e,i){return Math.sqrt(qt(t,e,i,!0))}function ko(t,e,i){return qt(t,e,i)}function Oo(t,e){var i=t.length,n=typeof Uint8Array!="undefined"?Uint8Array:Array,o=new n(i);o[0]=o[i-1]=1,ei(t,o,e,0,i-1);var s,r=[];for(s=0;s<i;s++)o[s]&&r.push(t[s]);return r}function ei(t,e,i,n,o){var s=0,r,h,u;for(h=n+1;h<=o-1;h++)u=qt(t[h],t[n],t[o],!0),u>s&&(r=h,s=u);s>i&&(e[r]=1,ei(t,e,i,n,r),ei(t,e,i,r,o))}function Io(t,e){for(var i=[t[0]],n=1,o=0,s=t.length;n<s;n++)Bo(t[n],t[o])>e&&(i.push(t[n]),o=n);return o<s-1&&i.push(t[s-1]),i}var on;function sn(t,e,i,n,o){var s=n?on:Mt(t,i),r=Mt(e,i),h,u,c;for(on=r;;){if(!(s|r))return[t,e];if(s&r)return!1;h=s||r,u=ce(t,e,h,i,o),c=Mt(u,i),h===s?(t=u,s=c):(e=u,r=c)}}function ce(t,e,i,n,o){var s=e.x-t.x,r=e.y-t.y,h=n.min,u=n.max,c,m;return i&8?(c=t.x+s*(u.y-t.y)/r,m=u.y):i&4?(c=t.x+s*(h.y-t.y)/r,m=h.y):i&2?(c=u.x,m=t.y+r*(u.x-t.x)/s):i&1&&(c=h.x,m=t.y+r*(h.x-t.x)/s),new w(c,m,o)}function Mt(t,e){var i=0;return t.x<e.min.x?i|=1:t.x>e.max.x&&(i|=2),t.y<e.min.y?i|=4:t.y>e.max.y&&(i|=8),i}function Bo(t,e){var i=e.x-t.x,n=e.y-t.y;return i*i+n*n}function qt(t,e,i,n){var o=e.x,s=e.y,r=i.x-o,h=i.y-s,u=r*r+h*h,c;return u>0&&(c=((t.x-o)*r+(t.y-s)*h)/u,c>1?(o=i.x,s=i.y):c>0&&(o+=r*c,s+=h*c)),r=t.x-o,h=t.y-s,n?r*r+h*h:new w(o,s)}function $(t){return!W(t[0])||typeof t[0][0]!="object"&&typeof t[0][0]!="undefined"}function rn(t){return console.warn("Deprecated use of _flat, please use L.LineUtil.isFlat instead."),$(t)}function an(t,e){var i,n,o,s,r,h,u,c;if(!t||t.length===0)throw new Error("latlngs not passed");$(t)||(console.warn("latlngs are not flat! Only the first ring will be used"),t=t[0]);var m=C([0,0]),v=R(t),T=v.getNorthWest().distanceTo(v.getSouthWest())*v.getNorthEast().distanceTo(v.getNorthWest());T<1700&&(m=ti(t));var V=t.length,F=[];for(i=0;i<V;i++){var tt=C(t[i]);F.push(e.project(C([tt.lat-m.lat,tt.lng-m.lng])))}for(i=0,n=0;i<V-1;i++)n+=F[i].distanceTo(F[i+1])/2;if(n===0)c=F[0];else for(i=0,s=0;i<V-1;i++)if(r=F[i],h=F[i+1],o=r.distanceTo(h),s+=o,s>n){u=(s-n)/o,c=[h.x-u*(h.x-r.x),h.y-u*(h.y-r.y)];break}var J=e.unproject(y(c));return C([J.lat+m.lat,J.lng+m.lng])}var Zo={__proto__:null,simplify:en,pointToSegmentDistance:nn,closestPointOnSegment:ko,clipSegment:sn,_getEdgeIntersection:ce,_getBitCode:Mt,_sqClosestPointOnSegment:qt,isFlat:$,_flat:rn,polylineCenter:an},ii={project:function(t){return new w(t.lng,t.lat)},unproject:function(t){return new z(t.y,t.x)},bounds:new B([-180,-90],[180,90])},ni={R:6378137,R_MINOR:6356752314245179e-9,bounds:new B([-2003750834279e-5,-1549657073972e-5],[2003750834279e-5,1876465623138e-5]),project:function(t){var e=Math.PI/180,i=this.R,n=t.lat*e,o=this.R_MINOR/i,s=Math.sqrt(1-o*o),r=s*Math.sin(n),h=Math.tan(Math.PI/4-n/2)/Math.pow((1-r)/(1+r),s/2);return n=-i*Math.log(Math.max(h,1e-10)),new w(t.lng*e*i,n)},unproject:function(t){for(var e=180/Math.PI,i=this.R,n=this.R_MINOR/i,o=Math.sqrt(1-n*n),s=Math.exp(-t.y/i),r=Math.PI/2-2*Math.atan(s),h=0,u=.1,c;h<15&&Math.abs(u)>1e-7;h++)c=o*Math.sin(r),c=Math.pow((1-c)/(1+c),o/2),u=Math.PI/2-2*Math.atan(s*c)-r,r+=u;return new z(r*e,t.x*e/i)}},No={__proto__:null,LonLat:ii,Mercator:ni,SphericalMercator:Ae},Ro=_({},vt,{code:"EPSG:3395",projection:ni,transformation:function(){var t=.5/(Math.PI*ni.R);return Rt(t,.5,-t,.5)}()}),hn=_({},vt,{code:"EPSG:4326",projection:ii,transformation:Rt(1/180,1,-1/180,.5)}),Do=_({},dt,{projection:ii,transformation:Rt(1,0,-1,0),scale:function(t){return Math.pow(2,t)},zoom:function(t){return Math.log(t)/Math.LN2},distance:function(t,e){var i=e.lng-t.lng,n=e.lat-t.lat;return Math.sqrt(i*i+n*n)},infinite:!0});dt.Earth=vt,dt.EPSG3395=Ro,dt.EPSG3857=ze,dt.EPSG900913=Gn,dt.EPSG4326=hn,dt.Simple=Do;var nt=Nt.extend({options:{pane:"overlayPane",attribution:null,bubblingMouseEvents:!0},addTo:function(t){return t.addLayer(this),this},remove:function(){return this.removeFrom(this._map||this._mapToAdd)},removeFrom:function(t){return t&&t.removeLayer(this),this},getPane:function(t){return this._map.getPane(t?this.options[t]||t:this.options.pane)},addInteractiveTarget:function(t){return this._map._targets[P(t)]=this,this},removeInteractiveTarget:function(t){return delete this._map._targets[P(t)],this},getAttribution:function(){return this.options.attribution},_layerAdd:function(t){var e=t.target;if(e.hasLayer(this)){if(this._map=e,this._zoomAnimated=e._zoomAnimated,this.getEvents){var i=this.getEvents();e.on(i,this),this.once("remove",function(){e.off(i,this)},this)}this.onAdd(e),this.fire("add"),e.fire("layeradd",{layer:this})}}});S.include({addLayer:function(t){if(!t._layerAdd)throw new Error("The provided object is not a Layer.");var e=P(t);return this._layers[e]?this:(this._layers[e]=t,t._mapToAdd=this,t.beforeAdd&&t.beforeAdd(this),this.whenReady(t._layerAdd,t),this)},removeLayer:function(t){var e=P(t);return this._layers[e]?(this._loaded&&t.onRemove(this),delete this._layers[e],this._loaded&&(this.fire("layerremove",{layer:t}),t.fire("remove")),t._map=t._mapToAdd=null,this):this},hasLayer:function(t){return P(t)in this._layers},eachLayer:function(t,e){for(var i in this._layers)t.call(e,this._layers[i]);return this},_addLayers:function(t){t=t?W(t)?t:[t]:[];for(var e=0,i=t.length;e<i;e++)this.addLayer(t[e])},_addZoomLimit:function(t){(!isNaN(t.options.maxZoom)||!isNaN(t.options.minZoom))&&(this._zoomBoundLayers[P(t)]=t,this._updateZoomLevels())},_removeZoomLimit:function(t){var e=P(t);this._zoomBoundLayers[e]&&(delete this._zoomBoundLayers[e],this._updateZoomLevels())},_updateZoomLevels:function(){var t=1/0,e=-1/0,i=this._getZoomSpan();for(var n in this._zoomBoundLayers){var o=this._zoomBoundLayers[n].options;t=o.minZoom===void 0?t:Math.min(t,o.minZoom),e=o.maxZoom===void 0?e:Math.max(e,o.maxZoom)}this._layersMaxZoom=e===-1/0?void 0:e,this._layersMinZoom=t===1/0?void 0:t,i!==this._getZoomSpan()&&this.fire("zoomlevelschange"),this.options.maxZoom===void 0&&this._layersMaxZoom&&this.getZoom()>this._layersMaxZoom&&this.setZoom(this._layersMaxZoom),this.options.minZoom===void 0&&this._layersMinZoom&&this.getZoom()<this._layersMinZoom&&this.setZoom(this._layersMinZoom)}});var zt=nt.extend({initialize:function(t,e){M(this,e),this._layers={};var i,n;if(t)for(i=0,n=t.length;i<n;i++)this.addLayer(t[i])},addLayer:function(t){var e=this.getLayerId(t);return this._layers[e]=t,this._map&&this._map.addLayer(t),this},removeLayer:function(t){var e=t in this._layers?t:this.getLayerId(t);return this._map&&this._layers[e]&&this._map.removeLayer(this._layers[e]),delete this._layers[e],this},hasLayer:function(t){var e=typeof t=="number"?t:this.getLayerId(t);return e in this._layers},clearLayers:function(){return this.eachLayer(this.removeLayer,this)},invoke:function(t){var e=Array.prototype.slice.call(arguments,1),i,n;for(i in this._layers)n=this._layers[i],n[t]&&n[t].apply(n,e);return this},onAdd:function(t){this.eachLayer(t.addLayer,t)},onRemove:function(t){this.eachLayer(t.removeLayer,t)},eachLayer:function(t,e){for(var i in this._layers)t.call(e,this._layers[i]);return this},getLayer:function(t){return this._layers[t]},getLayers:function(){var t=[];return this.eachLayer(t.push,t),t},setZIndex:function(t){return this.invoke("setZIndex",t)},getLayerId:function(t){return P(t)}}),Ho=function(t,e){return new zt(t,e)},ft=zt.extend({addLayer:function(t){return this.hasLayer(t)?this:(t.addEventParent(this),zt.prototype.addLayer.call(this,t),this.fire("layeradd",{layer:t}))},removeLayer:function(t){return this.hasLayer(t)?(t in this._layers&&(t=this._layers[t]),t.removeEventParent(this),zt.prototype.removeLayer.call(this,t),this.fire("layerremove",{layer:t})):this},setStyle:function(t){return this.invoke("setStyle",t)},bringToFront:function(){return this.invoke("bringToFront")},bringToBack:function(){return this.invoke("bringToBack")},getBounds:function(){var t=new j;for(var e in this._layers){var i=this._layers[e];t.extend(i.getBounds?i.getBounds():i.getLatLng())}return t}}),Fo=function(t,e){return new ft(t,e)},kt=lt.extend({options:{popupAnchor:[0,0],tooltipAnchor:[0,0],crossOrigin:!1},initialize:function(t){M(this,t)},createIcon:function(t){return this._createIcon("icon",t)},createShadow:function(t){return this._createIcon("shadow",t)},_createIcon:function(t,e){var i=this._getIconUrl(t);if(!i){if(t==="icon")throw new Error("iconUrl not set in Icon options (see the docs).");return null}var n=this._createImg(i,e&&e.tagName==="IMG"?e:null);return this._setIconStyles(n,t),(this.options.crossOrigin||this.options.crossOrigin==="")&&(n.crossOrigin=this.options.crossOrigin===!0?"":this.options.crossOrigin),n},_setIconStyles:function(t,e){var i=this.options,n=i[e+"Size"];typeof n=="number"&&(n=[n,n]);var o=y(n),s=y(e==="shadow"&&i.shadowAnchor||i.iconAnchor||o&&o.divideBy(2,!0));t.className="leaflet-marker-"+e+" "+(i.className||""),s&&(t.style.marginLeft=-s.x+"px",t.style.marginTop=-s.y+"px"),o&&(t.style.width=o.x+"px",t.style.height=o.y+"px")},_createImg:function(t,e){return e=e||document.createElement("img"),e.src=t,e},_getIconUrl:function(t){return g.retina&&this.options[t+"RetinaUrl"]||this.options[t+"Url"]}});function Wo(t){return new kt(t)}var jt=kt.extend({options:{iconUrl:"marker-icon.png",iconRetinaUrl:"marker-icon-2x.png",shadowUrl:"marker-shadow.png",iconSize:[25,41],iconAnchor:[12,41],popupAnchor:[1,-34],tooltipAnchor:[16,-28],shadowSize:[41,41]},_getIconUrl:function(t){return typeof jt.imagePath!="string"&&(jt.imagePath=this._detectIconPath()),(this.options.imagePath||jt.imagePath)+kt.prototype._getIconUrl.call(this,t)},_stripUrl:function(t){var e=function(i,n,o){var s=n.exec(i);return s&&s[o]};return t=e(t,/^url\((['"])?(.+)\1\)$/,2),t&&e(t,/^(.*)marker-icon\.png$/,1)},_detectIconPath:function(){var t=E("div","leaflet-default-icon-path",document.body),e=Ft(t,"background-image")||Ft(t,"backgroundImage");if(document.body.removeChild(t),e=this._stripUrl(e),e)return e;var i=document.querySelector('link[href$="leaflet.css"]');return i?i.href.substring(0,i.href.length-11-1):""}}),un=ht.extend({initialize:function(t){this._marker=t},addHooks:function(){var t=this._marker._icon;this._draggable||(this._draggable=new yt(t,t,!0)),this._draggable.on({dragstart:this._onDragStart,predrag:this._onPreDrag,drag:this._onDrag,dragend:this._onDragEnd},this).enable(),b(t,"leaflet-marker-draggable")},removeHooks:function(){this._draggable.off({dragstart:this._onDragStart,predrag:this._onPreDrag,drag:this._onDrag,dragend:this._onDragEnd},this).disable(),this._marker._icon&&N(this._marker._icon,"leaflet-marker-draggable")},moved:function(){return this._draggable&&this._draggable._moved},_adjustPan:function(t){var e=this._marker,i=e._map,n=this._marker.options.autoPanSpeed,o=this._marker.options.autoPanPadding,s=Lt(e._icon),r=i.getPixelBounds(),h=i.getPixelOrigin(),u=q(r.min._subtract(h).add(o),r.max._subtract(h).subtract(o));if(!u.contains(s)){var c=y((Math.max(u.max.x,s.x)-u.max.x)/(r.max.x-u.max.x)-(Math.min(u.min.x,s.x)-u.min.x)/(r.min.x-u.min.x),(Math.max(u.max.y,s.y)-u.max.y)/(r.max.y-u.max.y)-(Math.min(u.min.y,s.y)-u.min.y)/(r.min.y-u.min.y)).multiplyBy(n);i.panBy(c,{animate:!1}),this._draggable._newPos._add(c),this._draggable._startPos._add(c),D(e._icon,this._draggable._newPos),this._onDrag(t),this._panRequest=K(this._adjustPan.bind(this,t))}},_onDragStart:function(){this._oldLatLng=this._marker.getLatLng(),this._marker.closePopup&&this._marker.closePopup(),this._marker.fire("movestart").fire("dragstart")},_onPreDrag:function(t){this._marker.options.autoPan&&(X(this._panRequest),this._panRequest=K(this._adjustPan.bind(this,t)))},_onDrag:function(t){var e=this._marker,i=e._shadow,n=Lt(e._icon),o=e._map.layerPointToLatLng(n);i&&D(i,n),e._latlng=o,t.latlng=o,t.oldLatLng=this._oldLatLng,e.fire("move",t).fire("drag",t)},_onDragEnd:function(t){X(this._panRequest),delete this._oldLatLng,this._marker.fire("moveend").fire("dragend",t)}}),le=nt.extend({options:{icon:new jt,interactive:!0,keyboard:!0,title:"",alt:"Marker",zIndexOffset:0,opacity:1,riseOnHover:!1,riseOffset:250,pane:"markerPane",shadowPane:"shadowPane",bubblingMouseEvents:!1,autoPanOnFocus:!0,draggable:!1,autoPan:!1,autoPanPadding:[50,50],autoPanSpeed:10},initialize:function(t,e){M(this,e),this._latlng=C(t)},onAdd:function(t){this._zoomAnimated=this._zoomAnimated&&t.options.markerZoomAnimation,this._zoomAnimated&&t.on("zoomanim",this._animateZoom,this),this._initIcon(),this.update()},onRemove:function(t){this.dragging&&this.dragging.enabled()&&(this.options.draggable=!0,this.dragging.removeHooks()),delete this.dragging,this._zoomAnimated&&t.off("zoomanim",this._animateZoom,this),this._removeIcon(),this._removeShadow()},getEvents:function(){return{zoom:this.update,viewreset:this.update}},getLatLng:function(){return this._latlng},setLatLng:function(t){var e=this._latlng;return this._latlng=C(t),this.update(),this.fire("move",{oldLatLng:e,latlng:this._latlng})},setZIndexOffset:function(t){return this.options.zIndexOffset=t,this.update()},getIcon:function(){return this.options.icon},setIcon:function(t){return this.options.icon=t,this._map&&(this._initIcon(),this.update()),this._popup&&this.bindPopup(this._popup,this._popup.options),this},getElement:function(){return this._icon},update:function(){if(this._icon&&this._map){var t=this._map.latLngToLayerPoint(this._latlng).round();this._setPos(t)}return this},_initIcon:function(){var t=this.options,e="leaflet-zoom-"+(this._zoomAnimated?"animated":"hide"),i=t.icon.createIcon(this._icon),n=!1;i!==this._icon&&(this._icon&&this._removeIcon(),n=!0,t.title&&(i.title=t.title),i.tagName==="IMG"&&(i.alt=t.alt||"")),b(i,e),t.keyboard&&(i.tabIndex="0",i.setAttribute("role","button")),this._icon=i,t.riseOnHover&&this.on({mouseover:this._bringToFront,mouseout:this._resetZIndex}),this.options.autoPanOnFocus&&x(i,"focus",this._panOnFocus,this);var o=t.icon.createShadow(this._shadow),s=!1;o!==this._shadow&&(this._removeShadow(),s=!0),o&&(b(o,e),o.alt=""),this._shadow=o,t.opacity<1&&this._updateOpacity(),n&&this.getPane().appendChild(this._icon),this._initInteraction(),o&&s&&this.getPane(t.shadowPane).appendChild(this._shadow)},_removeIcon:function(){this.options.riseOnHover&&this.off({mouseover:this._bringToFront,mouseout:this._resetZIndex}),this.options.autoPanOnFocus&&O(this._icon,"focus",this._panOnFocus,this),Z(this._icon),this.removeInteractiveTarget(this._icon),this._icon=null},_removeShadow:function(){this._shadow&&Z(this._shadow),this._shadow=null},_setPos:function(t){this._icon&&D(this._icon,t),this._shadow&&D(this._shadow,t),this._zIndex=t.y+this.options.zIndexOffset,this._resetZIndex()},_updateZIndex:function(t){this._icon&&(this._icon.style.zIndex=this._zIndex+t)},_animateZoom:function(t){var e=this._map._latLngToNewLayerPoint(this._latlng,t.zoom,t.center).round();this._setPos(e)},_initInteraction:function(){if(this.options.interactive&&(b(this._icon,"leaflet-interactive"),this.addInteractiveTarget(this._icon),un)){var t=this.options.draggable;this.dragging&&(t=this.dragging.enabled(),this.dragging.disable()),this.dragging=new un(this),t&&this.dragging.enable()}},setOpacity:function(t){return this.options.opacity=t,this._map&&this._updateOpacity(),this},_updateOpacity:function(){var t=this.options.opacity;this._icon&&Q(this._icon,t),this._shadow&&Q(this._shadow,t)},_bringToFront:function(){this._updateZIndex(this.options.riseOffset)},_resetZIndex:function(){this._updateZIndex(0)},_panOnFocus:function(){var t=this._map;if(t){var e=this.options.icon.options,i=e.iconSize?y(e.iconSize):y(0,0),n=e.iconAnchor?y(e.iconAnchor):y(0,0);t.panInside(this._latlng,{paddingTopLeft:n,paddingBottomRight:i.subtract(n)})}},_getPopupAnchor:function(){return this.options.icon.options.popupAnchor},_getTooltipAnchor:function(){return this.options.icon.options.tooltipAnchor}});function Uo(t,e){return new le(t,e)}var wt=nt.extend({options:{stroke:!0,color:"#3388ff",weight:3,opacity:1,lineCap:"round",lineJoin:"round",dashArray:null,dashOffset:null,fill:!1,fillColor:null,fillOpacity:.2,fillRule:"evenodd",interactive:!0,bubblingMouseEvents:!0},beforeAdd:function(t){this._renderer=t.getRenderer(this)},onAdd:function(){this._renderer._initPath(this),this._reset(),this._renderer._addPath(this)},onRemove:function(){this._renderer._removePath(this)},redraw:function(){return this._map&&this._renderer._updatePath(this),this},setStyle:function(t){return M(this,t),this._renderer&&(this._renderer._updateStyle(this),this.options.stroke&&t&&Object.prototype.hasOwnProperty.call(t,"weight")&&this._updateBounds()),this},bringToFront:function(){return this._renderer&&this._renderer._bringToFront(this),this},bringToBack:function(){return this._renderer&&this._renderer._bringToBack(this),this},getElement:function(){return this._path},_reset:function(){this._project(),this._update()},_clickTolerance:function(){return(this.options.stroke?this.options.weight/2:0)+(this._renderer.options.tolerance||0)}}),de=wt.extend({options:{fill:!0,radius:10},initialize:function(t,e){M(this,e),this._latlng=C(t),this._radius=this.options.radius},setLatLng:function(t){var e=this._latlng;return this._latlng=C(t),this.redraw(),this.fire("move",{oldLatLng:e,latlng:this._latlng})},getLatLng:function(){return this._latlng},setRadius:function(t){return this.options.radius=this._radius=t,this.redraw()},getRadius:function(){return this._radius},setStyle:function(t){var e=t&&t.radius||this._radius;return wt.prototype.setStyle.call(this,t),this.setRadius(e),this},_project:function(){this._point=this._map.latLngToLayerPoint(this._latlng),this._updateBounds()},_updateBounds:function(){var t=this._radius,e=this._radiusY||t,i=this._clickTolerance(),n=[t+i,e+i];this._pxBounds=new B(this._point.subtract(n),this._point.add(n))},_update:function(){this._map&&this._updatePath()},_updatePath:function(){this._renderer._updateCircle(this)},_empty:function(){return this._radius&&!this._renderer._bounds.intersects(this._pxBounds)},_containsPoint:function(t){return t.distanceTo(this._point)<=this._radius+this._clickTolerance()}});function Go(t,e){return new de(t,e)}var oi=de.extend({initialize:function(t,e,i){if(typeof e=="number"&&(e=_({},i,{radius:e})),M(this,e),this._latlng=C(t),isNaN(this.options.radius))throw new Error("Circle radius cannot be NaN");this._mRadius=this.options.radius},setRadius:function(t){return this._mRadius=t,this.redraw()},getRadius:function(){return this._mRadius},getBounds:function(){var t=[this._radius,this._radiusY||this._radius];return new j(this._map.layerPointToLatLng(this._point.subtract(t)),this._map.layerPointToLatLng(this._point.add(t)))},setStyle:wt.prototype.setStyle,_project:function(){var t=this._latlng.lng,e=this._latlng.lat,i=this._map,n=i.options.crs;if(n.distance===vt.distance){var o=Math.PI/180,s=this._mRadius/vt.R/o,r=i.project([e+s,t]),h=i.project([e-s,t]),u=r.add(h).divideBy(2),c=i.unproject(u).lat,m=Math.acos((Math.cos(s*o)-Math.sin(e*o)*Math.sin(c*o))/(Math.cos(e*o)*Math.cos(c*o)))/o;(isNaN(m)||m===0)&&(m=s/Math.cos(Math.PI/180*e)),this._point=u.subtract(i.getPixelOrigin()),this._radius=isNaN(m)?0:u.x-i.project([c,t-m]).x,this._radiusY=u.y-r.y}else{var v=n.unproject(n.project(this._latlng).subtract([this._mRadius,0]));this._point=i.latLngToLayerPoint(this._latlng),this._radius=this._point.x-i.latLngToLayerPoint(v).x}this._updateBounds()}});function Vo(t,e,i){return new oi(t,e,i)}var _t=wt.extend({options:{smoothFactor:1,noClip:!1},initialize:function(t,e){M(this,e),this._setLatLngs(t)},getLatLngs:function(){return this._latlngs},setLatLngs:function(t){return this._setLatLngs(t),this.redraw()},isEmpty:function(){return!this._latlngs.length},closestLayerPoint:function(t){for(var e=1/0,i=null,n=qt,o,s,r=0,h=this._parts.length;r<h;r++)for(var u=this._parts[r],c=1,m=u.length;c<m;c++){o=u[c-1],s=u[c];var v=n(t,o,s,!0);v<e&&(e=v,i=n(t,o,s))}return i&&(i.distance=Math.sqrt(e)),i},getCenter:function(){if(!this._map)throw new Error("Must add layer to map before using getCenter()");return an(this._defaultShape(),this._map.options.crs)},getBounds:function(){return this._bounds},addLatLng:function(t,e){return e=e||this._defaultShape(),t=C(t),e.push(t),this._bounds.extend(t),this.redraw()},_setLatLngs:function(t){this._bounds=new j,this._latlngs=this._convertLatLngs(t)},_defaultShape:function(){return $(this._latlngs)?this._latlngs:this._latlngs[0]},_convertLatLngs:function(t){for(var e=[],i=$(t),n=0,o=t.length;n<o;n++)i?(e[n]=C(t[n]),this._bounds.extend(e[n])):e[n]=this._convertLatLngs(t[n]);return e},_project:function(){var t=new B;this._rings=[],this._projectLatlngs(this._latlngs,this._rings,t),this._bounds.isValid()&&t.isValid()&&(this._rawPxBounds=t,this._updateBounds())},_updateBounds:function(){var t=this._clickTolerance(),e=new w(t,t);this._rawPxBounds&&(this._pxBounds=new B([this._rawPxBounds.min.subtract(e),this._rawPxBounds.max.add(e)]))},_projectLatlngs:function(t,e,i){var n=t[0]instanceof z,o=t.length,s,r;if(n){for(r=[],s=0;s<o;s++)r[s]=this._map.latLngToLayerPoint(t[s]),i.extend(r[s]);e.push(r)}else for(s=0;s<o;s++)this._projectLatlngs(t[s],e,i)},_clipPoints:function(){var t=this._renderer._bounds;if(this._parts=[],!(!this._pxBounds||!this._pxBounds.intersects(t))){if(this.options.noClip){this._parts=this._rings;return}var e=this._parts,i,n,o,s,r,h,u;for(i=0,o=0,s=this._rings.length;i<s;i++)for(u=this._rings[i],n=0,r=u.length;n<r-1;n++)h=sn(u[n],u[n+1],t,n,!0),h&&(e[o]=e[o]||[],e[o].push(h[0]),(h[1]!==u[n+1]||n===r-2)&&(e[o].push(h[1]),o++))}},_simplifyPoints:function(){for(var t=this._parts,e=this.options.smoothFactor,i=0,n=t.length;i<n;i++)t[i]=en(t[i],e)},_update:function(){this._map&&(this._clipPoints(),this._simplifyPoints(),this._updatePath())},_updatePath:function(){this._renderer._updatePoly(this)},_containsPoint:function(t,e){var i,n,o,s,r,h,u=this._clickTolerance();if(!this._pxBounds||!this._pxBounds.contains(t))return!1;for(i=0,s=this._parts.length;i<s;i++)for(h=this._parts[i],n=0,r=h.length,o=r-1;n<r;o=n++)if(!(!e&&n===0)&&nn(t,h[o],h[n])<=u)return!0;return!1}});function Ko(t,e){return new _t(t,e)}_t._flat=rn;var Ot=_t.extend({options:{fill:!0},isEmpty:function(){return!this._latlngs.length||!this._latlngs[0].length},getCenter:function(){if(!this._map)throw new Error("Must add layer to map before using getCenter()");return tn(this._defaultShape(),this._map.options.crs)},_convertLatLngs:function(t){var e=_t.prototype._convertLatLngs.call(this,t),i=e.length;return i>=2&&e[0]instanceof z&&e[0].equals(e[i-1])&&e.pop(),e},_setLatLngs:function(t){_t.prototype._setLatLngs.call(this,t),$(this._latlngs)&&(this._latlngs=[this._latlngs])},_defaultShape:function(){return $(this._latlngs[0])?this._latlngs[0]:this._latlngs[0][0]},_clipPoints:function(){var t=this._renderer._bounds,e=this.options.weight,i=new w(e,e);if(t=new B(t.min.subtract(i),t.max.add(i)),this._parts=[],!(!this._pxBounds||!this._pxBounds.intersects(t))){if(this.options.noClip){this._parts=this._rings;return}for(var n=0,o=this._rings.length,s;n<o;n++)s=$i(this._rings[n],t,!0),s.length&&this._parts.push(s)}},_updatePath:function(){this._renderer._updatePoly(this,!0)},_containsPoint:function(t){var e=!1,i,n,o,s,r,h,u,c;if(!this._pxBounds||!this._pxBounds.contains(t))return!1;for(s=0,u=this._parts.length;s<u;s++)for(i=this._parts[s],r=0,c=i.length,h=c-1;r<c;h=r++)n=i[r],o=i[h],n.y>t.y!=o.y>t.y&&t.x<(o.x-n.x)*(t.y-n.y)/(o.y-n.y)+n.x&&(e=!e);return e||_t.prototype._containsPoint.call(this,t,!0)}});function qo(t,e){return new Ot(t,e)}var mt=ft.extend({initialize:function(t,e){M(this,e),this._layers={},t&&this.addData(t)},addData:function(t){var e=W(t)?t:t.features,i,n,o;if(e){for(i=0,n=e.length;i<n;i++)o=e[i],(o.geometries||o.geometry||o.features||o.coordinates)&&this.addData(o);return this}var s=this.options;if(s.filter&&!s.filter(t))return this;var r=fe(t,s);return r?(r.feature=pe(t),r.defaultOptions=r.options,this.resetStyle(r),s.onEachFeature&&s.onEachFeature(t,r),this.addLayer(r)):this},resetStyle:function(t){return t===void 0?this.eachLayer(this.resetStyle,this):(t.options=_({},t.defaultOptions),this._setLayerStyle(t,this.options.style),this)},setStyle:function(t){return this.eachLayer(function(e){this._setLayerStyle(e,t)},this)},_setLayerStyle:function(t,e){t.setStyle&&(typeof e=="function"&&(e=e(t.feature)),t.setStyle(e))}});function fe(t,e){var i=t.type==="Feature"?t.geometry:t,n=i?i.coordinates:null,o=[],s=e&&e.pointToLayer,r=e&&e.coordsToLatLng||si,h,u,c,m;if(!n&&!i)return null;switch(i.type){case"Point":return h=r(n),cn(s,t,h,e);case"MultiPoint":for(c=0,m=n.length;c<m;c++)h=r(n[c]),o.push(cn(s,t,h,e));return new ft(o);case"LineString":case"MultiLineString":return u=_e(n,i.type==="LineString"?0:1,r),new _t(u,e);case"Polygon":case"MultiPolygon":return u=_e(n,i.type==="Polygon"?1:2,r),new Ot(u,e);case"GeometryCollection":for(c=0,m=i.geometries.length;c<m;c++){var v=fe({geometry:i.geometries[c],type:"Feature",properties:t.properties},e);v&&o.push(v)}return new ft(o);case"FeatureCollection":for(c=0,m=i.features.length;c<m;c++){var T=fe(i.features[c],e);T&&o.push(T)}return new ft(o);default:throw new Error("Invalid GeoJSON object.")}}function cn(t,e,i,n){return t?t(e,i):new le(i,n&&n.markersInheritOptions&&n)}function si(t){return new z(t[1],t[0],t[2])}function _e(t,e,i){for(var n=[],o=0,s=t.length,r;o<s;o++)r=e?_e(t[o],e-1,i):(i||si)(t[o]),n.push(r);return n}function ri(t,e){return t=C(t),t.alt!==void 0?[et(t.lng,e),et(t.lat,e),et(t.alt,e)]:[et(t.lng,e),et(t.lat,e)]}function me(t,e,i,n){for(var o=[],s=0,r=t.length;s<r;s++)o.push(e?me(t[s],$(t[s])?0:e-1,i,n):ri(t[s],n));return!e&&i&&o.length>0&&o.push(o[0].slice()),o}function It(t,e){return t.feature?_({},t.feature,{geometry:e}):pe(e)}function pe(t){return t.type==="Feature"||t.type==="FeatureCollection"?t:{type:"Feature",properties:{},geometry:t}}var ai={toGeoJSON:function(t){return It(this,{type:"Point",coordinates:ri(this.getLatLng(),t)})}};le.include(ai),oi.include(ai),de.include(ai),_t.include({toGeoJSON:function(t){var e=!$(this._latlngs),i=me(this._latlngs,e?1:0,!1,t);return It(this,{type:(e?"Multi":"")+"LineString",coordinates:i})}}),Ot.include({toGeoJSON:function(t){var e=!$(this._latlngs),i=e&&!$(this._latlngs[0]),n=me(this._latlngs,i?2:e?1:0,!0,t);return e||(n=[n]),It(this,{type:(i?"Multi":"")+"Polygon",coordinates:n})}}),zt.include({toMultiPoint:function(t){var e=[];return this.eachLayer(function(i){e.push(i.toGeoJSON(t).geometry.coordinates)}),It(this,{type:"MultiPoint",coordinates:e})},toGeoJSON:function(t){var e=this.feature&&this.feature.geometry&&this.feature.geometry.type;if(e==="MultiPoint")return this.toMultiPoint(t);var i=e==="GeometryCollection",n=[];return this.eachLayer(function(o){if(o.toGeoJSON){var s=o.toGeoJSON(t);if(i)n.push(s.geometry);else{var r=pe(s);r.type==="FeatureCollection"?n.push.apply(n,r.features):n.push(r)}}}),i?It(this,{geometries:n,type:"GeometryCollection"}):{type:"FeatureCollection",features:n}}});function ln(t,e){return new mt(t,e)}var jo=ln,ge=nt.extend({options:{opacity:1,alt:"",interactive:!1,crossOrigin:!1,errorOverlayUrl:"",zIndex:1,className:""},initialize:function(t,e,i){this._url=t,this._bounds=R(e),M(this,i)},onAdd:function(){this._image||(this._initImage(),this.options.opacity<1&&this._updateOpacity()),this.options.interactive&&(b(this._image,"leaflet-interactive"),this.addInteractiveTarget(this._image)),this.getPane().appendChild(this._image),this._reset()},onRemove:function(){Z(this._image),this.options.interactive&&this.removeInteractiveTarget(this._image)},setOpacity:function(t){return this.options.opacity=t,this._image&&this._updateOpacity(),this},setStyle:function(t){return t.opacity&&this.setOpacity(t.opacity),this},bringToFront:function(){return this._map&&At(this._image),this},bringToBack:function(){return this._map&&Et(this._image),this},setUrl:function(t){return this._url=t,this._image&&(this._image.src=t),this},setBounds:function(t){return this._bounds=R(t),this._map&&this._reset(),this},getEvents:function(){var t={zoom:this._reset,viewreset:this._reset};return this._zoomAnimated&&(t.zoomanim=this._animateZoom),t},setZIndex:function(t){return this.options.zIndex=t,this._updateZIndex(),this},getBounds:function(){return this._bounds},getElement:function(){return this._image},_initImage:function(){var t=this._url.tagName==="IMG",e=this._image=t?this._url:E("img");if(b(e,"leaflet-image-layer"),this._zoomAnimated&&b(e,"leaflet-zoom-animated"),this.options.className&&b(e,this.options.className),e.onselectstart=I,e.onmousemove=I,e.onload=p(this.fire,this,"load"),e.onerror=p(this._overlayOnError,this,"error"),(this.options.crossOrigin||this.options.crossOrigin==="")&&(e.crossOrigin=this.options.crossOrigin===!0?"":this.options.crossOrigin),this.options.zIndex&&this._updateZIndex(),t){this._url=e.src;return}e.src=this._url,e.alt=this.options.alt},_animateZoom:function(t){var e=this._map.getZoomScale(t.zoom),i=this._map._latLngBoundsToNewLayerBounds(this._bounds,t.zoom,t.center).min;Pt(this._image,i,e)},_reset:function(){var t=this._image,e=new B(this._map.latLngToLayerPoint(this._bounds.getNorthWest()),this._map.latLngToLayerPoint(this._bounds.getSouthEast())),i=e.getSize();D(t,e.min),t.style.width=i.x+"px",t.style.height=i.y+"px"},_updateOpacity:function(){Q(this._image,this.options.opacity)},_updateZIndex:function(){this._image&&this.options.zIndex!==void 0&&this.options.zIndex!==null&&(this._image.style.zIndex=this.options.zIndex)},_overlayOnError:function(){this.fire("error");var t=this.options.errorOverlayUrl;t&&this._url!==t&&(this._url=t,this._image.src=t)},getCenter:function(){return this._bounds.getCenter()}}),Jo=function(t,e,i){return new ge(t,e,i)},dn=ge.extend({options:{autoplay:!0,loop:!0,keepAspectRatio:!0,muted:!1,playsInline:!0},_initImage:function(){var t=this._url.tagName==="VIDEO",e=this._image=t?this._url:E("video");if(b(e,"leaflet-image-layer"),this._zoomAnimated&&b(e,"leaflet-zoom-animated"),this.options.className&&b(e,this.options.className),e.onselectstart=I,e.onmousemove=I,e.onloadeddata=p(this.fire,this,"load"),t){for(var i=e.getElementsByTagName("source"),n=[],o=0;o<i.length;o++)n.push(i[o].src);this._url=i.length>0?n:[e.src];return}W(this._url)||(this._url=[this._url]),!this.options.keepAspectRatio&&Object.prototype.hasOwnProperty.call(e.style,"objectFit")&&(e.style.objectFit="fill"),e.autoplay=!!this.options.autoplay,e.loop=!!this.options.loop,e.muted=!!this.options.muted,e.playsInline=!!this.options.playsInline;for(var s=0;s<this._url.length;s++){var r=E("source");r.src=this._url[s],e.appendChild(r)}}});function Yo(t,e,i){return new dn(t,e,i)}var fn=ge.extend({_initImage:function(){var t=this._image=this._url;b(t,"leaflet-image-layer"),this._zoomAnimated&&b(t,"leaflet-zoom-animated"),this.options.className&&b(t,this.options.className),t.onselectstart=I,t.onmousemove=I}});function Xo(t,e,i){return new fn(t,e,i)}var ut=nt.extend({options:{interactive:!1,offset:[0,0],className:"",pane:void 0,content:""},initialize:function(t,e){t&&(t instanceof z||W(t))?(this._latlng=C(t),M(this,e)):(M(this,t),this._source=e),this.options.content&&(this._content=this.options.content)},openOn:function(t){return t=arguments.length?t:this._source._map,t.hasLayer(this)||t.addLayer(this),this},close:function(){return this._map&&this._map.removeLayer(this),this},toggle:function(t){return this._map?this.close():(arguments.length?this._source=t:t=this._source,this._prepareOpen(),this.openOn(t._map)),this},onAdd:function(t){this._zoomAnimated=t._zoomAnimated,this._container||this._initLayout(),t._fadeAnimated&&Q(this._container,0),clearTimeout(this._removeTimeout),this.getPane().appendChild(this._container),this.update(),t._fadeAnimated&&Q(this._container,1),this.bringToFront(),this.options.interactive&&(b(this._container,"leaflet-interactive"),this.addInteractiveTarget(this._container))},onRemove:function(t){t._fadeAnimated?(Q(this._container,0),this._removeTimeout=setTimeout(p(Z,void 0,this._container),200)):Z(this._container),this.options.interactive&&(N(this._container,"leaflet-interactive"),this.removeInteractiveTarget(this._container))},getLatLng:function(){return this._latlng},setLatLng:function(t){return this._latlng=C(t),this._map&&(this._updatePosition(),this._adjustPan()),this},getContent:function(){return this._content},setContent:function(t){return this._content=t,this.update(),this},getElement:function(){return this._container},update:function(){this._map&&(this._container.style.visibility="hidden",this._updateContent(),this._updateLayout(),this._updatePosition(),this._container.style.visibility="",this._adjustPan())},getEvents:function(){var t={zoom:this._updatePosition,viewreset:this._updatePosition};return this._zoomAnimated&&(t.zoomanim=this._animateZoom),t},isOpen:function(){return!!this._map&&this._map.hasLayer(this)},bringToFront:function(){return this._map&&At(this._container),this},bringToBack:function(){return this._map&&Et(this._container),this},_prepareOpen:function(t){var e=this._source;if(!e._map)return!1;if(e instanceof ft){e=null;var i=this._source._layers;for(var n in i)if(i[n]._map){e=i[n];break}if(!e)return!1;this._source=e}if(!t)if(e.getCenter)t=e.getCenter();else if(e.getLatLng)t=e.getLatLng();else if(e.getBounds)t=e.getBounds().getCenter();else throw new Error("Unable to get source layer LatLng.");return this.setLatLng(t),this._map&&this.update(),!0},_updateContent:function(){if(this._content){var t=this._contentNode,e=typeof this._content=="function"?this._content(this._source||this):this._content;if(typeof e=="string")t.innerHTML=e;else{for(;t.hasChildNodes();)t.removeChild(t.firstChild);t.appendChild(e)}this.fire("contentupdate")}},_updatePosition:function(){if(this._map){var t=this._map.latLngToLayerPoint(this._latlng),e=y(this.options.offset),i=this._getAnchor();this._zoomAnimated?D(this._container,t.add(i)):e=e.add(t).add(i);var n=this._containerBottom=-e.y,o=this._containerLeft=-Math.round(this._containerWidth/2)+e.x;this._container.style.bottom=n+"px",this._container.style.left=o+"px"}},_getAnchor:function(){return[0,0]}});S.include({_initOverlay:function(t,e,i,n){var o=e;return o instanceof t||(o=new t(n).setContent(e)),i&&o.setLatLng(i),o}}),nt.include({_initOverlay:function(t,e,i,n){var o=i;return o instanceof t?(M(o,n),o._source=this):(o=e&&!n?e:new t(n,this),o.setContent(i)),o}});var ve=ut.extend({options:{pane:"popupPane",offset:[0,7],maxWidth:300,minWidth:50,maxHeight:null,autoPan:!0,autoPanPaddingTopLeft:null,autoPanPaddingBottomRight:null,autoPanPadding:[5,5],keepInView:!1,closeButton:!0,autoClose:!0,closeOnEscapeKey:!0,className:""},openOn:function(t){return t=arguments.length?t:this._source._map,!t.hasLayer(this)&&t._popup&&t._popup.options.autoClose&&t.removeLayer(t._popup),t._popup=this,ut.prototype.openOn.call(this,t)},onAdd:function(t){ut.prototype.onAdd.call(this,t),t.fire("popupopen",{popup:this}),this._source&&(this._source.fire("popupopen",{popup:this},!0),this._source instanceof wt||this._source.on("preclick",bt))},onRemove:function(t){ut.prototype.onRemove.call(this,t),t.fire("popupclose",{popup:this}),this._source&&(this._source.fire("popupclose",{popup:this},!0),this._source instanceof wt||this._source.off("preclick",bt))},getEvents:function(){var t=ut.prototype.getEvents.call(this);return(this.options.closeOnClick!==void 0?this.options.closeOnClick:this._map.options.closePopupOnClick)&&(t.preclick=this.close),this.options.keepInView&&(t.moveend=this._adjustPan),t},_initLayout:function(){var t="leaflet-popup",e=this._container=E("div",t+" "+(this.options.className||"")+" leaflet-zoom-animated"),i=this._wrapper=E("div",t+"-content-wrapper",e);if(this._contentNode=E("div",t+"-content",i),Vt(e),Ye(this._contentNode),x(e,"contextmenu",bt),this._tipContainer=E("div",t+"-tip-container",e),this._tip=E("div",t+"-tip",this._tipContainer),this.options.closeButton){var n=this._closeButton=E("a",t+"-close-button",e);n.setAttribute("role","button"),n.setAttribute("aria-label","Close popup"),n.href="#close",n.innerHTML='<span aria-hidden="true">&#215;</span>',x(n,"click",function(o){U(o),this.close()},this)}},_updateLayout:function(){var t=this._contentNode,e=t.style;e.width="",e.whiteSpace="nowrap";var i=t.offsetWidth;i=Math.min(i,this.options.maxWidth),i=Math.max(i,this.options.minWidth),e.width=i+1+"px",e.whiteSpace="",e.height="";var n=t.offsetHeight,o=this.options.maxHeight,s="leaflet-popup-scrolled";o&&n>o?(e.height=o+"px",b(t,s)):N(t,s),this._containerWidth=this._container.offsetWidth},_animateZoom:function(t){var e=this._map._latLngToNewLayerPoint(this._latlng,t.zoom,t.center),i=this._getAnchor();D(this._container,e.add(i))},_adjustPan:function(){if(this.options.autoPan){if(this._map._panAnim&&this._map._panAnim.stop(),this._autopanning){this._autopanning=!1;return}var t=this._map,e=parseInt(Ft(this._container,"marginBottom"),10)||0,i=this._container.offsetHeight+e,n=this._containerWidth,o=new w(this._containerLeft,-i-this._containerBottom);o._add(Lt(this._container));var s=t.layerPointToContainerPoint(o),r=y(this.options.autoPanPadding),h=y(this.options.autoPanPaddingTopLeft||r),u=y(this.options.autoPanPaddingBottomRight||r),c=t.getSize(),m=0,v=0;s.x+n+u.x>c.x&&(m=s.x+n-c.x+u.x),s.x-m-h.x<0&&(m=s.x-h.x),s.y+i+u.y>c.y&&(v=s.y+i-c.y+u.y),s.y-v-h.y<0&&(v=s.y-h.y),(m||v)&&(this.options.keepInView&&(this._autopanning=!0),t.fire("autopanstart").panBy([m,v]))}},_getAnchor:function(){return y(this._source&&this._source._getPopupAnchor?this._source._getPopupAnchor():[0,0])}}),Qo=function(t,e){return new ve(t,e)};S.mergeOptions({closePopupOnClick:!0}),S.include({openPopup:function(t,e,i){return this._initOverlay(ve,t,e,i).openOn(this),this},closePopup:function(t){return t=arguments.length?t:this._popup,t&&t.close(),this}}),nt.include({bindPopup:function(t,e){return this._popup=this._initOverlay(ve,this._popup,t,e),this._popupHandlersAdded||(this.on({click:this._openPopup,keypress:this._onKeyPress,remove:this.closePopup,move:this._movePopup}),this._popupHandlersAdded=!0),this},unbindPopup:function(){return this._popup&&(this.off({click:this._openPopup,keypress:this._onKeyPress,remove:this.closePopup,move:this._movePopup}),this._popupHandlersAdded=!1,this._popup=null),this},openPopup:function(t){return this._popup&&(this instanceof ft||(this._popup._source=this),this._popup._prepareOpen(t||this._latlng)&&this._popup.openOn(this._map)),this},closePopup:function(){return this._popup&&this._popup.close(),this},togglePopup:function(){return this._popup&&this._popup.toggle(this),this},isPopupOpen:function(){return this._popup?this._popup.isOpen():!1},setPopupContent:function(t){return this._popup&&this._popup.setContent(t),this},getPopup:function(){return this._popup},_openPopup:function(t){if(!(!this._popup||!this._map)){Tt(t);var e=t.layer||t.target;if(this._popup._source===e&&!(e instanceof wt)){this._map.hasLayer(this._popup)?this.closePopup():this.openPopup(t.latlng);return}this._popup._source=e,this.openPopup(t.latlng)}},_movePopup:function(t){this._popup.setLatLng(t.latlng)},_onKeyPress:function(t){t.originalEvent.keyCode===13&&this._openPopup(t)}});var ye=ut.extend({options:{pane:"tooltipPane",offset:[0,0],direction:"auto",permanent:!1,sticky:!1,opacity:.9},onAdd:function(t){ut.prototype.onAdd.call(this,t),this.setOpacity(this.options.opacity),t.fire("tooltipopen",{tooltip:this}),this._source&&(this.addEventParent(this._source),this._source.fire("tooltipopen",{tooltip:this},!0))},onRemove:function(t){ut.prototype.onRemove.call(this,t),t.fire("tooltipclose",{tooltip:this}),this._source&&(this.removeEventParent(this._source),this._source.fire("tooltipclose",{tooltip:this},!0))},getEvents:function(){var t=ut.prototype.getEvents.call(this);return this.options.permanent||(t.preclick=this.close),t},_initLayout:function(){var t="leaflet-tooltip",e=t+" "+(this.options.className||"")+" leaflet-zoom-"+(this._zoomAnimated?"animated":"hide");this._contentNode=this._container=E("div",e),this._container.setAttribute("role","tooltip"),this._container.setAttribute("id","leaflet-tooltip-"+P(this))},_updateLayout:function(){},_adjustPan:function(){},_setPosition:function(t){var e,i,n=this._map,o=this._container,s=n.latLngToContainerPoint(n.getCenter()),r=n.layerPointToContainerPoint(t),h=this.options.direction,u=o.offsetWidth,c=o.offsetHeight,m=y(this.options.offset),v=this._getAnchor();h==="top"?(e=u/2,i=c):h==="bottom"?(e=u/2,i=0):h==="center"?(e=u/2,i=c/2):h==="right"?(e=0,i=c/2):h==="left"?(e=u,i=c/2):r.x<s.x?(h="right",e=0,i=c/2):(h="left",e=u+(m.x+v.x)*2,i=c/2),t=t.subtract(y(e,i,!0)).add(m).add(v),N(o,"leaflet-tooltip-right"),N(o,"leaflet-tooltip-left"),N(o,"leaflet-tooltip-top"),N(o,"leaflet-tooltip-bottom"),b(o,"leaflet-tooltip-"+h),D(o,t)},_updatePosition:function(){var t=this._map.latLngToLayerPoint(this._latlng);this._setPosition(t)},setOpacity:function(t){this.options.opacity=t,this._container&&Q(this._container,t)},_animateZoom:function(t){var e=this._map._latLngToNewLayerPoint(this._latlng,t.zoom,t.center);this._setPosition(e)},_getAnchor:function(){return y(this._source&&this._source._getTooltipAnchor&&!this.options.sticky?this._source._getTooltipAnchor():[0,0])}}),$o=function(t,e){return new ye(t,e)};S.include({openTooltip:function(t,e,i){return this._initOverlay(ye,t,e,i).openOn(this),this},closeTooltip:function(t){return t.close(),this}}),nt.include({bindTooltip:function(t,e){return this._tooltip&&this.isTooltipOpen()&&this.unbindTooltip(),this._tooltip=this._initOverlay(ye,this._tooltip,t,e),this._initTooltipInteractions(),this._tooltip.options.permanent&&this._map&&this._map.hasLayer(this)&&this.openTooltip(),this},unbindTooltip:function(){return this._tooltip&&(this._initTooltipInteractions(!0),this.closeTooltip(),this._tooltip=null),this},_initTooltipInteractions:function(t){if(!(!t&&this._tooltipHandlersAdded)){var e=t?"off":"on",i={remove:this.closeTooltip,move:this._moveTooltip};this._tooltip.options.permanent?i.add=this._openTooltip:(i.mouseover=this._openTooltip,i.mouseout=this.closeTooltip,i.click=this._openTooltip,this._map?this._addFocusListeners():i.add=this._addFocusListeners),this._tooltip.options.sticky&&(i.mousemove=this._moveTooltip),this[e](i),this._tooltipHandlersAdded=!t}},openTooltip:function(t){return this._tooltip&&(this instanceof ft||(this._tooltip._source=this),this._tooltip._prepareOpen(t)&&(this._tooltip.openOn(this._map),this.getElement?this._setAriaDescribedByOnLayer(this):this.eachLayer&&this.eachLayer(this._setAriaDescribedByOnLayer,this))),this},closeTooltip:function(){if(this._tooltip)return this._tooltip.close()},toggleTooltip:function(){return this._tooltip&&this._tooltip.toggle(this),this},isTooltipOpen:function(){return this._tooltip.isOpen()},setTooltipContent:function(t){return this._tooltip&&this._tooltip.setContent(t),this},getTooltip:function(){return this._tooltip},_addFocusListeners:function(){this.getElement?this._addFocusListenersOnLayer(this):this.eachLayer&&this.eachLayer(this._addFocusListenersOnLayer,this)},_addFocusListenersOnLayer:function(t){var e=typeof t.getElement=="function"&&t.getElement();e&&(x(e,"focus",function(){this._tooltip._source=t,this.openTooltip()},this),x(e,"blur",this.closeTooltip,this))},_setAriaDescribedByOnLayer:function(t){var e=typeof t.getElement=="function"&&t.getElement();e&&e.setAttribute("aria-describedby",this._tooltip._container.id)},_openTooltip:function(t){if(!(!this._tooltip||!this._map)){if(this._map.dragging&&this._map.dragging.moving()&&!this._openOnceFlag){this._openOnceFlag=!0;var e=this;this._map.once("moveend",function(){e._openOnceFlag=!1,e._openTooltip(t)});return}this._tooltip._source=t.layer||t.target,this.openTooltip(this._tooltip.options.sticky?t.latlng:void 0)}},_moveTooltip:function(t){var e=t.latlng,i,n;this._tooltip.options.sticky&&t.originalEvent&&(i=this._map.mouseEventToContainerPoint(t.originalEvent),n=this._map.containerPointToLayerPoint(i),e=this._map.layerPointToLatLng(n)),this._tooltip.setLatLng(e)}});var _n=kt.extend({options:{iconSize:[12,12],html:!1,bgPos:null,className:"leaflet-div-icon"},createIcon:function(t){var e=t&&t.tagName==="DIV"?t:document.createElement("div"),i=this.options;if(i.html instanceof Element?(se(e),e.appendChild(i.html)):e.innerHTML=i.html!==!1?i.html:"",i.bgPos){var n=y(i.bgPos);e.style.backgroundPosition=-n.x+"px "+-n.y+"px"}return this._setIconStyles(e,"icon"),e},createShadow:function(){return null}});function ts(t){return new _n(t)}kt.Default=jt;var Jt=nt.extend({options:{tileSize:256,opacity:1,updateWhenIdle:g.mobile,updateWhenZooming:!0,updateInterval:200,zIndex:1,bounds:null,minZoom:0,maxZoom:void 0,maxNativeZoom:void 0,minNativeZoom:void 0,noWrap:!1,pane:"tilePane",className:"",keepBuffer:2},initialize:function(t){M(this,t)},onAdd:function(){this._initContainer(),this._levels={},this._tiles={},this._resetView()},beforeAdd:function(t){t._addZoomLimit(this)},onRemove:function(t){this._removeAllTiles(),Z(this._container),t._removeZoomLimit(this),this._container=null,this._tileZoom=void 0},bringToFront:function(){return this._map&&(At(this._container),this._setAutoZIndex(Math.max)),this},bringToBack:function(){return this._map&&(Et(this._container),this._setAutoZIndex(Math.min)),this},getContainer:function(){return this._container},setOpacity:function(t){return this.options.opacity=t,this._updateOpacity(),this},setZIndex:function(t){return this.options.zIndex=t,this._updateZIndex(),this},isLoading:function(){return this._loading},redraw:function(){if(this._map){this._removeAllTiles();var t=this._clampZoom(this._map.getZoom());t!==this._tileZoom&&(this._tileZoom=t,this._updateLevels()),this._update()}return this},getEvents:function(){var t={viewprereset:this._invalidateAll,viewreset:this._resetView,zoom:this._resetView,moveend:this._onMoveEnd};return this.options.updateWhenIdle||(this._onMove||(this._onMove=ot(this._onMoveEnd,this.options.updateInterval,this)),t.move=this._onMove),this._zoomAnimated&&(t.zoomanim=this._animateZoom),t},createTile:function(){return document.createElement("div")},getTileSize:function(){var t=this.options.tileSize;return t instanceof w?t:new w(t,t)},_updateZIndex:function(){this._container&&this.options.zIndex!==void 0&&this.options.zIndex!==null&&(this._container.style.zIndex=this.options.zIndex)},_setAutoZIndex:function(t){for(var e=this.getPane().children,i=-t(-1/0,1/0),n=0,o=e.length,s;n<o;n++)s=e[n].style.zIndex,e[n]!==this._container&&s&&(i=t(i,+s));isFinite(i)&&(this.options.zIndex=i+t(-1,1),this._updateZIndex())},_updateOpacity:function(){if(this._map&&!g.ielt9){Q(this._container,this.options.opacity);var t=+new Date,e=!1,i=!1;for(var n in this._tiles){var o=this._tiles[n];if(!(!o.current||!o.loaded)){var s=Math.min(1,(t-o.loaded)/200);Q(o.el,s),s<1?e=!0:(o.active?i=!0:this._onOpaqueTile(o),o.active=!0)}}i&&!this._noPrune&&this._pruneTiles(),e&&(X(this._fadeFrame),this._fadeFrame=K(this._updateOpacity,this))}},_onOpaqueTile:I,_initContainer:function(){this._container||(this._container=E("div","leaflet-layer "+(this.options.className||"")),this._updateZIndex(),this.options.opacity<1&&this._updateOpacity(),this.getPane().appendChild(this._container))},_updateLevels:function(){var t=this._tileZoom,e=this.options.maxZoom;if(t!==void 0){for(var i in this._levels)i=Number(i),this._levels[i].el.children.length||i===t?(this._levels[i].el.style.zIndex=e-Math.abs(t-i),this._onUpdateLevel(i)):(Z(this._levels[i].el),this._removeTilesAtZoom(i),this._onRemoveLevel(i),delete this._levels[i]);var n=this._levels[t],o=this._map;return n||(n=this._levels[t]={},n.el=E("div","leaflet-tile-container leaflet-zoom-animated",this._container),n.el.style.zIndex=e,n.origin=o.project(o.unproject(o.getPixelOrigin()),t).round(),n.zoom=t,this._setZoomTransform(n,o.getCenter(),o.getZoom()),I(n.el.offsetWidth),this._onCreateLevel(n)),this._level=n,n}},_onUpdateLevel:I,_onRemoveLevel:I,_onCreateLevel:I,_pruneTiles:function(){if(this._map){var t,e,i=this._map.getZoom();if(i>this.options.maxZoom||i<this.options.minZoom){this._removeAllTiles();return}for(t in this._tiles)e=this._tiles[t],e.retain=e.current;for(t in this._tiles)if(e=this._tiles[t],e.current&&!e.active){var n=e.coords;this._retainParent(n.x,n.y,n.z,n.z-5)||this._retainChildren(n.x,n.y,n.z,n.z+2)}for(t in this._tiles)this._tiles[t].retain||this._removeTile(t)}},_removeTilesAtZoom:function(t){for(var e in this._tiles)this._tiles[e].coords.z===t&&this._removeTile(e)},_removeAllTiles:function(){for(var t in this._tiles)this._removeTile(t)},_invalidateAll:function(){for(var t in this._levels)Z(this._levels[t].el),this._onRemoveLevel(Number(t)),delete this._levels[t];this._removeAllTiles(),this._tileZoom=void 0},_retainParent:function(t,e,i,n){var o=Math.floor(t/2),s=Math.floor(e/2),r=i-1,h=new w(+o,+s);h.z=+r;var u=this._tileCoordsToKey(h),c=this._tiles[u];return c&&c.active?(c.retain=!0,!0):(c&&c.loaded&&(c.retain=!0),r>n?this._retainParent(o,s,r,n):!1)},_retainChildren:function(t,e,i,n){for(var o=2*t;o<2*t+2;o++)for(var s=2*e;s<2*e+2;s++){var r=new w(o,s);r.z=i+1;var h=this._tileCoordsToKey(r),u=this._tiles[h];if(u&&u.active){u.retain=!0;continue}else u&&u.loaded&&(u.retain=!0);i+1<n&&this._retainChildren(o,s,i+1,n)}},_resetView:function(t){var e=t&&(t.pinch||t.flyTo);this._setView(this._map.getCenter(),this._map.getZoom(),e,e)},_animateZoom:function(t){this._setView(t.center,t.zoom,!0,t.noUpdate)},_clampZoom:function(t){var e=this.options;return e.minNativeZoom!==void 0&&t<e.minNativeZoom?e.minNativeZoom:e.maxNativeZoom!==void 0&&e.maxNativeZoom<t?e.maxNativeZoom:t},_setView:function(t,e,i,n){var o=Math.round(e);this.options.maxZoom!==void 0&&o>this.options.maxZoom||this.options.minZoom!==void 0&&o<this.options.minZoom?o=void 0:o=this._clampZoom(o);var s=this.options.updateWhenZooming&&o!==this._tileZoom;(!n||s)&&(this._tileZoom=o,this._abortLoading&&this._abortLoading(),this._updateLevels(),this._resetGrid(),o!==void 0&&this._update(t),i||this._pruneTiles(),this._noPrune=!!i),this._setZoomTransforms(t,e)},_setZoomTransforms:function(t,e){for(var i in this._levels)this._setZoomTransform(this._levels[i],t,e)},_setZoomTransform:function(t,e,i){var n=this._map.getZoomScale(i,t.zoom),o=t.origin.multiplyBy(n).subtract(this._map._getNewPixelOrigin(e,i)).round();g.any3d?Pt(t.el,o,n):D(t.el,o)},_resetGrid:function(){var t=this._map,e=t.options.crs,i=this._tileSize=this.getTileSize(),n=this._tileZoom,o=this._map.getPixelWorldBounds(this._tileZoom);o&&(this._globalTileRange=this._pxBoundsToTileRange(o)),this._wrapX=e.wrapLng&&!this.options.noWrap&&[Math.floor(t.project([0,e.wrapLng[0]],n).x/i.x),Math.ceil(t.project([0,e.wrapLng[1]],n).x/i.y)],this._wrapY=e.wrapLat&&!this.options.noWrap&&[Math.floor(t.project([e.wrapLat[0],0],n).y/i.x),Math.ceil(t.project([e.wrapLat[1],0],n).y/i.y)]},_onMoveEnd:function(){!this._map||this._map._animatingZoom||this._update()},_getTiledPixelBounds:function(t){var e=this._map,i=e._animatingZoom?Math.max(e._animateToZoom,e.getZoom()):e.getZoom(),n=e.getZoomScale(i,this._tileZoom),o=e.project(t,this._tileZoom).floor(),s=e.getSize().divideBy(n*2);return new B(o.subtract(s),o.add(s))},_update:function(t){var e=this._map;if(e){var i=this._clampZoom(e.getZoom());if(t===void 0&&(t=e.getCenter()),this._tileZoom!==void 0){var n=this._getTiledPixelBounds(t),o=this._pxBoundsToTileRange(n),s=o.getCenter(),r=[],h=this.options.keepBuffer,u=new B(o.getBottomLeft().subtract([h,-h]),o.getTopRight().add([h,-h]));if(!(isFinite(o.min.x)&&isFinite(o.min.y)&&isFinite(o.max.x)&&isFinite(o.max.y)))throw new Error("Attempted to load an infinite number of tiles");for(var c in this._tiles){var m=this._tiles[c].coords;(m.z!==this._tileZoom||!u.contains(new w(m.x,m.y)))&&(this._tiles[c].current=!1)}if(Math.abs(i-this._tileZoom)>1){this._setView(t,i);return}for(var v=o.min.y;v<=o.max.y;v++)for(var T=o.min.x;T<=o.max.x;T++){var V=new w(T,v);if(V.z=this._tileZoom,!!this._isValidTile(V)){var F=this._tiles[this._tileCoordsToKey(V)];F?F.current=!0:r.push(V)}}if(r.sort(function(J,Zt){return J.distanceTo(s)-Zt.distanceTo(s)}),r.length!==0){this._loading||(this._loading=!0,this.fire("loading"));var tt=document.createDocumentFragment();for(T=0;T<r.length;T++)this._addTile(r[T],tt);this._level.el.appendChild(tt)}}}},_isValidTile:function(t){var e=this._map.options.crs;if(!e.infinite){var i=this._globalTileRange;if(!e.wrapLng&&(t.x<i.min.x||t.x>i.max.x)||!e.wrapLat&&(t.y<i.min.y||t.y>i.max.y))return!1}if(!this.options.bounds)return!0;var n=this._tileCoordsToBounds(t);return R(this.options.bounds).overlaps(n)},_keyToBounds:function(t){return this._tileCoordsToBounds(this._keyToTileCoords(t))},_tileCoordsToNwSe:function(t){var e=this._map,i=this.getTileSize(),n=t.scaleBy(i),o=n.add(i),s=e.unproject(n,t.z),r=e.unproject(o,t.z);return[s,r]},_tileCoordsToBounds:function(t){var e=this._tileCoordsToNwSe(t),i=new j(e[0],e[1]);return this.options.noWrap||(i=this._map.wrapLatLngBounds(i)),i},_tileCoordsToKey:function(t){return t.x+":"+t.y+":"+t.z},_keyToTileCoords:function(t){var e=t.split(":"),i=new w(+e[0],+e[1]);return i.z=+e[2],i},_removeTile:function(t){var e=this._tiles[t];e&&(Z(e.el),delete this._tiles[t],this.fire("tileunload",{tile:e.el,coords:this._keyToTileCoords(t)}))},_initTile:function(t){b(t,"leaflet-tile");var e=this.getTileSize();t.style.width=e.x+"px",t.style.height=e.y+"px",t.onselectstart=I,t.onmousemove=I,g.ielt9&&this.options.opacity<1&&Q(t,this.options.opacity)},_addTile:function(t,e){var i=this._getTilePos(t),n=this._tileCoordsToKey(t),o=this.createTile(this._wrapCoords(t),p(this._tileReady,this,t));this._initTile(o),this.createTile.length<2&&K(p(this._tileReady,this,t,null,o)),D(o,i),this._tiles[n]={el:o,coords:t,current:!0},e.appendChild(o),this.fire("tileloadstart",{tile:o,coords:t})},_tileReady:function(t,e,i){e&&this.fire("tileerror",{error:e,tile:i,coords:t});var n=this._tileCoordsToKey(t);i=this._tiles[n],i&&(i.loaded=+new Date,this._map._fadeAnimated?(Q(i.el,0),X(this._fadeFrame),this._fadeFrame=K(this._updateOpacity,this)):(i.active=!0,this._pruneTiles()),e||(b(i.el,"leaflet-tile-loaded"),this.fire("tileload",{tile:i.el,coords:t})),this._noTilesToLoad()&&(this._loading=!1,this.fire("load"),g.ielt9||!this._map._fadeAnimated?K(this._pruneTiles,this):setTimeout(p(this._pruneTiles,this),250)))},_getTilePos:function(t){return t.scaleBy(this.getTileSize()).subtract(this._level.origin)},_wrapCoords:function(t){var e=new w(this._wrapX?st(t.x,this._wrapX):t.x,this._wrapY?st(t.y,this._wrapY):t.y);return e.z=t.z,e},_pxBoundsToTileRange:function(t){var e=this.getTileSize();return new B(t.min.unscaleBy(e).floor(),t.max.unscaleBy(e).ceil().subtract([1,1]))},_noTilesToLoad:function(){for(var t in this._tiles)if(!this._tiles[t].loaded)return!1;return!0}});function es(t){return new Jt(t)}var Bt=Jt.extend({options:{minZoom:0,maxZoom:18,subdomains:"abc",errorTileUrl:"",zoomOffset:0,tms:!1,zoomReverse:!1,detectRetina:!1,crossOrigin:!1,referrerPolicy:!1},initialize:function(t,e){this._url=t,e=M(this,e),e.detectRetina&&g.retina&&e.maxZoom>0?(e.tileSize=Math.floor(e.tileSize/2),e.zoomReverse?(e.zoomOffset--,e.minZoom=Math.min(e.maxZoom,e.minZoom+1)):(e.zoomOffset++,e.maxZoom=Math.max(e.minZoom,e.maxZoom-1)),e.minZoom=Math.max(0,e.minZoom)):e.zoomReverse?e.minZoom=Math.min(e.maxZoom,e.minZoom):e.maxZoom=Math.max(e.minZoom,e.maxZoom),typeof e.subdomains=="string"&&(e.subdomains=e.subdomains.split("")),this.on("tileunload",this._onTileRemove)},setUrl:function(t,e){return this._url===t&&e===void 0&&(e=!0),this._url=t,e||this.redraw(),this},createTile:function(t,e){var i=document.createElement("img");return x(i,"load",p(this._tileOnLoad,this,e,i)),x(i,"error",p(this._tileOnError,this,e,i)),(this.options.crossOrigin||this.options.crossOrigin==="")&&(i.crossOrigin=this.options.crossOrigin===!0?"":this.options.crossOrigin),typeof this.options.referrerPolicy=="string"&&(i.referrerPolicy=this.options.referrerPolicy),i.alt="",i.src=this.getTileUrl(t),i},getTileUrl:function(t){var e={r:g.retina?"@2x":"",s:this._getSubdomain(t),x:t.x,y:t.y,z:this._getZoomForUrl()};if(this._map&&!this._map.options.crs.infinite){var i=this._globalTileRange.max.y-t.y;this.options.tms&&(e.y=i),e["-y"]=i}return ee(this._url,_(e,this.options))},_tileOnLoad:function(t,e){g.ielt9?setTimeout(p(t,this,null,e),0):t(null,e)},_tileOnError:function(t,e,i){var n=this.options.errorTileUrl;n&&e.getAttribute("src")!==n&&(e.src=n),t(i,e)},_onTileRemove:function(t){t.tile.onload=null},_getZoomForUrl:function(){var t=this._tileZoom,e=this.options.maxZoom,i=this.options.zoomReverse,n=this.options.zoomOffset;return i&&(t=e-t),t+n},_getSubdomain:function(t){var e=Math.abs(t.x+t.y)%this.options.subdomains.length;return this.options.subdomains[e]},_abortLoading:function(){var t,e;for(t in this._tiles)if(this._tiles[t].coords.z!==this._tileZoom&&(e=this._tiles[t].el,e.onload=I,e.onerror=I,!e.complete)){e.src=ie;var i=this._tiles[t].coords;Z(e),delete this._tiles[t],this.fire("tileabort",{tile:e,coords:i})}},_removeTile:function(t){var e=this._tiles[t];if(e)return e.el.setAttribute("src",ie),Jt.prototype._removeTile.call(this,t)},_tileReady:function(t,e,i){if(!(!this._map||i&&i.getAttribute("src")===ie))return Jt.prototype._tileReady.call(this,t,e,i)}});function mn(t,e){return new Bt(t,e)}var pn=Bt.extend({defaultWmsParams:{service:"WMS",request:"GetMap",layers:"",styles:"",format:"image/jpeg",transparent:!1,version:"1.1.1"},options:{crs:null,uppercase:!1},initialize:function(t,e){this._url=t;var i=_({},this.defaultWmsParams);for(var n in e)n in this.options||(i[n]=e[n]);e=M(this,e);var o=e.detectRetina&&g.retina?2:1,s=this.getTileSize();i.width=s.x*o,i.height=s.y*o,this.wmsParams=i},onAdd:function(t){this._crs=this.options.crs||t.options.crs,this._wmsVersion=parseFloat(this.wmsParams.version);var e=this._wmsVersion>=1.3?"crs":"srs";this.wmsParams[e]=this._crs.code,Bt.prototype.onAdd.call(this,t)},getTileUrl:function(t){var e=this._tileCoordsToNwSe(t),i=this._crs,n=q(i.project(e[0]),i.project(e[1])),o=n.min,s=n.max,r=(this._wmsVersion>=1.3&&this._crs===hn?[o.y,o.x,s.y,s.x]:[o.x,o.y,s.x,s.y]).join(","),h=Bt.prototype.getTileUrl.call(this,t);return h+te(this.wmsParams,h,this.options.uppercase)+(this.options.uppercase?"&BBOX=":"&bbox=")+r},setParams:function(t,e){return _(this.wmsParams,t),e||this.redraw(),this}});function is(t,e){return new pn(t,e)}Bt.WMS=pn,mn.wms=is;var pt=nt.extend({options:{padding:.1},initialize:function(t){M(this,t),P(this),this._layers=this._layers||{}},onAdd:function(){this._container||(this._initContainer(),b(this._container,"leaflet-zoom-animated")),this.getPane().appendChild(this._container),this._update(),this.on("update",this._updatePaths,this)},onRemove:function(){this.off("update",this._updatePaths,this),this._destroyContainer()},getEvents:function(){var t={viewreset:this._reset,zoom:this._onZoom,moveend:this._update,zoomend:this._onZoomEnd};return this._zoomAnimated&&(t.zoomanim=this._onAnimZoom),t},_onAnimZoom:function(t){this._updateTransform(t.center,t.zoom)},_onZoom:function(){this._updateTransform(this._map.getCenter(),this._map.getZoom())},_updateTransform:function(t,e){var i=this._map.getZoomScale(e,this._zoom),n=this._map.getSize().multiplyBy(.5+this.options.padding),o=this._map.project(this._center,e),s=n.multiplyBy(-i).add(o).subtract(this._map._getNewPixelOrigin(t,e));g.any3d?Pt(this._container,s,i):D(this._container,s)},_reset:function(){this._update(),this._updateTransform(this._center,this._zoom);for(var t in this._layers)this._layers[t]._reset()},_onZoomEnd:function(){for(var t in this._layers)this._layers[t]._project()},_updatePaths:function(){for(var t in this._layers)this._layers[t]._update()},_update:function(){var t=this.options.padding,e=this._map.getSize(),i=this._map.containerPointToLayerPoint(e.multiplyBy(-t)).round();this._bounds=new B(i,i.add(e.multiplyBy(1+t*2)).round()),this._center=this._map.getCenter(),this._zoom=this._map.getZoom()}}),gn=pt.extend({options:{tolerance:0},getEvents:function(){var t=pt.prototype.getEvents.call(this);return t.viewprereset=this._onViewPreReset,t},_onViewPreReset:function(){this._postponeUpdatePaths=!0},onAdd:function(){pt.prototype.onAdd.call(this),this._draw()},_initContainer:function(){var t=this._container=document.createElement("canvas");x(t,"mousemove",this._onMouseMove,this),x(t,"click dblclick mousedown mouseup contextmenu",this._onClick,this),x(t,"mouseout",this._handleMouseOut,this),t._leaflet_disable_events=!0,this._ctx=t.getContext("2d")},_destroyContainer:function(){X(this._redrawRequest),delete this._ctx,Z(this._container),O(this._container),delete this._container},_updatePaths:function(){if(!this._postponeUpdatePaths){var t;this._redrawBounds=null;for(var e in this._layers)t=this._layers[e],t._update();this._redraw()}},_update:function(){if(!(this._map._animatingZoom&&this._bounds)){pt.prototype._update.call(this);var t=this._bounds,e=this._container,i=t.getSize(),n=g.retina?2:1;D(e,t.min),e.width=n*i.x,e.height=n*i.y,e.style.width=i.x+"px",e.style.height=i.y+"px",g.retina&&this._ctx.scale(2,2),this._ctx.translate(-t.min.x,-t.min.y),this.fire("update")}},_reset:function(){pt.prototype._reset.call(this),this._postponeUpdatePaths&&(this._postponeUpdatePaths=!1,this._updatePaths())},_initPath:function(t){this._updateDashArray(t),this._layers[P(t)]=t;var e=t._order={layer:t,prev:this._drawLast,next:null};this._drawLast&&(this._drawLast.next=e),this._drawLast=e,this._drawFirst=this._drawFirst||this._drawLast},_addPath:function(t){this._requestRedraw(t)},_removePath:function(t){var e=t._order,i=e.next,n=e.prev;i?i.prev=n:this._drawLast=n,n?n.next=i:this._drawFirst=i,delete t._order,delete this._layers[P(t)],this._requestRedraw(t)},_updatePath:function(t){this._extendRedrawBounds(t),t._project(),t._update(),this._requestRedraw(t)},_updateStyle:function(t){this._updateDashArray(t),this._requestRedraw(t)},_updateDashArray:function(t){if(typeof t.options.dashArray=="string"){var e=t.options.dashArray.split(/[, ]+/),i=[],n,o;for(o=0;o<e.length;o++){if(n=Number(e[o]),isNaN(n))return;i.push(n)}t.options._dashArray=i}else t.options._dashArray=t.options.dashArray},_requestRedraw:function(t){this._map&&(this._extendRedrawBounds(t),this._redrawRequest=this._redrawRequest||K(this._redraw,this))},_extendRedrawBounds:function(t){if(t._pxBounds){var e=(t.options.weight||0)+1;this._redrawBounds=this._redrawBounds||new B,this._redrawBounds.extend(t._pxBounds.min.subtract([e,e])),this._redrawBounds.extend(t._pxBounds.max.add([e,e]))}},_redraw:function(){this._redrawRequest=null,this._redrawBounds&&(this._redrawBounds.min._floor(),this._redrawBounds.max._ceil()),this._clear(),this._draw(),this._redrawBounds=null},_clear:function(){var t=this._redrawBounds;if(t){var e=t.getSize();this._ctx.clearRect(t.min.x,t.min.y,e.x,e.y)}else this._ctx.save(),this._ctx.setTransform(1,0,0,1,0,0),this._ctx.clearRect(0,0,this._container.width,this._container.height),this._ctx.restore()},_draw:function(){var t,e=this._redrawBounds;if(this._ctx.save(),e){var i=e.getSize();this._ctx.beginPath(),this._ctx.rect(e.min.x,e.min.y,i.x,i.y),this._ctx.clip()}this._drawing=!0;for(var n=this._drawFirst;n;n=n.next)t=n.layer,(!e||t._pxBounds&&t._pxBounds.intersects(e))&&t._updatePath();this._drawing=!1,this._ctx.restore()},_updatePoly:function(t,e){if(this._drawing){var i,n,o,s,r=t._parts,h=r.length,u=this._ctx;if(h){for(u.beginPath(),i=0;i<h;i++){for(n=0,o=r[i].length;n<o;n++)s=r[i][n],u[n?"lineTo":"moveTo"](s.x,s.y);e&&u.closePath()}this._fillStroke(u,t)}}},_updateCircle:function(t){if(!(!this._drawing||t._empty())){var e=t._point,i=this._ctx,n=Math.max(Math.round(t._radius),1),o=(Math.max(Math.round(t._radiusY),1)||n)/n;o!==1&&(i.save(),i.scale(1,o)),i.beginPath(),i.arc(e.x,e.y/o,n,0,Math.PI*2,!1),o!==1&&i.restore(),this._fillStroke(i,t)}},_fillStroke:function(t,e){var i=e.options;i.fill&&(t.globalAlpha=i.fillOpacity,t.fillStyle=i.fillColor||i.color,t.fill(i.fillRule||"evenodd")),i.stroke&&i.weight!==0&&(t.setLineDash&&t.setLineDash(e.options&&e.options._dashArray||[]),t.globalAlpha=i.opacity,t.lineWidth=i.weight,t.strokeStyle=i.color,t.lineCap=i.lineCap,t.lineJoin=i.lineJoin,t.stroke())},_onClick:function(t){for(var e=this._map.mouseEventToLayerPoint(t),i,n,o=this._drawFirst;o;o=o.next)i=o.layer,i.options.interactive&&i._containsPoint(e)&&(!(t.type==="click"||t.type==="preclick")||!this._map._draggableMoved(i))&&(n=i);this._fireEvent(n?[n]:!1,t)},_onMouseMove:function(t){if(!(!this._map||this._map.dragging.moving()||this._map._animatingZoom)){var e=this._map.mouseEventToLayerPoint(t);this._handleMouseHover(t,e)}},_handleMouseOut:function(t){var e=this._hoveredLayer;e&&(N(this._container,"leaflet-interactive"),this._fireEvent([e],t,"mouseout"),this._hoveredLayer=null,this._mouseHoverThrottled=!1)},_handleMouseHover:function(t,e){if(!this._mouseHoverThrottled){for(var i,n,o=this._drawFirst;o;o=o.next)i=o.layer,i.options.interactive&&i._containsPoint(e)&&(n=i);n!==this._hoveredLayer&&(this._handleMouseOut(t),n&&(b(this._container,"leaflet-interactive"),this._fireEvent([n],t,"mouseover"),this._hoveredLayer=n)),this._fireEvent(this._hoveredLayer?[this._hoveredLayer]:!1,t),this._mouseHoverThrottled=!0,setTimeout(p(function(){this._mouseHoverThrottled=!1},this),32)}},_fireEvent:function(t,e,i){this._map._fireDOMEvent(e,i||e.type,t)},_bringToFront:function(t){var e=t._order;if(e){var i=e.next,n=e.prev;if(i)i.prev=n;else return;n?n.next=i:i&&(this._drawFirst=i),e.prev=this._drawLast,this._drawLast.next=e,e.next=null,this._drawLast=e,this._requestRedraw(t)}},_bringToBack:function(t){var e=t._order;if(e){var i=e.next,n=e.prev;if(n)n.next=i;else return;i?i.prev=n:n&&(this._drawLast=n),e.prev=null,e.next=this._drawFirst,this._drawFirst.prev=e,this._drawFirst=e,this._requestRedraw(t)}}});function vn(t){return g.canvas?new gn(t):null}var Yt=function(){try{return document.namespaces.add("lvml","urn:schemas-microsoft-com:vml"),function(t){return document.createElement("<lvml:"+t+' class="lvml">')}}catch(t){}return function(t){return document.createElement("<"+t+' xmlns="urn:schemas-microsoft.com:vml" class="lvml">')}}(),ns={_initContainer:function(){this._container=E("div","leaflet-vml-container")},_update:function(){this._map._animatingZoom||(pt.prototype._update.call(this),this.fire("update"))},_initPath:function(t){var e=t._container=Yt("shape");b(e,"leaflet-vml-shape "+(this.options.className||"")),e.coordsize="1 1",t._path=Yt("path"),e.appendChild(t._path),this._updateStyle(t),this._layers[P(t)]=t},_addPath:function(t){var e=t._container;this._container.appendChild(e),t.options.interactive&&t.addInteractiveTarget(e)},_removePath:function(t){var e=t._container;Z(e),t.removeInteractiveTarget(e),delete this._layers[P(t)]},_updateStyle:function(t){var e=t._stroke,i=t._fill,n=t.options,o=t._container;o.stroked=!!n.stroke,o.filled=!!n.fill,n.stroke?(e||(e=t._stroke=Yt("stroke")),o.appendChild(e),e.weight=n.weight+"px",e.color=n.color,e.opacity=n.opacity,n.dashArray?e.dashStyle=W(n.dashArray)?n.dashArray.join(" "):n.dashArray.replace(/( *, *)/g," "):e.dashStyle="",e.endcap=n.lineCap.replace("butt","flat"),e.joinstyle=n.lineJoin):e&&(o.removeChild(e),t._stroke=null),n.fill?(i||(i=t._fill=Yt("fill")),o.appendChild(i),i.color=n.fillColor||n.color,i.opacity=n.fillOpacity):i&&(o.removeChild(i),t._fill=null)},_updateCircle:function(t){var e=t._point.round(),i=Math.round(t._radius),n=Math.round(t._radiusY||i);this._setPath(t,t._empty()?"M0 0":"AL "+e.x+","+e.y+" "+i+","+n+" 0,"+65535*360)},_setPath:function(t,e){t._path.v=e},_bringToFront:function(t){At(t._container)},_bringToBack:function(t){Et(t._container)}},we=g.vml?Yt:xi,Xt=pt.extend({_initContainer:function(){this._container=we("svg"),this._container.setAttribute("pointer-events","none"),this._rootGroup=we("g"),this._container.appendChild(this._rootGroup)},_destroyContainer:function(){Z(this._container),O(this._container),delete this._container,delete this._rootGroup,delete this._svgSize},_update:function(){if(!(this._map._animatingZoom&&this._bounds)){pt.prototype._update.call(this);var t=this._bounds,e=t.getSize(),i=this._container;(!this._svgSize||!this._svgSize.equals(e))&&(this._svgSize=e,i.setAttribute("width",e.x),i.setAttribute("height",e.y)),D(i,t.min),i.setAttribute("viewBox",[t.min.x,t.min.y,e.x,e.y].join(" ")),this.fire("update")}},_initPath:function(t){var e=t._path=we("path");t.options.className&&b(e,t.options.className),t.options.interactive&&b(e,"leaflet-interactive"),this._updateStyle(t),this._layers[P(t)]=t},_addPath:function(t){this._rootGroup||this._initContainer(),this._rootGroup.appendChild(t._path),t.addInteractiveTarget(t._path)},_removePath:function(t){Z(t._path),t.removeInteractiveTarget(t._path),delete this._layers[P(t)]},_updatePath:function(t){t._project(),t._update()},_updateStyle:function(t){var e=t._path,i=t.options;e&&(i.stroke?(e.setAttribute("stroke",i.color),e.setAttribute("stroke-opacity",i.opacity),e.setAttribute("stroke-width",i.weight),e.setAttribute("stroke-linecap",i.lineCap),e.setAttribute("stroke-linejoin",i.lineJoin),i.dashArray?e.setAttribute("stroke-dasharray",i.dashArray):e.removeAttribute("stroke-dasharray"),i.dashOffset?e.setAttribute("stroke-dashoffset",i.dashOffset):e.removeAttribute("stroke-dashoffset")):e.setAttribute("stroke","none"),i.fill?(e.setAttribute("fill",i.fillColor||i.color),e.setAttribute("fill-opacity",i.fillOpacity),e.setAttribute("fill-rule",i.fillRule||"evenodd")):e.setAttribute("fill","none"))},_updatePoly:function(t,e){this._setPath(t,Pi(t._parts,e))},_updateCircle:function(t){var e=t._point,i=Math.max(Math.round(t._radius),1),n=Math.max(Math.round(t._radiusY),1)||i,o="a"+i+","+n+" 0 1,0 ",s=t._empty()?"M0 0":"M"+(e.x-i)+","+e.y+o+i*2+",0 "+o+-i*2+",0 ";this._setPath(t,s)},_setPath:function(t,e){t._path.setAttribute("d",e)},_bringToFront:function(t){At(t._path)},_bringToBack:function(t){Et(t._path)}});g.vml&&Xt.include(ns);function yn(t){return g.svg||g.vml?new Xt(t):null}S.include({getRenderer:function(t){var e=t.options.renderer||this._getPaneRenderer(t.options.pane)||this.options.renderer||this._renderer;return e||(e=this._renderer=this._createRenderer()),this.hasLayer(e)||this.addLayer(e),e},_getPaneRenderer:function(t){if(t==="overlayPane"||t===void 0)return!1;var e=this._paneRenderers[t];return e===void 0&&(e=this._createRenderer({pane:t}),this._paneRenderers[t]=e),e},_createRenderer:function(t){return this.options.preferCanvas&&vn(t)||yn(t)}});var wn=Ot.extend({initialize:function(t,e){Ot.prototype.initialize.call(this,this._boundsToLatLngs(t),e)},setBounds:function(t){return this.setLatLngs(this._boundsToLatLngs(t))},_boundsToLatLngs:function(t){return t=R(t),[t.getSouthWest(),t.getNorthWest(),t.getNorthEast(),t.getSouthEast()]}});function os(t,e){return new wn(t,e)}Xt.create=we,Xt.pointsToPath=Pi,mt.geometryToLayer=fe,mt.coordsToLatLng=si,mt.coordsToLatLngs=_e,mt.latLngToCoords=ri,mt.latLngsToCoords=me,mt.getFeature=It,mt.asFeature=pe,S.mergeOptions({boxZoom:!0});var xn=ht.extend({initialize:function(t){this._map=t,this._container=t._container,this._pane=t._panes.overlayPane,this._resetStateTimeout=0,t.on("unload",this._destroy,this)},addHooks:function(){x(this._container,"mousedown",this._onMouseDown,this)},removeHooks:function(){O(this._container,"mousedown",this._onMouseDown,this)},moved:function(){return this._moved},_destroy:function(){Z(this._pane),delete this._pane},_resetState:function(){this._resetStateTimeout=0,this._moved=!1},_clearDeferredResetState:function(){this._resetStateTimeout!==0&&(clearTimeout(this._resetStateTimeout),this._resetStateTimeout=0)},_onMouseDown:function(t){if(!t.shiftKey||t.which!==1&&t.button!==1)return!1;this._clearDeferredResetState(),this._resetState(),Wt(),We(),this._startPoint=this._map.mouseEventToContainerPoint(t),x(document,{contextmenu:Tt,mousemove:this._onMouseMove,mouseup:this._onMouseUp,keydown:this._onKeyDown},this)},_onMouseMove:function(t){this._moved||(this._moved=!0,this._box=E("div","leaflet-zoom-box",this._container),b(this._container,"leaflet-crosshair"),this._map.fire("boxzoomstart")),this._point=this._map.mouseEventToContainerPoint(t);var e=new B(this._point,this._startPoint),i=e.getSize();D(this._box,e.min),this._box.style.width=i.x+"px",this._box.style.height=i.y+"px"},_finish:function(){this._moved&&(Z(this._box),N(this._container,"leaflet-crosshair")),Ut(),Ue(),O(document,{contextmenu:Tt,mousemove:this._onMouseMove,mouseup:this._onMouseUp,keydown:this._onKeyDown},this)},_onMouseUp:function(t){if(!(t.which!==1&&t.button!==1)&&(this._finish(),!!this._moved)){this._clearDeferredResetState(),this._resetStateTimeout=setTimeout(p(this._resetState,this),0);var e=new j(this._map.containerPointToLatLng(this._startPoint),this._map.containerPointToLatLng(this._point));this._map.fitBounds(e).fire("boxzoomend",{boxZoomBounds:e})}},_onKeyDown:function(t){t.keyCode===27&&(this._finish(),this._clearDeferredResetState(),this._resetState())}});S.addInitHook("addHandler","boxZoom",xn),S.mergeOptions({doubleClickZoom:!0});var Pn=ht.extend({addHooks:function(){this._map.on("dblclick",this._onDoubleClick,this)},removeHooks:function(){this._map.off("dblclick",this._onDoubleClick,this)},_onDoubleClick:function(t){var e=this._map,i=e.getZoom(),n=e.options.zoomDelta,o=t.originalEvent.shiftKey?i-n:i+n;e.options.doubleClickZoom==="center"?e.setZoom(o):e.setZoomAround(t.containerPoint,o)}});S.addInitHook("addHandler","doubleClickZoom",Pn),S.mergeOptions({dragging:!0,inertia:!0,inertiaDeceleration:3400,inertiaMaxSpeed:1/0,easeLinearity:.2,worldCopyJump:!1,maxBoundsViscosity:0});var Ln=ht.extend({addHooks:function(){if(!this._draggable){var t=this._map;this._draggable=new yt(t._mapPane,t._container),this._draggable.on({dragstart:this._onDragStart,drag:this._onDrag,dragend:this._onDragEnd},this),this._draggable.on("predrag",this._onPreDragLimit,this),t.options.worldCopyJump&&(this._draggable.on("predrag",this._onPreDragWrap,this),t.on("zoomend",this._onZoomEnd,this),t.whenReady(this._onZoomEnd,this))}b(this._map._container,"leaflet-grab leaflet-touch-drag"),this._draggable.enable(),this._positions=[],this._times=[]},removeHooks:function(){N(this._map._container,"leaflet-grab"),N(this._map._container,"leaflet-touch-drag"),this._draggable.disable()},moved:function(){return this._draggable&&this._draggable._moved},moving:function(){return this._draggable&&this._draggable._moving},_onDragStart:function(){var t=this._map;if(t._stop(),this._map.options.maxBounds&&this._map.options.maxBoundsViscosity){var e=R(this._map.options.maxBounds);this._offsetLimit=q(this._map.latLngToContainerPoint(e.getNorthWest()).multiplyBy(-1),this._map.latLngToContainerPoint(e.getSouthEast()).multiplyBy(-1).add(this._map.getSize())),this._viscosity=Math.min(1,Math.max(0,this._map.options.maxBoundsViscosity))}else this._offsetLimit=null;t.fire("movestart").fire("dragstart"),t.options.inertia&&(this._positions=[],this._times=[])},_onDrag:function(t){if(this._map.options.inertia){var e=this._lastTime=+new Date,i=this._lastPos=this._draggable._absPos||this._draggable._newPos;this._positions.push(i),this._times.push(e),this._prunePositions(e)}this._map.fire("move",t).fire("drag",t)},_prunePositions:function(t){for(;this._positions.length>1&&t-this._times[0]>50;)this._positions.shift(),this._times.shift()},_onZoomEnd:function(){var t=this._map.getSize().divideBy(2),e=this._map.latLngToLayerPoint([0,0]);this._initialWorldOffset=e.subtract(t).x,this._worldWidth=this._map.getPixelWorldBounds().getSize().x},_viscousLimit:function(t,e){return t-(t-e)*this._viscosity},_onPreDragLimit:function(){if(!(!this._viscosity||!this._offsetLimit)){var t=this._draggable._newPos.subtract(this._draggable._startPos),e=this._offsetLimit;t.x<e.min.x&&(t.x=this._viscousLimit(t.x,e.min.x)),t.y<e.min.y&&(t.y=this._viscousLimit(t.y,e.min.y)),t.x>e.max.x&&(t.x=this._viscousLimit(t.x,e.max.x)),t.y>e.max.y&&(t.y=this._viscousLimit(t.y,e.max.y)),this._draggable._newPos=this._draggable._startPos.add(t)}},_onPreDragWrap:function(){var t=this._worldWidth,e=Math.round(t/2),i=this._initialWorldOffset,n=this._draggable._newPos.x,o=(n-e+i)%t+e-i,s=(n+e+i)%t-e-i,r=Math.abs(o+i)<Math.abs(s+i)?o:s;this._draggable._absPos=this._draggable._newPos.clone(),this._draggable._newPos.x=r},_onDragEnd:function(t){var e=this._map,i=e.options,n=!i.inertia||t.noInertia||this._times.length<2;if(e.fire("dragend",t),n)e.fire("moveend");else{this._prunePositions(+new Date);var o=this._lastPos.subtract(this._positions[0]),s=(this._lastTime-this._times[0])/1e3,r=i.easeLinearity,h=o.multiplyBy(r/s),u=h.distanceTo([0,0]),c=Math.min(i.inertiaMaxSpeed,u),m=h.multiplyBy(c/u),v=c/(i.inertiaDeceleration*r),T=m.multiplyBy(-v/2).round();!T.x&&!T.y?e.fire("moveend"):(T=e._limitOffset(T,e.options.maxBounds),K(function(){e.panBy(T,{duration:v,easeLinearity:r,noMoveStart:!0,animate:!0})}))}}});S.addInitHook("addHandler","dragging",Ln),S.mergeOptions({keyboard:!0,keyboardPanDelta:80});var bn=ht.extend({keyCodes:{left:[37],right:[39],down:[40],up:[38],zoomIn:[187,107,61,171],zoomOut:[189,109,54,173]},initialize:function(t){this._map=t,this._setPanDelta(t.options.keyboardPanDelta),this._setZoomDelta(t.options.zoomDelta)},addHooks:function(){var t=this._map._container;t.tabIndex<=0&&(t.tabIndex="0"),x(t,{focus:this._onFocus,blur:this._onBlur,mousedown:this._onMouseDown},this),this._map.on({focus:this._addHooks,blur:this._removeHooks},this)},removeHooks:function(){this._removeHooks(),O(this._map._container,{focus:this._onFocus,blur:this._onBlur,mousedown:this._onMouseDown},this),this._map.off({focus:this._addHooks,blur:this._removeHooks},this)},_onMouseDown:function(){if(!this._focused){var t=document.body,e=document.documentElement,i=t.scrollTop||e.scrollTop,n=t.scrollLeft||e.scrollLeft;this._map._container.focus(),window.scrollTo(n,i)}},_onFocus:function(){this._focused=!0,this._map.fire("focus")},_onBlur:function(){this._focused=!1,this._map.fire("blur")},_setPanDelta:function(t){var e=this._panKeys={},i=this.keyCodes,n,o;for(n=0,o=i.left.length;n<o;n++)e[i.left[n]]=[-1*t,0];for(n=0,o=i.right.length;n<o;n++)e[i.right[n]]=[t,0];for(n=0,o=i.down.length;n<o;n++)e[i.down[n]]=[0,t];for(n=0,o=i.up.length;n<o;n++)e[i.up[n]]=[0,-1*t]},_setZoomDelta:function(t){var e=this._zoomKeys={},i=this.keyCodes,n,o;for(n=0,o=i.zoomIn.length;n<o;n++)e[i.zoomIn[n]]=t;for(n=0,o=i.zoomOut.length;n<o;n++)e[i.zoomOut[n]]=-t},_addHooks:function(){x(document,"keydown",this._onKeyDown,this)},_removeHooks:function(){O(document,"keydown",this._onKeyDown,this)},_onKeyDown:function(t){if(!(t.altKey||t.ctrlKey||t.metaKey)){var e=t.keyCode,i=this._map,n;if(e in this._panKeys){if(!i._panAnim||!i._panAnim._inProgress)if(n=this._panKeys[e],t.shiftKey&&(n=y(n).multiplyBy(3)),i.options.maxBounds&&(n=i._limitOffset(y(n),i.options.maxBounds)),i.options.worldCopyJump){var o=i.wrapLatLng(i.unproject(i.project(i.getCenter()).add(n)));i.panTo(o)}else i.panBy(n)}else if(e in this._zoomKeys)i.setZoom(i.getZoom()+(t.shiftKey?3:1)*this._zoomKeys[e]);else if(e===27&&i._popup&&i._popup.options.closeOnEscapeKey)i.closePopup();else return;Tt(t)}}});S.addInitHook("addHandler","keyboard",bn),S.mergeOptions({scrollWheelZoom:!0,wheelDebounceTime:40,wheelPxPerZoomLevel:60});var Tn=ht.extend({addHooks:function(){x(this._map._container,"wheel",this._onWheelScroll,this),this._delta=0},removeHooks:function(){O(this._map._container,"wheel",this._onWheelScroll,this)},_onWheelScroll:function(t){var e=ji(t),i=this._map.options.wheelDebounceTime;this._delta+=e,this._lastMousePos=this._map.mouseEventToContainerPoint(t),this._startTime||(this._startTime=+new Date);var n=Math.max(i-(+new Date-this._startTime),0);clearTimeout(this._timer),this._timer=setTimeout(p(this._performZoom,this),n),Tt(t)},_performZoom:function(){var t=this._map,e=t.getZoom(),i=this._map.options.zoomSnap||0;t._stop();var n=this._delta/(this._map.options.wheelPxPerZoomLevel*4),o=4*Math.log(2/(1+Math.exp(-Math.abs(n))))/Math.LN2,s=i?Math.ceil(o/i)*i:o,r=t._limitZoom(e+(this._delta>0?s:-s))-e;this._delta=0,this._startTime=null,r&&(t.options.scrollWheelZoom==="center"?t.setZoom(e+r):t.setZoomAround(this._lastMousePos,e+r))}});S.addInitHook("addHandler","scrollWheelZoom",Tn);var ss=600;S.mergeOptions({tapHold:g.touchNative&&g.safari&&g.mobile,tapTolerance:15});var Mn=ht.extend({addHooks:function(){x(this._map._container,"touchstart",this._onDown,this)},removeHooks:function(){O(this._map._container,"touchstart",this._onDown,this)},_onDown:function(t){if(clearTimeout(this._holdTimeout),t.touches.length===1){var e=t.touches[0];this._startPos=this._newPos=new w(e.clientX,e.clientY),this._holdTimeout=setTimeout(p(function(){this._cancel(),this._isTapValid()&&(x(document,"touchend",U),x(document,"touchend touchcancel",this._cancelClickPrevent),this._simulateEvent("contextmenu",e))},this),ss),x(document,"touchend touchcancel contextmenu",this._cancel,this),x(document,"touchmove",this._onMove,this)}},_cancelClickPrevent:function t(){O(document,"touchend",U),O(document,"touchend touchcancel",t)},_cancel:function(){clearTimeout(this._holdTimeout),O(document,"touchend touchcancel contextmenu",this._cancel,this),O(document,"touchmove",this._onMove,this)},_onMove:function(t){var e=t.touches[0];this._newPos=new w(e.clientX,e.clientY)},_isTapValid:function(){return this._newPos.distanceTo(this._startPos)<=this._map.options.tapTolerance},_simulateEvent:function(t,e){var i=new MouseEvent(t,{bubbles:!0,cancelable:!0,view:window,screenX:e.screenX,screenY:e.screenY,clientX:e.clientX,clientY:e.clientY});i._simulated=!0,e.target.dispatchEvent(i)}});S.addInitHook("addHandler","tapHold",Mn),S.mergeOptions({touchZoom:g.touch,bounceAtZoomLimits:!0});var Cn=ht.extend({addHooks:function(){b(this._map._container,"leaflet-touch-zoom"),x(this._map._container,"touchstart",this._onTouchStart,this)},removeHooks:function(){N(this._map._container,"leaflet-touch-zoom"),O(this._map._container,"touchstart",this._onTouchStart,this)},_onTouchStart:function(t){var e=this._map;if(!(!t.touches||t.touches.length!==2||e._animatingZoom||this._zooming)){var i=e.mouseEventToContainerPoint(t.touches[0]),n=e.mouseEventToContainerPoint(t.touches[1]);this._centerPoint=e.getSize()._divideBy(2),this._startLatLng=e.containerPointToLatLng(this._centerPoint),e.options.touchZoom!=="center"&&(this._pinchStartLatLng=e.containerPointToLatLng(i.add(n)._divideBy(2))),this._startDist=i.distanceTo(n),this._startZoom=e.getZoom(),this._moved=!1,this._zooming=!0,e._stop(),x(document,"touchmove",this._onTouchMove,this),x(document,"touchend touchcancel",this._onTouchEnd,this),U(t)}},_onTouchMove:function(t){if(!(!t.touches||t.touches.length!==2||!this._zooming)){var e=this._map,i=e.mouseEventToContainerPoint(t.touches[0]),n=e.mouseEventToContainerPoint(t.touches[1]),o=i.distanceTo(n)/this._startDist;if(this._zoom=e.getScaleZoom(o,this._startZoom),!e.options.bounceAtZoomLimits&&(this._zoom<e.getMinZoom()&&o<1||this._zoom>e.getMaxZoom()&&o>1)&&(this._zoom=e._limitZoom(this._zoom)),e.options.touchZoom==="center"){if(this._center=this._startLatLng,o===1)return}else{var s=i._add(n)._divideBy(2)._subtract(this._centerPoint);if(o===1&&s.x===0&&s.y===0)return;this._center=e.unproject(e.project(this._pinchStartLatLng,this._zoom).subtract(s),this._zoom)}this._moved||(e._moveStart(!0,!1),this._moved=!0),X(this._animRequest);var r=p(e._move,e,this._center,this._zoom,{pinch:!0,round:!1},void 0);this._animRequest=K(r,this,!0),U(t)}},_onTouchEnd:function(){if(!this._moved||!this._zooming){this._zooming=!1;return}this._zooming=!1,X(this._animRequest),O(document,"touchmove",this._onTouchMove,this),O(document,"touchend touchcancel",this._onTouchEnd,this),this._map.options.zoomAnimation?this._map._animateZoom(this._center,this._map._limitZoom(this._zoom),!0,this._map.options.zoomSnap):this._map._resetView(this._center,this._map._limitZoom(this._zoom))}});S.addInitHook("addHandler","touchZoom",Cn),S.BoxZoom=xn,S.DoubleClickZoom=Pn,S.Drag=Ln,S.Keyboard=bn,S.ScrollWheelZoom=Tn,S.TapHold=Mn,S.TouchZoom=Cn,a.Bounds=B,a.Browser=g,a.CRS=dt,a.Canvas=gn,a.Circle=oi,a.CircleMarker=de,a.Class=lt,a.Control=it,a.DivIcon=_n,a.DivOverlay=ut,a.DomEvent=Lo,a.DomUtil=xo,a.Draggable=yt,a.Evented=Nt,a.FeatureGroup=ft,a.GeoJSON=mt,a.GridLayer=Jt,a.Handler=ht,a.Icon=kt,a.ImageOverlay=ge,a.LatLng=z,a.LatLngBounds=j,a.Layer=nt,a.LayerGroup=zt,a.LineUtil=Zo,a.Map=S,a.Marker=le,a.Mixin=Eo,a.Path=wt,a.Point=w,a.PolyUtil=zo,a.Polygon=Ot,a.Polyline=_t,a.Popup=ve,a.PosAnimation=Ji,a.Projection=No,a.Rectangle=wn,a.Renderer=pt,a.SVG=Xt,a.SVGOverlay=fn,a.TileLayer=Bt,a.Tooltip=ye,a.Transformation=Ee,a.Util=Wn,a.VideoOverlay=dn,a.bind=p,a.bounds=q,a.canvas=vn,a.circle=Vo,a.circleMarker=Go,a.control=Kt,a.divIcon=ts,a.extend=_,a.featureGroup=Fo,a.geoJSON=ln,a.geoJson=jo,a.gridLayer=es,a.icon=Wo,a.imageOverlay=Jo,a.latLng=C,a.latLngBounds=R,a.layerGroup=Ho,a.map=bo,a.marker=Uo,a.point=y,a.polygon=qo,a.polyline=Ko,a.popup=Qo,a.rectangle=os,a.setOptions=M,a.stamp=P,a.svg=yn,a.svgOverlay=Xo,a.tileLayer=mn,a.tooltip=$o,a.transformation=Rt,a.version=f,a.videoOverlay=Yo;var rs=window.L;a.noConflict=function(){return window.L=rs,this},window.L=a})}(Qt,Qt.exports)),Qt.exports}var $t=Ss();const As=Nn($t);function _i(d,l,a){return Object.freeze({instance:d,context:l,container:a})}function mi(d,l){return l==null?function(f,_){const A=k.useRef();return A.current||(A.current=d(f,_)),A}:function(f,_){const A=k.useRef();A.current||(A.current=d(f,_));const p=k.useRef(f),{instance:G}=A.current;return k.useEffect(function(){p.current!==f&&(l(G,f,p.current),p.current=f)},[G,f,_]),A}}function Fn(d,l){k.useEffect(function(){var _;return((_=l.layerContainer)!=null?_:l.map).addLayer(d.instance),function(){var p;(p=l.layerContainer)==null||p.removeLayer(d.instance),l.map.removeLayer(d.instance)}},[l,d])}function Es(d){return function(a){const f=di(),_=d(Te(a,f),f);return Rn(f.map,a.attribution),fi(_.current,a.eventHandlers),Fn(_.current,f),_}}function zs(d,l){const a=k.useRef();k.useEffect(function(){var _;if(l.pathOptions!==a.current){const A=(_=l.pathOptions)!=null?_:{};d.instance.setStyle(A),a.current=A}},[d,l])}function ks(d){return function(a){const f=di(),_=d(Te(a,f),f);return fi(_.current,a.eventHandlers),Fn(_.current,f),zs(_.current,a),_}}function Os(d,l){const a=mi(d),f=Ms(a,l);return bs(f)}function Is(d,l){const a=mi(d,l),f=ks(a);return Ls(f)}function Bs(d,l){const a=mi(d,l),f=Es(a);return Ts(f)}function Zs(d,l,a){const{opacity:f,zIndex:_}=l;f!=null&&f!==a.opacity&&d.setOpacity(f),_!=null&&_!==a.zIndex&&d.setZIndex(_)}const Ns=Is(function(A,_){var p=A,{center:l,children:a}=p,f=be(p,["center","children"]);const G=new $t.CircleMarker(l,f);return _i(G,Ps(_,{overlayContainer:G}))},ys);function li(){return li=Object.assign||function(d){for(var l=1;l<arguments.length;l++){var a=arguments[l];for(var f in a)Object.prototype.hasOwnProperty.call(a,f)&&(d[f]=a[f])}return d},li.apply(this,arguments)}function Rs(et,I){var Ct=et,{bounds:d,boundsOptions:l,center:a,children:f,className:_,id:A,placeholder:p,style:G,whenReady:P,zoom:ot}=Ct,st=be(Ct,["bounds","boundsOptions","center","children","className","id","placeholder","style","whenReady","zoom"]);const[ct]=k.useState({className:_,id:A,style:G}),[M,te]=k.useState(null);k.useImperativeHandle(I,()=>{var W;return(W=M==null?void 0:M.map)!=null?W:null},[M]);const Me=k.useCallback(W=>{if(W!==null&&M===null){const gt=new $t.Map(W,st);a!=null&&ot!=null?gt.setView(a,ot):d!=null&&gt.fitBounds(d,l),P!=null&&gt.whenReady(P),te(xs(gt))}},[]);k.useEffect(()=>()=>{M==null||M.map.remove()},[M]);const ee=M?ci.createElement(Hn,{value:M},f):p!=null?p:null;return ci.createElement("div",li({},ct,{ref:Me}),ee)}const Ds=k.forwardRef(Rs),Hs=Os(function(l,a){const f=new $t.Popup(l,a.overlayContainer);return _i(f,a)},function(l,a,{position:f},_){k.useEffect(function(){const{instance:p}=l;function G(ot){ot.popup===p&&(p.update(),_(!0))}function P(ot){ot.popup===p&&_(!1)}return a.map.on({popupopen:G,popupclose:P}),a.overlayContainer==null?(f!=null&&p.setLatLng(f),p.openOn(a.map)):a.overlayContainer.bindPopup(p),function(){var st;a.map.off({popupopen:G,popupclose:P}),(st=a.overlayContainer)==null||st.unbindPopup(),a.map.removeLayer(p)}},[l,a,_,f])}),Fs=Bs(function(_,f){var A=_,{url:l}=A,a=be(A,["url"]);const p=new $t.TileLayer(l,Te(a,f));return _i(p,f)},function(l,a,f){Zs(l,a,f);const{url:_}=a;_!=null&&_!==f.url&&l.setUrl(_)}),Ws="data:image/png;base64,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",Us="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABkAAAApCAYAAADAk4LOAAAFgUlEQVR4Aa1XA5BjWRTN2oW17d3YaZtr2962HUzbDNpjszW24mRt28p47v7zq/bXZtrp/lWnXr337j3nPCe85NcypgSFdugCpW5YoDAMRaIMqRi6aKq5E3YqDQO3qAwjVWrD8Ncq/RBpykd8oZUb/kaJutow8r1aP9II0WmLKLIsJyv1w/kqw9Ch2MYdB++12Onxee/QMwvf4/Dk/Lfp/i4nxTXtOoQ4pW5Aj7wpici1A9erdAN2OH64x8OSP9j3Ft3b7aWkTg/Fm91siTra0f9on5sQr9INejH6CUUUpavjFNq1B+Oadhxmnfa8RfEmN8VNAsQhPqF55xHkMzz3jSmChWU6f7/XZKNH+9+hBLOHYozuKQPxyMPUKkrX/K0uWnfFaJGS1QPRtZsOPtr3NsW0uyh6NNCOkU3Yz+bXbT3I8G3xE5EXLXtCXbbqwCO9zPQYPRTZ5vIDXD7U+w7rFDEoUUf7ibHIR4y6bLVPXrz8JVZEql13trxwue/uDivd3fkWRbS6/IA2bID4uk0UpF1N8qLlbBlXs4Ee7HLTfV1j54APvODnSfOWBqtKVvjgLKzF5YdEk5ewRkGlK0i33Eofffc7HT56jD7/6U+qH3Cx7SBLNntH5YIPvODnyfIXZYRVDPqgHtLs5ABHD3YzLuespb7t79FY34DjMwrVrcTuwlT55YMPvOBnRrJ4VXTdNnYug5ucHLBjEpt30701A3Ts+HEa73u6dT3FNWwflY86eMHPk+Yu+i6pzUpRrW7SNDg5JHR4KapmM5Wv2E8Tfcb1HoqqHMHU+uWDD7zg54mz5/2BSnizi9T1Dg4QQXLToGNCkb6tb1NU+QAlGr1++eADrzhn/u8Q2YZhQVlZ5+CAOtqfbhmaUCS1ezNFVm2imDbPmPng5wmz+gwh+oHDce0eUtQ6OGDIyR0uUhUsoO3vfDmmgOezH0mZN59x7MBi++WDL1g/eEiU3avlidO671bkLfwbw5XV2P8Pzo0ydy4t2/0eu33xYSOMOD8hTf4CrBtGMSoXfPLchX+J0ruSePw3LZeK0juPJbYzrhkH0io7B3k164hiGvawhOKMLkrQLyVpZg8rHFW7E2uHOL888IBPlNZ1FPzstSJM694fWr6RwpvcJK60+0HCILTBzZLFNdtAzJaohze60T8qBzyh5ZuOg5e7uwQppofEmf2++DYvmySqGBuKaicF1blQjhuHdvCIMvp8whTTfZzI7RldpwtSzL+F1+wkdZ2TBOW2gIF88PBTzD/gpeREAMEbxnJcaJHNHrpzji0gQCS6hdkEeYt9DF/2qPcEC8RM28Hwmr3sdNyht00byAut2k3gufWNtgtOEOFGUwcXWNDbdNbpgBGxEvKkOQsxivJx33iow0Vw5S6SVTrpVq11ysA2Rp7gTfPfktc6zhtXBBC+adRLshf6sG2RfHPZ5EAc4sVZ83yCN00Fk/4kggu40ZTvIEm5g24qtU4KjBrx/BTTH8ifVASAG7gKrnWxJDcU7x8X6Ecczhm3o6YicvsLXWfh3Ch1W0k8x0nXF+0fFxgt4phz8QvypiwCCFKMqXCnqXExjq10beH+UUA7+nG6mdG/Pu0f3LgFcGrl2s0kNNjpmoJ9o4B29CMO8dMT4Q5ox8uitF6fqsrJOr8qnwNbRzv6hSnG5wP+64C7h9lp30hKNtKdWjtdkbuPA19nJ7Tz3zR/ibgARbhb4AlhavcBebmTHcFl2fvYEnW0ox9xMxKBS8btJ+KiEbq9zA4RthQXDhPa0T9TEe69gWupwc6uBUphquXgf+/FrIjweHQS4/pduMe5ERUMHUd9xv8ZR98CxkS4F2n3EUrUZ10EYNw7BWm9x1GiPssi3GgiGRDKWRYZfXlON+dfNbM+GgIwYdwAAAAASUVORK5CYII=",Gs="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACkAAAApCAQAAAACach9AAACMUlEQVR4Ae3ShY7jQBAE0Aoz/f9/HTMzhg1zrdKUrJbdx+Kd2nD8VNudfsL/Th///dyQN2TH6f3y/BGpC379rV+S+qqetBOxImNQXL8JCAr2V4iMQXHGNJxeCfZXhSRBcQMfvkOWUdtfzlLgAENmZDcmo2TVmt8OSM2eXxBp3DjHSMFutqS7SbmemzBiR+xpKCNUIRkdkkYxhAkyGoBvyQFEJEefwSmmvBfJuJ6aKqKWnAkvGZOaZXTUgFqYULWNSHUckZuR1HIIimUExutRxwzOLROIG4vKmCKQt364mIlhSyzAf1m9lHZHJZrlAOMMztRRiKimp/rpdJDc9Awry5xTZCte7FHtuS8wJgeYGrex28xNTd086Dik7vUMscQOa8y4DoGtCCSkAKlNwpgNtphjrC6MIHUkR6YWxxs6Sc5xqn222mmCRFzIt8lEdKx+ikCtg91qS2WpwVfBelJCiQJwvzixfI9cxZQWgiSJelKnwBElKYtDOb2MFbhmUigbReQBV0Cg4+qMXSxXSyGUn4UbF8l+7qdSGnTC0XLCmahIgUHLhLOhpVCtw4CzYXvLQWQbJNmxoCsOKAxSgBJno75avolkRw8iIAFcsdc02e9iyCd8tHwmeSSoKTowIgvscSGZUOA7PuCN5b2BX9mQM7S0wYhMNU74zgsPBj3HU7wguAfnxxjFQGBE6pwN+GjME9zHY7zGp8wVxMShYX9NXvEWD3HbwJf4giO4CFIQxXScH1/TM+04kkBiAAAAAElFTkSuQmCC",Vs={coordinate:["42.546245","1.601554"],name:"Andorra"},Ks={coordinate:["23.424076","53.847818"],name:"United Arab Emirates"},qs={coordinate:["33.93911","67.709953"],name:"Afghanistan"},js={coordinate:["17.060816","-61.796428"],name:"Antigua and Barbuda"},Js={coordinate:["18.220554","-63.068615"],name:"Anguilla"},Ys={coordinate:["41.153332","20.168331"],name:"Albania"},Xs={coordinate:["40.069099","45.038189"],name:"Armenia"},Qs={coordinate:["12.226079","-69.060087"],name:"Netherlands Antilles"},$s={coordinate:["-11.202692","17.873887"],name:"Angola"},tr={coordinate:["-75.250973","-0.071389"],name:"Antarctica"},er={coordinate:["-38.416097","-63.616672"],name:"Argentina"},ir={coordinate:["-14.270972","-170.132217"],name:"American Samoa"},nr={coordinate:["47.516231","14.550072"],name:"Austria"},or={coordinate:["-25.274398","133.775136"],name:"Australia"},sr={coordinate:["12.52111","-69.968338"],name:"Aruba"},rr={coordinate:["40.143105","47.576927"],name:"Azerbaijan"},ar={coordinate:["43.915886","17.679076"],name:"Bosnia and Herzegovina"},hr={coordinate:["13.193887","-59.543198"],name:"Barbados"},ur={coordinate:["23.684994","90.356331"],name:"Bangladesh"},cr={coordinate:["50.503887","4.469936"],name:"Belgium"},lr={coordinate:["12.238333","-1.561593"],name:"Burkina Faso"},dr={coordinate:["42.733883","25.48583"],name:"Bulgaria"},fr={coordinate:["25.930414","50.637772"],name:"Bahrain"},_r={coordinate:["-3.373056","29.918886"],name:"Burundi"},mr={coordinate:["9.30769","2.315834"],name:"Benin"},pr={coordinate:["32.321384","-64.75737"],name:"Bermuda"},gr={coordinate:["4.535277","114.727669"],name:"Brunei"},vr={coordinate:["-16.290154","-63.588653"],name:"Bolivia"},yr={coordinate:["-14.235004","-51.92528"],name:"Brazil"},wr={coordinate:["25.03428","-77.39628"],name:"Bahamas"},xr={coordinate:["27.514162","90.433601"],name:"Bhutan"},Pr={coordinate:["-54.423199","3.413194"],name:"Bouvet Island"},Lr={coordinate:["-22.328474","24.684866"],name:"Botswana"},br={coordinate:["53.709807","27.953389"],name:"Belarus"},Tr={coordinate:["17.189877","-88.49765"],name:"Belize"},Mr={coordinate:["56.130366","-106.346771"],name:"Canada"},Cr={coordinate:["-12.164165","96.870956"],name:"Cocos [Keeling] Islands"},Sr={coordinate:["-4.038333","21.758664"],name:"Congo [DRC]"},Ar={coordinate:["6.611111","20.939444"],name:"Central African Republic"},Er={coordinate:["-0.228021","15.827659"],name:"Congo [Republic]"},zr={coordinate:["46.818188","8.227512"],name:"Switzerland"},kr={coordinate:["7.539989","-5.54708"],name:"Côte d'Ivoire"},Or={coordinate:["-21.236736","-159.777671"],name:"Cook Islands"},Ir={coordinate:["-35.675147","-71.542969"],name:"Chile"},Br={coordinate:["7.369722","12.354722"],name:"Cameroon"},Zr={coordinate:["35.86166","104.195397"],name:"China"},Nr={coordinate:["4.570868","-74.297333"],name:"Colombia"},Rr={coordinate:["9.748917","-83.753428"],name:"Costa Rica"},Dr={coordinate:["21.521757","-77.781167"],name:"Cuba"},Hr={coordinate:["16.002082","-24.013197"],name:"Cape Verde"},Fr={coordinate:["-10.447525","105.690449"],name:"Christmas Island"},Wr={coordinate:["35.126413","33.429859"],name:"Cyprus"},Ur={coordinate:["49.817492","15.472962"],name:"Czech Republic"},Gr={coordinate:["51.165691","10.451526"],name:"Germany"},Vr={coordinate:["11.825138","42.590275"],name:"Djibouti"},Kr={coordinate:["56.26392","9.501785"],name:"Denmark"},qr={coordinate:["15.414999","-61.370976"],name:"Dominica"},jr={coordinate:["18.735693","-70.162651"],name:"Dominican Republic"},Jr={coordinate:["28.033886","1.659626"],name:"Algeria"},Yr={coordinate:["-1.831239","-78.183406"],name:"Ecuador"},Xr={coordinate:["58.595272","25.013607"],name:"Estonia"},Qr={coordinate:["26.820553","30.802498"],name:"Egypt"},$r={coordinate:["24.215527","-12.885834"],name:"Western Sahara"},ta={coordinate:["15.179384","39.782334"],name:"Eritrea"},ea={coordinate:["40.463667","-3.74922"],name:"Spain"},ia={coordinate:["9.145","40.489673"],name:"Ethiopia"},na={coordinate:["61.92411","25.748151"],name:"Finland"},oa={coordinate:["-16.578193","179.414413"],name:"Fiji"},sa={coordinate:["-51.796253","-59.523613"],name:"Falkland Islands [Islas Malvinas]"},ra={coordinate:["7.425554","150.550812"],name:"Micronesia"},aa={coordinate:["61.892635","-6.911806"],name:"Faroe Islands"},ha={coordinate:["46.227638","2.213749"],name:"France"},ua={coordinate:["-0.803689","11.609444"],name:"Gabon"},ca={coordinate:["55.378051","-3.435973"],name:"United Kingdom"},la={coordinate:["12.262776","-61.604171"],name:"Grenada"},da={coordinate:["42.315407","43.356892"],name:"Georgia"},fa={coordinate:["3.933889","-53.125782"],name:"French Guiana"},_a={coordinate:["49.465691","-2.585278"],name:"Guernsey"},ma={coordinate:["7.946527","-1.023194"],name:"Ghana"},pa={coordinate:["36.137741","-5.345374"],name:"Gibraltar"},ga={coordinate:["71.706936","-42.604303"],name:"Greenland"},va={coordinate:["13.443182","-15.310139"],name:"Gambia"},ya={coordinate:["9.945587","-9.696645"],name:"Guinea"},wa={coordinate:["16.995971","-62.067641"],name:"Guadeloupe"},xa={coordinate:["1.650801","10.267895"],name:"Equatorial Guinea"},Pa={coordinate:["39.074208","21.824312"],name:"Greece"},La={coordinate:["-54.429579","-36.587909"],name:"South Georgia and the South Sandwich Islands"},ba={coordinate:["15.783471","-90.230759"],name:"Guatemala"},Ta={coordinate:["13.444304","144.793731"],name:"Guam"},Ma={coordinate:["11.803749","-15.180413"],name:"Guinea-Bissau"},Ca={coordinate:["4.860416","-58.93018"],name:"Guyana"},Sa={coordinate:["31.354676","34.308825"],name:"Gaza Strip"},Aa={coordinate:["22.396428","114.109497"],name:"Hong Kong"},Ea={coordinate:["-53.08181","73.504158"],name:"Heard Island and McDonald Islands"},za={coordinate:["15.199999","-86.241905"],name:"Honduras"},ka={coordinate:["45.1","15.2"],name:"Croatia"},Oa={coordinate:["18.971187","-72.285215"],name:"Haiti"},Ia={coordinate:["47.162494","19.503304"],name:"Hungary"},Ba={coordinate:["-0.789275","113.921327"],name:"Indonesia"},Za={coordinate:["53.41291","-8.24389"],name:"Ireland"},Na={coordinate:["31.046051","34.851612"],name:"Israel"},Ra={coordinate:["54.236107","-4.548056"],name:"Isle of Man"},Da={coordinate:["20.593684","78.96288"],name:"India"},Ha={coordinate:["-6.343194","71.876519"],name:"British Indian Ocean Territory"},Fa={coordinate:["33.223191","43.679291"],name:"Iraq"},Wa={coordinate:["32.427908","53.688046"],name:"Iran"},Ua={coordinate:["64.963051","-19.020835"],name:"Iceland"},Ga={coordinate:["41.87194","12.56738"],name:"Italy"},Va={coordinate:["49.214439","-2.13125"],name:"Jersey"},Ka={coordinate:["18.109581","-77.297508"],name:"Jamaica"},qa={coordinate:["30.585164","36.238414"],name:"Jordan"},ja={coordinate:["36.204824","138.252924"],name:"Japan"},Ja={coordinate:["-0.023559","37.906193"],name:"Kenya"},Ya={coordinate:["41.20438","74.766098"],name:"Kyrgyzstan"},Xa={coordinate:["12.565679","104.990963"],name:"Cambodia"},Qa={coordinate:["-3.370417","-168.734039"],name:"Kiribati"},$a={coordinate:["-11.875001","43.872219"],name:"Comoros"},th={coordinate:["17.357822","-62.782998"],name:"Saint Kitts and Nevis"},eh={coordinate:["40.339852","127.510093"],name:"North Korea"},ih={coordinate:["35.907757","127.766922"],name:"South Korea"},nh={coordinate:["29.31166","47.481766"],name:"Kuwait"},oh={coordinate:["19.513469","-80.566956"],name:"Cayman Islands"},sh={coordinate:["48.019573","66.923684"],name:"Kazakhstan"},rh={coordinate:["19.85627","102.495496"],name:"Laos"},ah={coordinate:["33.854721","35.862285"],name:"Lebanon"},hh={coordinate:["13.909444","-60.978893"],name:"Saint Lucia"},uh={coordinate:["47.166","9.555373"],name:"Liechtenstein"},ch={coordinate:["7.873054","80.771797"],name:"Sri Lanka"},lh={coordinate:["6.428055","-9.429499"],name:"Liberia"},dh={coordinate:["-29.609988","28.233608"],name:"Lesotho"},fh={coordinate:["55.169438","23.881275"],name:"Lithuania"},_h={coordinate:["49.815273","6.129583"],name:"Luxembourg"},mh={coordinate:["56.879635","24.603189"],name:"Latvia"},ph={coordinate:["26.3351","17.228331"],name:"Libya"},gh={coordinate:["31.791702","-7.09262"],name:"Morocco"},vh={coordinate:["43.750298","7.412841"],name:"Monaco"},yh={coordinate:["47.411631","28.369885"],name:"Moldova"},wh={coordinate:["42.708678","19.37439"],name:"Montenegro"},xh={coordinate:["-18.766947","46.869107"],name:"Madagascar"},Ph={coordinate:["7.131474","171.184478"],name:"Marshall Islands"},Lh={coordinate:["41.608635","21.745275"],name:"Macedonia [FYROM]"},bh={coordinate:["17.570692","-3.996166"],name:"Mali"},Th={coordinate:["21.913965","95.956223"],name:"Myanmar [Burma]"},Mh={coordinate:["46.862496","103.846656"],name:"Mongolia"},Ch={coordinate:["22.198745","113.543873"],name:"Macau"},Sh={coordinate:["17.33083","145.38469"],name:"Northern Mariana Islands"},Ah={coordinate:["14.641528","-61.024174"],name:"Martinique"},Eh={coordinate:["21.00789","-10.940835"],name:"Mauritania"},zh={coordinate:["16.742498","-62.187366"],name:"Montserrat"},kh={coordinate:["35.937496","14.375416"],name:"Malta"},Oh={coordinate:["-20.348404","57.552152"],name:"Mauritius"},Ih={coordinate:["3.202778","73.22068"],name:"Maldives"},Bh={coordinate:["-13.254308","34.301525"],name:"Malawi"},Zh={coordinate:["23.634501","-102.552784"],name:"Mexico"},Nh={coordinate:["4.210484","101.975766"],name:"Malaysia"},Rh={coordinate:["-18.665695","35.529562"],name:"Mozambique"},Dh={coordinate:["-22.95764","18.49041"],name:"Namibia"},Hh={coordinate:["-20.904305","165.618042"],name:"New Caledonia"},Fh={coordinate:["17.607789","8.081666"],name:"Niger"},Wh={coordinate:["-29.040835","167.954712"],name:"Norfolk Island"},Uh={coordinate:["9.081999","8.675277"],name:"Nigeria"},Gh={coordinate:["12.865416","-85.207229"],name:"Nicaragua"},Vh={coordinate:["52.132633","5.291266"],name:"Netherlands"},Kh={coordinate:["60.472024","8.468946"],name:"Norway"},qh={coordinate:["28.394857","84.124008"],name:"Nepal"},jh={coordinate:["-0.522778","166.931503"],name:"Nauru"},Jh={coordinate:["-19.054445","-169.867233"],name:"Niue"},Yh={coordinate:["-40.900557","174.885971"],name:"New Zealand"},Xh={coordinate:["21.512583","55.923255"],name:"Oman"},Qh={coordinate:["8.537981","-80.782127"],name:"Panama"},$h={coordinate:["-9.189967","-75.015152"],name:"Peru"},tu={coordinate:["-17.679742","-149.406843"],name:"French Polynesia"},eu={coordinate:["-6.314993","143.95555"],name:"Papua New Guinea"},iu={coordinate:["12.879721","121.774017"],name:"Philippines"},nu={coordinate:["30.375321","69.345116"],name:"Pakistan"},ou={coordinate:["51.919438","19.145136"],name:"Poland"},su={coordinate:["46.941936","-56.27111"],name:"Saint Pierre and Miquelon"},ru={coordinate:["-24.703615","-127.439308"],name:"Pitcairn Islands"},au={coordinate:["18.220833","-66.590149"],name:"Puerto Rico"},hu={coordinate:["31.952162","35.233154"],name:"Palestinian Territories"},uu={coordinate:["39.399872","-8.224454"],name:"Portugal"},cu={coordinate:["7.51498","134.58252"],name:"Palau"},lu={coordinate:["-23.442503","-58.443832"],name:"Paraguay"},du={coordinate:["25.354826","51.183884"],name:"Qatar"},fu={coordinate:["-21.115141","55.536384"],name:"Réunion"},_u={coordinate:["45.943161","24.96676"],name:"Romania"},mu={coordinate:["44.016521","21.005859"],name:"Serbia"},pu={coordinate:["61.52401","105.318756"],name:"Russia"},gu={coordinate:["-1.940278","29.873888"],name:"Rwanda"},vu={coordinate:["23.885942","45.079162"],name:"Saudi Arabia"},yu={coordinate:["-9.64571","160.156194"],name:"Solomon Islands"},wu={coordinate:["-4.679574","55.491977"],name:"Seychelles"},xu={coordinate:["12.862807","30.217636"],name:"Sudan"},Pu={coordinate:["60.128161","18.643501"],name:"Sweden"},Lu={coordinate:["1.352083","103.819836"],name:"Singapore"},bu={coordinate:["-24.143474","-10.030696"],name:"Saint Helena"},Tu={coordinate:["46.151241","14.995463"],name:"Slovenia"},Mu={coordinate:["77.553604","23.670272"],name:"Svalbard and Jan Mayen"},Cu={coordinate:["48.669026","19.699024"],name:"Slovakia"},Su={coordinate:["8.460555","-11.779889"],name:"Sierra Leone"},Au={coordinate:["43.94236","12.457777"],name:"San Marino"},Eu={coordinate:["14.497401","-14.452362"],name:"Senegal"},zu={coordinate:["5.152149","46.199616"],name:"Somalia"},ku={coordinate:["3.919305","-56.027783"],name:"Suriname"},Ou={coordinate:["0.18636","6.613081"],name:"São Tomé and Príncipe"},Iu={coordinate:["13.794185","-88.89653"],name:"El Salvador"},Bu={coordinate:["34.802075","38.996815"],name:"Syria"},Zu={coordinate:["-26.522503","31.465866"],name:"Swaziland"},Nu={coordinate:["21.694025","-71.797928"],name:"Turks and Caicos Islands"},Ru={coordinate:["15.454166","18.732207"],name:"Chad"},Du={coordinate:["-49.280366","69.348557"],name:"French Southern Territories"},Hu={coordinate:["8.619543","0.824782"],name:"Togo"},Fu={coordinate:["15.870032","100.992541"],name:"Thailand"},Wu={coordinate:["38.861034","71.276093"],name:"Tajikistan"},Uu={coordinate:["-8.967363","-171.855881"],name:"Tokelau"},Gu={coordinate:["-8.874217","125.727539"],name:"Timor-Leste"},Vu={coordinate:["38.969719","59.556278"],name:"Turkmenistan"},Ku={coordinate:["33.886917","9.537499"],name:"Tunisia"},qu={coordinate:["-21.178986","-175.198242"],name:"Tonga"},ju={coordinate:["38.963745","35.243322"],name:"Turkey"},Ju={coordinate:["10.691803","-61.222503"],name:"Trinidad and Tobago"},Yu={coordinate:["-7.109535","177.64933"],name:"Tuvalu"},Xu={coordinate:["23.69781","120.960515"],name:"Taiwan"},Qu={coordinate:["-6.369028","34.888822"],name:"Tanzania"},$u={coordinate:["48.379433","31.16558"],name:"Ukraine"},tc={coordinate:["1.373333","32.290275"],name:"Uganda"},ec={coordinate:["",""],name:"U.S. Minor Outlying Islands"},ic={coordinate:["37.09024","-95.712891"],name:"United States"},nc={coordinate:["-32.522779","-55.765835"],name:"Uruguay"},oc={coordinate:["41.377491","64.585262"],name:"Uzbekistan"},sc={coordinate:["41.902916","12.453389"],name:"Vatican City"},rc={coordinate:["12.984305","-61.287228"],name:"Saint Vincent and the Grenadines"},ac={coordinate:["6.42375","-66.58973"],name:"Venezuela"},hc={coordinate:["18.420695","-64.639968"],name:"British Virgin Islands"},uc={coordinate:["18.335765","-64.896335"],name:"U.S. Virgin Islands"},cc={coordinate:["14.058324","108.277199"],name:"Vietnam"},lc={coordinate:["-15.376706","166.959158"],name:"Vanuatu"},dc={coordinate:["-13.768752","-177.156097"],name:"Wallis and Futuna"},fc={coordinate:["-13.759029","-172.104629"],name:"Samoa"},_c={coordinate:["42.602636","20.902977"],name:"Kosovo"},mc={coordinate:["15.552727","48.516388"],name:"Yemen"},pc={coordinate:["-12.8275","45.166244"],name:"Mayotte"},gc={coordinate:["-30.559482","22.937506"],name:"South Africa"},vc={coordinate:["-13.133897","27.849332"],name:"Zambia"},yc={coordinate:["-19.015438","29.154857"],name:"Zimbabwe"},wc={AD:Vs,AE:Ks,AF:qs,AG:js,AI:Js,AL:Ys,AM:Xs,AN:Qs,AO:$s,AQ:tr,AR:er,AS:ir,AT:nr,AU:or,AW:sr,AZ:rr,BA:ar,BB:hr,BD:ur,BE:cr,BF:lr,BG:dr,BH:fr,BI:_r,BJ:mr,BM:pr,BN:gr,BO:vr,BR:yr,BS:wr,BT:xr,BV:Pr,BW:Lr,BY:br,BZ:Tr,CA:Mr,CC:Cr,CD:Sr,CF:Ar,CG:Er,CH:zr,CI:kr,CK:Or,CL:Ir,CM:Br,CN:Zr,CO:Nr,CR:Rr,CU:Dr,CV:Hr,CX:Fr,CY:Wr,CZ:Ur,DE:Gr,DJ:Vr,DK:Kr,DM:qr,DO:jr,DZ:Jr,EC:Yr,EE:Xr,EG:Qr,EH:$r,ER:ta,ES:ea,ET:ia,FI:na,FJ:oa,FK:sa,FM:ra,FO:aa,FR:ha,GA:ua,GB:ca,GD:la,GE:da,GF:fa,GG:_a,GH:ma,GI:pa,GL:ga,GM:va,GN:ya,GP:wa,GQ:xa,GR:Pa,GS:La,GT:ba,GU:Ta,GW:Ma,GY:Ca,GZ:Sa,HK:Aa,HM:Ea,HN:za,HR:ka,HT:Oa,HU:Ia,ID:Ba,IE:Za,IL:Na,IM:Ra,IN:Da,IO:Ha,IQ:Fa,IR:Wa,IS:Ua,IT:Ga,JE:Va,JM:Ka,JO:qa,JP:ja,KE:Ja,KG:Ya,KH:Xa,KI:Qa,KM:$a,KN:th,KP:eh,KR:ih,KW:nh,KY:oh,KZ:sh,LA:rh,LB:ah,LC:hh,LI:uh,LK:ch,LR:lh,LS:dh,LT:fh,LU:_h,LV:mh,LY:ph,MA:gh,MC:vh,MD:yh,ME:wh,MG:xh,MH:Ph,MK:Lh,ML:bh,MM:Th,MN:Mh,MO:Ch,MP:Sh,MQ:Ah,MR:Eh,MS:zh,MT:kh,MU:Oh,MV:Ih,MW:Bh,MX:Zh,MY:Nh,MZ:Rh,NA:Dh,NC:Hh,NE:Fh,NF:Wh,NG:Uh,NI:Gh,NL:Vh,NO:Kh,NP:qh,NR:jh,NU:Jh,NZ:Yh,OM:Xh,PA:Qh,PE:$h,PF:tu,PG:eu,PH:iu,PK:nu,PL:ou,PM:su,PN:ru,PR:au,PS:hu,PT:uu,PW:cu,PY:lu,QA:du,RE:fu,RO:_u,RS:mu,RU:pu,RW:gu,SA:vu,SB:yu,SC:wu,SD:xu,SE:Pu,SG:Lu,SH:bu,SI:Tu,SJ:Mu,SK:Cu,SL:Su,SM:Au,SN:Eu,SO:zu,SR:ku,ST:Ou,SV:Iu,SY:Bu,SZ:Zu,TC:Nu,TD:Ru,TF:Du,TG:Hu,TH:Fu,TJ:Wu,TK:Uu,TL:Gu,TM:Vu,TN:Ku,TO:qu,TR:ju,TT:Ju,TV:Yu,TW:Xu,TZ:Qu,UA:$u,UG:tc,UM:ec,US:ic,UY:nc,UZ:oc,VA:sc,VC:rc,VE:ac,VG:hc,VI:uc,VN:cc,VU:lc,WF:dc,WS:fc,XK:_c,YE:mc,YT:pc,ZA:gc,ZM:vc,ZW:yc};var ui,Zn;function xc(){return Zn||(Zn=1,ui=wc),ui}var Pc=xc();const Tc=Nn(Pc);As.Icon.Default.mergeOptions({iconRetinaUrl:Ws,iconUrl:Us,shadowUrl:Gs});const Mc=({geoData:d})=>xt.jsxs(Ds,{center:[20,0],zoom:2,style:{height:"400px",width:"100%"},className:"rounded-lg",children:[xt.jsx(Fs,{url:"https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png",attribution:'© <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'}),d.map((l,a)=>xt.jsx(Ns,{center:[l.lat,l.lon],radius:Math.max(5,Math.min(20,l.count/10)),fillColor:"blue",color:"blue",weight:1,opacity:.8,fillOpacity:.6,children:xt.jsx(Hs,{children:xt.jsxs("div",{children:[xt.jsx("strong",{children:l.region}),l.country&&xt.jsxs("div",{children:["Country: ",l.country]}),xt.jsxs("div",{children:["Visitors: ",l.count]})]})})},a))]});export{Tc as IsoToLatLong,Mc as default,Ac as geocodeRegion};
