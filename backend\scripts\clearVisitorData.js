import mongoose from 'mongoose';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Import models
import Visitor from '../src/components/visitor/visitorModel.js';
import Analytics from '../src/components/analytics/analyticsModel.js';

const clearVisitorData = async () => {
  try {
    // Connect to MongoDB
    await mongoose.connect(process.env.MONGO_URI);
    console.log('Connected to MongoDB');

    // Clear visitor data
    const visitorResult = await Visitor.deleteMany({});
    console.log(`Deleted ${visitorResult.deletedCount} visitor records`);

    // Clear analytics data
    const analyticsResult = await Analytics.deleteMany({});
    console.log(`Deleted ${analyticsResult.deletedCount} analytics records`);

    console.log('✅ All visitor and analytics data cleared successfully');
    
    // Close connection
    await mongoose.connection.close();
    console.log('Database connection closed');
    
  } catch (error) {
    console.error('❌ Error clearing data:', error);
    process.exit(1);
  }
};

// Run the script
clearVisitorData();
