import { useState, useEffect } from "react";
import { Link, useLocation } from "react-router-dom";
import { Menu, X, ChevronDown, Megaphone, Search, Target, ChevronRight, Briefcase, Globe, Star, Layers, Code, Users, Settings, Sparkles } from "lucide-react";
import { cn } from "@/core/utils/utils";
import { useLanguage } from "@/core/providers/LanguageContext";
import LanguageSwitcher from "./LanguageSwitcher";
import UserAuthModal from "./UserAuthModal";
import AuthUserForm from "./AuthUserForm";
import axios from "axios";
import { useAuth } from "@/core/providers/AuthContext";
import { useUserAuthModal } from "@/core/contexts/UserAuthModalContext";
import { useAppSelector, useAppDispatch } from "@/store/hooks";
import { logout as reduxLogout } from "@/store/authSlice";
import { getServices } from "@/shared/services/service.service";
import { Service } from "@/core/types";
import { useClickTracking } from "@/core/providers/ClickTrackingContext";
import { useAutoScroll } from "@/shared/hooks/use-auto-scroll";
import CartIcon from "./CartIcon";
import CartDrawer from "./CartDrawer";

const Navigation = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [isScrolled, setIsScrolled] = useState(false);
  const [isServicesOpen, setIsServicesOpen] = useState(false);
  const [showRegister, setShowRegister] = useState(false);
  const [isCartOpen, setIsCartOpen] = useState(false);
  const { isOpen: showUserAuthModal, openModal: openUserAuthModal, closeModal: closeUserAuthModal, message: authMessage } = useUserAuthModal();
  const location = useLocation();
  const { t, isRTL } = useLanguage();
  const { user: authUser, isAuthenticated, logout } = useAuth();
  const reduxUser = useAppSelector((state) => state.auth.user);
  const reduxIsAuthenticated = useAppSelector((state) => state.auth.isAuthenticated);
  const dispatch = useAppDispatch();
  const [services, setServices] = useState<Service[]>([]);
  const { trackClick } = useClickTracking();
  const { scrollToTop } = useAutoScroll();

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 50);
    };

    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);

  useEffect(() => {
    setIsOpen(false);
    setIsServicesOpen(false);
    // Fetch services for nav dropdown
    getServices({ limit: 100 }).then(res => setServices(res.data)).catch(() => setServices([]));
  }, [location.pathname]);

  const navItems = [
    { label: t("nav.home"), href: "/" },
    { label: t("nav.about"), href: "/about" },
    { label: t("nav.services"), href: "/services" },
    { label: t("nav.blog"), href: "/blog" },
    { label: t("nav.contact"), href: "/contact" },
  ];

  return (
    <nav
      className={cn(
        "fixed top-0 left-0 right-0 z-50 transition-all duration-300",
        isScrolled
          ? "bg-brand-black/90 blur-background shadow-lg"
          : "bg-transparent",
      )}
    >
      <div className="container-custom">
        <div
          className={cn(
            "flex items-center justify-between h-24 md:h-28 transition-all duration-300",
            isRTL && "flex-row-reverse",
          )}
        >
          {/* Logo */}
          <Link
            to="/"
            className="flex items-center gap-3 text-3xl font-extrabold text-brand-white hover:text-brand-accent-100 transition-colors drop-shadow-lg"
            onClick={() => {
              scrollToTop();
              trackClick({ pageName: 'Navigation', buttonLabel: 'Logo' });
            }}
          >
            <img
              src="/logo.jpg"
              alt="Click4You Logo"
              className="h-12 w-12 object-contain"
              style={{ minWidth: '3rem' }}
            />
            <span className="hidden md:inline-block text-2xl font-bold bg-gradient-to-r from-brand-accent-100 to-brand-accent-200 bg-clip-text text-transparent">
              Click<span className="text-brand-white">4You</span>
            </span>
          </Link>

          {/* Desktop Navigation */}
          <div
            className={cn(
              "hidden md:flex items-center space-x-10 text-lg font-semibold",
              isRTL && "space-x-reverse",
            )}
          >
            {navItems.map((item) => {
              if (item.label === t("nav.services")) {
                return (
                  <div
                    key={item.href}
                    className="relative group"
                    onMouseEnter={() => setIsServicesOpen(true)}
                    onMouseLeave={() => setIsServicesOpen(false)}
                  >
                    <Link
                      to={item.href}
                      className={cn(
                        "flex items-center text-lg font-semibold transition-colors hover:text-brand-accent-100 px-3 py-2 rounded-xl hover:bg-brand-grey-800/60",
                        location.pathname === item.href
                          ? "text-brand-accent-100 bg-brand-grey-800/80 shadow"
                          : "text-brand-grey-300",
                      )}
                      onClick={() => {
                        scrollToTop();
                        trackClick({ pageName: 'Navigation', buttonLabel: item.label });
                      }}
                    >
                      {item.label}
                      <ChevronDown
                        size={16}
                        className={cn(
                          "ml-1 transition-transform duration-200",
                          isServicesOpen ? "rotate-180" : "rotate-0",
                        )}
                      />
                    </Link>
                    {/* Professional, minimal dropdown */}
                    <div
                      className={cn(
                        // Main dropdown width increased for professional look
                        "absolute top-full mt-2 w-80 md:w-96 transition-all duration-300 ease-out z-50",
                        isRTL
                          ? "right-0"
                          : "left-1/2 transform -translate-x-1/2",
                        isServicesOpen
                          ? "opacity-100 visible translate-y-0"
                          : "opacity-0 invisible -translate-y-2",
                      )}
                    >
                      <div className="bg-brand-grey-900/95 blur-background border border-brand-grey-700 rounded-xl shadow-2xl overflow-hidden">
                        <ul className="py-2">
                          {services.map((service) => (
                            <li key={service.id} className="relative group">
                              <Link
                                to={`/services/${service.slug}`}
                                className="flex items-center gap-3 px-5 py-2 text-brand-white hover:bg-brand-grey-800 hover:text-brand-accent-100 text-base font-bold tracking-wide transition-colors rounded-lg border-l-4 border-transparent group-hover:border-brand-accent-100"
                                style={{ minWidth: 180 }}
                                onClick={() => {
                                  scrollToTop();
                                  trackClick({ pageName: 'Navigation', buttonLabel: service.title });
                                }}
                              >
                                {/* Service Icon/Logo */}
                                {service.image ? (
                                  <img src={service.image} alt={service.title} className="w-7 h-7 object-cover rounded shadow border border-brand-grey-700 bg-brand-white" />
                                ) : (
                                  // Pick an icon based on service title or fallback
                                  <span className="w-7 h-7 flex items-center justify-center bg-brand-grey-800 rounded border border-brand-grey-700">
                                    {service.title.toLowerCase().includes("seo") ? <Globe size={18} /> :
                                     service.title.toLowerCase().includes("marketing") ? <Star size={18} /> :
                                     service.title.toLowerCase().includes("strategy") ? <Layers size={18} /> :
                                     service.title.toLowerCase().includes("development") ? <Code size={18} /> :
                                     service.title.toLowerCase().includes("team") ? <Users size={18} /> :
                                     service.title.toLowerCase().includes("admin") ? <Settings size={18} /> :
                                     <Briefcase size={18} />}
                                  </span>
                                )}
                                <span>{service.title}</span>
                                {/* Chevron if sub-services exist */}
                                {service.subServices && service.subServices.length > 0 && (
                                  <ChevronRight size={16} className="ml-auto text-brand-accent-100" />
                                )}
                              </Link>
                              {/* Sub-Services Nested Dropdown */}
                              {service.subServices && service.subServices.length > 0 && (
                                <div className="absolute left-full top-0 ml-2 w-64 bg-brand-grey-900 border border-brand-grey-700 rounded-xl shadow-xl opacity-0 invisible group-hover:opacity-100 group-hover:visible pointer-events-auto transition-all duration-300 z-[9999]">
                                  <ul className="py-2">
                                    {service.subServices.map((sub, idx) => (
                                      <li key={idx}>
                                        <span className="block px-5 py-2 text-brand-grey-200 hover:bg-brand-grey-800 hover:text-brand-accent-100 text-sm font-medium transition-colors rounded-lg cursor-default">
                                          {sub.title}
                                        </span>
                                      </li>
                                    ))}
                                  </ul>
                                </div>
                              )}
                            </li>
                          ))}
                        </ul>
                        <div className="bg-brand-grey-800 p-3 border-t border-brand-grey-700">
                          <Link
                            to="/services"
                            className={cn(
                              "block text-center text-brand-white text-sm font-medium hover:text-brand-grey-300 transition-colors",
                              isRTL && "text-right",
                            )}
                            onClick={() => trackClick({ pageName: 'Navigation', buttonLabel: t("services.viewAll") })}
                          >
                            {t("services.viewAll")} {isRTL ? "←" : "→"}
                          </Link>
                        </div>
                      </div>
                    </div>
                  </div>
                );
              }

              return (
                <Link
                  key={item.href}
                  to={item.href}
                  className={cn(
                    "text-lg font-semibold transition-colors hover:text-brand-accent-100 px-3 py-2 rounded-xl hover:bg-brand-grey-800/60",
                    location.pathname === item.href
                      ? "text-brand-accent-100 bg-brand-grey-800/80 shadow"
                      : "text-brand-grey-300",
                  )}
                  onClick={() => {
                    scrollToTop();
                    trackClick({ pageName: 'Navigation', buttonLabel: item.label });
                  }}
                >
                  {item.label}
                </Link>
              );
            })}
          </div>

          {/* Language Switcher & CTA Button */}
          <div
            className={cn(
              "hidden md:flex items-center space-x-6",
              isRTL && "space-x-reverse",
            )}
          >
            <LanguageSwitcher />

            {/* Cart Icon */}
            <CartIcon
              onClick={() => setIsCartOpen(true)}
              className="hover:scale-110 transition-transform"
            />

            {(reduxIsAuthenticated && reduxUser) ? (
              <button
                className="btn-primary shadow-lg text-lg px-8 py-3 rounded-xl font-bold bg-brand-accent-100 hover:bg-brand-white text-brand-black transition-all duration-200"
                onClick={async () => {
                  await logout();
                  dispatch(reduxLogout());
                  trackClick({ pageName: 'Navigation', buttonLabel: 'Logout' });

                  // Dispatch logout event for cart and other components
                  setTimeout(() => {
                    window.dispatchEvent(new CustomEvent('userLoggedOut'));
                  }, 100);
                }}
                type="button"
              >
                Logout
              </button>
            ) : (
              <>
                <button
                  className="btn-primary shadow-lg text-lg px-8 py-3 rounded-xl font-bold bg-brand-accent-100 hover:bg-brand-white text-brand-black transition-all duration-200"
                  onClick={() => {
                    openUserAuthModal();
                    trackClick({ pageName: 'Navigation', buttonLabel: t("nav.getStarted") });
                  }}
                  type="button"
                >
                  {t("nav.getStarted")}
                </button>
                {showUserAuthModal && (
                  <div
                    className="modal-overlay bg-black/70 backdrop-blur-md animate-fade-in"
                    onClick={(e) => {
                      if (e.target === e.currentTarget) {
                        closeUserAuthModal();
                      }
                    }}
                  >
                    <div className="modal-content bg-white rounded-2xl sm:rounded-3xl shadow-2xl animate-slide-in-up">
                      {/* Enhanced Header with Purple Theme */}
                      <div className="bg-gradient-to-r from-brand-purple-600 to-brand-purple-700 px-4 sm:px-6 lg:px-8 py-4 sm:py-6 text-white sticky top-0 z-10 overflow-hidden">
                        <div className="absolute inset-0 bg-gradient-to-r from-brand-purple-500/20 to-brand-purple-600/20 animate-pulse"></div>
                        <button
                          className="absolute top-3 sm:top-4 right-3 sm:right-4 text-white/70 hover:text-white text-xl sm:text-2xl transition-all duration-300 hover:bg-white/10 rounded-full w-7 h-7 sm:w-8 sm:h-8 flex items-center justify-center hover:scale-110 z-10"
                          onClick={closeUserAuthModal}
                          aria-label="Close"
                        >
                          &times;
                        </button>
                        <div className="relative z-10">
                          <h2 className="text-lg sm:text-xl lg:text-2xl font-bold pr-8 sm:pr-10 flex items-center gap-2">
                            <Sparkles className="w-5 h-5 sm:w-6 sm:h-6" />
                            Welcome to ClickForYou
                          </h2>
                          <p className="text-white/90 mt-1 text-xs sm:text-sm lg:text-base">
                            {authMessage || "Join thousands of businesses growing with us"}
                          </p>
                        </div>
                      </div>

                      {/* Content */}
                      <div className="p-4 sm:p-6 lg:p-8 overflow-y-auto max-h-[calc(90vh-8rem)]">
                        <AuthUserForm onSuccess={closeUserAuthModal} />
                      </div>
                    </div>
                  </div>
                )}
              </>
            )}
          </div>

          {/* Mobile Menu Button */}
          <button
            onClick={() => setIsOpen(!isOpen)}
            className="md:hidden text-brand-white hover:text-brand-accent-100 transition-colors"
            aria-label="Toggle menu"
          >
            {isOpen ? <X size={24} /> : <Menu size={24} />}
          </button>
        </div>

        {/* Mobile Navigation */}
        {isOpen && (
          <div className="md:hidden bg-brand-black/95 blur-background border-t border-brand-grey-700 rounded-b-2xl shadow-2xl">
            <div className="px-2 pt-2 pb-3 space-y-1">
              {navItems.map((item) => {
                if (item.label === "Services") {
                  return (
                    <div key={item.href} className="space-y-1">
                      <button
                        onClick={() => setIsServicesOpen(!isServicesOpen)}
                        className={cn(
                          "flex items-center justify-between px-3 py-2 text-base font-medium transition-colors hover:text-brand-accent-100 w-full text-left",
                          location.pathname === item.href
                            ? "text-brand-white"
                            : "text-brand-grey-500",
                          isRTL && "flex-row-reverse text-right",
                        )}
                      >
                        {item.label}
                        <ChevronDown
                          size={16}
                          className={cn(
                            "transition-transform duration-200",
                            isServicesOpen ? "rotate-180" : "rotate-0",
                          )}
                        />
                      </button>
                      {isServicesOpen && (
                        <div
                          className={cn(
                            "ml-4 space-y-1 animate-fade-in",
                            isRTL && "mr-4 ml-0",
                          )}
                        >
                          {services.map((service) => (
                            <div key={service.id} className="space-y-1">
                              <Link
                                to={`/services/${service.slug}`}
                                className={cn(
                                  "flex items-center space-x-3 px-3 py-2 text-sm text-brand-grey-400 hover:text-brand-white transition-colors",
                                  isRTL && "space-x-reverse",
                                )}
                                onClick={() => {
                                  scrollToTop();
                                  setIsOpen(false); // Close mobile menu
                                  trackClick({ pageName: 'Navigation', buttonLabel: service.title });
                                }}
                              >
                                <div className="w-6 h-6 bg-brand-white rounded flex items-center justify-center flex-shrink-0">
                                  {service.image ? (
                                    <img src={service.image} alt={service.title} className="w-5 h-5 object-cover rounded" />
                                  ) : (
                                    <Megaphone size={16} className="text-brand-black" />
                                  )}
                                </div>
                                <span>{service.title}</span>
                              </Link>
                              {service.subServices && service.subServices.length > 0 && (
                                <div className="ml-4 space-y-1 animate-fade-in">
                                  {service.subServices.map((sub) => (
                                    <Link
                                      key={sub.id}
                                      to={`/services/${sub.slug}`}
                                      className="flex items-center space-x-3 px-3 py-2 text-xs text-brand-grey-300 hover:text-brand-white transition-colors"
                                      onClick={() => trackClick({ pageName: 'Navigation', buttonLabel: sub.title })}
                                    >
                                      <div className="w-4 h-4 bg-brand-white rounded flex items-center justify-center flex-shrink-0">
                                        <ChevronDown size={12} className="text-brand-black" />
                                      </div>
                                      <span>{sub.title}</span>
                                    </Link>
                                  ))}
                                </div>
                              )}
                            </div>
                          ))}
                        </div>
                      )}
                    </div>
                  );
                }

                return (
                  <Link
                    key={item.href}
                    to={item.href}
                    className={cn(
                      "block px-3 py-3 text-lg font-semibold transition-colors hover:text-brand-accent-100 hover:bg-brand-grey-800/60 rounded-xl",
                      location.pathname === item.href
                        ? "text-brand-accent-100 bg-brand-grey-800/80 shadow"
                        : "text-brand-grey-300",
                    )}
                    onClick={() => {
                      scrollToTop();
                      setIsOpen(false); // Close mobile menu
                      trackClick({ pageName: 'Navigation', buttonLabel: item.label });
                    }}
                  >
                    {item.label}
                  </Link>
                );
              })}
              <div className="px-3 py-2 space-y-3">
                <div className="flex justify-center items-center gap-4">
                  <LanguageSwitcher />
                  <CartIcon
                    onClick={() => {
                      setIsCartOpen(true);
                      setIsOpen(false);
                    }}
                    className="hover:scale-110 transition-transform"
                  />
                </div>
                <button
                  className="btn-primary shadow-lg text-lg px-8 py-3 rounded-xl font-bold bg-brand-accent-100 hover:bg-brand-white text-brand-black transition-all duration-200 w-full text-center inline-block"
                  onClick={() => {
                    openUserAuthModal();
                    setIsOpen(false);
                    trackClick({ pageName: 'Navigation', buttonLabel: t("nav.getStarted") });
                  }}
                  type="button"
                >
                  {t("nav.getStarted")}
                </button>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Cart Drawer */}
      <CartDrawer
        isOpen={isCartOpen}
        onClose={() => setIsCartOpen(false)}
      />
    </nav>
  );
};

export default Navigation;
