import{r,j as e}from"./react-vendor-Dq0qSR31.js";import{H as _}from"./utils-vendor-DSNVchvY.js";import"./vendor-OXu-rwpf.js";const O=({refreshTrigger:k})=>{const[m,P]=r.useState([]),[h,j]=r.useState(!0),[N,f]=r.useState(null),[n,v]=r.useState(1),[i,A]=r.useState(1),[d,D]=r.useState("lastVisit"),[l,w]=r.useState("desc"),[c,I]=r.useState(""),[x,T]=r.useState(""),[t,b]=r.useState(null),[$,p]=r.useState(!1),[L,g]=r.useState(!1),[R,u]=r.useState(!1),U=async()=>{try{j(!0);const s=new URLSearchParams({page:n.toString(),limit:"20",sortBy:d,sortOrder:l,...c&&{leadStatus:c},...x&&{businessDomain:x}}),a=await _.get(`/api/visitor/list?${s}`);console.log("Visitor API Response:",a.data),console.log("Visitors array:",a.data.visitors),console.log("First visitor:",a.data.visitors?.[0]),P(a.data.visitors||[]),A(a.data.totalPages||1),f(null)}catch(s){f("Failed to fetch visitor data"),console.error("Visitor fetch error:",s)}finally{j(!1)}};r.useEffect(()=>{U()},[n,d,l,c,x,k]);const S=s=>{d===s?w(l==="asc"?"desc":"asc"):(D(s),w("desc"))},q=s=>({new:"bg-blue-100 text-blue-800",in_progress:"bg-yellow-100 text-yellow-800",qualified:"bg-green-100 text-green-800",converted:"bg-purple-100 text-purple-800",lost:"bg-red-100 text-red-800"})[s]||"bg-gray-100 text-gray-800",M=s=>({customer:"bg-purple-100 text-purple-800",hot_lead:"bg-red-100 text-red-800",warm_lead:"bg-orange-100 text-orange-800",returning_visitor:"bg-blue-100 text-blue-800",new_visitor:"bg-gray-100 text-gray-800"})[s]||"bg-gray-100 text-gray-800",C=s=>{const a=Math.floor(s/6e4),y=Math.floor(s%6e4/1e3);return`${a}m ${y}s`},o=s=>new Date(s).toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"}),E=s=>{if(!s.pageVisits||s.pageVisits.length===0)return"N/A";const a=s.pageVisits[s.pageVisits.length-1];return a.pageName||a.path||"Unknown"},V=s=>s.pageVisits?s.pageVisits.map(a=>({path:a.path,pageName:a.pageName||"Unknown Page",timestamp:a.timestamp,duration:a.duration,sessionId:a.sessionId})).sort((a,y)=>new Date(y.timestamp).getTime()-new Date(a.timestamp).getTime()):[],Q=s=>{b(s),p(!0)};return h?e.jsx("div",{className:"bg-brand-grey-900 border border-brand-grey-700 rounded-xl p-6",children:e.jsx("div",{className:"flex items-center justify-center h-32",children:e.jsx("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"})})}):N?e.jsx("div",{className:"bg-brand-grey-900 border border-brand-grey-700 rounded-xl p-6",children:e.jsx("div",{className:"text-red-500 text-center",children:N})}):e.jsxs("div",{className:"bg-brand-grey-900 border border-brand-grey-700 rounded-xl p-6",children:[e.jsxs("div",{className:"flex justify-between items-center mb-6",children:[e.jsx("h3",{className:"text-lg font-bold text-brand-white",children:"Visitor Analytics"}),e.jsxs("div",{className:"flex gap-4",children:[e.jsxs("select",{value:c,onChange:s=>I(s.target.value),className:"bg-brand-grey-800 text-brand-white border border-brand-grey-600 rounded px-3 py-1 text-sm",children:[e.jsx("option",{value:"",children:"All Statuses"}),e.jsx("option",{value:"new",children:"New"}),e.jsx("option",{value:"in_progress",children:"In Progress"}),e.jsx("option",{value:"qualified",children:"Qualified"}),e.jsx("option",{value:"converted",children:"Converted"}),e.jsx("option",{value:"lost",children:"Lost"})]}),e.jsxs("select",{value:x,onChange:s=>T(s.target.value),className:"bg-brand-grey-800 text-brand-white border border-brand-grey-600 rounded px-3 py-1 text-sm",children:[e.jsx("option",{value:"",children:"All Domains"}),e.jsx("option",{value:"technology",children:"Technology"}),e.jsx("option",{value:"healthcare",children:"Healthcare"}),e.jsx("option",{value:"finance",children:"Finance"}),e.jsx("option",{value:"education",children:"Education"}),e.jsx("option",{value:"retail",children:"Retail"}),e.jsx("option",{value:"other",children:"Other"})]})]})]}),e.jsx("div",{className:"overflow-x-auto bg-brand-grey-900 rounded-xl border border-brand-grey-700",children:e.jsxs("table",{className:"min-w-full table-auto",children:[e.jsx("thead",{className:"bg-brand-grey-800 sticky top-0 z-10",children:e.jsxs("tr",{className:"text-brand-white text-left border-b-2 border-brand-purple-500",children:[e.jsxs("th",{className:"py-4 px-6 font-semibold text-sm",children:[e.jsx("div",{className:"text-brand-purple-300",children:"Visitor Identity"}),e.jsx("div",{className:"text-xs text-brand-grey-400 font-normal",children:"IP & Location"})]}),e.jsxs("th",{className:"py-4 px-6 font-semibold text-sm",children:[e.jsx("div",{className:"text-brand-blue-300",children:"Visit Information"}),e.jsx("div",{className:"text-xs text-brand-grey-400 font-normal",children:"Time & Pages"})]}),e.jsxs("th",{className:"py-4 px-6 font-semibold text-sm cursor-pointer hover:bg-brand-grey-700 transition-colors rounded",onClick:()=>S("leadScore"),children:[e.jsxs("div",{className:"text-brand-yellow-300",children:["Lead Score ",d==="leadScore"&&(l==="asc"?"↑":"↓")]}),e.jsx("div",{className:"text-xs text-brand-grey-400 font-normal",children:"Quality Rating"})]}),e.jsxs("th",{className:"py-4 px-6 font-semibold text-sm",children:[e.jsx("div",{className:"text-brand-green-300",children:"Lead Status"}),e.jsx("div",{className:"text-xs text-brand-grey-400 font-normal",children:"Classification"})]}),e.jsxs("th",{className:"py-4 px-6 font-semibold text-sm",children:[e.jsx("div",{className:"text-brand-orange-300",children:"Business Profile"}),e.jsx("div",{className:"text-xs text-brand-grey-400 font-normal",children:"Domain & Segment"})]}),e.jsxs("th",{className:"py-4 px-6 font-semibold text-sm cursor-pointer hover:bg-brand-grey-700 transition-colors rounded",onClick:()=>S("totalVisits"),children:[e.jsxs("div",{className:"text-brand-cyan-300",children:["Activity Stats ",d==="totalVisits"&&(l==="asc"?"↑":"↓")]}),e.jsx("div",{className:"text-xs text-brand-grey-400 font-normal",children:"Visits & Engagement"})]}),e.jsxs("th",{className:"py-4 px-6 font-semibold text-sm",children:[e.jsx("div",{className:"text-brand-pink-300",children:"User Account"}),e.jsx("div",{className:"text-xs text-brand-grey-400 font-normal",children:"Registration Status"})]})]})}),e.jsx("tbody",{children:!m||m.length===0?e.jsx("tr",{children:e.jsx("td",{colSpan:7,className:"py-12 text-center text-brand-grey-400",children:e.jsxs("div",{className:"space-y-2",children:[e.jsx("div",{className:"text-lg",children:h?"Loading visitors...":"No visitors found"}),!h&&e.jsx("div",{className:"text-sm",children:"Visitor data will appear here once users start browsing your site."})]})})}):m.map(s=>!s||typeof s!="object"?(console.error("Invalid visitor object:",s),null):e.jsxs("tr",{className:"border-b border-brand-grey-700 hover:bg-brand-grey-800/50 transition-colors",children:[e.jsx("td",{className:"py-4 px-6",children:e.jsxs("div",{className:"space-y-1",children:[e.jsx("div",{className:"text-brand-white font-mono text-sm",children:s.ipAddress}),e.jsx("div",{className:"text-brand-grey-400 text-xs",children:s.location.region&&s.location.country?`${s.location.region}, ${s.location.country}`:s.location.country||"Unknown Location"})]})}),e.jsx("td",{className:"py-4 px-6",children:e.jsxs("div",{className:"space-y-2",children:[e.jsx("div",{className:"text-brand-white text-sm",children:o(s.lastVisit)}),e.jsxs("div",{className:"text-brand-grey-400 text-xs",children:["Last: ",E(s)]}),e.jsxs("button",{onClick:()=>Q(s),className:"bg-brand-blue-600 hover:bg-brand-blue-700 text-white px-3 py-1 rounded text-xs font-medium transition-colors",children:["View Pages (",s.totalPageViews||0,")"]})]})}),e.jsx("td",{className:"py-4 px-6",children:e.jsxs("div",{className:"space-y-2",children:[e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx("div",{className:"w-20 bg-brand-grey-700 rounded-full h-3",children:e.jsx("div",{className:`h-3 rounded-full transition-all duration-300 ${s.leadScore>=80?"bg-green-500":s.leadScore>=60?"bg-yellow-500":s.leadScore>=40?"bg-orange-500":"bg-red-500"}`,style:{width:`${Math.min(s.leadScore,100)}%`}})}),e.jsx("span",{className:"text-brand-white text-lg font-bold",children:s.leadScore})]}),e.jsx("div",{className:"text-xs text-brand-grey-400",children:s.leadScore>=80?"Excellent":s.leadScore>=60?"Good":s.leadScore>=40?"Fair":"Low"})]})}),e.jsx("td",{className:"py-4 px-6",children:e.jsxs("div",{className:"space-y-2",children:[e.jsx("span",{className:`px-3 py-1 rounded-full text-sm font-medium ${q(s.leadStatus)}`,children:s.leadStatus.replace("_"," ")}),e.jsx("div",{className:"text-xs",children:e.jsx("span",{className:`px-2 py-1 rounded text-xs ${M(s.visitorType)}`,children:s.visitorType.replace("_"," ")})})]})}),e.jsx("td",{className:"py-4 px-6",children:e.jsxs("div",{className:"space-y-2",children:[e.jsx("div",{className:"text-brand-white text-sm font-medium",children:e.jsx("span",{className:"px-3 py-1 bg-brand-cyan-900 text-brand-cyan-300 rounded-lg text-xs",children:s.businessDomain||"Not specified"})}),e.jsx("div",{className:"text-brand-grey-400 text-xs",children:e.jsx("span",{className:"px-3 py-1 bg-brand-emerald-900 text-brand-emerald-300 rounded-lg text-xs",children:s.clientSegment||"Not specified"})})]})}),e.jsx("td",{className:"py-4 px-6",children:e.jsxs("div",{className:"space-y-3",children:[e.jsxs("div",{className:"flex items-center space-x-4",children:[e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"text-brand-white text-lg font-bold",children:s.totalVisits}),e.jsx("div",{className:"text-xs text-brand-grey-400",children:"Visits"})]}),e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"text-brand-blue-300 text-sm font-medium",children:s.totalPageViews||0}),e.jsx("div",{className:"text-xs text-brand-grey-400",children:"Pages"})]}),e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"text-brand-grey-300 text-sm",children:s.totalClicks||0}),e.jsx("div",{className:"text-xs text-brand-grey-400",children:"Clicks"})]})]}),e.jsxs("div",{className:"flex items-center space-x-4",children:[e.jsxs("div",{className:"text-center cursor-pointer hover:bg-brand-purple-900/20 p-2 rounded transition-colors",onClick:()=>{b(s),g(!0)},title:"Click to view quote requests",children:[e.jsx("div",{className:"text-brand-purple-300 text-sm font-bold",children:s.totalQuoteRequests||0}),e.jsx("div",{className:"text-xs text-brand-purple-400",children:"Quotes"})]}),e.jsxs("div",{className:"text-center cursor-pointer hover:bg-brand-green-900/20 p-2 rounded transition-colors",onClick:()=>{b(s),u(!0)},title:"Click to view cart actions",children:[e.jsx("div",{className:"text-brand-green-300 text-sm font-bold",children:s.totalCartActions||0}),e.jsx("div",{className:"text-xs text-brand-green-400",children:"Cart"})]})]}),e.jsxs("div",{className:"text-xs text-brand-grey-400 text-center",children:["Avg: ",C(s.totalTimeSpent/(s.totalVisits||1))]})]})}),e.jsx("td",{className:"py-4 px-6",children:e.jsx("div",{className:"text-center",children:s.visitorName&&s.visitorEmail?e.jsxs("div",{className:"space-y-1",children:[e.jsx("div",{className:"text-brand-white font-medium text-sm",children:s.visitorName}),e.jsx("div",{className:"text-brand-grey-400 text-xs",children:s.visitorEmail}),e.jsx("div",{className:"inline-block px-2 py-1 bg-brand-green-900 text-brand-green-300 rounded-full text-xs",children:"Registered"})]}):e.jsxs("div",{className:"space-y-1",children:[e.jsx("div",{className:"text-brand-grey-500 text-sm",children:"Anonymous"}),e.jsx("div",{className:"inline-block px-2 py-1 bg-brand-grey-700 text-brand-grey-400 rounded-full text-xs",children:"Guest"})]})})})]},s._id))})]})}),i>1&&e.jsxs("div",{className:"flex justify-center items-center mt-6 space-x-2",children:[e.jsx("button",{onClick:()=>v(Math.max(1,n-1)),disabled:n===1,className:"px-3 py-1 bg-brand-grey-800 text-brand-white rounded disabled:opacity-50",children:"Previous"}),e.jsxs("span",{className:"text-brand-grey-300",children:["Page ",n," of ",i]}),e.jsx("button",{onClick:()=>v(Math.min(i,n+1)),disabled:n===i,className:"px-3 py-1 bg-brand-grey-800 text-brand-white rounded disabled:opacity-50",children:"Next"})]}),$&&t&&e.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:e.jsxs("div",{className:"bg-brand-grey-900 border border-brand-grey-700 rounded-xl p-6 max-w-4xl w-full mx-4 max-h-[80vh] overflow-hidden",children:[e.jsxs("div",{className:"flex justify-between items-center mb-4",children:[e.jsxs("h3",{className:"text-lg font-bold text-brand-white",children:["Pages Visited - ",t.visitorName||t.ipAddress]}),e.jsx("button",{onClick:()=>p(!1),className:"text-brand-grey-400 hover:text-brand-white text-xl",children:"×"})]}),e.jsxs("div",{className:"mb-4 text-brand-grey-300 text-sm",children:[e.jsxs("div",{children:["Total Visits: ",t.totalVisits]}),e.jsxs("div",{children:["Total Pages Viewed: ",t.totalPageViews||0]}),e.jsxs("div",{children:["User: ",t.userId?`${t.userId.name} (${t.userId.email})`:"Anonymous"]})]}),e.jsx("div",{className:"overflow-y-auto max-h-96",children:e.jsxs("table",{className:"min-w-full",children:[e.jsx("thead",{className:"sticky top-0 bg-brand-grey-900",children:e.jsxs("tr",{className:"text-brand-white text-left border-b border-brand-grey-700",children:[e.jsx("th",{className:"py-2 px-3",children:"Date & Time"}),e.jsx("th",{className:"py-2 px-3",children:"Page"}),e.jsx("th",{className:"py-2 px-3",children:"Path"}),e.jsx("th",{className:"py-2 px-3",children:"Duration"})]})}),e.jsxs("tbody",{children:[V(t).map((s,a)=>e.jsxs("tr",{className:"border-b border-brand-grey-700 hover:bg-brand-grey-800",children:[e.jsx("td",{className:"py-2 px-3 text-brand-grey-300 text-sm",children:o(s.timestamp)}),e.jsx("td",{className:"py-2 px-3 text-brand-white text-sm",children:s.pageName||"Unknown Page"}),e.jsx("td",{className:"py-2 px-3 text-brand-grey-300 text-sm font-mono",children:s.path}),e.jsx("td",{className:"py-2 px-3 text-brand-grey-300 text-sm",children:s.duration?C(s.duration):"N/A"})]},a)),V(t).length===0&&e.jsx("tr",{children:e.jsx("td",{colSpan:4,className:"py-4 px-3 text-center text-brand-grey-500",children:"No page data available"})})]})]})}),e.jsx("div",{className:"mt-4 flex justify-end",children:e.jsx("button",{onClick:()=>p(!1),className:"bg-brand-grey-800 hover:bg-brand-grey-700 text-brand-white px-4 py-2 rounded",children:"Close"})})]})}),L&&t&&e.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:e.jsxs("div",{className:"bg-brand-grey-900 border border-brand-grey-700 rounded-xl p-6 max-w-4xl w-full mx-4 max-h-[80vh] overflow-hidden",children:[e.jsxs("div",{className:"flex justify-between items-center mb-4",children:[e.jsxs("h3",{className:"text-lg font-bold text-brand-white",children:["Quote Requests - ",t.visitorName||t.ipAddress]}),e.jsx("button",{onClick:()=>g(!1),className:"text-brand-grey-400 hover:text-brand-white text-xl",children:"×"})]}),e.jsxs("div",{className:"mb-4 text-brand-grey-300 text-sm",children:[e.jsxs("div",{children:["Total Quote Requests: ",t.totalQuoteRequests||0]}),e.jsxs("div",{children:["User: ",t.userId?`${t.userId.name} (${t.userId.email})`:"Anonymous"]}),e.jsxs("div",{children:["Lead Score: ",e.jsx("span",{className:"text-brand-purple-300 font-medium",children:t.leadScore})]})]}),e.jsx("div",{className:"overflow-y-auto max-h-96",children:t.quoteRequests&&t.quoteRequests.length>0?e.jsxs("table",{className:"min-w-full",children:[e.jsx("thead",{className:"sticky top-0 bg-brand-grey-900",children:e.jsxs("tr",{className:"text-brand-white text-left border-b border-brand-grey-700",children:[e.jsx("th",{className:"py-2 px-3",children:"Date & Time"}),e.jsx("th",{className:"py-2 px-3",children:"Service"}),e.jsx("th",{className:"py-2 px-3",children:"Sub-Service"}),e.jsx("th",{className:"py-2 px-3",children:"Description"}),e.jsx("th",{className:"py-2 px-3",children:"Page"})]})}),e.jsx("tbody",{children:t.quoteRequests.map((s,a)=>e.jsxs("tr",{className:"border-b border-brand-grey-700 hover:bg-brand-grey-800",children:[e.jsx("td",{className:"py-2 px-3 text-brand-grey-300 text-sm",children:o(s.timestamp)}),e.jsx("td",{className:"py-2 px-3 text-brand-white text-sm font-medium",children:s.serviceName}),e.jsx("td",{className:"py-2 px-3 text-brand-purple-300 text-sm font-medium",children:s.subServiceName}),e.jsx("td",{className:"py-2 px-3 text-brand-grey-300 text-sm",children:s.subServiceDescription||"No description"}),e.jsx("td",{className:"py-2 px-3 text-brand-grey-400 text-sm font-mono",children:s.path||"Unknown"})]},a))})]}):e.jsx("div",{className:"text-center py-8 text-brand-grey-400",children:"No quote requests found for this visitor."})}),e.jsx("div",{className:"mt-4 flex justify-end",children:e.jsx("button",{onClick:()=>g(!1),className:"bg-brand-grey-800 hover:bg-brand-grey-700 text-brand-white px-4 py-2 rounded",children:"Close"})})]})}),R&&t&&e.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:e.jsxs("div",{className:"bg-brand-grey-900 border border-brand-grey-700 rounded-xl p-6 max-w-4xl w-full mx-4 max-h-[80vh] overflow-hidden",children:[e.jsxs("div",{className:"flex justify-between items-center mb-4",children:[e.jsxs("h3",{className:"text-lg font-bold text-brand-white",children:["Cart Actions - ",t.visitorName||t.ipAddress]}),e.jsx("button",{onClick:()=>u(!1),className:"text-brand-grey-400 hover:text-brand-white text-xl",children:"×"})]}),e.jsxs("div",{className:"mb-4 text-brand-grey-300 text-sm",children:[e.jsxs("div",{children:["Total Cart Actions: ",t.totalCartActions||0]}),e.jsxs("div",{children:["User: ",t.userId?`${t.userId.name} (${t.userId.email})`:"Anonymous"]}),e.jsxs("div",{children:["Lead Score: ",e.jsx("span",{className:"text-brand-purple-300 font-medium",children:t.leadScore})]})]}),e.jsx("div",{className:"overflow-y-auto max-h-96",children:t.cartActions&&t.cartActions.length>0?e.jsxs("table",{className:"min-w-full",children:[e.jsx("thead",{className:"sticky top-0 bg-brand-grey-900",children:e.jsxs("tr",{className:"text-brand-white text-left border-b border-brand-grey-700",children:[e.jsx("th",{className:"py-2 px-3",children:"Date & Time"}),e.jsx("th",{className:"py-2 px-3",children:"Action"}),e.jsx("th",{className:"py-2 px-3",children:"Service"}),e.jsx("th",{className:"py-2 px-3",children:"Sub-Service"}),e.jsx("th",{className:"py-2 px-3",children:"Page"})]})}),e.jsx("tbody",{children:t.cartActions.map((s,a)=>e.jsxs("tr",{className:"border-b border-brand-grey-700 hover:bg-brand-grey-800",children:[e.jsx("td",{className:"py-2 px-3 text-brand-grey-300 text-sm",children:o(s.timestamp)}),e.jsx("td",{className:"py-2 px-3 text-sm",children:e.jsx("span",{className:`px-2 py-1 rounded text-xs font-medium ${s.action==="add"?"bg-green-500/20 text-green-300":"bg-red-500/20 text-red-300"}`,children:s.action.toUpperCase()})}),e.jsx("td",{className:"py-2 px-3 text-brand-white text-sm font-medium",children:s.serviceName}),e.jsx("td",{className:"py-2 px-3 text-brand-purple-300 text-sm font-medium",children:s.subServiceName}),e.jsx("td",{className:"py-2 px-3 text-brand-grey-400 text-sm font-mono",children:s.path||"Unknown"})]},a))})]}):e.jsx("div",{className:"text-center py-8 text-brand-grey-400",children:"No cart actions found for this visitor."})}),e.jsx("div",{className:"mt-4 flex justify-end",children:e.jsx("button",{onClick:()=>u(!1),className:"bg-brand-grey-800 hover:bg-brand-grey-700 text-brand-white px-4 py-2 rounded",children:"Close"})})]})})]})};export{O as default};
