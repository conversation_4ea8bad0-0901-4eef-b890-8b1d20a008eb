import mongoose from 'mongoose';

// Cart Item Schema for individual services/sub-services
const cartItemSchema = new mongoose.Schema({
  serviceId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Service',
    required: true
  },
  serviceName: {
    type: String,
    required: true
  },
  subServiceId: {
    type: String,
    default: null // null if it's a main service, otherwise sub-service ID
  },
  subServiceName: {
    type: String,
    default: null
  },
  description: {
    type: String,
    default: ''
  },
  price: {
    type: Number,
    default: 0 // You can add pricing later
  },
  quantity: {
    type: Number,
    default: 1,
    min: 1
  },
  addedAt: {
    type: Date,
    default: Date.now
  }
});

// Main Cart Schema
const cartSchema = new mongoose.Schema({
  userId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    default: null // null for guest users
  },
  sessionId: {
    type: String,
    required: true // For guest users, we'll use session ID
  },
  items: [cartItemSchema],
  totalItems: {
    type: Number,
    default: 0
  },
  totalPrice: {
    type: Number,
    default: 0
  },
  status: {
    type: String,
    enum: ['active', 'abandoned', 'converted'],
    default: 'active'
  },
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  },
  expiresAt: {
    type: Date,
    default: () => new Date(Date.now() + 30 * 24 * 60 * 60 * 1000) // 30 days
  }
});

// Indexes for better performance
cartSchema.index({ userId: 1 });
cartSchema.index({ sessionId: 1 });
cartSchema.index({ expiresAt: 1 }, { expireAfterSeconds: 0 });

// Methods
cartSchema.methods.addItem = function(itemData) {
  const existingItemIndex = this.items.findIndex(item => 
    item.serviceId.toString() === itemData.serviceId.toString() &&
    item.subServiceId === itemData.subServiceId
  );

  if (existingItemIndex > -1) {
    // Update existing item quantity
    this.items[existingItemIndex].quantity += itemData.quantity || 1;
  } else {
    // Add new item
    this.items.push(itemData);
  }

  this.updateTotals();
  this.updatedAt = new Date();
  return this.save();
};

cartSchema.methods.removeItem = function(serviceId, subServiceName = null) {
  console.log('🗑️ Backend removeItem called:', {
    serviceId,
    subServiceName,
    currentItems: this.items.length
  });

  const initialLength = this.items.length;

  this.items = this.items.filter(item => {
    // Fix serviceId comparison - extract _id from object if needed
    const itemServiceIdStr = typeof item.serviceId === 'object'
      ? (item.serviceId._id || item.serviceId.id || item.serviceId).toString()
      : item.serviceId.toString();

    const serviceMatch = itemServiceIdStr === serviceId.toString();

    // Match by subServiceId (which contains the name) since that's what we store
    let subServiceMatch = false;
    if (subServiceName === null || subServiceName === undefined) {
      // Removing main service (no subservice)
      subServiceMatch = (item.subServiceId === null || item.subServiceId === undefined);
    } else {
      // Removing specific subservice - compare with subServiceId field (which contains the name)
      subServiceMatch = (item.subServiceId === subServiceName);
    }

    const shouldRemove = serviceMatch && subServiceMatch;

    console.log('🔍 Backend item comparison:', {
      itemServiceIdRaw: typeof item.serviceId === 'object' ? 'OBJECT' : item.serviceId,
      itemServiceIdStr: itemServiceIdStr,
      targetServiceId: serviceId.toString(),
      serviceMatch,
      itemSubServiceId: item.subServiceId,
      targetSubServiceName: subServiceName,
      subServiceMatch,
      shouldRemove,
      itemTitle: item.serviceName + (item.subServiceName ? ` - ${item.subServiceName}` : '')
    });

    return !shouldRemove;
  });

  const finalLength = this.items.length;
  console.log('🗑️ Backend removal result:', {
    initialLength,
    finalLength,
    removed: initialLength - finalLength
  });

  this.updateTotals();
  this.updatedAt = new Date();
  return this.save();
};

cartSchema.methods.updateItemQuantity = function(serviceId, subServiceId, quantity) {
  const item = this.items.find(item => 
    item.serviceId.toString() === serviceId.toString() &&
    item.subServiceId === subServiceId
  );

  if (item) {
    item.quantity = Math.max(1, quantity);
    this.updateTotals();
    this.updatedAt = new Date();
    return this.save();
  }
  
  throw new Error('Item not found in cart');
};

cartSchema.methods.updateTotals = function() {
  this.totalItems = this.items.reduce((total, item) => total + item.quantity, 0);
  this.totalPrice = this.items.reduce((total, item) => total + (item.price * item.quantity), 0);
};

cartSchema.methods.clearCart = function() {
  this.items = [];
  this.totalItems = 0;
  this.totalPrice = 0;
  this.updatedAt = new Date();
  return this.save();
};

// Static methods
cartSchema.statics.findBySessionId = function(sessionId) {
  return this.findOne({ sessionId, status: 'active' }).populate('items.serviceId');
};

cartSchema.statics.findByUserId = function(userId) {
  return this.findOne({ userId, status: 'active' }).populate('items.serviceId');
};

cartSchema.statics.createOrGetCart = async function(sessionId, userId = null) {
  let cart;
  let sessionCart;

  // If user is authenticated, look for user cart first
  if (userId) {
    cart = await this.findByUserId(userId);

    // If no user cart exists, check for session cart to merge
    if (!cart) {
      sessionCart = await this.findBySessionId(sessionId);
      if (sessionCart) {
        // Convert session cart to user cart
        sessionCart.userId = userId;
        await sessionCart.save();
        cart = sessionCart;
      }
    }
  }

  // If still no cart, look for session cart
  if (!cart) {
    cart = await this.findBySessionId(sessionId);
  }

  // Create new cart if none exists
  if (!cart) {
    cart = new this({
      sessionId,
      userId,
      items: [],
      totalItems: 0,
      totalPrice: 0
    });
    await cart.save();
  }

  return cart;
};

// Pre-save middleware
cartSchema.pre('save', function(next) {
  this.updateTotals();
  this.updatedAt = new Date();
  next();
});

const Cart = mongoose.model('Cart', cartSchema);

export default Cart;
